// devicequery_multillm/llm/client.go
package llm

import (
	"context"
	"encoding/json"
)

// LLMProviderType defines the type of LLM provider.
type LLMProviderType string

const (
	ProviderGemini LLMProviderType = "gemini"
	ProviderOpenAI LLMProviderType = "openai"
	ProviderOllama LLMProviderType = "ollama"
	ProviderOpenRouter LLMProviderType = "openrouter"
)

// Message represents a single message in a chat conversation.
type Message struct {
	Role       string     `json:"role"` // "user", "assistant" (model/model), "system", "tool"
	Content    string     `json:"content,omitempty"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`   // For when the assistant requests a tool call
	ToolCallID string     `json:"tool_call_id,omitempty"` // For tool response message, linking to the request
	Name       string     `json:"name,omitempty"`         // For tool role, the name of the function that was called
}

// ToolCall represents a tool call requested by the LLM.
type ToolCall struct {
	ID       string         `json:"id"`       // ID of the tool call, generated by the LLM
	Type     string         `json:"type"`     // Usually "function"
	Function FunctionCall `json:"function"` // The function to be called
}

// FunctionCall contains the name and arguments of the function to be called.
type FunctionCall struct {
	Name      string `json:"name"`      // Name of the function
	Arguments string `json:"arguments"` // Arguments as a JSON string
}

// LLMConfig holds configuration for an LLM client.
type LLMConfig struct {
	Provider    LLMProviderType
	Model       string
	APIKey      string
	BaseURL     string // For OpenAI-compatible APIs like Ollama or custom endpoints
	TableSchema string // Schema of the 'devices' table for the system prompt
}

// LLMResponse is the structured response from the LLM after processing a message.
type LLMResponse struct {
	Message    Message                // The message from the LLM (can contain content and/or tool_calls)
	Usage      map[string]interface{} // Optional: token usage, etc.
	StopReason string                 // e.g., "tool_calls", "stop"
}

// LLMClient defines the interface for interacting with different LLM providers.
type LLMClient interface {
	Init(config LLMConfig) error
	GenerateResponse(ctx context.Context, messages []Message) (*LLMResponse, error)
	Close() error
}

// Helper to marshal arguments for a tool call, typically a map[string]interface{}
func MarshalArguments(args interface{}) (string, error) {
	argBytes, err := json.Marshal(args)
	if err != nil {
		return "", err
	}
	return string(argBytes), nil
}

// Helper to unmarshal arguments from a tool call (string) into a map
func UnmarshalArguments(jsonArgs string) (map[string]interface{}, error) {
	var argsMap map[string]interface{}
	err := json.Unmarshal([]byte(jsonArgs), &argsMap)
	if err != nil {
		return nil, err
	}
	return argsMap, nil
}
