# BBNIM agent guide
The agent can communicate with the agentclient of the device and send commands.

## Prerequisites
The agent needs to know the Nimbl network service URL. There are two methods:
1. The agent client of the device uses https to communicate with Nimbl network service, the Nimbl network service needs a Nimbl service URL and adds the flag -ns when Nimbl network service starts. We use caddy as an example 1 in windows.
2. The agent client of the device uses http to communicate with Nimbl network service. We will use the IP of the same domain as your device as the URL as an example 2 in windows..

```shell
# example 1 : 
./caddy.exe reverse-proxy --from *********** --to http://localhost:27182
./bbnmssvc.exe -n client -r http://***********:27182 -rs ***********:5514 -ns https://***********

# example 2 :
# If bbnim network service local IP address is *********** and ************.
# If your device IP address is ***********00, bbnim will send this URL http://***********:27182.
# If your device IP address is **************, bbnim will send this URL http://************:27182.
./bbnmssvc.exe -n client -r http://***********:27182 -rs ***********:5514
```
Note: If your device is celluar device, you can register your Nimbl network service URL on the agent server.

## Process
The communication process between the `agentclient` and the `nimbl network service`:
1. The `agentclient` will request the `nimbl network service URL` from the `nimbl network service` or agent registation server.
2. When the `agentclient` can successfully connect with `nimbl network service`, the `agentclient` will send device information to the `nimbl network service`.
3. The `agentclient` will poll the command and execute the command.
4. If the `agentclient` completes the command, the `agentclient` will send an http POST command message to the `nimbl network service` agent api (/api/v1/agent/commads). The `nimbl network service` agent api will organize the commands and pass the messages into the `nimbl network service` commands list.

## Commands
Go to the `scripts` tab of the web UI. You need to start frontend progrem.
```shell
./bbnimbl.exe
```

### firmware
update agentclient itself.
```
agent agentclient upgrade [mac address] [file url] [checksum sha256]
```
example:
```shell
agent agentclient upgrade AA-BB-CC-DD-EE-FF https://*************/api/v1/files/agentclient 12c0ced1a84158357525378ebcfa31967dd9bb3a32600602714bfe2222a1d609
```

update device firmware.
```
agent firmware update [mac address] [file url]
```
example:
```shell
agent firmware update AA-BB-CC-DD-EE-FF https://*************/api/v1/files/xxxxxx.dld
```

### reset
reset device.
```
agent reset [mac address]
```
example:
```shell
agent reset AA-BB-CC-DD-EE-FF
```

### beep
beep device.
```
agent beep [mac address]
```
example:
```shell
agent beep AA-BB-CC-DD-EE-FF
```

### save config
Save running config to Device
```
agent config save [mac address]
```
example:
```shell
agent config save AA-BB-CC-DD-EE-FF
```

### snmp
set snmp status.
```
agent snmp enable [mac address] [enable]
```
example:
```shell
agent snmp enable AA-BB-CC-DD-EE-FF 1
agent snmp enable AA-BB-CC-DD-EE-FF 0
```

### port
set port enable setting.
```
agent config port enable [mac address] [port name] [enable]
```
Example :
```shell
agent config port enable AA-BB-CC-DD-EE-FF port1 1
agent config port enable AA-BB-CC-DD-EE-FF port1 0
```

### syslog
set syslog setting.
```
agent config syslog set [mac address] [enable] [server ip] [server port] [log level] [log to flash]
```
example:
```shell
agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1
```

get syslog setting, it will feedback syslog setting in command result.
```
agent config syslog get [mac address]
```
example:
```shell
agent config syslog get AA-BB-CC-DD-EE-FF
```

### snmp trap
set snmp trap setting.
```
agent snmp trap [trap option] [mac address] [server ip] [server port] [community]
```
example:
```shell
agent snmp trap add AA-BB-CC-DD-EE-FF ************* 5162 public
agent snmp trap del AA-BB-CC-DD-EE-FF ************* 5162 public
```

get snmp trap setting, it will feedback snmp trap setting in command result.
```
agent snmp trap get [mac address]
```
example:
```shell
agent snmp trap get AA-BB-CC-DD-EE-FF
```

### network
set network setting.
```
agent config network set [mac address] [ip] [mask] [gateway] [hostname] [dhcp]
```
example:
```shell
agent config network set AA-BB-CC-DD-EE-FF *********** ************* 0.0.0.0 switch 1

agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0
```

### device information
send device information to nimbl.
```
agent devinfo send [mac address]
```
example:
```shell
agent devinfo send AA-BB-CC-DD-EE-FF
```

### topology information
send topology information to nimbl.
```
agent topologyinfo send [mac address]
```
example:
```shell
agent topologyinfo send AA-BB-CC-DD-EE-FF
```

### port and power information
send port and power information to the Nimbl.
```
agent portpwinfo send [mac address]
```
Example :
```shell
agent portpwinfo send AA-BB-CC-DD-EE-FF
```

### Device user
Add new user. Unable to add an account with `admin` name.
```
agent config user add [mac address] [username] [passwd] [permission]
```
Example :
```shell
agent config user add AA-BB-CC-DD-EE-FF daniel daniel user
agent config user add AA-BB-CC-DD-EE-FF daniel daniel admin
```

Edit user password.
```
agent config user edit [mac address] [username] [passwd] [new passwd]
```
Example :
```shell
agent config user edit AA-BB-CC-DD-EE-FF daniel daniel default
```

Delete user. Unable to delete an account with `admin` name.
```
agent config user del [mac address] [username] [passwd]
```
Example :
```shell
agent config user del AA-BB-CC-DD-EE-FF daniel daniel
```

### GPS enable
Set GPS enable setting.
```
agent config gps enable [mac address] [enable]
```
Example :
```shell
agent config gps enable AA-BB-CC-DD-EE-FF 1
agent config gps enable AA-BB-CC-DD-EE-FF 0
```

### openvpn
#### openvpn general setting
set openvpn general setting.

##### enable and mode
The `enable` has two options: 0 or 1. The 0 means `disable` and the 1 means `enable`. When the `server mode` is enabled, the `client mode` will be disabled. Only one mode can be active at the same time.

##### protocol
The `protocol` has two options: 0 or 1. The 0 means `udp` and the 1 means `tcp`.

##### port
The `port` set port setting.

##### encryption cipher
The `cipher` has five options: 0 to 4. The 0 means `blowfish`, the 1 means `AES256`, the 2 means `AES192`, the 3 means `AES128` and the 4 means `disable`.

##### hash algorithm
The `hash` has five options: 0 to 4. The 0 means `SHA1`, the 1 means `MD5`, the 2 means `SHA256`, the 3 means `SHA512` and the 4 means `disable`.

##### compression
The `compress` has three options: 0 to 2. The 0 means `LZ4`, the 1 means `LZO` and the 2 means `disable`.

##### authentication mode
The `auth mode` has two options: 0 or 1. The 0 means `SSL/TLS` and the 1 means `static key`. If the `auth mode` set `SSL/TLS`, the `server mode` just can set `virtual IP`. If the `auth mode` set `static key`, the `server mode` can set `local virtual IP` and `remote virtual IP`. If the `auth mode` set `SSL/TLS`, the `client mode` just can set `remote IP/FQDN`. If the `auth mode` set `static key`, the `client mode` can set `local virtual IP` and `remote virtual IP`.

##### push LAN to clients
The `Push LAN to clients` has two options: 0 or 1. The 0 means `disable` and the 1 means `enable`. Only `server mode` is selected. When this option is enabled, the device will push the LAN port subnet to the OpenVPN remote client so that the remote client will add a route to the device local network.

```
agent openvpn set [mac address] [mode] [enable] [protocol] [port] [cipher] [hash] [compress] [auth mode] [Push LAN to clients] [virtual IP] [remote IP/FQDN] [local virtual IP] [remote virtual IP]

# server, authentication mode is SSL/TLS
agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress] 0 [Push LAN to clients] [virtual IP]

# server, authentication mode is static key
agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress] 1 [Push LAN to clients] [local virtual IP] [remote virtual IP]

# client, authentication mode is SSL/TLS
agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress] 0 [remote IP/FQDN]

# client, authentication mode is static key
agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress] 1 [remote IP/FQDN] [local virtual IP] [remote virtual IP]
```
Example:
```shell
agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 0 0 ********
agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 0 ***********
agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 1 1 ******** ********
agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 1 *********** ******** ********
```

#### generate openvpn keys
Agent will generate VPN server key if the [vpn client name] field do not exist. The `server`, `ca` and `static` key will be created together. The generation of the `dh`(Diffie-Hellman) key is very slow, and you will need to wait for a long time. Therefore, the `dh` key uses the native `dh` key by default, and this command does not generate the `dh` key.

Agent will generate VPN client key if the [vpn client name] field exists, otherwise VPN server key generated.
```
agent openvpn keys generate [mac address] [country code] [country Name] [city] [organization] [organizational Unit] [email Address] [vpn client name]
```
Example:
```shell
agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL>
agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL> client1
```

#### upload openvpn keys
Upload openvpn keys to the agent client.
##### keys
The `keys` have seven options: ca, serverCert, serverKey, clientCert, clientKey, staticKey and dh.

```
agent openvpn keys upload [mac address] [keys] [url] [sha256]
```
Example:
```shell
agent openvpn keys upload AA-BB-CC-DD-EE-FF ca http://*************:27182/api/v1/files/ca.crt 3c933f3374c95451ea936df3bc009ed7df98f24cd0a09c40e6603d115d685e38
agent openvpn keys upload AA-BB-CC-DD-EE-FF dh http://*************:27182/api/v1/files/dh2048.pem 73ef04589be955555771d6beeed930251fbb98acc446096d2844861860609eb1
```

#### download openvpn keys to the nimbl api
Download openvpn keys to the nimbl api `/api/v1/agent/openvpn/keys`. The openvpn keys include `ca.crt`, `client.crt`, `client.key`, `statickey.key`. The `client name` is client openvpn key name.

```
agent openvpn keys download [mac address] [client name]
```
Example:
```shell
agent openvpn keys download AA-BB-CC-DD-EE-FF client1
```

#### get openvpn keys from the nimbl api
get openvpn client keys from the nimbl api `/api/v1/agent/openvpn/keys`. The `client name` is client openvpn key name. The keys will be returned in the `result`.

```
agent openvpn keylist [client name]
```
Example:
```shell
agent openvpn keylist client1
```

#### openvpn status
set openvpn status. If the state is set to `start`, it will be decided to start `server mode` or `client mode` according to the `enable` of the `openvpn general setting`. If the state is set to `stop`, it will stop openvpn.

```
agent openvpn status [mac address] start
agent openvpn status [mac address] stop
```
Example:
```shell
agent openvpn status AA-BB-CC-DD-EE-FF start
agent openvpn status AA-BB-CC-DD-EE-FF stop
```

### ipsec
set ipsec setting.
```
agent ipsec set [mac address] [enable] [peer addr] [remote subnet] [local subnet] [conn type] [share key] [p1 mode] [p1 dh group] [p1 encrypt] [p1 auth] [p1 lifetime] [p2 proto] [p2 fwd secrecy] [p2 encrypt] [p2 auth] [p2 lifetime] [dpd action] [dpd interval] [dpd timeout]
```
Example:
```shell
agent ipsec set AA-BB-CC-DD-EE-FF 1 none none none 0 secrets 0 0 0 0 10800 0 0 0 0 3600 1 30 120
agent ipsec set AA-BB-CC-DD-EE-FF 1 *********** ***********/24 *********/24 1 secrets 1 1 1 1 3600 1 1 1 1 3600 1 30 120
```

set ipsec status.
```
agent ipsec status [mac address] [status]
```
Example:
```shell
agent ipsec status AA-BB-CC-DD-EE-FF start
agent ipsec status AA-BB-CC-DD-EE-FF stop
```

### SSH

Some ssh commands in order to create reverse tunnel.

#### start ssh
Start reverse ssh tunnel.
```shell
agent ssh reverse start [mac] [domain] [listen port] [remote port] [username] [ssh server port]
```
Example:
```shell
agent ssh reverse start AA-BB-CC-DD-EE-FF domain.com 2222 443 root 22
```

#### stop ssh
Stop ssh reversing in the device.
```shell
agent ssh reverse stop [mac] [domain]
```
Example:
```shell
agent ssh reverse stop AA-BB-CC-DD-EE-FF domain.com
```

#### ssh status
Stop ssh reversing in the device.
```shell
agent ssh reverse status [mac]
```
Example:
```shell
agent ssh reverse status AA-BB-CC-DD-EE-FF
```

#### ssh auto satrt
Set reverse ssh tunnel to autostart.
```shell
agent ssh reverse websrv [mac]
```
Example:
```shell
agent ssh reverse websrv AA-BB-CC-DD-EE-FF
```
