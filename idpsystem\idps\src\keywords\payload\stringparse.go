package payload

import (
	"math"
	"strconv"
)

func stringParseU16RangeCheck(res *uint16, base int, v string, min, max uint16) int {
	n, err := strconv.ParseUint(v, base, 64)
	if err != nil {
		return -1
	}

	*res = uint16(n)
	if *res < min || *res > max {
		return -1
	}

	if n != uint64(*res) {
		return -1
	}
	return 1
}
func stringParseI32RangeCheck(res *int32, base int, v string, min, max int32) int {
	n, err := strconv.ParseInt(v, base, 64)
	if err != nil {
		return -1
	}

	if n < math.MinInt32 || n > math.MaxInt32 {
		return -1
	}

	*res = int32(n)
	if *res < min || *res > max {
		return -1
	}
	if n != int64(*res) {
		return -1
	}
	return 1
}

func stringParseUint16(res *uint16, base int, v string) int {

	n, err := strconv.ParseUint(v, base, 64)
	if err != nil {
		return -1
	}

	if n > math.MaxUint16 {
		return -1
	}

	*res = uint16(n)

	if uint16(n) != *res {
		return -1
	}

	return 1
}
