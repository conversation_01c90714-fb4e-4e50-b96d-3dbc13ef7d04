import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Checkbox,
  Select,
  Space,
  Tooltip,
  theme as antdTheme,
  Flex,
} from "antd";
import G6 from "@antv/g6";
import domtoimage from "dom-to-image";
import saveAs from "file-saver";
import { useDispatch, useSelector } from "react-redux";
import {
  getGraphDataOnClientChange,
  getTopologyData,
  newTopologySelector,
  saveNodesPosition,
  setGPhysics,
  setIsEditMode,
} from "../../features/topology/topologySlice";
import { useEffect } from "react";
import { useState } from "react";
import { TopologyImage } from "../../components/topology/TopologyImage";
import { getFilename } from "../../components/exportData/ExportData";
import {
  FileAddOutlined,
  FileSyncOutlined,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import ManualTopoForm from "../../components/topology/ManualTopoFormModel";
import {
  useAddTopologyMutation,
  useSaveRestoreTopologyMutation,
} from "../../app/services/commandApi";
import { createManualTpologyData } from "../../utils/comman/dataMapping";
import ManualTopoSaveRestoreForm from "../../components/topology/ManualTopoSaveRestoreForm";

G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";
      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint === null ? 0 : startPoint.x,
          y: startPoint === null ? 0 : startPoint.y,
          fill: color,
          r: 3,
        },
      });
      circle.animate(
        (ratio) => {
          const tmpPoint = shape.getPoint(ratio);
          return {
            x: tmpPoint === null ? 0 : tmpPoint.x,
            y: tmpPoint === null ? 0 : tmpPoint.y,
          };
        },
        {
          repeat: true, // Whether executes the animation repeatly
          duration: 3000, // the duration for executing once
        }
      );
    },
    update: undefined,
  },
  "line" // extend the built-in edge 'cubic'
);

const animateCfg = { duration: 200, easing: "easeCubic" };

const toolbar = new G6.ToolBar({
  position: { x: 10, y: 10 },
  getContent: () => `
  <ul class='g6-component-toolbar'>
    <li  code='zoomOut'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
        <path d="M658.432 428.736a33.216 33.216 0 0 1-33.152 33.152H525.824v99.456a33.216 33.216 0 0 1-66.304 0V461.888H360.064a33.152 33.152 0 0 1 0-66.304H459.52V296.128a33.152 33.152 0 0 1 66.304 0V395.52H625.28c18.24 0 33.152 14.848 33.152 33.152z m299.776 521.792a43.328 43.328 0 0 1-60.864-6.912l-189.248-220.992a362.368 362.368 0 0 1-215.36 70.848 364.8 364.8 0 1 1 364.8-364.736 363.072 363.072 0 0 1-86.912 235.968l192.384 224.64a43.392 43.392 0 0 1-4.8 61.184z m-465.536-223.36a298.816 298.816 0 0 0 298.432-298.432 298.816 298.816 0 0 0-298.432-298.432A298.816 298.816 0 0 0 194.24 428.8a298.816 298.816 0 0 0 298.432 298.432z"></path>
      </svg>
    </li>
    <li code='zoomIn'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
        <path d="M639.936 416a32 32 0 0 1-32 32h-256a32 32 0 0 1 0-64h256a32 32 0 0 1 32 32z m289.28 503.552a41.792 41.792 0 0 1-58.752-6.656l-182.656-213.248A349.76 349.76 0 0 1 480 768 352 352 0 1 1 832 416a350.4 350.4 0 0 1-83.84 227.712l185.664 216.768a41.856 41.856 0 0 1-4.608 59.072zM479.936 704c158.784 0 288-129.216 288-288S638.72 128 479.936 128a288.32 288.32 0 0 0-288 288c0 158.784 129.216 288 288 288z" p-id="3853"></path>
      </svg>
    </li>
    <li code='realZoom'>
      <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">
        <path d="M384 320v384H320V320h64z m256 0v384H576V320h64zM512 576v64H448V576h64z m0-192v64H448V384h64z m355.968 576H92.032A28.16 28.16 0 0 1 64 931.968V28.032C64 12.608 76.608 0 95.168 0h610.368L896 192v739.968a28.16 28.16 0 0 1-28.032 28.032zM704 64v128h128l-128-128z m128 192h-190.464V64H128v832h704V256z"></path>
      </svg>
    </li>
    
  </ul>`,
  handleClick: (code, graph) => {
    switch (code) {
      case "zoomOut":
        graph.zoom(1.2, undefined, true, animateCfg);
        break;
      case "zoomIn":
        graph.zoom(0.8, undefined, true, animateCfg);
        break;
      case "realZoom":
        graph.zoomTo(1, undefined, true, animateCfg);
        break;
      case "autoZoom":
        graph.fitView(5, undefined, true, animateCfg);
        break;
    }
  },
});

const TopologyPage = () => {
  // local state manage
  const { token } = antdTheme.useToken();
  const [isRendered, setIsRendered] = useState(false);
  const { notification } = App.useApp();
  const [openAddtopoForm, setOpenAddtopoForm] = useState(false);
  const [openSaveRestore, setOpenSaveRestore] = useState(false);
  const [actionType, setActionType] = useState("");
  const [tGraph, setTGraph] = useState(null);
  // redux dispatch and selector
  const dispatch = useDispatch();

  const [addTopology, { isLoading: topoLoading }] = useAddTopologyMutation();
  const [saveRestoreTopology, { isLoading: saveResLoading }] =
    useSaveRestoreTopologyMutation();

  const { isEditMode, gPhysics, gData, savedNodes, clientsData, reqClient } =
    useSelector(newTopologySelector);
  // function handler
  const onChangeEditMode = (e) => {
    dispatch(setIsEditMode(e.target.checked));
  };
  const handleSaveNodePosition = () => {
    const { nodes } = tGraph.save();
    dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
    dispatch(setIsEditMode(false));
  };
  const onChangePhysics = (e) => {
    dispatch(setGPhysics(e.target.checked));
  };
  const handleFitinView = () => {
    if (!tGraph || tGraph.destroyed) return;
    tGraph.fitView(5);
    const { nodes } = tGraph.save();
    dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
  };

  const handleRefresh = () => {
    dispatch(getTopologyData());
    setIsRendered(false);
  };

  function filter(node) {
    return node.tagName !== "i";
  }
  const handleDownloadImage = () => {
    domtoimage
      .toSvg(document.getElementById("topology-container"), {
        filter: filter,
      })
      .then(function (dataUrl) {
        saveAs(dataUrl, `${getFilename("topology")}.svg`);
      });
  };

  //useEffect manage
  useEffect(() => {
    if (!tGraph) {
      const container = document.getElementById("topology-container");
      const width = container.clientWidth;
      const height = container.clientHeight || 500;
      const graph = new G6.Graph({
        container: "topology-container",
        width,
        height,
        plugins: [toolbar],
        renderer: "svg",
        linkCenter: true,
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
        },
        defaultNode: {
          type: "image",
          size: [56, 56],
        },
        defaultEdge: {
          type: "circle-running",
        },
      });
      setTGraph(graph);
    }
  }, [tGraph]);

  useEffect(() => {
    const container = document.getElementById("topology-container");
    window.onresize = () => {
      if (!tGraph || tGraph.get("destroyed")) return;
      if (!container || !container.clientWidth || !container.clientHeight)
        return;
      tGraph.changeSize(container.clientWidth, container.clientHeight);
    };
  }, []);

  useEffect(() => {
    if (!tGraph || tGraph.destroyed) return;
    if (gPhysics) {
      tGraph.updateLayout({
        type: "force",
        linkDistance: 250, // Edge length
        nodeStrength: -50,
        edgeStrength: 0.1,
        collideStrength: 0.8,
        nodeSize: 30,
        alpha: 0.3,
        alphaDecay: 0.028,
        alphaMin: 0.01,
        forceSimulation: null,
        onTick: () => {
          console.log("ticking");
        },
        onLayoutEnd: () => {
          const { nodes } = tGraph.save();
          dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
          dispatch(setGPhysics(false));
        },
      });
    }
  }, [gPhysics]);

  const getNodePosition = (nodeId) => {
    let x = undefined;
    let y = undefined;
    savedNodes?.forEach((prevNode) => {
      if (nodeId === prevNode.id) {
        if (prevNode.x !== null || prevNode.y !== null) {
          x = prevNode.x;
          y = prevNode.y;
        }
      }
    });
    return { x, y };
  };

  const createData = (data) => {
    const editblen = data.nodes.map((o) => {
      return {
        ...o,
        x: isEditMode ? undefined : getNodePosition(o.id).x,
        y: isEditMode ? undefined : getNodePosition(o.id).y,
        label: `${o.ipAddress}\n${o.macAddress}\n${o.modelname}`,
        img: TopologyImage(o.modelname),
        labelCfg: {
          style: {
            fill: token.colorText,
            fontSize: 18,
            background: {
              fill: "transparent",
              padding: [2, 2, 2, 2],
            },
          },
          position: "bottom",
        },
      };
    });
    const editblee = data.edges.map((o) => ({
      ...o,
      label: `${o.source}_${o.sourcePort}\n${o.target}_${o.targetPort}`,
      color: o.blockedPort === "true" ? "#faad14" : token.colorTextDisabled,
      circleColor:
        o.blockedPort === "true" ? "transparent" : token.colorPrimary,
      labelCfg: {
        style: {
          fill: token.colorText,
          fontSize: 18,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      },
      style: {
        lineWidth: 2,
      },
    }));
    return { nodes: editblen, edges: editblee };
  };

  useEffect(() => {
    if (tGraph && !isRendered && gData.nodes.length > 0) {
      tGraph.data(createData(gData));
      tGraph.render();
      setIsRendered(true);
    }
    return () => {
      if (tGraph) {
        tGraph.destroyLayout();
        tGraph.changeData(createData(gData));
      }
    };
  }, [token, gData, isEditMode]);

  useEffect(() => {
    if (!tGraph || tGraph.destroyed) return;
    tGraph.changeData(createData(gData));
  }, [token, gPhysics, isEditMode, gData]);

  useEffect(() => {
    dispatch(getTopologyData());
  }, []);
  const handleSelectChange = (value) => {
    dispatch(getGraphDataOnClientChange(value));
    setIsRendered(false);
  };

  const handleAddTopology = async (values) => {
    try {
      const data = createManualTpologyData(values);
      console.log(data);
      await addTopology(data).unwrap();
      notification.success({
        message: "successfully added topology!",
      });
      handleRefresh();
    } catch (error) {
      notification.error({ message: error.data.error || error.data || error });
    } finally {
      setOpenAddtopoForm(false);
    }
  };

  const handleSaveRestoreTopology = async (values) => {
    if (actionType === "") {
      setOpenSaveRestore(false);
      notification.error({ message: "action type is missing!" });
      return;
    }
    try {
      await saveRestoreTopology({
        filename: `${values.filename}.json`,
        actionType,
      }).unwrap();
      notification.success({
        message: `successfully ${actionType} topology!`,
      });
      handleRefresh();
    } catch (error) {
      notification.error({ message: error.data.error || error.data || error });
    } finally {
      setOpenSaveRestore(false);
      setActionType("");
    }
  };

  return (
    <>
      <Card
        bordered={false}
        title="Device Topology"
        bodyStyle={{
          padding: 8,
          position: "relative",
          width: "100%",
          height: `calc(100vh - 145px)`,
        }}
        extra={
          <Flex gap={10} align="center" wrap="wrap">
            {clientsData.length > 0 && (
              <Select
                defaultValue="All Network Service"
                style={{
                  width: 240,
                }}
                value={reqClient}
                onChange={handleSelectChange}
                options={clientsData.map((item) => ({
                  value: item,
                  label: item,
                }))}
              />
            )}

            <Tooltip title="Refresh">
              <Button
                type="text"
                onClick={handleRefresh}
                icon={<ReloadOutlined />}
              ></Button>
            </Tooltip>

            <Tooltip title="add manual topology">
              <Button
                type="text"
                onClick={() => setOpenAddtopoForm(true)}
                icon={<PlusOutlined />}
              ></Button>
            </Tooltip>
            <Tooltip title="save manual topology">
              <Button
                type="text"
                onClick={() => {
                  setActionType("save");
                  setOpenSaveRestore(true);
                }}
                icon={<FileAddOutlined />}
              ></Button>
            </Tooltip>
            <Tooltip title="restore manual topology">
              <Button
                type="text"
                onClick={() => {
                  setActionType("restore");
                  setOpenSaveRestore(true);
                }}
                icon={<FileSyncOutlined />}
              ></Button>
            </Tooltip>

            <Checkbox
              onChange={(e) => onChangeEditMode(e)}
              checked={isEditMode}
            >
              Edit Node Position
            </Checkbox>
            {isEditMode ? (
              <Button type="primary" onClick={handleSaveNodePosition}>
                Save Node Position
              </Button>
            ) : (
              <>
                <Checkbox
                  onChange={(e) => onChangePhysics(e)}
                  checked={gPhysics}
                >
                  Physics
                </Checkbox>
                <Button type="primary" onClick={handleFitinView}>
                  Fit view
                </Button>
              </>
            )}

            <Button type="primary" onClick={handleDownloadImage}>
              Export Toplogy
            </Button>
          </Flex>
        }
      >
        <div
          id="topology-container"
          style={{
            position: "relative",
            border: "1px solid gray",
            overflow: "hidden",
            width: "100%",
            height: "100%",
            background: token.colorBgContainer,
          }}
        ></div>
      </Card>
      <ManualTopoForm
        open={openAddtopoForm}
        onCancel={() => setOpenAddtopoForm(false)}
        onOk={handleAddTopology}
        loading={topoLoading}
      />
      <ManualTopoSaveRestoreForm
        open={openSaveRestore}
        onCancel={() => setOpenSaveRestore(false)}
        onOk={handleSaveRestoreTopology}
        loading={saveResLoading}
      />
    </>
  );
};

export default TopologyPage;

function createDataSaveNodePosition(data = []) {
  return data.map((o) => ({ id: o.id, x: o.x, y: o.y }));
}
