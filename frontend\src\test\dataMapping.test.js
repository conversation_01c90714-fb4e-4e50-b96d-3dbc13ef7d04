import {
  convertToJsonObject,
  getTopologyClient,
  ParseJsonToTopoData,
  mapDeviceDataForExportOnOff,
  checkTimestampDiff,

} from '../utils/comman/dataMapping';
import { describe, expect, it } from 'vitest';

describe('convertToJsonObject', () => {
  it('should convert a single parameter to a JSON object', () => {
    const params = 'param1';
    const flags = {};
    const expectedJson = '[{"command":"param1"}]';
    expect(convertToJsonObject(params, flags)).toEqual(expectedJson);
  });

  it('should convert multiple parameters to a JSON object', () => {
    const params = 'param1\nparam2\nparam3';
    const flags = {};
    const expectedJson = '[{"command":"param1"},{"command":"param2"},{"command":"param3"}]';
    expect(convertToJsonObject(params, flags)).toEqual(expectedJson);
  });
});

describe('getTopologyClient', () => {
  it('should return an array of client keys', () => {
    const data = {
      client1: {},
      client2: {},
      client3: {},
    };
    const expectedClients = ['client1', 'client2', 'client3'];

    expect(getTopologyClient(data)).toEqual(expectedClients);
  });
});


describe('getTopologyClient', () => {
  it('should return an array of client names', () => {
    const data = {
      client1: {},
      client2: {},
    };
    const expectedOutput = ['client1', 'client2'];
    expect(getTopologyClient(data)).toEqual(expectedOutput);
  });
});

describe('checkTimestampDiff', () => {
  it('should calculate time differences and filter out specific mac addresses', () => {
    const currentData = [
      { mac: '11-22-33-44-55-66', timestamp: '1234567890' },
      { mac: '22-33-44-55-66-77', timestamp: '1234567895' },
    ];
    expect(currentData).not.toBeNull();
    expect(currentData.length).toBeGreaterThan(0);
    currentData.forEach(item => {
      expect(item.mac).not.toBeNull();
      expect(item.timestamp).not.toBeNull();
    });
    
    const expectedOutput = [{ mac: '22-33-44-55-66-77', timestamp: '1234567895', timeDiff: expect.any(Number) }];
    expect(checkTimestampDiff(currentData)).toEqual(expectedOutput);
  });
});


describe('mapDeviceDataForExportOnOff', () => {
  it('should map time differences to "online" or "offline"', () => {
    const deviceData = [{ timeDiff: 91 }, { timeDiff: 89 }];
    const expectedOutput = [{ timeDiff: 'offline' }, { timeDiff: 'online' }];
    expect(mapDeviceDataForExportOnOff(deviceData)).toEqual(expectedOutput);
  });
});

describe('ParseJsonToTopoData', () => {
  it('should parse JSON data and group by network services', () => {
    const topodata = {
      1: { services: 'service1' },
      2: { services: 'service2' },
      3: { services: 'service1' },
    };
    const expectedOutput = {
      service1: [{ services: 'service1' }, { services: 'service1' }],
      service2: [{ services: 'service2' }],
    };
    expect(ParseJsonToTopoData(topodata)).toEqual(expectedOutput);
  });
});
