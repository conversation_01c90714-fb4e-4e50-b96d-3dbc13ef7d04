import React, { memo, useState } from "react";
import { Modal, Form, Select, Input, Button, message } from "antd";
import { useSetLLMsessionMutation } from "../../../app/services/aiassistApi";
import { useChatStore } from "../../../features/chat/chat-store";

const { Option } = Select;

// Provider options based on aiassistant.md
const PROVIDERS = [
  { value: "openai", label: "OpenAI" },
  { value: "gemini", label: "Gemini" },
  { value: "ollama", label: "Ollama (Local)" },
  { value: "openrouter", label: "OpenRouter" },
];

// Default models for each provider
const DEFAULT_MODELS = {
  openai: "gpt-4o",
  gemini: "gemini-1.5-flash-latest",
  ollama: "llama3:8b",
  openrouter: "openai/gpt-3.5-turbo",
};

const NewSessionModal = memo(() => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const {
    isNewSessionModalOpen,
    toggleNewSessionModal,
    setCurrentSession,
    clearMessages,
    addMessage,
  } = useChatStore();

  const [createSession] = useSetLLMsessionMutation();

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const sessionData = {
        provider: values.provider,
        model: values.model,
        ...(values.api_key && { api_key: values.api_key }),
        ...(values.base_url && { base_url: values.base_url }),
      };

      const response = await createSession(sessionData).unwrap();

      // Set the new session as current
      setCurrentSession(response);

      // Clear existing messages and add welcome message
      clearMessages();
      addMessage({
        content: `New session started with ${response.provider}/${response.model}. How can I help you today?`,
        role: "assistant",
        timestamp: new Date(),
      });

      message.success("New session created successfully!");
      toggleNewSessionModal();
      form.resetFields();
    } catch (error) {
      console.error("Failed to create session:", error);
      message.error("Failed to create new session. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    toggleNewSessionModal();
    form.resetFields();
  };

  return (
    <Modal
      title="Create New AI Session"
      open={isNewSessionModalOpen}
      onCancel={handleCancel}
      footer={null}
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          provider: "openai",
          model: DEFAULT_MODELS.openai,
        }}
      >
        <Form.Item
          name="provider"
          label="Provider"
          rules={[{ required: true, message: "Please select a provider" }]}
        >
          <Select placeholder="Select AI provider">
            {PROVIDERS.map((provider) => (
              <Option key={provider.value} value={provider.value}>
                {provider.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="model"
          label="Model"
          rules={[{ required: true, message: "Please enter a model name" }]}
        >
          <Input placeholder="Enter model name (e.g., gpt-4o, gemini-1.5-flash-latest, llama3:8b)" />
        </Form.Item>

        <Form.Item
          name="api_key"
          label="API Key (Optional)"
          help="Leave empty to use environment variables"
        >
          <Input.Password
            placeholder="Enter API key (optional)"
            autoComplete="off"
          />
        </Form.Item>

        <Form.Item
          name="base_url"
          label="Base URL (Optional)"
          help="Custom endpoint URL (e.g., http://localhost:11434 for Ollama)"
        >
          <Input placeholder="Enter custom base URL (optional)" />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
          <div style={{ display: "flex", gap: 8, justifyContent: "flex-end" }}>
            <Button onClick={handleCancel}>Cancel</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Create Session
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
});

NewSessionModal.displayName = "NewSessionModal";

export default NewSessionModal;
