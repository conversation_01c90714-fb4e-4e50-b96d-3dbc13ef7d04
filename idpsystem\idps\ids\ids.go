package ids

import (
	"encoding/json"
	"fmt"
	"log"
	"mnms/idpsystem/idps/detect"
	"os"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/pcap"
	"github.com/google/gopacket/pcapgo"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	snapshotLen int32 = 65536
	promiscuous bool  = false
	dir               = "logs"
	fpcdir            = fmt.Sprintf("%v/fpc", dir)
	jsonname          = "eve.json"
	jsonpath          = fmt.Sprintf("%v/%v", dir, jsonname)
	maxsize     int64 = 1024 * 1024 * 10 //10M bytes
)

// NewIds  create NewIds
//
// pcap enable/disbale out file.pcap
//
// json enable/disbale out file.json
func NewIds(pcap, json bool) (*Ids, error) {
	ids := &Ids{bfpc: pcap, bjson: json}
	if ids.bfpc {
		err := os.MkdirAll(fpcdir, os.ModePerm)
		if err != nil {
			return nil, err
		}
	}
	if ids.bjson {
		f, err := os.OpenFile(jsonpath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return nil, err
		}
		defer f.Close()
	}
	d, err := detect.NewDetectEngineCtx()
	if err != nil {
		return nil, err
	}
	ids.detect = d
	return ids, nil
}

type Ids struct {
	handles   []*pcap.Handle
	bfpc      bool
	fpcfile   *os.File
	mu        sync.Mutex
	bjson     bool
	jsonlog   *lumberjack.Logger
	detect    *detect.DetectEngineCtx
	includelo bool
	event     Eventfunc
}

func (i *Ids) Enablelo(b bool) {
	i.includelo = b

}

func (i *Ids) RegisterMatchEvent(e Eventfunc) {
	i.event = e
}

// writePacket save packet to file.pcap
func (i *Ids) writePacket(ci gopacket.CaptureInfo, data []byte) {
	if i.bfpc {
		i.mu.Lock()
		defer i.mu.Unlock()
		if i.fpcfile == nil {
			i.fpcfile, _ = i.createfpc()
			w := pcapgo.NewWriter(i.fpcfile)
			w.WriteFileHeader(uint32(snapshotLen), layers.LinkTypeEthernet)
			w.WritePacket(ci, data)
		} else {
			s, err := i.fpcfile.Stat()
			if err != nil {
				return
			}
			if s.Size() > maxsize {
				i.fpcfile.Close()
				i.fpcfile, _ = i.createfpc()
				w := pcapgo.NewWriter(i.fpcfile)
				w.WriteFileHeader(uint32(snapshotLen), layers.LinkTypeEthernet)
				w.WritePacket(ci, data)
			} else {
				w := pcapgo.NewWriter(i.fpcfile)
				w.WritePacket(ci, data)
			}

		}
	}

}

// writeJson save packet to file.json
func (i *Ids) writeJson(packet gopacket.Packet) {
	if i.bjson {
		if i.jsonlog == nil {
			i.jsonlog = &lumberjack.Logger{
				Filename:   fmt.Sprintf("%v/%v.json", dir, "eve"),
				MaxSize:    1,
				MaxBackups: 5,
				Compress:   false,
				LocalTime:  true,
			}
			b, err := i.parsingPacketToJson(packet)
			if err != nil {
				return
			}
			i.jsonlog.Write(b)
			i.jsonlog.Write([]byte("\n"))
		} else {
			b, err := i.parsingPacketToJson(packet)
			if err != nil {
				return
			}
			i.jsonlog.Write(b)
			i.jsonlog.Write([]byte("\n"))
		}
	}

}

// parsingPacketToJson parsing packet to json
func (i *Ids) parsingPacketToJson(packet gopacket.Packet) ([]byte, error) {
	info := PackInfo{}
	ethernetLayer := packet.Layer(layers.LayerTypeEthernet)
	if ethernetLayer != nil {
		ethernetPacket, _ := ethernetLayer.(*layers.Ethernet)
		info.Timestamp = time.Now().UTC().Format(time.RFC3339)
		switch ethernetPacket.EthernetType {
		case layers.EthernetTypeIPv4:
			ipLayer := packet.Layer(layers.LayerTypeIPv4)
			if ipLayer != nil {
				ip, _ := ipLayer.(*layers.IPv4)
				info.Proto = ip.Protocol.String()
				info.Src_Ip = ip.SrcIP.String()
				info.Dest_Ip = ip.DstIP.String()
				switch ip.Protocol {
				case layers.IPProtocolTCP:
					tcpLayer := packet.Layer(layers.LayerTypeTCP)
					tcp, _ := tcpLayer.(*layers.TCP)
					info.Src_Port = strconv.Itoa(int(tcp.SrcPort))
					info.Dest_Port = strconv.Itoa(int(tcp.DstPort))
					if name, ok := layers.TCPPortNames[tcp.SrcPort]; ok {
						info.App_Proto = name
					}
					if name, ok := layers.TCPPortNames[tcp.DstPort]; ok {
						info.App_Proto = name
					}
				case layers.IPProtocolUDP:
					udpLayer := packet.Layer(layers.LayerTypeUDP)
					udp, _ := udpLayer.(*layers.UDP)
					info.Src_Port = strconv.Itoa(int(udp.SrcPort))
					info.Dest_Port = strconv.Itoa(int(udp.DstPort))
					if name, ok := layers.UDPPortNames[udp.SrcPort]; ok {
						info.App_Proto = name
					}
					if name, ok := layers.UDPPortNames[udp.DstPort]; ok {
						info.App_Proto = name
					}
				default:
					//info.Stats = ipLayer.LayerPayload()
				}

			}
		default:
			//info.Stats = ethernetLayer.LayerPayload()
		}
		b, err := json.Marshal(info)
		if err != nil {
			fmt.Println(err)
			return nil, err
		}
		return b, err
	} else {
		return nil, errNoPacketExisted
	}
}

func (i *Ids) createfpc() (*os.File, error) {
	f, err := os.Create(fmt.Sprintf("%v/log.%v.pcap", fpcdir, time.Now().Unix()))
	if err != nil {
		return nil, err
	}
	return f, err
}

func (i *Ids) Run() error {
	eths, err := GetEthName()
	if err != nil {
		return err
	}
	for _, eth := range eths {
		if !i.includelo {
			if eth.flags&pacap_lopback == pacap_lopback {
				continue
			}
		}

		go func(eth ethInfo) {
			handle, err := pcap.OpenLive(eth.name, snapshotLen, promiscuous, time.Nanosecond)
			if err != nil {
				log.Printf("Error capturing on %q: %v", eth.name, err)
				return
			}
			BPFFilter(handle)
			var name string
			switch runtime.GOOS {
			case "windows":
				name = FindNameByDescrption(eth.description)
			default:
				name = eth.name
			}
			i.handles = append(i.handles, handle)
			packetSource := gopacket.NewPacketSource(handle, handle.LinkType())
			for packet := range packetSource.Packets() {
				pools.Submit(func() {
					func(packet gopacket.Packet) {
						netlayer := packet.NetworkLayer()
						if netlayer != nil {
							b := make([]byte, 0, len(packet.Data()))
							b = append(b, netlayer.LayerContents()...)
							b = append(b, netlayer.LayerPayload()...)
							i.writePacket(packet.Metadata().CaptureInfo, packet.Data())
							i.writeJson(packet)
							time.Sleep(time.Millisecond * 10)
							i.detect.DetectPacket(newNetLinker(name, i.event), b)
						}
					}(packet)
				})
				/*go func(packet gopacket.Packet) {
					netlayer := packet.NetworkLayer()
					if netlayer != nil {
						b := make([]byte, 0, len(packet.Data()))
						b = append(b, netlayer.LayerContents()...)
						b = append(b, netlayer.LayerPayload()...)
						i.writePacket(packet.Metadata().CaptureInfo, packet.Data())
						i.writeJson(packet)
						time.Sleep(time.Millisecond * 10)
						i.detect.DetectPacket(newNetLinker(name, i.event), b)
					}
				}(packet)*/

			}
		}(eth)
	}
	return nil
}

func (w *Ids) AddGonidsRule(r *gonids.Rule) error {
	return w.detect.LoadGoNidRule(*r)
}

func (i *Ids) ApplyRules() error {
	return i.detect.Apply()
}

func (i *Ids) Build() error {
	return i.detect.Build()

}

func (i *Ids) Close() {
	for _, h := range i.handles {
		h.Close()
	}
}

func newNetLinker(name string, event Eventfunc) detect.NetLinker {
	return &netlink{event: event, name: name}
}

type netlink struct {
	event Eventfunc
	name  string
}

func (n *netlink) Default() {
}

func (n *netlink) Pass() {
}

func (n *netlink) Drop() {
}

func (n *netlink) Alert(e detect.InfoMatched) {
	n.match(e)
}
func (n *netlink) match(e detect.InfoMatched) {
	if n.event != nil {
		go n.event(convertToEvent(n.name, e))
	}
}
