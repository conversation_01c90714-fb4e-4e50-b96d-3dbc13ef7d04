## example program to convert device json

the program takes a json file as input.
This file should contain the output of the result of api call to 
Nimbl endpoint /api/v1/devices

This endpoint produces json object of devices. Each device is an object
inside the json object, keyed by MAC address tag.

This input file can be given to the program which can be used
to produce a json output which is an array of device objects.

for example you can run this program as follows:

    go run main.go ../devices.json > devicelist.json

