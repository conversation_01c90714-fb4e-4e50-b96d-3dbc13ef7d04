-- *****************************************************************
-- SYSLOG-MIB:  
-- ****************************************************************

MGMT-SYSLOG-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    DateAndTime FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInetAddress FROM MGMT-TC
    ;

mgmtSyslogMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for Syslog"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 37 }


MGMTSyslogLevelType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The syslog severity level."
    SYNTAX      INTEGER { error(3), warning(4), notice(5),
                          informational(6), all(8) }

mgmtSyslogMibObjects OBJECT IDENTIFIER
    ::= { mgmtSyslogMib 1 }

mgmtSyslogConfig OBJECT IDENTIFIER
    ::= { mgmtSyslogMibObjects 2 }

mgmtSyslogConfigServer OBJECT IDENTIFIER
    ::= { mgmtSyslogConfig 1 }

mgmtSyslogConfigServerMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the syslog server mode operation. When the mode operation is
         enabled, the syslog message will send out to syslog server."
    ::= { mgmtSyslogConfigServer 1 }

mgmtSyslogConfigServerAddress OBJECT-TYPE
    SYNTAX      MGMTInetAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The domain name of syslog server."
    ::= { mgmtSyslogConfigServer 2 }

mgmtSyslogConfigServerLevel OBJECT-TYPE
    SYNTAX      MGMTSyslogLevelType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates what level of message will send to syslog server. For
         example, the error level will send the specific messages which severity
         code is less or equal than error(3), the warning level will send the
         specific messages which severity code is less or equal than warning(4),
         the notice level will send the specific messages which severity code is
         less or equal than notice(5), the informational level will send the
         specific messages which severity code is less or equal than
         informational(6) and the enumeration option of all(8) isn't used in
         this case."
    ::= { mgmtSyslogConfigServer 3 }

mgmtSyslogStatus OBJECT IDENTIFIER
    ::= { mgmtSyslogMibObjects 3 }

mgmtSyslogStatusHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSyslogStatusHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The syslog history table."
    ::= { mgmtSyslogStatus 1 }

mgmtSyslogStatusHistoryEntry OBJECT-TYPE
    SYNTAX      MGMTSyslogStatusHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains a set of parameters."
    INDEX       { mgmtSyslogStatusHistorySwitchId,
                  mgmtSyslogStatusHistoryMsgId }
    ::= { mgmtSyslogStatusHistoryTable 1 }

MGMTSyslogStatusHistoryEntry ::= SEQUENCE {
    mgmtSyslogStatusHistorySwitchId      Integer32,
    mgmtSyslogStatusHistoryMsgId         Integer32,
    mgmtSyslogStatusHistoryMsgLevel      MGMTSyslogLevelType,
    mgmtSyslogStatusHistoryMsgTimeStamp  DateAndTime,
    mgmtSyslogStatusHistoryMsgText       MGMTDisplayString
}

mgmtSyslogStatusHistorySwitchId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch. For non-stackable switch, the valid value
         is limited to 1. "
    ::= { mgmtSyslogStatusHistoryEntry 1 }

mgmtSyslogStatusHistoryMsgId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of Syslog message."
    ::= { mgmtSyslogStatusHistoryEntry 2 }

mgmtSyslogStatusHistoryMsgLevel OBJECT-TYPE
    SYNTAX      MGMTSyslogLevelType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The severity level of the system log message. Note that enumeration
         option of all(8) isn't used in this case."
    ::= { mgmtSyslogStatusHistoryEntry 3 }

mgmtSyslogStatusHistoryMsgTimeStamp OBJECT-TYPE
    SYNTAX      DateAndTime
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of sysUpTime when this message was generated."
    ::= { mgmtSyslogStatusHistoryEntry 4 }

mgmtSyslogStatusHistoryMsgText OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..4000))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The detailed context of the system log message."
    ::= { mgmtSyslogStatusHistoryEntry 5 }

mgmtSyslogControl OBJECT IDENTIFIER
    ::= { mgmtSyslogMibObjects 4 }

mgmtSyslogControlHistoryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSyslogControlHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The syslog history clear table."
    ::= { mgmtSyslogControl 1 }

mgmtSyslogControlHistoryEntry OBJECT-TYPE
    SYNTAX      MGMTSyslogControlHistoryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains a set of parameters."
    INDEX       { mgmtSyslogControlHistorySwitchId,
                  mgmtSyslogControlHistoryClearLevel }
    ::= { mgmtSyslogControlHistoryTable 1 }

MGMTSyslogControlHistoryEntry ::= SEQUENCE {
    mgmtSyslogControlHistorySwitchId    Integer32,
    mgmtSyslogControlHistoryClearLevel  MGMTSyslogLevelType,
    mgmtSyslogControlHistoryClear       TruthValue
}

mgmtSyslogControlHistorySwitchId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch. For non-stackable switch, the valid value
         is limited to 1. For stackable switch, value 0 means the action is
         applied to all switches."
    ::= { mgmtSyslogControlHistoryEntry 1 }

mgmtSyslogControlHistoryClearLevel OBJECT-TYPE
    SYNTAX      MGMTSyslogLevelType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Indicates which level of message want to clear."
    ::= { mgmtSyslogControlHistoryEntry 2 }

mgmtSyslogControlHistoryClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear syslog history by setting to true."
    ::= { mgmtSyslogControlHistoryEntry 3 }

mgmtSyslogMibConformance OBJECT IDENTIFIER
    ::= { mgmtSyslogMib 2 }

mgmtSyslogMibCompliances OBJECT IDENTIFIER
    ::= { mgmtSyslogMibConformance 1 }

mgmtSyslogMibGroups OBJECT IDENTIFIER
    ::= { mgmtSyslogMibConformance 2 }

mgmtSyslogConfigServerInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSyslogConfigServerMode,
                  mgmtSyslogConfigServerAddress,
                  mgmtSyslogConfigServerLevel }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSyslogMibGroups 1 }

mgmtSyslogStatusHistoryTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSyslogStatusHistorySwitchId,
                  mgmtSyslogStatusHistoryMsgId,
                  mgmtSyslogStatusHistoryMsgLevel,
                  mgmtSyslogStatusHistoryMsgTimeStamp,
                  mgmtSyslogStatusHistoryMsgText }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSyslogMibGroups 2 }

mgmtSyslogControlHistoryTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSyslogControlHistorySwitchId,
                  mgmtSyslogControlHistoryClearLevel,
                  mgmtSyslogControlHistoryClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSyslogMibGroups 3 }

mgmtSyslogMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtSyslogConfigServerInfoGroup,
                       mgmtSyslogStatusHistoryTableInfoGroup,
                       mgmtSyslogControlHistoryTableInfoGroup }

    ::= { mgmtSyslogMibCompliances 1 }

END
