import "@testing-library/jest-dom/extend-expect";
import { Provider } from "react-redux";
import ScriptPage from "../pages/script/ScriptPage";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { store } from "../app/store";
import { loginUser } from "../features/auth/userAuthSlice";
import { GetMibCommandResult } from "../features/mibbrowser/MibBrowserSlice";
import { render, fireEvent, screen } from "@testing-library/react";
import {
  RequestDebugCommand,
} from "../features/debugPage/debugPageSlice";
import { convertToJsonObject } from "../utils/comman/dataMapping";

vi.mock("../../app/store", () => ({
  store: {
    dispatch: vi.fn(),
    getState: () => ({
      mibmgmt: { mibBrowserStatus: "in_progress" },
    }),
  },
}));

describe("ScriptPage Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("renders correctly", () => {
    render(
      <Provider store={store}>
        <ScriptPage />
      </Provider>
    );
    expect(screen.getByText("Request Script")).toBeInTheDocument();
    expect(screen.getByText("Response Result")).toBeInTheDocument();
  });

  it("Should handle GetMibCommandResult correctly when successful", async () => {
    const paramObj = {
      cValue: "snmp get *************",
      pageSize: 10,
      pageNum: 1,
      totalPage: 5,
    };
    await store.dispatch(GetMibCommandResult(paramObj));
    const updatedState = store.getState().mibmgmt;
    expect(updatedState.mibBrowserStatus).toEqual("in_progress");
  });
});

describe("ScriptPage Component", () => {
  it("renders the component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <ScriptPage />
      </Provider>
    );

    expect(container).toBeTruthy();
  });

  it("handles button click correctly", async () => {
    const { getByText } = render(
      <Provider store={store}>
        <ScriptPage />
      </Provider>
    );
    const button = getByText("run command");
    fireEvent.click(button);
  });

  it("Should handles command input with various whitespace characters", async () => {
    let inputCommand = `beep 00-60-E9-28-F4-3E   
                           beep 00-60-E9-28-F4-3E
                           beep 00-60-E9-28-F4-3E   
                        beep              00-60-E9-28-F4-3E  `
    let commandFlags = "client1";
    const cmdJsonObject = convertToJsonObject(inputCommand, commandFlags);
    await store.dispatch(RequestDebugCommand(cmdJsonObject));

    const updatedState = store.getState().debugCmd;
    console.log("updatedState", updatedState);
    expect(updatedState.debugCmdStatus).toEqual("success");
  });
});

it("sets and clears command flags correctly", () => {
  render(
    <Provider store={store}>
      <ScriptPage />
    </Provider>
  );

  const clientInput = screen.getByPlaceholderText("-cc");
  const tagInput = screen.getByPlaceholderText("-ct");
  const kindInput = screen.getByPlaceholderText("-ck");

  fireEvent.change(clientInput, { target: { value: "client1" } });
  fireEvent.change(tagInput, { target: { value: "tag1" } });
  fireEvent.change(kindInput, { target: { value: "kind1" } });

  expect(clientInput.value).toBe("client1");
  expect(tagInput.value).toBe("tag1");
  expect(kindInput.value).toBe("kind1");

  const clearButton = screen.getByRole("button", {
    name: /clear command flags/i,
  });
  fireEvent.click(clearButton);

  expect(clientInput.value).toBe("");
  expect(tagInput.value).toBe("");
  expect(kindInput.value).toBe("");
});
