package mpm

import (
	"bytes"
	"fmt"

	"github.com/flier/gohs/hyperscan"
)

type Flag uint

const (
	OffsetFlag Flag = 1 << iota
)

type Content struct {
	Id      int
	Pattern string
	Offset  uint16
	Nocase  bool
	Flag    Flag
}

func (p Content) express() string {
	var buffer bytes.Buffer
	for i := 0; i < len(p.<PERSON>tern); i++ {
		buffer.WriteString(fmt.Sprintf("\\x%02x", p.Pattern[i]))
	}
	s := buffer.String()
	if p.Flag&OffsetFlag == OffsetFlag && p.Offset > 0 {
		s = fmt.Sprintf(".{%v}%v", p.Offset, s)
	}
	return s
}

func (c Content) createPattern() (*hyperscan.Pattern, error) {
	if len(c.<PERSON>tern) == 0 {
		return nil, fmt.Errorf("empty pattern")
	}
	var flags hyperscan.CompileFlag
	if c.Nocase {
		flags |= hyperscan.Caseless
	}
	r := hyperscan.NewPattern(c.express(), flags|hyperscan.SingleMatch|hyperscan.DotAll)
	r.Id = c.Id
	return r, nil
}

func newschsPattern(c Content) *schsPattern {
	return &schsPattern{Content: c}
}

type schsPattern struct {
	Content
	sids []int
	next *schsPattern
}
