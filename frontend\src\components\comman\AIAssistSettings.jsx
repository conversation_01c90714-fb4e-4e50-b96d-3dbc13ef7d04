import React, { useState } from "react";
import { Form, Input, Select, Button, message } from "antd";
import { useSetLLMSettingsMutation } from "../../app/services/aiassistApi"; // Assuming RTK Query is set up

const { Option } = Select;

const SetLLMSettings = () => {
  const [form] = Form.useForm();
  const [setLLMSettings, { isLoading }] = useSetLLMSettingsMutation();
  const [llmType, setLlmType] = useState("open-ai"); // Default type

  const handleSubmit = async (values) => {
    try {
      const response = await setLLMSettings(values).unwrap();
      message.success("LLM settings updated successfully!");
      console.log("Response:", response);
    } catch (error) {
      message.error(`Error updating LLM settings: ${error.message}`);
    }
  };

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "auto" }}>
      <h2>Set LLM Settings</h2>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          type: "open-ai",
          model: "gpt-3",
          api_key: "",
          host: "",
          port: 8080,
        }}
      >
        <Form.Item
          label="Type"
          name="type"
          rules={[{ required: true, message: "Please select the LLM type!" }]}
        >
          <Select onChange={(value) => setLlmType(value)}>
            <Option value="open-ai">OpenAI</Option>
            <Option value="ollama">Ollama</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Model"
          name="model"
          rules={[{ required: true, message: "Please enter the model!" }]}
        >
          <Input placeholder="e.g., gpt-3" />
        </Form.Item>

        {llmType === "open-ai" && (
          <Form.Item
            label="API Key"
            name="api_key"
            rules={[
              {
                required: llmType === "open-ai",
                message: "Please enter the OpenAI API key!",
              },
            ]}
          >
            <Input.Password placeholder="Enter OpenAI API key" />
          </Form.Item>
        )}

        {llmType === "ollama" && (
          <>
            <Form.Item
              label="Host"
              name="host"
              rules={[
                {
                  required: llmType === "ollama",
                  message: "Please enter the Ollama host!",
                },
              ]}
            >
              <Input placeholder="e.g., http://localhost" />
            </Form.Item>

            <Form.Item
              label="Port"
              name="port"
              rules={[
                {
                  required: llmType === "ollama",
                  message: "Please enter the Ollama port!",
                },
              ]}
            >
              <Input type="number" placeholder="e.g., 8080" />
            </Form.Item>
          </>
        )}

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={isLoading}>
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default SetLLMSettings;
