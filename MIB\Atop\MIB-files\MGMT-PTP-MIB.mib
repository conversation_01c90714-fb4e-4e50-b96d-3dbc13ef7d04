-- *****************************************************************
-- PTP-MIB:  
-- ****************************************************************

-- Microchip is aware that some terminology used in this technical document is
-- antiquated and inappropriate. As a result of the complex nature of software
-- where seemingly simple changes have unpredictable, and often far-reaching
-- negative results on the software's functionality (requiring extensive retesting
-- and revalidation) we are unable to make the desired changes in all legacy
-- systems without compromising our product or our clients' products.

MGMT-PTP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInteger16 FROM MGMT-TC
    MGMTInteger32e-9 FROM MGMT-TC
    MGMTInteger64 FROM MGMT-TC
    MGMTInteger8 FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtPtpMib MODULE-IDENTITY
    LAST-UPDATED "201905080000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private PTP MIB."
    REVISION    "201905080000Z"
    DESCRIPTION
        "Updated new protocol ethip4ip6."
    REVISION    "201902120000Z"
    DESCRIPTION
        "Updated 802.1as CMLDS names,descriptions and reorganised CMLDS
         DefaultDS MIB objects."
    REVISION    "201806270000Z"
    DESCRIPTION
        "Added 802.1AS Intervals."
    REVISION    "201604290000Z"
    DESCRIPTION
        "Added statistic parameters for PTP port."
    REVISION    "201604200000Z"
    DESCRIPTION
        "Removed vlanTagEnable parameter."
    REVISION    "201511100000Z"
    DESCRIPTION
        "Added virtual port parameters."
    REVISION    "201510280000Z"
    DESCRIPTION
        "Added parameters for specifying leap event."
    REVISION    "201506030000Z"
    DESCRIPTION
        "Added MS-PDV parameters"
    REVISION    "201503260000Z"
    DESCRIPTION
        "Added attribute Dscp to PtpConfigClocksDefaultDs"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 65 }


MGMTptp8021asPortRole ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { disabledPort(3), masterPort(6),
                          passivePort(7), slavePort(9) }

MGMTptpClockPortState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { initializing(0), faulty(1), disabled(2),
                          listening(3), preMaster(4), master(5),
                          passive(6), uncalibrated(7), slave(8),
                          p2pTransparent(9), e2eTransparent(10),
                          frontend(11) }

MGMTptpDestAdrType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { default(0), linkLocal(1) }

MGMTptpDeviceType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { none(0), ordBound(1), p2pTransparent(2),
                          e2eTransparent(3), masterOnly(4),
                          slaveOnly(5), bcFrontend(6) }

MGMTptpExtClock1pps ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { onePpsDisable(0), onePpsOutput(1),
                          onePpsInput(2), onePpsOutInput(3) }

MGMTptpLeapSecondType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { leap59(0), leap61(1) }

MGMTptpPreferredAdj ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { preferredAdjLtc(0), preferredAdjSingle(1),
                          preferredAdjIndependent(2),
                          preferredAdjCommon(3), preferredAdjAuto(4) }

MGMTptpProfile ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { noProfile(0), ieee1588(1), g8265(2),
                          g8275(3), ieee802d1as(4) }

MGMTptpProtocol ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { ethernet(0), ethernetMixed(1), ip4multi(2),
                          ip4mixed(3), ip4uni(4), oam(5), onePps(6),
                          ip6mixed(7), ethip4ip6combo(8) }

MGMTptpServoClockOption ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { clockFree(0), clockSyncE(1) }

MGMTptpSlaveClockState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { slaveClockStateFreerun(0),
                          slaveClockStateFreqLocking(1),
                          slaveClockStateFreqLocked(2),
                          slaveClockStatePhaseLocking(3),
                          slaveClockStatePhaseLocked(4),
                          slaveClockStateHoldover(5),
                          slaveClockStateInvalid(6) }

MGMTptpSystemTimeSyncMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { systemTimeNoSync(0), systemTimeSyncGet(1),
                          systemTimeSyncSet(2) }

MGMTptpUcCommState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { idle(0), initializing(1), connected(2),
                          selected(3), synced(4) }

mgmtPtpMibObjects OBJECT IDENTIFIER
    ::= { mgmtPtpMib 1 }

mgmtPtpCapabilities OBJECT IDENTIFIER
    ::= { mgmtPtpMibObjects 1 }

mgmtPtpCapabilitiesGlobals OBJECT IDENTIFIER
    ::= { mgmtPtpCapabilities 1 }

mgmtPtpCapabilitiesGlobalsClockCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of PTP clocks supported by the device."
    ::= { mgmtPtpCapabilitiesGlobals 1 }

mgmtPtpCapabilitiesGlobalsHasMsPdv OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the build supports the MS-PDV."
    ::= { mgmtPtpCapabilitiesGlobals 2 }

mgmtPtpCapabilitiesGlobalsHasHwClkDomains OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the build supports hardware clock domains."
    ::= { mgmtPtpCapabilitiesGlobals 3 }

mgmtPtpConfig OBJECT IDENTIFIER
    ::= { mgmtPtpMibObjects 2 }

mgmtPtpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtPtpConfig 1 }

mgmtPtpConfigGlobalsExternalClockMode OBJECT IDENTIFIER
    ::= { mgmtPtpConfigGlobals 1 }

mgmtPtpConfigGlobalsExternalClockModeOnePpsMode OBJECT-TYPE
    SYNTAX      MGMTptpExtClock1pps
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mode of the PPS pin."
    ::= { mgmtPtpConfigGlobalsExternalClockMode 1 }

mgmtPtpConfigGlobalsExternalClockModeExternalEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "External enable of PPS output."
    ::= { mgmtPtpConfigGlobalsExternalClockMode 2 }

mgmtPtpConfigGlobalsExternalClockModeAdjustMethod OBJECT-TYPE
    SYNTAX      MGMTptpPreferredAdj
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The adjustment method of the PTP timer.
         
         0: LTC
         
         1: Single DPLL
         
         2: Independent DPLL
         
         3: Common DPLL's 4: Auto"
    ::= { mgmtPtpConfigGlobalsExternalClockMode 3 }

mgmtPtpConfigGlobalsExternalClockModeClockFrequency OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The frequency in hertz (Hz) of the PPS external output."
    ::= { mgmtPtpConfigGlobalsExternalClockMode 4 }

mgmtPtpConfigGlobalsSystemTimeSyncMode OBJECT IDENTIFIER
    ::= { mgmtPtpConfigGlobals 2 }

mgmtPtpConfigGlobalsSystemTimeSyncModeMode OBJECT-TYPE
    SYNTAX      MGMTptpSystemTimeSyncMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mode of the System time <-> ptp time synchronization."
    ::= { mgmtPtpConfigGlobalsSystemTimeSyncMode 1 }

mgmtPtpConfigClocks OBJECT IDENTIFIER
    ::= { mgmtPtpConfig 2 }

mgmtPtpConfigClocksDefaultDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksDefaultDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks DefaultDS."
    ::= { mgmtPtpConfigClocks 1 }

mgmtPtpConfigClocksDefaultDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksDefaultDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksDefaultDsClockId }
    ::= { mgmtPtpConfigClocksDefaultDsTable 1 }

MGMTPtpConfigClocksDefaultDsEntry ::= SEQUENCE {
    mgmtPtpConfigClocksDefaultDsClockId          Integer32,
    mgmtPtpConfigClocksDefaultDsDeviceType       MGMTptpDeviceType,
    mgmtPtpConfigClocksDefaultDsTwoStepFlag      TruthValue,
    mgmtPtpConfigClocksDefaultDsPriority1        MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsPriority2        MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsOneWay           TruthValue,
    mgmtPtpConfigClocksDefaultDsDomainNumber     MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsProtocol         MGMTptpProtocol,
    mgmtPtpConfigClocksDefaultDsVid              MGMTUnsigned16,
    mgmtPtpConfigClocksDefaultDsPcp              MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsMep              Integer32,
    mgmtPtpConfigClocksDefaultDsClkDom           Unsigned32,
    mgmtPtpConfigClocksDefaultDsDscp             MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsProfile          MGMTptpProfile,
    mgmtPtpConfigClocksDefaultDsLocalPriority    MGMTUnsigned8,
    mgmtPtpConfigClocksDefaultDsFilterType       Unsigned32,
    mgmtPtpConfigClocksDefaultDsPathTraceEnable  TruthValue,
    mgmtPtpConfigClocksDefaultDsAction           MGMTRowEditorState
}

mgmtPtpConfigClocksDefaultDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksDefaultDsEntry 1 }

mgmtPtpConfigClocksDefaultDsDeviceType OBJECT-TYPE
    SYNTAX      MGMTptpDeviceType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PTP clock type."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 2 }

mgmtPtpConfigClocksDefaultDsTwoStepFlag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether clock uses follow-up packets."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 3 }

mgmtPtpConfigClocksDefaultDsPriority1 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority1 value."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 4 }

mgmtPtpConfigClocksDefaultDsPriority2 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority2 value."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 5 }

mgmtPtpConfigClocksDefaultDsOneWay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether clock uses sync packets only."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 6 }

mgmtPtpConfigClocksDefaultDsDomainNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The domain number."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 7 }

mgmtPtpConfigClocksDefaultDsProtocol OBJECT-TYPE
    SYNTAX      MGMTptpProtocol
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol to be used for the encapsulation of the PTP packets."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 8 }

mgmtPtpConfigClocksDefaultDsVid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN id for this PTP instance."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 10 }

mgmtPtpConfigClocksDefaultDsPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value for this PTP instance."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 11 }

mgmtPtpConfigClocksDefaultDsMep OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mep instance number (if protocol is OAM)."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 12 }

mgmtPtpConfigClocksDefaultDsClkDom OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clock domain used.
         
         This object is only available if the capability object
         'mgmtPtpCapabilitiesGlobalsHasHwClkDomains' is True."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 13 }

mgmtPtpConfigClocksDefaultDsDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP field value (if protocol is IPv4)."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 14 }

mgmtPtpConfigClocksDefaultDsProfile OBJECT-TYPE
    SYNTAX      MGMTptpProfile
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PTP profile."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 15 }

mgmtPtpConfigClocksDefaultDsLocalPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The local priority value."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 16 }

mgmtPtpConfigClocksDefaultDsFilterType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects the type of filter/servo used."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 17 }

mgmtPtpConfigClocksDefaultDsPathTraceEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Announce Path Trace supported."
    ::= { mgmtPtpConfigClocksDefaultDsEntry 18 }

mgmtPtpConfigClocksDefaultDsAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPtpConfigClocksDefaultDsEntry 100 }

mgmtPtpConfigClocksDefaultDsTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtPtpConfigClocks 2 }

mgmtPtpConfigClocksDefaultDsTableRowEditorClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 1 }

mgmtPtpConfigClocksDefaultDsTableRowEditorDeviceType OBJECT-TYPE
    SYNTAX      MGMTptpDeviceType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PTP clock type."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 2 }

mgmtPtpConfigClocksDefaultDsTableRowEditorTwoStepFlag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether clock uses follow-up packets."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 3 }

mgmtPtpConfigClocksDefaultDsTableRowEditorPriority1 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority1 value."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 4 }

mgmtPtpConfigClocksDefaultDsTableRowEditorPriority2 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority2 value."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 5 }

mgmtPtpConfigClocksDefaultDsTableRowEditorOneWay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether clock uses sync packets only."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 6 }

mgmtPtpConfigClocksDefaultDsTableRowEditorDomainNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The domain number."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 7 }

mgmtPtpConfigClocksDefaultDsTableRowEditorProtocol OBJECT-TYPE
    SYNTAX      MGMTptpProtocol
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol to be used for the encapsulation of the PTP packets."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 8 }

mgmtPtpConfigClocksDefaultDsTableRowEditorVid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN id for this PTP instance."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 10 }

mgmtPtpConfigClocksDefaultDsTableRowEditorPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value for this PTP instance."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 11 }

mgmtPtpConfigClocksDefaultDsTableRowEditorMep OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mep instance number (if protocol is OAM)."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 12 }

mgmtPtpConfigClocksDefaultDsTableRowEditorClkDom OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clock domain used.
         
         This object is only available if the capability object
         'mgmtPtpCapabilitiesGlobalsHasHwClkDomains' is True."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 13 }

mgmtPtpConfigClocksDefaultDsTableRowEditorDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP field value (if protocol is IPv4)."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 14 }

mgmtPtpConfigClocksDefaultDsTableRowEditorProfile OBJECT-TYPE
    SYNTAX      MGMTptpProfile
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PTP profile."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 15 }

mgmtPtpConfigClocksDefaultDsTableRowEditorLocalPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The local priority value."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 16 }

mgmtPtpConfigClocksDefaultDsTableRowEditorFilterType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects the type of filter/servo used."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 17 }

mgmtPtpConfigClocksDefaultDsTableRowEditorPathTraceEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Announce Path Trace supported."
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 18 }

mgmtPtpConfigClocksDefaultDsTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPtpConfigClocksDefaultDsTableRowEditor 100 }

mgmtPtpConfigClocksTimePropertiesDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksTimePropertiesDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks TimePropertiesDS."
    ::= { mgmtPtpConfigClocks 3 }

mgmtPtpConfigClocksTimePropertiesDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksTimePropertiesDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksTimePropertiesDsClockId }
    ::= { mgmtPtpConfigClocksTimePropertiesDsTable 1 }

MGMTPtpConfigClocksTimePropertiesDsEntry ::= SEQUENCE {
    mgmtPtpConfigClocksTimePropertiesDsClockId                Integer32,
    mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffset       MGMTInteger16,
    mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffsetValid  TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsLeap59                 TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsLeap61                 TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsTimeTraceable          TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsFrequencyTraceable     TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsPtpTimescale           TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsTimeSource             MGMTUnsigned8,
    mgmtPtpConfigClocksTimePropertiesDsPendingLeap            TruthValue,
    mgmtPtpConfigClocksTimePropertiesDsLeapDate               MGMTUnsigned16,
    mgmtPtpConfigClocksTimePropertiesDsLeapType               MGMTptpLeapSecondType
}

mgmtPtpConfigClocksTimePropertiesDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 1 }

mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffset OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The current UTC time offset."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 2 }

mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffsetValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether the current UTC time offset value is valid."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 3 }

mgmtPtpConfigClocksTimePropertiesDsLeap59 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates that the last minute of the day has only 59 seconds."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 4 }

mgmtPtpConfigClocksTimePropertiesDsLeap61 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates that the last minute of the day has 61 seconds."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 5 }

mgmtPtpConfigClocksTimePropertiesDsTimeTraceable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates that time is traceable to a primary reference."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 6 }

mgmtPtpConfigClocksTimePropertiesDsFrequencyTraceable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates that frequency is traceable to a primary reference."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 7 }

mgmtPtpConfigClocksTimePropertiesDsPtpTimescale OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether timescale of the grandmaster clock is PTP."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 8 }

mgmtPtpConfigClocksTimePropertiesDsTimeSource OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source of time used by the grandmaster clock."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 9 }

mgmtPtpConfigClocksTimePropertiesDsPendingLeap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether a leap event is pending."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 10 }

mgmtPtpConfigClocksTimePropertiesDsLeapDate OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Date of leap event represented as number of days after 1970/01/01"
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 11 }

mgmtPtpConfigClocksTimePropertiesDsLeapType OBJECT-TYPE
    SYNTAX      MGMTptpLeapSecondType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of leap event i.e. leap59 or leap61."
    ::= { mgmtPtpConfigClocksTimePropertiesDsEntry 12 }

mgmtPtpConfigClocksFilterParametersTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksFilterParametersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks filter parameters."
    ::= { mgmtPtpConfigClocks 4 }

mgmtPtpConfigClocksFilterParametersEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksFilterParametersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksFilterParametersClockId }
    ::= { mgmtPtpConfigClocksFilterParametersTable 1 }

MGMTPtpConfigClocksFilterParametersEntry ::= SEQUENCE {
    mgmtPtpConfigClocksFilterParametersClockId      Integer32,
    mgmtPtpConfigClocksFilterParametersDelayFilter  Unsigned32,
    mgmtPtpConfigClocksFilterParametersPeriod       Unsigned32,
    mgmtPtpConfigClocksFilterParametersDist         Unsigned32
}

mgmtPtpConfigClocksFilterParametersClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksFilterParametersEntry 1 }

mgmtPtpConfigClocksFilterParametersDelayFilter OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines the time constant of the delay filter."
    ::= { mgmtPtpConfigClocksFilterParametersEntry 2 }

mgmtPtpConfigClocksFilterParametersPeriod OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines the period of the offset filter."
    ::= { mgmtPtpConfigClocksFilterParametersEntry 4 }

mgmtPtpConfigClocksFilterParametersDist OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Sets the dist value of the offset filter."
    ::= { mgmtPtpConfigClocksFilterParametersEntry 5 }

mgmtPtpConfigClocksServoParametersTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksServoParametersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks servo parameters."
    ::= { mgmtPtpConfigClocks 5 }

mgmtPtpConfigClocksServoParametersEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksServoParametersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksServoParametersClockId }
    ::= { mgmtPtpConfigClocksServoParametersTable 1 }

MGMTPtpConfigClocksServoParametersEntry ::= SEQUENCE {
    mgmtPtpConfigClocksServoParametersClockId             Integer32,
    mgmtPtpConfigClocksServoParametersDisplay             TruthValue,
    mgmtPtpConfigClocksServoParametersPEnable             TruthValue,
    mgmtPtpConfigClocksServoParametersIEnable             TruthValue,
    mgmtPtpConfigClocksServoParametersDEnable             TruthValue,
    mgmtPtpConfigClocksServoParametersPval                Unsigned32,
    mgmtPtpConfigClocksServoParametersIval                Unsigned32,
    mgmtPtpConfigClocksServoParametersDval                Unsigned32,
    mgmtPtpConfigClocksServoParametersSrvOption           MGMTptpServoClockOption,
    mgmtPtpConfigClocksServoParametersSynceThreshold      Unsigned32,
    mgmtPtpConfigClocksServoParametersSynceAp             Unsigned32,
    mgmtPtpConfigClocksServoParametersHoFilter            Integer32,
    mgmtPtpConfigClocksServoParametersStableAdjThreshold  MGMTUnsigned64,
    mgmtPtpConfigClocksServoParametersGain                Unsigned32
}

mgmtPtpConfigClocksServoParametersClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksServoParametersEntry 1 }

mgmtPtpConfigClocksServoParametersDisplay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether output shall be sent to the debug terminal."
    ::= { mgmtPtpConfigClocksServoParametersEntry 2 }

mgmtPtpConfigClocksServoParametersPEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether P-value of servo algorithm shall be used."
    ::= { mgmtPtpConfigClocksServoParametersEntry 3 }

mgmtPtpConfigClocksServoParametersIEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether I-value of servo algorithm shall be used."
    ::= { mgmtPtpConfigClocksServoParametersEntry 4 }

mgmtPtpConfigClocksServoParametersDEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether D-value of servo algorithm shall be used."
    ::= { mgmtPtpConfigClocksServoParametersEntry 5 }

mgmtPtpConfigClocksServoParametersPval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "P-value of the offset filter."
    ::= { mgmtPtpConfigClocksServoParametersEntry 6 }

mgmtPtpConfigClocksServoParametersIval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "I-value of the offset filter."
    ::= { mgmtPtpConfigClocksServoParametersEntry 7 }

mgmtPtpConfigClocksServoParametersDval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "D-value of the offset filter."
    ::= { mgmtPtpConfigClocksServoParametersEntry 8 }

mgmtPtpConfigClocksServoParametersSrvOption OBJECT-TYPE
    SYNTAX      MGMTptpServoClockOption
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether clock is free running or locked to SyncE."
    ::= { mgmtPtpConfigClocksServoParametersEntry 9 }

mgmtPtpConfigClocksServoParametersSynceThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SyncE Threshold"
    ::= { mgmtPtpConfigClocksServoParametersEntry 10 }

mgmtPtpConfigClocksServoParametersSynceAp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SyncE Ap"
    ::= { mgmtPtpConfigClocksServoParametersEntry 11 }

mgmtPtpConfigClocksServoParametersHoFilter OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Holdoff low pass filter constant."
    ::= { mgmtPtpConfigClocksServoParametersEntry 12 }

mgmtPtpConfigClocksServoParametersStableAdjThreshold OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Threshold at which offset is assumed to be stable."
    ::= { mgmtPtpConfigClocksServoParametersEntry 13 }

mgmtPtpConfigClocksServoParametersGain OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "gain-value of the offset filter."
    ::= { mgmtPtpConfigClocksServoParametersEntry 14 }

mgmtPtpConfigClocksSlaveConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksSlaveConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks slave configuration."
    ::= { mgmtPtpConfigClocks 6 }

mgmtPtpConfigClocksSlaveConfigEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksSlaveConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksSlaveConfigClockId }
    ::= { mgmtPtpConfigClocksSlaveConfigTable 1 }

MGMTPtpConfigClocksSlaveConfigEntry ::= SEQUENCE {
    mgmtPtpConfigClocksSlaveConfigClockId       Integer32,
    mgmtPtpConfigClocksSlaveConfigStableOffset  Unsigned32,
    mgmtPtpConfigClocksSlaveConfigOffsetOk      Unsigned32,
    mgmtPtpConfigClocksSlaveConfigOffsetFail    Unsigned32
}

mgmtPtpConfigClocksSlaveConfigClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksSlaveConfigEntry 1 }

mgmtPtpConfigClocksSlaveConfigStableOffset OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stable offset threshold in ns."
    ::= { mgmtPtpConfigClocksSlaveConfigEntry 2 }

mgmtPtpConfigClocksSlaveConfigOffsetOk OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Offset OK threshold in ns."
    ::= { mgmtPtpConfigClocksSlaveConfigEntry 3 }

mgmtPtpConfigClocksSlaveConfigOffsetFail OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Offset fail threshold in ns."
    ::= { mgmtPtpConfigClocksSlaveConfigEntry 4 }

mgmtPtpConfigClocksUnicastSlaveConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksUnicastSlaveConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks unicast slave
         configuration."
    ::= { mgmtPtpConfigClocks 7 }

mgmtPtpConfigClocksUnicastSlaveConfigEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksUnicastSlaveConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksUnicastSlaveConfigClockId,
                  mgmtPtpConfigClocksUnicastSlaveConfigMasterId }
    ::= { mgmtPtpConfigClocksUnicastSlaveConfigTable 1 }

MGMTPtpConfigClocksUnicastSlaveConfigEntry ::= SEQUENCE {
    mgmtPtpConfigClocksUnicastSlaveConfigClockId    Integer32,
    mgmtPtpConfigClocksUnicastSlaveConfigMasterId   Integer32,
    mgmtPtpConfigClocksUnicastSlaveConfigDuration   Unsigned32,
    mgmtPtpConfigClocksUnicastSlaveConfigIpAddress  IpAddress
}

mgmtPtpConfigClocksUnicastSlaveConfigClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksUnicastSlaveConfigEntry 1 }

mgmtPtpConfigClocksUnicastSlaveConfigMasterId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksUnicastSlaveConfigEntry 2 }

mgmtPtpConfigClocksUnicastSlaveConfigDuration OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of seconds for which the Announce/Sync messages are requested."
    ::= { mgmtPtpConfigClocksUnicastSlaveConfigEntry 3 }

mgmtPtpConfigClocksUnicastSlaveConfigIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 address of requested master clock."
    ::= { mgmtPtpConfigClocksUnicastSlaveConfigEntry 4 }

mgmtPtpConfigClocksPortDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks PortDS."
    ::= { mgmtPtpConfigClocks 8 }

mgmtPtpConfigClocksPortDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksPortDsClockId,
                  mgmtPtpConfigClocksPortDsPortId }
    ::= { mgmtPtpConfigClocksPortDsTable 1 }

MGMTPtpConfigClocksPortDsEntry ::= SEQUENCE {
    mgmtPtpConfigClocksPortDsClockId                                  Integer32,
    mgmtPtpConfigClocksPortDsPortId                                   MGMTInterfaceIndex,
    mgmtPtpConfigClocksPortDsEnabled                                  MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsLogAnnounceInterval                      MGMTInteger16,
    mgmtPtpConfigClocksPortDsAnnounceReceiptTimeout                   MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsLogSyncInterval                          MGMTInteger16,
    mgmtPtpConfigClocksPortDsDelayMechanism                           MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsLogMinPdelayReqInterval                  MGMTInteger16,
    mgmtPtpConfigClocksPortDsDelayAsymmetry                           MGMTInteger64,
    mgmtPtpConfigClocksPortDsIngressLatency                           MGMTInteger64,
    mgmtPtpConfigClocksPortDsEgressLatency                            MGMTInteger64,
    mgmtPtpConfigClocksPortDsPortInternal                             TruthValue,
    mgmtPtpConfigClocksPortDsVersionNumber                            MGMTUnsigned16,
    mgmtPtpConfigClocksPortDsMcastAddr                                MGMTptpDestAdrType,
    mgmtPtpConfigClocksPortDsNotSlave                                 TruthValue,
    mgmtPtpConfigClocksPortDsLocalPriority                            MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsC8021asNeighborPropDelayThresh           MGMTInteger64,
    mgmtPtpConfigClocksPortDsC8021asSyncReceiptTimeout                MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsC8021asAllowedLostResponses              MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsC8021asAllowedFaults                     MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogAnnounceIntvl    TruthValue,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogSyncIntvl        TruthValue,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogPdelayReqIntvl   TruthValue,
    mgmtPtpConfigClocksPortDsC8021asMgtSettableLogAnnounceIntvl       MGMTInteger8,
    mgmtPtpConfigClocksPortDsC8021asMgtSettableLogSyncIntvl           MGMTInteger8,
    mgmtPtpConfigClocksPortDsC8021asMgtSettableLogPdelayReqIntvl      MGMTInteger8,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogGptpCapMsgIntvl  TruthValue,
    mgmtPtpConfigClocksPortDsC8021asMgtSettableLogGptpCapMsgIntvl     MGMTInteger8,
    mgmtPtpConfigClocksPortDsC8021GptpCapableReceiptTimeout           MGMTUnsigned8,
    mgmtPtpConfigClocksPortDsC8021InitialLogGptpCapableMessageIntvl   MGMTInteger8,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompNbrRateRatio         TruthValue,
    mgmtPtpConfigClocksPortDsC8021asMgtSetCompNbrRateRatio            TruthValue,
    mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompMeanLinkDelay        TruthValue,
    mgmtPtpConfigClocksPortDsC8021asmgtSetCompMeanLinkDelay           TruthValue
}

mgmtPtpConfigClocksPortDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksPortDsEntry 1 }

mgmtPtpConfigClocksPortDsPortId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksPortDsEntry 2 }

mgmtPtpConfigClocksPortDsEnabled OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines whether port is enabled."
    ::= { mgmtPtpConfigClocksPortDsEntry 3 }

mgmtPtpConfigClocksPortDsLogAnnounceInterval OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interval between announce message transmissions."
    ::= { mgmtPtpConfigClocksPortDsEntry 4 }

mgmtPtpConfigClocksPortDsAnnounceReceiptTimeout OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The timeout for receiving announce messages on the port."
    ::= { mgmtPtpConfigClocksPortDsEntry 5 }

mgmtPtpConfigClocksPortDsLogSyncInterval OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The interval for issuing sync meesages in the master."
    ::= { mgmtPtpConfigClocksPortDsEntry 6 }

mgmtPtpConfigClocksPortDsDelayMechanism OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The delay mechanism used for the port."
    ::= { mgmtPtpConfigClocksPortDsEntry 7 }

mgmtPtpConfigClocksPortDsLogMinPdelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of logMinPdelayReqInterval"
    ::= { mgmtPtpConfigClocksPortDsEntry 8 }

mgmtPtpConfigClocksPortDsDelayAsymmetry OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value for the communication path asymmetry."
    ::= { mgmtPtpConfigClocksPortDsEntry 9 }

mgmtPtpConfigClocksPortDsIngressLatency OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Ingress delay for port."
    ::= { mgmtPtpConfigClocksPortDsEntry 10 }

mgmtPtpConfigClocksPortDsEgressLatency OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Egress delay for port."
    ::= { mgmtPtpConfigClocksPortDsEntry 11 }

mgmtPtpConfigClocksPortDsPortInternal OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines whether port is enabled as an internal interface."
    ::= { mgmtPtpConfigClocksPortDsEntry 12 }

mgmtPtpConfigClocksPortDsVersionNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The version of PTP being used by the port."
    ::= { mgmtPtpConfigClocksPortDsEntry 13 }

mgmtPtpConfigClocksPortDsMcastAddr OBJECT-TYPE
    SYNTAX      MGMTptpDestAdrType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The multicast address used (default or link local)."
    ::= { mgmtPtpConfigClocksPortDsEntry 14 }

mgmtPtpConfigClocksPortDsNotSlave OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When true, the port will not act as a slave."
    ::= { mgmtPtpConfigClocksPortDsEntry 15 }

mgmtPtpConfigClocksPortDsLocalPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The local priority value."
    ::= { mgmtPtpConfigClocksPortDsEntry 16 }

mgmtPtpConfigClocksPortDsC8021asNeighborPropDelayThresh OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "max allowed meanLinkDelay."
    ::= { mgmtPtpConfigClocksPortDsEntry 17 }

mgmtPtpConfigClocksPortDsC8021asSyncReceiptTimeout OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of time-synchronization transmission intervals that a slave port
         waits without receiving synchronization information."
    ::= { mgmtPtpConfigClocksPortDsEntry 18 }

mgmtPtpConfigClocksPortDsC8021asAllowedLostResponses OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of Pdelay_Req messages for which a valid response is not
         received, above which a port is considered to not be exchanging peer
         delay messages with its neighbor."
    ::= { mgmtPtpConfigClocksPortDsEntry 19 }

mgmtPtpConfigClocksPortDsC8021asAllowedFaults OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of allowed instances where the computed mean propagation delay
         exceeds the threshold meanLinkDelayThresh and/or instances where the
         computation of neighborRateRatio is invalid."
    ::= { mgmtPtpConfigClocksPortDsEntry 20 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogAnnounceIntvl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flag to decide the value to be used for Announce interval."
    ::= { mgmtPtpConfigClocksPortDsEntry 21 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogSyncIntvl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flag to decide the value to be used for sync packet interval."
    ::= { mgmtPtpConfigClocksPortDsEntry 22 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogPdelayReqIntvl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flag to decide the value to be used for Peer delay request interval."
    ::= { mgmtPtpConfigClocksPortDsEntry 23 }

mgmtPtpConfigClocksPortDsC8021asMgtSettableLogAnnounceIntvl OBJECT-TYPE
    SYNTAX      MGMTInteger8 (-3..4)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If useMgmtAnnounce is set, port uses this value to decide the Announce
         packet interval , else default value is used. Range is -3 to 4."
    ::= { mgmtPtpConfigClocksPortDsEntry 24 }

mgmtPtpConfigClocksPortDsC8021asMgtSettableLogSyncIntvl OBJECT-TYPE
    SYNTAX      MGMTInteger8 (-7..4)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If useMgmtSync is set, port uses this value to decide the sync packet
         interval , else default value is used. Range is -7 to 4 ."
    ::= { mgmtPtpConfigClocksPortDsEntry 25 }

mgmtPtpConfigClocksPortDsC8021asMgtSettableLogPdelayReqIntvl OBJECT-TYPE
    SYNTAX      MGMTInteger8 (-7..5)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If useMgmtPdelay is set, port uses this value to decide the Peer delay
         request packet interval , else default value is used. Range is -7 to 5."
    ::= { mgmtPtpConfigClocksPortDsEntry 26 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogGptpCapMsgIntvl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flag to set OPTION to configure gptp interval."
    ::= { mgmtPtpConfigClocksPortDsEntry 27 }

mgmtPtpConfigClocksPortDsC8021asMgtSettableLogGptpCapMsgIntvl OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If UseMgtSettableLogGptpCapableMessageInterval is set, set gptp capable
         tlv interval."
    ::= { mgmtPtpConfigClocksPortDsEntry 28 }

mgmtPtpConfigClocksPortDsC8021GptpCapableReceiptTimeout OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set gPtpCapableReceiptTimeout value per port."
    ::= { mgmtPtpConfigClocksPortDsEntry 29 }

mgmtPtpConfigClocksPortDsC8021InitialLogGptpCapableMessageIntvl OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set gptp interval per port."
    ::= { mgmtPtpConfigClocksPortDsEntry 30 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value determines the source of the value of
         computeNeighborRateRatio. 'True' indicates source as
         'mgtSettablecomputeNeighborRateRatio'. Otherwise, either initial value
         or value set by LinkDelayIntervalSetting state machine."
    ::= { mgmtPtpConfigClocksPortDsEntry 31 }

mgmtPtpConfigClocksPortDsC8021asMgtSetCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value indicates the input through management interface whether to
         compute neighbor rate ratio or not."
    ::= { mgmtPtpConfigClocksPortDsEntry 32 }

mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value determines the source of the value of computeMeanLinkDelay.
         'True' indicates source as 'mgtSettableComputeMeanLinkDelay'.
         Otherwise, initial value or value set by LinkDelayInterval State
         machine is used."
    ::= { mgmtPtpConfigClocksPortDsEntry 33 }

mgmtPtpConfigClocksPortDsC8021asmgtSetCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value indicates the input through management interface whether to
         compute Mean Link delay or not."
    ::= { mgmtPtpConfigClocksPortDsEntry 34 }

mgmtPtpConfigClocksVirtualPortCfgTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigClocksVirtualPortCfgEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks virtual port
         parameters."
    ::= { mgmtPtpConfigClocks 9 }

mgmtPtpConfigClocksVirtualPortCfgEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigClocksVirtualPortCfgEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpConfigClocksVirtualPortCfgClockId }
    ::= { mgmtPtpConfigClocksVirtualPortCfgTable 1 }

MGMTPtpConfigClocksVirtualPortCfgEntry ::= SEQUENCE {
    mgmtPtpConfigClocksVirtualPortCfgClockId                         Integer32,
    mgmtPtpConfigClocksVirtualPortCfgEnable                          TruthValue,
    mgmtPtpConfigClocksVirtualPortCfgIoPin                           MGMTUnsigned16,
    mgmtPtpConfigClocksVirtualPortCfgClockQualityClockClass          MGMTUnsigned8,
    mgmtPtpConfigClocksVirtualPortCfgClockQualityClockAccuracy       MGMTUnsigned8,
    mgmtPtpConfigClocksVirtualPortCfgClockQualityOffsetScaledLogVar  MGMTUnsigned16,
    mgmtPtpConfigClocksVirtualPortCfgPriority1                       MGMTUnsigned8,
    mgmtPtpConfigClocksVirtualPortCfgPriority2                       MGMTUnsigned8,
    mgmtPtpConfigClocksVirtualPortCfgLocalPriority                   MGMTUnsigned8
}

mgmtPtpConfigClocksVirtualPortCfgClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 1 }

mgmtPtpConfigClocksVirtualPortCfgEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether the virtual port is enabled."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 2 }

mgmtPtpConfigClocksVirtualPortCfgIoPin OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Defines the I/O-pin used by the virtual port."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 3 }

mgmtPtpConfigClocksVirtualPortCfgClockQualityClockClass OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is the clock class field of the clock quality structure."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 4 }

mgmtPtpConfigClocksVirtualPortCfgClockQualityClockAccuracy OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is the clock accuracy field of the clock quality structure."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 5 }

mgmtPtpConfigClocksVirtualPortCfgClockQualityOffsetScaledLogVar OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is the offsetScaledLogVariance field of the clock quality
         structure."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 6 }

mgmtPtpConfigClocksVirtualPortCfgPriority1 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority1 value."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 7 }

mgmtPtpConfigClocksVirtualPortCfgPriority2 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The priority2 value."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 8 }

mgmtPtpConfigClocksVirtualPortCfgLocalPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The local priority value."
    ::= { mgmtPtpConfigClocksVirtualPortCfgEntry 9 }

mgmtPtpConfigCmlds OBJECT IDENTIFIER
    ::= { mgmtPtpConfig 3 }

mgmtPtpConfigCmldsPortDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpConfigCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the configurable part of the PTP clocks CMLDS PortDS."
    ::= { mgmtPtpConfigCmlds 1 }

mgmtPtpConfigCmldsPortDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpConfigCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The port_id index must be a value from 0 up to the number of ports."
    INDEX       { mgmtPtpConfigCmldsPortDsPortId }
    ::= { mgmtPtpConfigCmldsPortDsTable 1 }

MGMTPtpConfigCmldsPortDsEntry ::= SEQUENCE {
    mgmtPtpConfigCmldsPortDsPortId                              Unsigned32,
    mgmtPtpConfigCmldsPortDsDelayAsymmetry                      MGMTInteger64,
    mgmtPtpConfigCmldsPortDsInitialLogPdelayReqInterval         MGMTInteger8,
    mgmtPtpConfigCmldsPortDsUseMgtSettableLogPdelayReqInterval  TruthValue,
    mgmtPtpConfigCmldsPortDsMgtSettableLogPdelayReqInterval     MGMTInteger8,
    mgmtPtpConfigCmldsPortDsInitialCompNbrRateRatio             TruthValue,
    mgmtPtpConfigCmldsPortDsUseMgtSetCompNbrRateRatio           TruthValue,
    mgmtPtpConfigCmldsPortDsMgtSetCompNbrRateRatio              TruthValue,
    mgmtPtpConfigCmldsPortDsInitialCompMeanLinkDelay            TruthValue,
    mgmtPtpConfigCmldsPortDsUseMgtSetCompMeanLinkDelay          TruthValue,
    mgmtPtpConfigCmldsPortDsMgtSetCompMeanLinkDelay             TruthValue,
    mgmtPtpConfigCmldsPortDsAllowedLostResponses                MGMTUnsigned8,
    mgmtPtpConfigCmldsPortDsAllowedFaults                       MGMTUnsigned8,
    mgmtPtpConfigCmldsPortDsMeanLinkDelayThresh                 MGMTInteger64
}

mgmtPtpConfigCmldsPortDsPortId OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpConfigCmldsPortDsEntry 1 }

mgmtPtpConfigCmldsPortDsDelayAsymmetry OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "the asymmetry in the propagation delay on the link attached to this
         port relative to the grandmaster time base, as defined in 8.3. If
         propagation delay asymmetry is not modeled, then delayAsymmetry is
         zero."
    ::= { mgmtPtpConfigCmldsPortDsEntry 2 }

mgmtPtpConfigCmldsPortDsInitialLogPdelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "the value is the logarithm to base 2 of the Pdelay_Req message
         transmission interval used when the Link Port is initialized, or a
         message interval request TLV is received with the linkDelayInterval
         field set to 126"
    ::= { mgmtPtpConfigCmldsPortDsEntry 3 }

mgmtPtpConfigCmldsPortDsUseMgtSettableLogPdelayReqInterval OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value determines the source of the sync interval and mean time
         interval between successive Pdelay_Req messages. TRUE indicates source
         as mgtSettableLogPdelayReqInterval. FALSE indicates source as
         LinkDelayIntervalSetting state machine "
    ::= { mgmtPtpConfigCmldsPortDsEntry 4 }

mgmtPtpConfigCmldsPortDsMgtSettableLogPdelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value is the logarithm to base 2 of the mean time interval between
         successive Pdelay_Req messages if useMgtSettableLogPdelayReqInterval is
         TRUE."
    ::= { mgmtPtpConfigCmldsPortDsEntry 5 }

mgmtPtpConfigCmldsPortDsInitialCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Initial value that indicates whether neighborRateRatio is to be
         computed by this port."
    ::= { mgmtPtpConfigCmldsPortDsEntry 6 }

mgmtPtpConfigCmldsPortDsUseMgtSetCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value determines the source of the value of
         computeNeighborRateRatio. 'True' indicates source as
         'mgtSettablecomputeNeighborRateRatio'. 'false' indicates source as
         'LinkDelayIntervalSetting state machine'."
    ::= { mgmtPtpConfigCmldsPortDsEntry 7 }

mgmtPtpConfigCmldsPortDsMgtSetCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value indicates the input through management interface whether to
         compute neighbor rate ratio or not."
    ::= { mgmtPtpConfigCmldsPortDsEntry 8 }

mgmtPtpConfigCmldsPortDsInitialCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Initial value that indicates whether mean Link delay is computed by
         this port or not."
    ::= { mgmtPtpConfigCmldsPortDsEntry 9 }

mgmtPtpConfigCmldsPortDsUseMgtSetCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value determines the source of the value of computeMeanLinkDelay.
         'True' indicates source as 'mgtSettableComputeMeanLinkDelay'. 'false'
         indicates source as 'LinkDelayIntervalSetting state machine'."
    ::= { mgmtPtpConfigCmldsPortDsEntry 10 }

mgmtPtpConfigCmldsPortDsMgtSetCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This value indicates the input through management interface whether to
         compute Mean Link Delay or not."
    ::= { mgmtPtpConfigCmldsPortDsEntry 11 }

mgmtPtpConfigCmldsPortDsAllowedLostResponses OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..10)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "It is the number of Pdelay_Req messages for which a valid response is
         not received, above which a Link Port is considered to not be
         exchanging peer delay messages with its neighbor."
    ::= { mgmtPtpConfigCmldsPortDsEntry 12 }

mgmtPtpConfigCmldsPortDsAllowedFaults OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "It is the number of faults, above which asCapableAcrossDomains is set
         to FALSE."
    ::= { mgmtPtpConfigCmldsPortDsEntry 13 }

mgmtPtpConfigCmldsPortDsMeanLinkDelayThresh OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The propagation time threshold, above which a port is not considered
         capable of participating in the IEEE 802.1AS protocol."
    ::= { mgmtPtpConfigCmldsPortDsEntry 14 }

mgmtPtpStatus OBJECT IDENTIFIER
    ::= { mgmtPtpMibObjects 3 }

mgmtPtpStatusClocks OBJECT IDENTIFIER
    ::= { mgmtPtpStatus 1 }

mgmtPtpStatusClocksDefaultDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksDefaultDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks DefaultDS."
    ::= { mgmtPtpStatusClocks 1 }

mgmtPtpStatusClocksDefaultDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksDefaultDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksDefaultDsClockId }
    ::= { mgmtPtpStatusClocksDefaultDsTable 1 }

MGMTPtpStatusClocksDefaultDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksDefaultDsClockId                         Integer32,
    mgmtPtpStatusClocksDefaultDsClockIdentity                   OCTET STRING,
    mgmtPtpStatusClocksDefaultDsClockQualityClockClass          MGMTUnsigned8,
    mgmtPtpStatusClocksDefaultDsClockQualityClockAccuracy       MGMTUnsigned8,
    mgmtPtpStatusClocksDefaultDsClockQualityOffsetScaledLogVar  MGMTUnsigned16,
    mgmtPtpStatusClocksDefaultDsS8021asGmCapable                TruthValue,
    mgmtPtpStatusClocksDefaultDsS8021asSdoId                    MGMTUnsigned16
}

mgmtPtpStatusClocksDefaultDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksDefaultDsEntry 1 }

mgmtPtpStatusClocksDefaultDsClockIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the unique 8 byte clockIdentify field from the DefaultDS
         structure."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 2 }

mgmtPtpStatusClocksDefaultDsClockQualityClockClass OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the clock class field of the clock quality structure."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 3 }

mgmtPtpStatusClocksDefaultDsClockQualityClockAccuracy OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the clock accuracy field of the clock quality structure."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 4 }

mgmtPtpStatusClocksDefaultDsClockQualityOffsetScaledLogVar OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the offsetScaledLogVariance field of the clock quality
         structure."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 5 }

mgmtPtpStatusClocksDefaultDsS8021asGmCapable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Defines IEEE 802.1AS specific default_DS status parameters, TRUE if the
         time-aware system is capable of being a Grandmaster."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 6 }

mgmtPtpStatusClocksDefaultDsS8021asSdoId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Defines IEEE 802.1AS specific default_DS status parameters, part of ptp
         domain identifier."
    ::= { mgmtPtpStatusClocksDefaultDsEntry 7 }

mgmtPtpStatusClocksCurrentDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksCurrentDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks CurrentDS."
    ::= { mgmtPtpStatusClocks 2 }

mgmtPtpStatusClocksCurrentDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksCurrentDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksCurrentDsClockId }
    ::= { mgmtPtpStatusClocksCurrentDsTable 1 }

MGMTPtpStatusClocksCurrentDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksCurrentDsClockId                               Integer32,
    mgmtPtpStatusClocksCurrentDsStepsRemoved                          MGMTUnsigned16,
    mgmtPtpStatusClocksCurrentDsOffsetFromMaster                      MGMTInteger64,
    mgmtPtpStatusClocksCurrentDsMeanPathDelay                         MGMTInteger64,
    mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeLow         MGMTUnsigned64,
    mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeHigh        Integer32,
    mgmtPtpStatusClocksCurrentDsCur8021asLastGMFreqChange             MGMTInteger32e-9,
    mgmtPtpStatusClocksCurrentDsCur8021asGmTimeBaseIndicator          MGMTUnsigned16,
    mgmtPtpStatusClocksCurrentDsCur8021asGmChangeCount                Unsigned32,
    mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMChangeEvt        Unsigned32,
    mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMPhaseChangeEvt   Unsigned32,
    mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMFreqChangeEvent  Unsigned32
}

mgmtPtpStatusClocksCurrentDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksCurrentDsEntry 1 }

mgmtPtpStatusClocksCurrentDsStepsRemoved OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of PTP clocks traversed from the grandmaster to the local
         PTP clock."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 2 }

mgmtPtpStatusClocksCurrentDsOffsetFromMaster OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time difference in ns from the grandmaster to the local PTP clock."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 3 }

mgmtPtpStatusClocksCurrentDsMeanPathDelay OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The mean path delay from the master to the local slave."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 4 }

mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeLow OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system time when the most recent change in grandmaster phase
         occurred due to a change of either the grandmaster or grandmaster time
         base 64 ls bits."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 5 }

mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeHigh OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system time when the most recent change in grandmaster phase
         occurred due to a change of either the grandmaster or grandmaster time
         base in 32 ms bits"
    ::= { mgmtPtpStatusClocksCurrentDsEntry 6 }

mgmtPtpStatusClocksCurrentDsCur8021asLastGMFreqChange OBJECT-TYPE
    SYNTAX      MGMTInteger32e-9
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the time-aware system is capable of being a Grandmaster."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 7 }

mgmtPtpStatusClocksCurrentDsCur8021asGmTimeBaseIndicator OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TimeBaseIndicator of the current grandmaster."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 8 }

mgmtPtpStatusClocksCurrentDsCur8021asGmChangeCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of times the grandmaster has changed in a gPTP domain."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 9 }

mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMChangeEvt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system time when the most recent grandmaster change occurred."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 10 }

mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMPhaseChangeEvt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system time when the most recent change in grandmaster phase
         occurred due to a change of either the grandmaster or grandmaster time
         base."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 11 }

mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMFreqChangeEvent OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system time when the most recent change in grandmaster frequency
         occurred due to a change of either the grandmaster or grandmaster time
         base."
    ::= { mgmtPtpStatusClocksCurrentDsEntry 12 }

mgmtPtpStatusClocksParentDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksParentDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks ParentDS."
    ::= { mgmtPtpStatusClocks 3 }

mgmtPtpStatusClocksParentDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksParentDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksParentDsClockId }
    ::= { mgmtPtpStatusClocksParentDsTable 1 }

MGMTPtpStatusClocksParentDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksParentDsClockId                             Integer32,
    mgmtPtpStatusClocksParentDsParentPortIdentityClockIdentity     OCTET STRING,
    mgmtPtpStatusClocksParentDsParentPortIdentityPortNumber        MGMTUnsigned16,
    mgmtPtpStatusClocksParentDsParentStats                         TruthValue,
    mgmtPtpStatusClocksParentDsObservedParentOffsetScaledLogVar    MGMTUnsigned16,
    mgmtPtpStatusClocksParentDsObservedParentClockPhaseChangeRate  Integer32,
    mgmtPtpStatusClocksParentDsGrmstrIdentity                      OCTET STRING,
    mgmtPtpStatusClocksParentDsGrmstrClkQualClockClass             MGMTUnsigned8,
    mgmtPtpStatusClocksParentDsGmstrClkQualClockAccuracy           MGMTUnsigned8,
    mgmtPtpStatusClocksParentDsGmstrClkQualOffsetScaledLogVar      MGMTUnsigned16,
    mgmtPtpStatusClocksParentDsGmstrPriority1                      MGMTUnsigned8,
    mgmtPtpStatusClocksParentDsGmstrPriority2                      MGMTUnsigned8,
    mgmtPtpStatusClocksParentDsPar8021asCumulativeRateRatio        Integer32
}

mgmtPtpStatusClocksParentDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksParentDsEntry 1 }

mgmtPtpStatusClocksParentDsParentPortIdentityClockIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the 8 byte unique clock identity of the parent port."
    ::= { mgmtPtpStatusClocksParentDsEntry 2 }

mgmtPtpStatusClocksParentDsParentPortIdentityPortNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the port number on the parent associated with the parent clock."
    ::= { mgmtPtpStatusClocksParentDsEntry 3 }

mgmtPtpStatusClocksParentDsParentStats OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Parents stats (always false)."
    ::= { mgmtPtpStatusClocksParentDsEntry 4 }

mgmtPtpStatusClocksParentDsObservedParentOffsetScaledLogVar OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This field is optional and is not computed (as signaled by parentStats
         being false)."
    ::= { mgmtPtpStatusClocksParentDsEntry 5 }

mgmtPtpStatusClocksParentDsObservedParentClockPhaseChangeRate OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This field is optional and is not computed (as signaled by parentStats
         being false)."
    ::= { mgmtPtpStatusClocksParentDsEntry 6 }

mgmtPtpStatusClocksParentDsGrmstrIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the 8 byte unique clock identity of the grand master clock."
    ::= { mgmtPtpStatusClocksParentDsEntry 7 }

mgmtPtpStatusClocksParentDsGrmstrClkQualClockClass OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the clock class of the clock quality structure of the grand
         master clock."
    ::= { mgmtPtpStatusClocksParentDsEntry 8 }

mgmtPtpStatusClocksParentDsGmstrClkQualClockAccuracy OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the clock accuracy of the clock quality structure of the grand
         master clock."
    ::= { mgmtPtpStatusClocksParentDsEntry 9 }

mgmtPtpStatusClocksParentDsGmstrClkQualOffsetScaledLogVar OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the offsetScaledLogVariance field of the clock quality
         structure of the grand master clock."
    ::= { mgmtPtpStatusClocksParentDsEntry 10 }

mgmtPtpStatusClocksParentDsGmstrPriority1 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Grandmaster Priority1 value."
    ::= { mgmtPtpStatusClocksParentDsEntry 11 }

mgmtPtpStatusClocksParentDsGmstrPriority2 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Grandmaster Priority2 value."
    ::= { mgmtPtpStatusClocksParentDsEntry 12 }

mgmtPtpStatusClocksParentDsPar8021asCumulativeRateRatio OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The ratio of the frequency og the grandmaster to the frequencu of the
         Local CLock entity, expressed as fractional frequency offset * 2**41 ."
    ::= { mgmtPtpStatusClocksParentDsEntry 13 }

mgmtPtpStatusClocksTimePropertiesDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksTimePropertiesDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks TimePropertiesDS."
    ::= { mgmtPtpStatusClocks 4 }

mgmtPtpStatusClocksTimePropertiesDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksTimePropertiesDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksTimePropertiesDsClockId }
    ::= { mgmtPtpStatusClocksTimePropertiesDsTable 1 }

MGMTPtpStatusClocksTimePropertiesDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksTimePropertiesDsClockId                Integer32,
    mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffset       MGMTInteger16,
    mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffsetValid  TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsLeap59                 TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsLeap61                 TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsTimeTraceable          TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsFrequencyTraceable     TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsPtpTimescale           TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsTimeSource             MGMTUnsigned8,
    mgmtPtpStatusClocksTimePropertiesDsPendingLeap            TruthValue,
    mgmtPtpStatusClocksTimePropertiesDsLeapDate               MGMTUnsigned16,
    mgmtPtpStatusClocksTimePropertiesDsLeapType               MGMTptpLeapSecondType
}

mgmtPtpStatusClocksTimePropertiesDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 1 }

mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffset OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current UTC time offset."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 2 }

mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffsetValid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the current UTC time offset value is valid."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 3 }

mgmtPtpStatusClocksTimePropertiesDsLeap59 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates that the last minute of the day has only 59 seconds."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 4 }

mgmtPtpStatusClocksTimePropertiesDsLeap61 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates that the last minute of the day has 61 seconds."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 5 }

mgmtPtpStatusClocksTimePropertiesDsTimeTraceable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates that time is traceable to a primary reference."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 6 }

mgmtPtpStatusClocksTimePropertiesDsFrequencyTraceable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates that frequency is traceable to a primary reference."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 7 }

mgmtPtpStatusClocksTimePropertiesDsPtpTimescale OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether timescale of the grandmaster clock is PTP."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 8 }

mgmtPtpStatusClocksTimePropertiesDsTimeSource OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Source of time used by the grandmaster clock."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 9 }

mgmtPtpStatusClocksTimePropertiesDsPendingLeap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether a leap event is pending."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 10 }

mgmtPtpStatusClocksTimePropertiesDsLeapDate OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Date of leap event represented as number of days after 1970/01/01"
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 11 }

mgmtPtpStatusClocksTimePropertiesDsLeapType OBJECT-TYPE
    SYNTAX      MGMTptpLeapSecondType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of leap event i.e. leap59 or leap61."
    ::= { mgmtPtpStatusClocksTimePropertiesDsEntry 12 }

mgmtPtpStatusClocksSlaveDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksSlaveDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks SlaveDS."
    ::= { mgmtPtpStatusClocks 5 }

mgmtPtpStatusClocksSlaveDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksSlaveDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksSlaveDsClockId }
    ::= { mgmtPtpStatusClocksSlaveDsTable 1 }

MGMTPtpStatusClocksSlaveDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksSlaveDsClockId         Integer32,
    mgmtPtpStatusClocksSlaveDsSlavePort       MGMTUnsigned16,
    mgmtPtpStatusClocksSlaveDsSlaveState      MGMTptpSlaveClockState,
    mgmtPtpStatusClocksSlaveDsHoldoverStable  MGMTUnsigned8,
    mgmtPtpStatusClocksSlaveDsHoldoverAdj     MGMTInteger64
}

mgmtPtpStatusClocksSlaveDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksSlaveDsEntry 1 }

mgmtPtpStatusClocksSlaveDsSlavePort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "0 => no slave port, 1..n => selected slave port."
    ::= { mgmtPtpStatusClocksSlaveDsEntry 2 }

mgmtPtpStatusClocksSlaveDsSlaveState OBJECT-TYPE
    SYNTAX      MGMTptpSlaveClockState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The slaves state."
    ::= { mgmtPtpStatusClocksSlaveDsEntry 3 }

mgmtPtpStatusClocksSlaveDsHoldoverStable OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "True if the stabilization period has expired."
    ::= { mgmtPtpStatusClocksSlaveDsEntry 4 }

mgmtPtpStatusClocksSlaveDsHoldoverAdj OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The calculated holdover offset (ppb*10)."
    ::= { mgmtPtpStatusClocksSlaveDsEntry 5 }

mgmtPtpStatusClocksUnicastMasterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksUnicastMasterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks unicast master
         table."
    ::= { mgmtPtpStatusClocks 6 }

mgmtPtpStatusClocksUnicastMasterEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksUnicastMasterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksUnicastMasterClockId,
                  mgmtPtpStatusClocksUnicastMasterSlaveIp }
    ::= { mgmtPtpStatusClocksUnicastMasterTable 1 }

MGMTPtpStatusClocksUnicastMasterEntry ::= SEQUENCE {
    mgmtPtpStatusClocksUnicastMasterClockId          Integer32,
    mgmtPtpStatusClocksUnicastMasterSlaveIp          IpAddress,
    mgmtPtpStatusClocksUnicastMasterSlaveMac         MacAddress,
    mgmtPtpStatusClocksUnicastMasterPort             MGMTUnsigned16,
    mgmtPtpStatusClocksUnicastMasterAnnLogMsgPeriod  MGMTInteger16,
    mgmtPtpStatusClocksUnicastMasterAnn              TruthValue,
    mgmtPtpStatusClocksUnicastMasterLogMsgPeriod     MGMTInteger16,
    mgmtPtpStatusClocksUnicastMasterSync             TruthValue
}

mgmtPtpStatusClocksUnicastMasterClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 1 }

mgmtPtpStatusClocksUnicastMasterSlaveIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 2 }

mgmtPtpStatusClocksUnicastMasterSlaveMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 3 }

mgmtPtpStatusClocksUnicastMasterPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port on the master that slave is connected to."
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 4 }

mgmtPtpStatusClocksUnicastMasterAnnLogMsgPeriod OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The granted Announce interval."
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 5 }

mgmtPtpStatusClocksUnicastMasterAnn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "True if sending announce messages."
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 6 }

mgmtPtpStatusClocksUnicastMasterLogMsgPeriod OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The granted sync interval."
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 7 }

mgmtPtpStatusClocksUnicastMasterSync OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "True if sending sync messages."
    ::= { mgmtPtpStatusClocksUnicastMasterEntry 8 }

mgmtPtpStatusClocksUnicastSlaveTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksUnicastSlaveEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks unicast slave
         table."
    ::= { mgmtPtpStatusClocks 7 }

mgmtPtpStatusClocksUnicastSlaveEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksUnicastSlaveEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksUnicastSlaveClockId,
                  mgmtPtpStatusClocksUnicastSlaveMasterId }
    ::= { mgmtPtpStatusClocksUnicastSlaveTable 1 }

MGMTPtpStatusClocksUnicastSlaveEntry ::= SEQUENCE {
    mgmtPtpStatusClocksUnicastSlaveClockId                          Integer32,
    mgmtPtpStatusClocksUnicastSlaveMasterId                         Integer32,
    mgmtPtpStatusClocksUnicastSlaveMasterIp                         IpAddress,
    mgmtPtpStatusClocksUnicastSlaveMasterMac                        MacAddress,
    mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityClockIdentity  OCTET STRING,
    mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityPortNumber     MGMTUnsigned16,
    mgmtPtpStatusClocksUnicastSlavePort                             MGMTUnsigned16,
    mgmtPtpStatusClocksUnicastSlaveLogMsgPeriod                     MGMTInteger16,
    mgmtPtpStatusClocksUnicastSlaveCommState                        MGMTptpUcCommState,
    mgmtPtpStatusClocksUnicastSlaveConfMasterIp                     IpAddress
}

mgmtPtpStatusClocksUnicastSlaveClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 1 }

mgmtPtpStatusClocksUnicastSlaveMasterId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 2 }

mgmtPtpStatusClocksUnicastSlaveMasterIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the IP address of the master."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 3 }

mgmtPtpStatusClocksUnicastSlaveMasterMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the MAC address of the master."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 4 }

mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityClockIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the 8 byte unique clock identity of the source port."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 5 }

mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityPortNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is port number of the port used on the source."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 6 }

mgmtPtpStatusClocksUnicastSlavePort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The port (on the slave) connected to the master."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 7 }

mgmtPtpStatusClocksUnicastSlaveLogMsgPeriod OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The granted sync interval."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 8 }

mgmtPtpStatusClocksUnicastSlaveCommState OBJECT-TYPE
    SYNTAX      MGMTptpUcCommState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Communication state."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 9 }

mgmtPtpStatusClocksUnicastSlaveConfMasterIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Copy of the destination ip address."
    ::= { mgmtPtpStatusClocksUnicastSlaveEntry 10 }

mgmtPtpStatusClocksPortsDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusClocksPortsDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP clocks PortDS."
    ::= { mgmtPtpStatusClocks 8 }

mgmtPtpStatusClocksPortsDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusClocksPortsDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatusClocksPortsDsClockId,
                  mgmtPtpStatusClocksPortsDsPortId }
    ::= { mgmtPtpStatusClocksPortsDsTable 1 }

MGMTPtpStatusClocksPortsDsEntry ::= SEQUENCE {
    mgmtPtpStatusClocksPortsDsClockId                              Integer32,
    mgmtPtpStatusClocksPortsDsPortId                               MGMTInterfaceIndex,
    mgmtPtpStatusClocksPortsDsPortState                            MGMTptpClockPortState,
    mgmtPtpStatusClocksPortsDsLogMinDelayReqInterval               MGMTInteger16,
    mgmtPtpStatusClocksPortsDsPeerMeanPathDelay                    MGMTInteger64,
    mgmtPtpStatusClocksPortsDsPeerDelayOk                          TruthValue,
    mgmtPtpStatusClocksPortsDsS8021asPortRole                      MGMTptp8021asPortRole,
    mgmtPtpStatusClocksPortsDsS8021asIsMeasuringDelay              TruthValue,
    mgmtPtpStatusClocksPortsDsS8021asAsCapable                     TruthValue,
    mgmtPtpStatusClocksPortsDsS8021asNeighborRateRatio             Integer32,
    mgmtPtpStatusClocksPortsDsS8021asCurrentLogAnnounceInterval    MGMTDisplayString,
    mgmtPtpStatusClocksPortsDsS8021asCurrentLogSyncInterval        MGMTDisplayString,
    mgmtPtpStatusClocksPortsDsS8021asSyncReceiptTimeInterval       MGMTInteger64,
    mgmtPtpStatusClocksPortsDsS8021asCurrentLogPDelayReqInterval   MGMTInteger8,
    mgmtPtpStatusClocksPortsDsS8021asAcceptableMasterTableEnabled  TruthValue,
    mgmtPtpStatusClocksPortsDsS8021asVersionNumber                 MGMTUnsigned8,
    mgmtPtpStatusClocksPortsDsS8021asCurrentCompNbrRateRatio       TruthValue,
    mgmtPtpStatusClocksPortsDsS8021asCurrentCompMeanLinkDelay      TruthValue
}

mgmtPtpStatusClocksPortsDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 1 }

mgmtPtpStatusClocksPortsDsPortId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 2 }

mgmtPtpStatusClocksPortsDsPortState OBJECT-TYPE
    SYNTAX      MGMTptpClockPortState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 3 }

mgmtPtpStatusClocksPortsDsLogMinDelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 4 }

mgmtPtpStatusClocksPortsDsPeerMeanPathDelay OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 5 }

mgmtPtpStatusClocksPortsDsPeerDelayOk OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatusClocksPortsDsEntry 6 }

mgmtPtpStatusClocksPortsDsS8021asPortRole OBJECT-TYPE
    SYNTAX      MGMTptp8021asPortRole
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port role of this port."
    ::= { mgmtPtpStatusClocksPortsDsEntry 8 }

mgmtPtpStatusClocksPortsDsS8021asIsMeasuringDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the port is measuring link propagation delay."
    ::= { mgmtPtpStatusClocksPortsDsEntry 9 }

mgmtPtpStatusClocksPortsDsS8021asAsCapable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the time-aware system at the other end of the link is 802.1AS
         capable."
    ::= { mgmtPtpStatusClocksPortsDsEntry 10 }

mgmtPtpStatusClocksPortsDsS8021asNeighborRateRatio OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Calculated neighbor rate ratio expressed as the fractional frequency
         offset multiplied by 2**41."
    ::= { mgmtPtpStatusClocksPortsDsEntry 11 }

mgmtPtpStatusClocksPortsDsS8021asCurrentLogAnnounceInterval OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..0))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "log2 of the current announce interval."
    ::= { mgmtPtpStatusClocksPortsDsEntry 12 }

mgmtPtpStatusClocksPortsDsS8021asCurrentLogSyncInterval OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..0))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "log2 of the current sync interval."
    ::= { mgmtPtpStatusClocksPortsDsEntry 13 }

mgmtPtpStatusClocksPortsDsS8021asSyncReceiptTimeInterval OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time interval after which sync receipt timeout occurs if
         time-synchronization information has not been received during the
         interval."
    ::= { mgmtPtpStatusClocksPortsDsEntry 14 }

mgmtPtpStatusClocksPortsDsS8021asCurrentLogPDelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "log2 of the current Pdelay_Req interval."
    ::= { mgmtPtpStatusClocksPortsDsEntry 15 }

mgmtPtpStatusClocksPortsDsS8021asAcceptableMasterTableEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Always FALSE."
    ::= { mgmtPtpStatusClocksPortsDsEntry 16 }

mgmtPtpStatusClocksPortsDsS8021asVersionNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IEEE 1588 PTP version number (always 2)."
    ::= { mgmtPtpStatusClocksPortsDsEntry 17 }

mgmtPtpStatusClocksPortsDsS8021asCurrentCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "current value of computeNeighborRateRatio."
    ::= { mgmtPtpStatusClocksPortsDsEntry 18 }

mgmtPtpStatusClocksPortsDsS8021asCurrentCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "current value of computeMeanLinkDelay."
    ::= { mgmtPtpStatusClocksPortsDsEntry 19 }

mgmtPtpStatusCmlds OBJECT IDENTIFIER
    ::= { mgmtPtpStatus 2 }

mgmtPtpStatusCmldsPortDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatusCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic part of the PTP clocks CMLDS PortDS."
    ::= { mgmtPtpStatusCmlds 1 }

mgmtPtpStatusCmldsPortDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatusCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The port_id index must be a value from 0 up to the number of ports."
    INDEX       { mgmtPtpStatusCmldsPortDsPortIdentityPortNumber }
    ::= { mgmtPtpStatusCmldsPortDsTable 1 }

MGMTPtpStatusCmldsPortDsEntry ::= SEQUENCE {
    mgmtPtpStatusCmldsPortDsPortIdentityPortNumber       Unsigned32,
    mgmtPtpStatusCmldsPortDsPortIdentityClockIdentity    OCTET STRING,
    mgmtPtpStatusCmldsPortDsCmldsLinkPortEnabled         TruthValue,
    mgmtPtpStatusCmldsPortDsIsMeasuringDelay             TruthValue,
    mgmtPtpStatusCmldsPortDsAsCapableAcrossDomains       TruthValue,
    mgmtPtpStatusCmldsPortDsMeanLinkDelay                MGMTInteger64,
    mgmtPtpStatusCmldsPortDsNeighborRateRatio            Integer32,
    mgmtPtpStatusCmldsPortDsCurrentLogPDelayReqInterval  MGMTInteger8,
    mgmtPtpStatusCmldsPortDsCurrentCompNbrRateRatio      TruthValue,
    mgmtPtpStatusCmldsPortDsCurrentCompMeanLinkDelay     TruthValue,
    mgmtPtpStatusCmldsPortDsVersionNumber                MGMTUnsigned8,
    mgmtPtpStatusCmldsPortDsMinorVersionNumber           MGMTUnsigned8
}

mgmtPtpStatusCmldsPortDsPortIdentityPortNumber OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This is the port number on which CMLDS can be enabled."
    ::= { mgmtPtpStatusCmldsPortDsEntry 1 }

mgmtPtpStatusCmldsPortDsPortIdentityClockIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is the 8 byte unique clock identity of the parent port."
    ::= { mgmtPtpStatusCmldsPortDsEntry 2 }

mgmtPtpStatusCmldsPortDsCmldsLinkPortEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This value is true if CMLDS is used on this port by atleast one PTP
         clock."
    ::= { mgmtPtpStatusCmldsPortDsEntry 3 }

mgmtPtpStatusCmldsPortDsIsMeasuringDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "true if the CMLDS service is measuring link propagation delay on this
         port."
    ::= { mgmtPtpStatusCmldsPortDsEntry 4 }

mgmtPtpStatusCmldsPortDsAsCapableAcrossDomains OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "true if CMLDS service reaches asCapable state when the conditions for
         neighbor rate ratio and link delay are met."
    ::= { mgmtPtpStatusCmldsPortDsEntry 5 }

mgmtPtpStatusCmldsPortDsMeanLinkDelay OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "the measured mean propagation delay on the link attached to this port"
    ::= { mgmtPtpStatusCmldsPortDsEntry 6 }

mgmtPtpStatusCmldsPortDsNeighborRateRatio OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Calculated neighbor rate ratio expressed as the fractional frequency
         offset multiplied by 2**41."
    ::= { mgmtPtpStatusCmldsPortDsEntry 7 }

mgmtPtpStatusCmldsPortDsCurrentLogPDelayReqInterval OBJECT-TYPE
    SYNTAX      MGMTInteger8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "the value is the logarithm to the base 2 of the current Pdelay_Req
         message transmission interval."
    ::= { mgmtPtpStatusCmldsPortDsEntry 8 }

mgmtPtpStatusCmldsPortDsCurrentCompNbrRateRatio OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "true if Neighbor rate ratio is being computed."
    ::= { mgmtPtpStatusCmldsPortDsEntry 9 }

mgmtPtpStatusCmldsPortDsCurrentCompMeanLinkDelay OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "true if MeanLinkDelay is being measured by CMLDS service currently."
    ::= { mgmtPtpStatusCmldsPortDsEntry 10 }

mgmtPtpStatusCmldsPortDsVersionNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Version number of IEEE 1588 PTP used in the PTP 802.1as profile."
    ::= { mgmtPtpStatusCmldsPortDsEntry 11 }

mgmtPtpStatusCmldsPortDsMinorVersionNumber OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minor version number set to 1 for transmitted messages of 802.1as
         profile."
    ::= { mgmtPtpStatusCmldsPortDsEntry 12 }

mgmtPtpStatusCmldsDefaultDs OBJECT IDENTIFIER
    ::= { mgmtPtpStatusCmlds 2 }

mgmtPtpStatusCmldsDefaultDsClockIdentity OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identity of the PTP clock associated with common link delay service."
    ::= { mgmtPtpStatusCmldsDefaultDs 1 }

mgmtPtpStatusCmldsDefaultDsNumberLinkPorts OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "number of Link Ports of the time-aware system on which the Common Mean
         Link Delay Service can be enabled."
    ::= { mgmtPtpStatusCmldsDefaultDs 2 }

mgmtPtpStatusCmldsDefaultDsSdoId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "sdoID for Common Mean Link Delay Service."
    ::= { mgmtPtpStatusCmldsDefaultDs 3 }

mgmtPtpControl OBJECT IDENTIFIER
    ::= { mgmtPtpMibObjects 4 }

mgmtPtpControlClocksTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpControlClocksEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the PTP clocks control structure."
    ::= { mgmtPtpControl 1 }

mgmtPtpControlClocksEntry OBJECT-TYPE
    SYNTAX      MGMTPtpControlClocksEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpControlClocksClockId }
    ::= { mgmtPtpControlClocksTable 1 }

MGMTPtpControlClocksEntry ::= SEQUENCE {
    mgmtPtpControlClocksClockId            Integer32,
    mgmtPtpControlClocksSyncToSystemClock  MGMTUnsigned8
}

mgmtPtpControlClocksClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpControlClocksEntry 1 }

mgmtPtpControlClocksSyncToSystemClock OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpControlClocksEntry 2 }

mgmtPtpStatistics OBJECT IDENTIFIER
    ::= { mgmtPtpMibObjects 5 }

mgmtPtpStatisticsClocks OBJECT IDENTIFIER
    ::= { mgmtPtpStatistics 1 }

mgmtPtpStatisticsClocksPortsDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatisticsClocksPortsDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the dynamic (status) part of the PTP port parameter statistics."
    ::= { mgmtPtpStatisticsClocks 1 }

mgmtPtpStatisticsClocksPortsDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatisticsClocksPortsDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The clockId index must be a value must be a value from 0 up to the
         number of PTP clocks minus one."
    INDEX       { mgmtPtpStatisticsClocksPortsDsClockId,
                  mgmtPtpStatisticsClocksPortsDsPortId }
    ::= { mgmtPtpStatisticsClocksPortsDsTable 1 }

MGMTPtpStatisticsClocksPortsDsEntry ::= SEQUENCE {
    mgmtPtpStatisticsClocksPortsDsClockId                        Integer32,
    mgmtPtpStatisticsClocksPortsDsPortId                         MGMTInterfaceIndex,
    mgmtPtpStatisticsClocksPortsDsRxSyncCount                    Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxFollowUpCount                Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxPdelayRequestCount           Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxPdelayResponseCount          Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxPdelayResponseFollowUpCount  Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxAnnounceCount                Unsigned32,
    mgmtPtpStatisticsClocksPortsDsRxPTPPacketDiscardCount        Unsigned32,
    mgmtPtpStatisticsClocksPortsDsSyncReceiptTimeoutCount        Unsigned32,
    mgmtPtpStatisticsClocksPortsDsAnnounceReceiptTimeoutCount    Unsigned32,
    mgmtPtpStatisticsClocksPortsDsPdelayAllowedLostResExcCount   Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxSyncCount                    Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxFollowUpCount                Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxPdelayRequestCount           Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxPdelayResponseCount          Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxPdelayResponseFollowUpCount  Unsigned32,
    mgmtPtpStatisticsClocksPortsDsTxAnnounceCount                Unsigned32
}

mgmtPtpStatisticsClocksPortsDsClockId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 1 }

mgmtPtpStatisticsClocksPortsDsPortId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 2 }

mgmtPtpStatisticsClocksPortsDsRxSyncCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time synchronization is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 3 }

mgmtPtpStatisticsClocksPortsDsRxFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Follow_Up is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 4 }

mgmtPtpStatisticsClocksPortsDsRxPdelayRequestCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Req is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 5 }

mgmtPtpStatisticsClocksPortsDsRxPdelayResponseCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 6 }

mgmtPtpStatisticsClocksPortsDsRxPdelayResponseFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp_Follow_Up is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 7 }

mgmtPtpStatisticsClocksPortsDsRxAnnounceCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Announce message is received."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 8 }

mgmtPtpStatisticsClocksPortsDsRxPTPPacketDiscardCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a PTP message is discarded due to the conditions
         described in IEEE 802.1AS clause 14.7.8."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 9 }

mgmtPtpStatisticsClocksPortsDsSyncReceiptTimeoutCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time sync receipt timeout occurs."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 10 }

mgmtPtpStatisticsClocksPortsDsAnnounceReceiptTimeoutCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time announce receipt timeout occurs."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 11 }

mgmtPtpStatisticsClocksPortsDsPdelayAllowedLostResExcCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time the value of the variable lostResponses exceeds
         the value of the variable allowedLostResponses."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 12 }

mgmtPtpStatisticsClocksPortsDsTxSyncCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time synchronization information is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 13 }

mgmtPtpStatisticsClocksPortsDsTxFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Follow_Up message is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 14 }

mgmtPtpStatisticsClocksPortsDsTxPdelayRequestCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Req message is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 15 }

mgmtPtpStatisticsClocksPortsDsTxPdelayResponseCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp message is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 16 }

mgmtPtpStatisticsClocksPortsDsTxPdelayResponseFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp_Follow_Up message is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 17 }

mgmtPtpStatisticsClocksPortsDsTxAnnounceCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time an Announce message is transmitted."
    ::= { mgmtPtpStatisticsClocksPortsDsEntry 18 }

mgmtPtpStatisticsCmlds OBJECT IDENTIFIER
    ::= { mgmtPtpStatistics 2 }

mgmtPtpStatisticsCmldsPortDsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPtpStatisticsCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the statistics part of Common Mean Link Delay Service Port."
    ::= { mgmtPtpStatisticsCmlds 1 }

mgmtPtpStatisticsCmldsPortDsEntry OBJECT-TYPE
    SYNTAX      MGMTPtpStatisticsCmldsPortDsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The port_id index must be a value from 0 up to the number of ports."
    INDEX       {                   mgmtPtpStatisticsCmldsPortDsPortIdentityPortNumber }
    ::= { mgmtPtpStatisticsCmldsPortDsTable 1 }

MGMTPtpStatisticsCmldsPortDsEntry ::= SEQUENCE {
    mgmtPtpStatisticsCmldsPortDsPortIdentityPortNumber         Unsigned32,
    mgmtPtpStatisticsCmldsPortDsRxPdelayRequestCount           Unsigned32,
    mgmtPtpStatisticsCmldsPortDsRxPdelayResponseCount          Unsigned32,
    mgmtPtpStatisticsCmldsPortDsRxPdelayResponseFollowUpCount  Unsigned32,
    mgmtPtpStatisticsCmldsPortDsRxPTPPacketDiscardCount        Unsigned32,
    mgmtPtpStatisticsCmldsPortDsPdelayAllowedLostResExcCount   Unsigned32,
    mgmtPtpStatisticsCmldsPortDsTxPdelayRequestCount           Unsigned32,
    mgmtPtpStatisticsCmldsPortDsTxPdelayResponseCount          Unsigned32,
    mgmtPtpStatisticsCmldsPortDsTxPdelayResponseFollowUpCount  Unsigned32
}

mgmtPtpStatisticsCmldsPortDsPortIdentityPortNumber OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This is the port number of the Common Mean Link Delay Service enabled
         port."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 1 }

mgmtPtpStatisticsCmldsPortDsRxPdelayRequestCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Req is received."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 2 }

mgmtPtpStatisticsCmldsPortDsRxPdelayResponseCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Response is received."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 3 }

mgmtPtpStatisticsCmldsPortDsRxPdelayResponseFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a PdelayResponseFollowup is received."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 4 }

mgmtPtpStatisticsCmldsPortDsRxPTPPacketDiscardCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a PTP message of Common Mean Link Delay Service
         is discarded."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 5 }

mgmtPtpStatisticsCmldsPortDsPdelayAllowedLostResExcCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time the value of the variable lostResponses exceeds
         the value of the variable allowedLostResponses."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 6 }

mgmtPtpStatisticsCmldsPortDsTxPdelayRequestCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Req message is transmitted."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 7 }

mgmtPtpStatisticsCmldsPortDsTxPdelayResponseCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp message is transmitted."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 8 }

mgmtPtpStatisticsCmldsPortDsTxPdelayResponseFollowUpCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Increments every time a Pdelay_Resp_Follow_Up message is transmitted."
    ::= { mgmtPtpStatisticsCmldsPortDsEntry 9 }

mgmtPtpMibConformance OBJECT IDENTIFIER
    ::= { mgmtPtpMib 2 }

mgmtPtpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPtpMibConformance 1 }

mgmtPtpMibGroups OBJECT IDENTIFIER
    ::= { mgmtPtpMibConformance 2 }

mgmtPtpCapabilitiesGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpCapabilitiesGlobalsClockCount,
                  mgmtPtpCapabilitiesGlobalsHasMsPdv,
                  mgmtPtpCapabilitiesGlobalsHasHwClkDomains }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 1 }

mgmtPtpConfigGlobalsExternalClockModeInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigGlobalsExternalClockModeOnePpsMode,
                  mgmtPtpConfigGlobalsExternalClockModeExternalEnable,
                  mgmtPtpConfigGlobalsExternalClockModeAdjustMethod,
                  mgmtPtpConfigGlobalsExternalClockModeClockFrequency }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 2 }

mgmtPtpConfigGlobalsSystemTimeSyncModeInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigGlobalsSystemTimeSyncModeMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 3 }

mgmtPtpConfigClocksDefaultDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksDefaultDsClockId,
                  mgmtPtpConfigClocksDefaultDsDeviceType,
                  mgmtPtpConfigClocksDefaultDsTwoStepFlag,
                  mgmtPtpConfigClocksDefaultDsPriority1,
                  mgmtPtpConfigClocksDefaultDsPriority2,
                  mgmtPtpConfigClocksDefaultDsOneWay,
                  mgmtPtpConfigClocksDefaultDsDomainNumber,
                  mgmtPtpConfigClocksDefaultDsProtocol,
                  mgmtPtpConfigClocksDefaultDsVid,
                  mgmtPtpConfigClocksDefaultDsPcp,
                  mgmtPtpConfigClocksDefaultDsMep,
                  mgmtPtpConfigClocksDefaultDsClkDom,
                  mgmtPtpConfigClocksDefaultDsDscp,
                  mgmtPtpConfigClocksDefaultDsProfile,
                  mgmtPtpConfigClocksDefaultDsLocalPriority,
                  mgmtPtpConfigClocksDefaultDsFilterType,
                  mgmtPtpConfigClocksDefaultDsPathTraceEnable,
                  mgmtPtpConfigClocksDefaultDsAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 4 }

mgmtPtpConfigClocksDefaultDsTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksDefaultDsTableRowEditorClockId,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorDeviceType,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorTwoStepFlag,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorPriority1,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorPriority2,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorOneWay,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorDomainNumber,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorProtocol,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorVid,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorPcp,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorMep,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorClkDom,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorDscp,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorProfile,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorLocalPriority,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorFilterType,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorPathTraceEnable,
                  mgmtPtpConfigClocksDefaultDsTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 5 }

mgmtPtpConfigClocksTimePropertiesDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksTimePropertiesDsClockId,
                  mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffset,
                  mgmtPtpConfigClocksTimePropertiesDsCurrentUtcOffsetValid,
                  mgmtPtpConfigClocksTimePropertiesDsLeap59,
                  mgmtPtpConfigClocksTimePropertiesDsLeap61,
                  mgmtPtpConfigClocksTimePropertiesDsTimeTraceable,
                  mgmtPtpConfigClocksTimePropertiesDsFrequencyTraceable,
                  mgmtPtpConfigClocksTimePropertiesDsPtpTimescale,
                  mgmtPtpConfigClocksTimePropertiesDsTimeSource,
                  mgmtPtpConfigClocksTimePropertiesDsPendingLeap,
                  mgmtPtpConfigClocksTimePropertiesDsLeapDate,
                  mgmtPtpConfigClocksTimePropertiesDsLeapType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 6 }

mgmtPtpConfigClocksFilterParametersTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksFilterParametersClockId,
                  mgmtPtpConfigClocksFilterParametersDelayFilter,
                  mgmtPtpConfigClocksFilterParametersPeriod,
                  mgmtPtpConfigClocksFilterParametersDist }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 7 }

mgmtPtpConfigClocksServoParametersTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksServoParametersClockId,
                  mgmtPtpConfigClocksServoParametersDisplay,
                  mgmtPtpConfigClocksServoParametersPEnable,
                  mgmtPtpConfigClocksServoParametersIEnable,
                  mgmtPtpConfigClocksServoParametersDEnable,
                  mgmtPtpConfigClocksServoParametersPval,
                  mgmtPtpConfigClocksServoParametersIval,
                  mgmtPtpConfigClocksServoParametersDval,
                  mgmtPtpConfigClocksServoParametersSrvOption,
                  mgmtPtpConfigClocksServoParametersSynceThreshold,
                  mgmtPtpConfigClocksServoParametersSynceAp,
                  mgmtPtpConfigClocksServoParametersHoFilter,
                  mgmtPtpConfigClocksServoParametersStableAdjThreshold,
                  mgmtPtpConfigClocksServoParametersGain }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 8 }

mgmtPtpConfigClocksSlaveConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksSlaveConfigClockId,
                  mgmtPtpConfigClocksSlaveConfigStableOffset,
                  mgmtPtpConfigClocksSlaveConfigOffsetOk,
                  mgmtPtpConfigClocksSlaveConfigOffsetFail }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 9 }

mgmtPtpConfigClocksUnicastSlaveConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksUnicastSlaveConfigClockId,
                  mgmtPtpConfigClocksUnicastSlaveConfigMasterId,
                  mgmtPtpConfigClocksUnicastSlaveConfigDuration,
                  mgmtPtpConfigClocksUnicastSlaveConfigIpAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 10 }

mgmtPtpConfigClocksPortDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksPortDsClockId,
                  mgmtPtpConfigClocksPortDsPortId,
                  mgmtPtpConfigClocksPortDsEnabled,
                  mgmtPtpConfigClocksPortDsLogAnnounceInterval,
                  mgmtPtpConfigClocksPortDsAnnounceReceiptTimeout,
                  mgmtPtpConfigClocksPortDsLogSyncInterval,
                  mgmtPtpConfigClocksPortDsDelayMechanism,
                  mgmtPtpConfigClocksPortDsLogMinPdelayReqInterval,
                  mgmtPtpConfigClocksPortDsDelayAsymmetry,
                  mgmtPtpConfigClocksPortDsIngressLatency,
                  mgmtPtpConfigClocksPortDsEgressLatency,
                  mgmtPtpConfigClocksPortDsPortInternal,
                  mgmtPtpConfigClocksPortDsVersionNumber,
                  mgmtPtpConfigClocksPortDsMcastAddr,
                  mgmtPtpConfigClocksPortDsNotSlave,
                  mgmtPtpConfigClocksPortDsLocalPriority,
                  mgmtPtpConfigClocksPortDsC8021asNeighborPropDelayThresh,
                  mgmtPtpConfigClocksPortDsC8021asSyncReceiptTimeout,
                  mgmtPtpConfigClocksPortDsC8021asAllowedLostResponses,
                  mgmtPtpConfigClocksPortDsC8021asAllowedFaults,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogAnnounceIntvl,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogSyncIntvl,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogPdelayReqIntvl,
                  mgmtPtpConfigClocksPortDsC8021asMgtSettableLogAnnounceIntvl,
                  mgmtPtpConfigClocksPortDsC8021asMgtSettableLogSyncIntvl,
                  mgmtPtpConfigClocksPortDsC8021asMgtSettableLogPdelayReqIntvl,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSettableLogGptpCapMsgIntvl,
                  mgmtPtpConfigClocksPortDsC8021asMgtSettableLogGptpCapMsgIntvl,
                  mgmtPtpConfigClocksPortDsC8021GptpCapableReceiptTimeout,
                  mgmtPtpConfigClocksPortDsC8021InitialLogGptpCapableMessageIntvl,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompNbrRateRatio,
                  mgmtPtpConfigClocksPortDsC8021asMgtSetCompNbrRateRatio,
                  mgmtPtpConfigClocksPortDsC8021asUseMgtSetCompMeanLinkDelay,
                  mgmtPtpConfigClocksPortDsC8021asmgtSetCompMeanLinkDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 11 }

mgmtPtpConfigClocksVirtualPortCfgTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigClocksVirtualPortCfgClockId,
                  mgmtPtpConfigClocksVirtualPortCfgEnable,
                  mgmtPtpConfigClocksVirtualPortCfgIoPin,
                  mgmtPtpConfigClocksVirtualPortCfgClockQualityClockClass,
                  mgmtPtpConfigClocksVirtualPortCfgClockQualityClockAccuracy,
                  mgmtPtpConfigClocksVirtualPortCfgClockQualityOffsetScaledLogVar,
                  mgmtPtpConfigClocksVirtualPortCfgPriority1,
                  mgmtPtpConfigClocksVirtualPortCfgPriority2,
                  mgmtPtpConfigClocksVirtualPortCfgLocalPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 12 }

mgmtPtpConfigCmldsPortDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpConfigCmldsPortDsPortId,
                  mgmtPtpConfigCmldsPortDsDelayAsymmetry,
                  mgmtPtpConfigCmldsPortDsInitialLogPdelayReqInterval,
                  mgmtPtpConfigCmldsPortDsUseMgtSettableLogPdelayReqInterval,
                  mgmtPtpConfigCmldsPortDsMgtSettableLogPdelayReqInterval,
                  mgmtPtpConfigCmldsPortDsInitialCompNbrRateRatio,
                  mgmtPtpConfigCmldsPortDsUseMgtSetCompNbrRateRatio,
                  mgmtPtpConfigCmldsPortDsMgtSetCompNbrRateRatio,
                  mgmtPtpConfigCmldsPortDsInitialCompMeanLinkDelay,
                  mgmtPtpConfigCmldsPortDsUseMgtSetCompMeanLinkDelay,
                  mgmtPtpConfigCmldsPortDsMgtSetCompMeanLinkDelay,
                  mgmtPtpConfigCmldsPortDsAllowedLostResponses,
                  mgmtPtpConfigCmldsPortDsAllowedFaults,
                  mgmtPtpConfigCmldsPortDsMeanLinkDelayThresh }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 13 }

mgmtPtpStatusClocksDefaultDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksDefaultDsClockId,
                  mgmtPtpStatusClocksDefaultDsClockIdentity,
                  mgmtPtpStatusClocksDefaultDsClockQualityClockClass,
                  mgmtPtpStatusClocksDefaultDsClockQualityClockAccuracy,
                  mgmtPtpStatusClocksDefaultDsClockQualityOffsetScaledLogVar,
                  mgmtPtpStatusClocksDefaultDsS8021asGmCapable,
                  mgmtPtpStatusClocksDefaultDsS8021asSdoId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 14 }

mgmtPtpStatusClocksCurrentDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksCurrentDsClockId,
                  mgmtPtpStatusClocksCurrentDsStepsRemoved,
                  mgmtPtpStatusClocksCurrentDsOffsetFromMaster,
                  mgmtPtpStatusClocksCurrentDsMeanPathDelay,
                  mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeLow,
                  mgmtPtpStatusClocksCurrentDsCur8021asLastGMPhaseChangeHigh,
                  mgmtPtpStatusClocksCurrentDsCur8021asLastGMFreqChange,
                  mgmtPtpStatusClocksCurrentDsCur8021asGmTimeBaseIndicator,
                  mgmtPtpStatusClocksCurrentDsCur8021asGmChangeCount,
                  mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMChangeEvt,
                  mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMPhaseChangeEvt,
                  mgmtPtpStatusClocksCurrentDsCur8021asTimeOfLastGMFreqChangeEvent }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 15 }

mgmtPtpStatusClocksParentDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksParentDsClockId,
                  mgmtPtpStatusClocksParentDsParentPortIdentityClockIdentity,
                  mgmtPtpStatusClocksParentDsParentPortIdentityPortNumber,
                  mgmtPtpStatusClocksParentDsParentStats,
                  mgmtPtpStatusClocksParentDsObservedParentOffsetScaledLogVar,
                  mgmtPtpStatusClocksParentDsObservedParentClockPhaseChangeRate,
                  mgmtPtpStatusClocksParentDsGrmstrIdentity,
                  mgmtPtpStatusClocksParentDsGrmstrClkQualClockClass,
                  mgmtPtpStatusClocksParentDsGmstrClkQualClockAccuracy,
                  mgmtPtpStatusClocksParentDsGmstrClkQualOffsetScaledLogVar,
                  mgmtPtpStatusClocksParentDsGmstrPriority1,
                  mgmtPtpStatusClocksParentDsGmstrPriority2,
                  mgmtPtpStatusClocksParentDsPar8021asCumulativeRateRatio }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 16 }

mgmtPtpStatusClocksTimePropertiesDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksTimePropertiesDsClockId,
                  mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffset,
                  mgmtPtpStatusClocksTimePropertiesDsCurrentUtcOffsetValid,
                  mgmtPtpStatusClocksTimePropertiesDsLeap59,
                  mgmtPtpStatusClocksTimePropertiesDsLeap61,
                  mgmtPtpStatusClocksTimePropertiesDsTimeTraceable,
                  mgmtPtpStatusClocksTimePropertiesDsFrequencyTraceable,
                  mgmtPtpStatusClocksTimePropertiesDsPtpTimescale,
                  mgmtPtpStatusClocksTimePropertiesDsTimeSource,
                  mgmtPtpStatusClocksTimePropertiesDsPendingLeap,
                  mgmtPtpStatusClocksTimePropertiesDsLeapDate,
                  mgmtPtpStatusClocksTimePropertiesDsLeapType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 17 }

mgmtPtpStatusClocksSlaveDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksSlaveDsClockId,
                  mgmtPtpStatusClocksSlaveDsSlavePort,
                  mgmtPtpStatusClocksSlaveDsSlaveState,
                  mgmtPtpStatusClocksSlaveDsHoldoverStable,
                  mgmtPtpStatusClocksSlaveDsHoldoverAdj }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 18 }

mgmtPtpStatusClocksUnicastMasterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksUnicastMasterClockId,
                  mgmtPtpStatusClocksUnicastMasterSlaveIp,
                  mgmtPtpStatusClocksUnicastMasterSlaveMac,
                  mgmtPtpStatusClocksUnicastMasterPort,
                  mgmtPtpStatusClocksUnicastMasterAnnLogMsgPeriod,
                  mgmtPtpStatusClocksUnicastMasterAnn,
                  mgmtPtpStatusClocksUnicastMasterLogMsgPeriod,
                  mgmtPtpStatusClocksUnicastMasterSync }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 19 }

mgmtPtpStatusClocksUnicastSlaveTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksUnicastSlaveClockId,
                  mgmtPtpStatusClocksUnicastSlaveMasterId,
                  mgmtPtpStatusClocksUnicastSlaveMasterIp,
                  mgmtPtpStatusClocksUnicastSlaveMasterMac,
                  mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityClockIdentity,
                  mgmtPtpStatusClocksUnicastSlaveSourcePortIdentityPortNumber,
                  mgmtPtpStatusClocksUnicastSlavePort,
                  mgmtPtpStatusClocksUnicastSlaveLogMsgPeriod,
                  mgmtPtpStatusClocksUnicastSlaveCommState,
                  mgmtPtpStatusClocksUnicastSlaveConfMasterIp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 20 }

mgmtPtpStatusClocksPortsDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusClocksPortsDsClockId,
                  mgmtPtpStatusClocksPortsDsPortId,
                  mgmtPtpStatusClocksPortsDsPortState,
                  mgmtPtpStatusClocksPortsDsLogMinDelayReqInterval,
                  mgmtPtpStatusClocksPortsDsPeerMeanPathDelay,
                  mgmtPtpStatusClocksPortsDsPeerDelayOk,
                  mgmtPtpStatusClocksPortsDsS8021asPortRole,
                  mgmtPtpStatusClocksPortsDsS8021asIsMeasuringDelay,
                  mgmtPtpStatusClocksPortsDsS8021asAsCapable,
                  mgmtPtpStatusClocksPortsDsS8021asNeighborRateRatio,
                  mgmtPtpStatusClocksPortsDsS8021asCurrentLogAnnounceInterval,
                  mgmtPtpStatusClocksPortsDsS8021asCurrentLogSyncInterval,
                  mgmtPtpStatusClocksPortsDsS8021asSyncReceiptTimeInterval,
                  mgmtPtpStatusClocksPortsDsS8021asCurrentLogPDelayReqInterval,
                  mgmtPtpStatusClocksPortsDsS8021asAcceptableMasterTableEnabled,
                  mgmtPtpStatusClocksPortsDsS8021asVersionNumber,
                  mgmtPtpStatusClocksPortsDsS8021asCurrentCompNbrRateRatio,
                  mgmtPtpStatusClocksPortsDsS8021asCurrentCompMeanLinkDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 21 }

mgmtPtpStatusCmldsPortDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusCmldsPortDsPortIdentityPortNumber,
                  mgmtPtpStatusCmldsPortDsPortIdentityClockIdentity,
                  mgmtPtpStatusCmldsPortDsCmldsLinkPortEnabled,
                  mgmtPtpStatusCmldsPortDsIsMeasuringDelay,
                  mgmtPtpStatusCmldsPortDsAsCapableAcrossDomains,
                  mgmtPtpStatusCmldsPortDsMeanLinkDelay,
                  mgmtPtpStatusCmldsPortDsNeighborRateRatio,
                  mgmtPtpStatusCmldsPortDsCurrentLogPDelayReqInterval,
                  mgmtPtpStatusCmldsPortDsCurrentCompNbrRateRatio,
                  mgmtPtpStatusCmldsPortDsCurrentCompMeanLinkDelay,
                  mgmtPtpStatusCmldsPortDsVersionNumber,
                  mgmtPtpStatusCmldsPortDsMinorVersionNumber }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 22 }

mgmtPtpStatusCmldsDefaultDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatusCmldsDefaultDsClockIdentity,
                  mgmtPtpStatusCmldsDefaultDsNumberLinkPorts,
                  mgmtPtpStatusCmldsDefaultDsSdoId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 23 }

mgmtPtpControlClocksTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpControlClocksClockId,
                  mgmtPtpControlClocksSyncToSystemClock }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 24 }

mgmtPtpStatisticsClocksPortsDsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPtpStatisticsClocksPortsDsClockId,
                  mgmtPtpStatisticsClocksPortsDsPortId,
                  mgmtPtpStatisticsClocksPortsDsRxSyncCount,
                  mgmtPtpStatisticsClocksPortsDsRxFollowUpCount,
                  mgmtPtpStatisticsClocksPortsDsRxPdelayRequestCount,
                  mgmtPtpStatisticsClocksPortsDsRxPdelayResponseCount,
                  mgmtPtpStatisticsClocksPortsDsRxPdelayResponseFollowUpCount,
                  mgmtPtpStatisticsClocksPortsDsRxAnnounceCount,
                  mgmtPtpStatisticsClocksPortsDsRxPTPPacketDiscardCount,
                  mgmtPtpStatisticsClocksPortsDsSyncReceiptTimeoutCount,
                  mgmtPtpStatisticsClocksPortsDsAnnounceReceiptTimeoutCount,
                  mgmtPtpStatisticsClocksPortsDsPdelayAllowedLostResExcCount,
                  mgmtPtpStatisticsClocksPortsDsTxSyncCount,
                  mgmtPtpStatisticsClocksPortsDsTxFollowUpCount,
                  mgmtPtpStatisticsClocksPortsDsTxPdelayRequestCount,
                  mgmtPtpStatisticsClocksPortsDsTxPdelayResponseCount,
                  mgmtPtpStatisticsClocksPortsDsTxPdelayResponseFollowUpCount,
                  mgmtPtpStatisticsClocksPortsDsTxAnnounceCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 25 }

mgmtPtpStatisticsCmldsPortDsTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtPtpStatisticsCmldsPortDsPortIdentityPortNumber,
                  mgmtPtpStatisticsCmldsPortDsRxPdelayRequestCount,
                  mgmtPtpStatisticsCmldsPortDsRxPdelayResponseCount,
                  mgmtPtpStatisticsCmldsPortDsRxPdelayResponseFollowUpCount,
                  mgmtPtpStatisticsCmldsPortDsRxPTPPacketDiscardCount,
                  mgmtPtpStatisticsCmldsPortDsPdelayAllowedLostResExcCount,
                  mgmtPtpStatisticsCmldsPortDsTxPdelayRequestCount,
                  mgmtPtpStatisticsCmldsPortDsTxPdelayResponseCount,
                  mgmtPtpStatisticsCmldsPortDsTxPdelayResponseFollowUpCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPtpMibGroups 26 }

mgmtPtpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPtpCapabilitiesGlobalsInfoGroup,
                       mgmtPtpConfigGlobalsExternalClockModeInfoGroup,
                       mgmtPtpConfigGlobalsSystemTimeSyncModeInfoGroup,
                       mgmtPtpConfigClocksDefaultDsTableInfoGroup,
                       mgmtPtpConfigClocksDefaultDsTableRowEditorInfoGroup,
                       mgmtPtpConfigClocksTimePropertiesDsTableInfoGroup,
                       mgmtPtpConfigClocksFilterParametersTableInfoGroup,
                       mgmtPtpConfigClocksServoParametersTableInfoGroup,
                       mgmtPtpConfigClocksSlaveConfigTableInfoGroup,
                       mgmtPtpConfigClocksUnicastSlaveConfigTableInfoGroup,
                       mgmtPtpConfigClocksPortDsTableInfoGroup,
                       mgmtPtpConfigClocksVirtualPortCfgTableInfoGroup,
                       mgmtPtpConfigCmldsPortDsTableInfoGroup,
                       mgmtPtpStatusClocksDefaultDsTableInfoGroup,
                       mgmtPtpStatusClocksCurrentDsTableInfoGroup,
                       mgmtPtpStatusClocksParentDsTableInfoGroup,
                       mgmtPtpStatusClocksTimePropertiesDsTableInfoGroup,
                       mgmtPtpStatusClocksSlaveDsTableInfoGroup,
                       mgmtPtpStatusClocksUnicastMasterTableInfoGroup,
                       mgmtPtpStatusClocksUnicastSlaveTableInfoGroup,
                       mgmtPtpStatusClocksPortsDsTableInfoGroup,
                       mgmtPtpStatusCmldsPortDsTableInfoGroup,
                       mgmtPtpStatusCmldsDefaultDsTableInfoGroup,
                       mgmtPtpControlClocksTableInfoGroup,
                       mgmtPtpStatisticsClocksPortsDsTableInfoGroup,
                       mgmtPtpStatisticsCmldsPortDsTableInfoGroup }

    ::= { mgmtPtpMibCompliances 1 }

END
