package test

import (
	"encoding/binary"
	"fmt"
	"mnms/idpsystem/idps/detect"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type TcpTestData struct {
	FIN, SYN, RST, PSH, ACK, URG, ECE, CWR, NS bool
	Seq, Ack                                   uint32
	Window, Mss                                uint16
}

func TcpPacketWithFlag(srcip string, srcport uint16, dstip string, dstport uint16, data TcpTestData, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP(srcip),
		DstIP:    net.ParseIP(dstip),
		Version:  4,
		Protocol: layers.IPProtocolTCP,
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
		FIN:     data.FIN,
		SYN:     data.SYN,
		RST:     data.RST,
		PSH:     data.PSH,
		ACK:     data.ACK,
		URG:     data.URG,
		ECE:     data.ECE,
		CWR:     data.CWR,
		NS:      data.NS,
		Seq:     data.Seq,
		Ack:     data.Ack,
		Window:  data.Window,
	}
	if data.Mss != 0 {
		bs := make([]byte, 2)
		binary.BigEndian.PutUint16(bs, data.Mss)
		tcpLayer.Options = append(tcpLayer.Options, layers.TCPOption{
			OptionType:   layers.TCPOptionKindMSS,
			OptionLength: 4,
			OptionData:   bs,
		})
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}
func TCPPacketIPV4(srcip string, srcport uint16, dstip string, dstport uint16, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP(srcip),
		DstIP:    net.ParseIP(dstip),
		Version:  4,
		Protocol: layers.IPProtocolTCP,
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func TestTcpFlags(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:S,CE;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				SYN: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:A,CE;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				SYN: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:!A,CE;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				SYN: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:!S,CE;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				SYN: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:*S,CE;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				SYN: true,
				FIN: true,
				RST: true,
				PSH: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:FPU,CE;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				FIN: true,
				PSH: true,
				URG: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:+AP,UR;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				ACK: true,
				PSH: true,
				SYN: true,
				RST: true,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.flags:0,UR;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				ACK: true,
				PSH: true,
				SYN: true,
				RST: true,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcpFlags:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestTcpSeq(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";seq:20;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Seq: 20,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";seq:20;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Seq: 2,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcpSeq:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestTcpAck(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ack:6000;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Ack: 6000,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ack:20;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Ack: 2000,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcpAck:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestTcpWindow(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";window:900;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Window: 900,
			},
			link: testNetLinker{},
		},

		{
			rule:     []string{`alert ip any any -> any any (msg:"test";window:900;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Window: 500,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";window:!900;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Window: 500,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcpWindow:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestTcpMss(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.mss:>536;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 4660,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.mss:536;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 4660,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.mss:8000-9000;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 8999,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.mss:<3000;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 3001,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.mss:3000;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 3000,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcpAck:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestTcphdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		data     TcpTestData
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.hdr;content:"|02 04|";offset:20;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			data: TcpTestData{
				Mss: 50,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";tcp.hdr;content:"|02 04|";offset:20;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTcphdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], test.data, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TCPPacketIPv6(srcip string, srcport uint16, dstip string, dstport uint16, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv6{
		SrcIP:      net.ParseIP(srcip),
		DstIP:      net.ParseIP(dstip),
		Version:    6,
		NextHeader: layers.IPProtocolTCP,
		HopLimit:   64,
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}
