import TrapSettingDrawer from "../components/drawer/TrapSettingDrawer";
import { it, describe } from "vitest";
import { fireEvent, render } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";

describe("TrapSettingDrawer", () => {
  it("check if the TrapSettingDrawer component render", () => {
    render(
      <Provider store={store}>
        <TrapSettingDrawer />
      </Provider>
    );
  });

  it("submits the trap setting form with valid values", () => {
    const { getByLabelText, getByText } = render(
      <Provider store={store}>
        <TrapSettingDrawer />
      </Provider>
    );

    const serverIPInput = getByLabelText("Server IP");
    const serverPortInput = getByLabelText("Server Port");
    const comStringInput = getByLabelText("Community String");
    const saveButton = getByText("save");

    fireEvent.change(serverIPInput, { target: { value: "***********" } });
    fireEvent.change(serverPortInput, { target: { value: 162 } });
    fireEvent.change(comStringInput, { target: { value: "public" } });
    fireEvent.click(saveButton);
  });

  it("Check save button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <TrapSettingDrawer />{" "}
      </Provider>
    );
    const saveButton = getByText("save");
    fireEvent.click(saveButton);
  });
  it("Check cancel button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <TrapSettingDrawer />{" "}
      </Provider>
    );
    const cancelButton = getByText("cancel");
    fireEvent.click(cancelButton);
  });
});
