import {
  Card,
  List,
  Space,
  Typography,
  theme as antdTheme,
  Button,
} from "antd";
import dayjs from "dayjs";
// eslint-disable-next-line no-unused-vars
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  clearSocketResultData,
  getInitialAlertData,
  socketControlSelector,
} from "../../features/socketControl/socketControlSlice";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(relativeTime);

const EventListCard = () => {
  const dispatch = useDispatch();
  const { token } = antdTheme.useToken();
  const { socketResultData, fetchingInitalAlertData } = useSelector(
    socketControlSelector
  );
  useEffect(() => {
    if (socketResultData?.length === 0) dispatch(getInitialAlertData());
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Card
      title="Alert Message"
      bordered={false}
      headStyle={{ minHeight: 40 }}
      bodyStyle={{
        height: "calc(100vh - 128px)",
        overflow: "auto",
        padding: "0 10px",
      }}
      extra={
        <Space>
          <Button
            type="primary"
            onClick={() => dispatch(clearSocketResultData())}
          >
            clear all
          </Button>
        </Space>
      }
    >
      <List
        itemLayout="horizontal"
        dataSource={socketResultData}
        loading={fetchingInitalAlertData}
        renderItem={(item) => (
          <List.Item>
            <Space direction="vertical" style={{ width: "100%" }}>
              <List.Item.Meta
                title={
                  <Typography.Text style={{ color: token.colorPrimary }} strong>
                    {item.title}
                  </Typography.Text>
                }
                description={item.message}
              />
              <Typography.Text italic>
                {dayjs(item.time_stamp).fromNow()}
              </Typography.Text>
            </Space>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default EventListCard;
