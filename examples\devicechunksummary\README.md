## example to create device chunk summary

Given a file input that contains a json list of devices, the program will create a json output file that contains a summary of the devices in chunks. The chunks are defined by the chunksize parameter.  Each chunk is a list of devices.  The devices in input file will be placed into chunks in the output file.

The output file will have a json list of chunks, summaries for each chunk, followed by final summary.


    go run main.go -chunksize 5 -input ../devicelist/devicelist.json -output devicechunksummary.json
