package mpm

import (
	"sync"

	"github.com/flier/gohs/hyperscan"
)

func newHyperScan() *HyperScan {
	return &HyperScan{hash: map[string]*schsPattern{}, m: new(sync.Mutex)}
}

type HyperScan struct {
	m            *sync.Mutex
	hash         map[string]*schsPattern
	db           hyperscan.BlockDatabase
	s            *hyperscan.Scratch
	parray       []*schsPattern
	patterncnt   int
	runningcount int
}

func newPatterns(count int) []*schsPattern {
	return make([]*schsPattern, 0, count)
}

func (h *HyperScan) lookup(contents Content) *schsPattern {
	value := contents.Pattern
	if v, ok := h.hash[value]; ok {
		if v.Offset == contents.Offset && v.Nocase == contents.Nocase {
			return v
		}
		return nil
	}
	return nil
}

func (h *HyperScan) addhash(patt *schsPattern) {
	value := patt.Content.Pattern
	if v, ok := h.hash[value]; ok {
		current := v
		for current.next != nil {
			current = current.next
		}
		current.next = patt
	} else {
		h.hash[value] = patt
	}

}
func (h *HyperScan) AddContent(content Content) error {
	h.m.Lock()
	defer h.m.Unlock()
	v := h.lookup(content)
	if v == nil {
		h.patterncnt++
		patt := newschsPattern(content)
		h.addhash(patt)
		patt.sids = append(patt.sids, content.Id)
	} else {
		found := false
		for _, id := range v.sids {
			if id == content.Id {
				found = true
				break
			}
		}
		if !found {
			v.sids = append(v.sids, content.Id)
		}

	}
	return nil
}

func (h *HyperScan) Build() error {
	h.m.Lock()
	defer func() {
		h.patterncnt = 0
		h.hash = map[string]*schsPattern{}
		h.m.Unlock()
	}()
	if h.patterncnt == 0 {
		h.db, h.s = nil, nil
		return nil
	}
	parray := newPatterns(h.patterncnt)
	for _, node := range h.hash {
		for node != nil {
			next := node.next
			node.next = nil
			parray = append(parray, node)
			node = next
		}
	}
	pattents := make(hyperscan.Patterns, 0, h.patterncnt)
	for k, node := range parray {
		pat, err := node.createPattern()
		if err != nil {
			return err
		}
		pat.Id = k
		pattents = append(pattents, pat)
	}
	db, err := hyperscan.NewManagedBlockDatabase(pattents...)
	if err != nil {
		return err
	}
	s, err := hyperscan.NewManagedScratch(db)
	if err != nil {
		return err
	}
	h.s, h.db, h.parray, h.runningcount = s, db, parray, h.patterncnt
	return nil
}

func (h *HyperScan) MatchBytes(b []byte) (bool, error) {
	if h.db == nil || h.s == nil {
		return false, nil
	}
	r := false
	s, err := h.s.Clone()
	if err != nil {
		return r, err
	}
	if b == nil {
		b = []byte{}
	}
	handler := hyperscan.MatchHandler(func(id uint, from, to uint64, flags uint, context interface{}) error {
		r = true
		return ErrorStopMatching
	})
	defer s.Free()
	h.db.Scan(b, s, handler, nil)
	return r, nil
}
func (h *HyperScan) MatcheIds(b []byte) []int {
	if h.db == nil || h.s == nil {
		return []int{}
	}
	s, err := h.s.Clone()
	if err != nil {
		return []int{}
	}
	ans := make([]int, 0, h.runningcount)
	handler := hyperscan.MatchHandler(func(id uint, from, to uint64, flags uint, context any) error {
		if id >= uint(h.runningcount) {
			return nil
		}
		ids := h.parray[id].sids
		ans = append(ans, ids...)
		return nil
	})
	defer s.Free()
	h.db.Scan(b, s, handler, nil)
	return ans
}
