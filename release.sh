#!/bin/bash
# Release script
# Besides make release, this script mainly copies files not in the repo to the release folder

clean_everything() {
    echo "Cleaning everything"
    echo ""
    rm -rf release
    rm -rf userguide
    rm -rf $HOME/mnms_installation
    rm -rf caddy_2.6.3_linux_amd64.tar.gz*
    rm -rf /tmp/caddy_linux_amd64
    rm -rf caddy_2.6.3_windows_amd64.zip*
    rm -rf /tmp/caddy_windows_amd64
    rm -rf npcap-1.72.exe*
    rm -rf ../pcre2-10.43.tar.gz
    rm -rf ../pcre2-10.43
}

update(){
    echo "download library"
    echo ""
    go mod tidy
    sudo apt-get install -y libpcap-dev
    sudo apt-get install -y libnetfilter-queue-dev
    sudo apt-get install -y libhyperscan-dev
    sudo apt-get install -y libpcre2-8-0 libpcre2-dev
    sudo apt-get install -y mingw-w64
    sudo apt-get install -y upx
    sudo apt-get install -y zip unzip
    install_builder
    installmdpdf
}



install_builder(){
    if which  builder >/dev/null 2>&1; then
    echo "builderhas installed"
    else
    ORIGINAL_DIR=$(pwd)
    cd ../&& curl --fail -sSL https://releases.installbuilder.com/installbuilder/installbuilder-enterprise-25.3.1-linux-x64-installer.run -o installbuilder.run
    chmod a+x installbuilder.run
    echo "installbuilder.."
    ./installbuilder.run --mode unattended --prefix /opt/installbuilder
    cmd='export PATH=$PATH:/opt/installbuilder/bin'
    eval "$cmd"
    echo $cmd >> ~/.bashrc
    source ~/.bashrc
    cd $ORIGINAL_DIR
    builder --version
    fi
}

installmdpdf(){
    which npm
    if [ $? -eq 0 ]; then
        echo "npm installed"
    else
        sudo apt-get install -y nodejs
        sudo apt-get install -y npm
    fi

    which mdpdf
    if [ $? -eq 0 ]; then
        echo "mdpdf installed"
    else
        sudo npm install -g mdpdf
    fi
}

show_help() {
    echo "Release script"
    echo "This script will create a release folder with all the necessary files for a release"
    echo "Note that for now only linux is supported"
    echo "Usage: ./release.sh"
    echo "Flags:"
    echo "-h: help"
    echo "-c: clean everything created/generated, including the release folder"
    echo "-p: pack folder (linux_amd64,windows_amd64) for windows and linux,note:please have the release folder created before"
    echo "no flags: run release script"
    echo ""

}

# version
version(){
read -p "Enter version (ex: v1.0.0): " VERSION; \
echo "Version: $VERSION"; \
if [ -z "$VERSION" ]; then
    echo "No version, exiting"
    exit 1
fi
export VERSION=$VERSION; \
}

setup(){
    RELEASE_DIR=release
    LINUX_DIR=$RELEASE_DIR/linux_amd64
    WINDOWS_DIR=$RELEASE_DIR/windows_amd64
    mkdir -p $LINUX_DIR
    mkdir -p $WINDOWS_DIR
}

pack(){
    packfile=installer.xml 
    builder build $packfile windows --verbose --setvars project.outputDirectory=./$RELEASE_DIR project.version=$VERSION \
    sourceDir=$WINDOWS_DIR project.shortName=bbnim

    builder build $packfile linux --verbose --setvars project.outputDirectory=./$RELEASE_DIR project.version=$VERSION \
    sourceDir=$LINUX_DIR project.shortName=bbnim 

}


# flags, -h for help, -c for clean everything
while getopts ":hcp" opt; do
  case ${opt} in
    h )
        show_help
        exit 0
        ;;
    c )
        clean_everything
        exit 0
        ;;
    p )
        setup
        version
        install_builder
        pack
        exit 0
        ;;    
    \? )
        show_help
        ;;
  esac
done


# unit tests, print verbose output and output to unit-tests.log
# echo "Running unit tests..."
# go test -v -p 1 2>&1 | tee unit-tests.log

# regression test

# if log contains failed, exit
# if grep -q "FAIL" unit-tests.log; then
#     echo "Unit tests failed"
#     exit 1
# fi
# echo "Unit tests passed"

#
clean_everything
setup
version
update


# lib_release
<NAME_EMAIL>:bbtechhive/mnms_installation.git
cp mnms_installation/linux/install_bbnim_lib_amd64.sh $LINUX_DIR/install_lib.sh
mv mnms_installation/windows/npcap-1.72.exe $WINDOWS_DIR
cp -r mnms_installation/windows/x64/* $WINDOWS_DIR
mv mnms_installation $HOME

# caddy windows
wget https://github.com/caddyserver/caddy/releases/download/v2.6.3/caddy_2.6.3_windows_amd64.zip
mkdir -p /tmp/caddy_windows_amd64
unzip caddy_2.6.3_windows_amd64.zip -d /tmp/caddy_windows_amd64
cp /tmp/caddy_windows_amd64/caddy.exe $WINDOWS_DIR
rm -rf caddy_2.6.3_windows_amd64.zip /tmp/caddy_windows_amd64
# caddy linux
wget https://github.com/caddyserver/caddy/releases/download/v2.6.3/caddy_2.6.3_linux_amd64.tar.gz
mkdir -p /tmp/caddy_linux_amd64
tar -xzf caddy_2.6.3_linux_amd64.tar.gz -C /tmp/caddy_linux_amd64
cp /tmp/caddy_linux_amd64/caddy $LINUX_DIR
rm -rf caddy_2.6.3_linux_amd64.tar.gz /tmp/caddy_linux_amd64

# aiagent
echo "Downloading aiagent.json"
# get from https://nms.blackbeartechhive.com/files/aiagent.json
wget https://nms.blackbeartechhive.com/files/aiagent.json   
mkdir -p $LINUX_DIR/projects
cp aiagent.json $LINUX_DIR/projects
mkdir -p $WINDOWS_DIR/projects
cp aiagent.json $WINDOWS_DIR/projects
echo "Downloading aiagent_rag.json"
wget https://nms.blackbeartechhive.com/files/aiagent_rag.json
mkdir -p $LINUX_DIR/rag
cp aiagent_rag.json $LINUX_DIR/rag
mkdir -p $WINDOWS_DIR/rag
cp aiagent_rag.json $WINDOWS_DIR/rag
echo "Downloading mcpservers.json"
wget https://nms.blackbeartechhive.com/files/mcpservers.json
cp mcpservers.json $LINUX_DIR
cp mcpservers.json $WINDOWS_DIR
rm -rf mcpservers.json
rm -rf aiagent.json
rm -rf aiagent_rag.json


# manual
MANUAL=Blackbear_NIMBL_User_Manual.pdf
<NAME_EMAIL>:bbtechhive/userguide.git
python userguide/merge_markdown.py
sudo mdpdf userguide/user_guide.md
mv userguide/user_guide.pdf release/$MANUAL
rm -rf userguide


# bbnim
VERSION=$VERSION make release

install_builder
pack

echo "Done, you can download the zip file now, ex: scp -P 64422 root@testbed:/root/mnms/release/bbnim.zip ."


# git tag, might fail if tag already exists
read -p "Create git tag and push? (y/n)" -n 1 -r
echo   # (optional) move to a new line
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git tag $VERSION
    git push origin $VERSION
fi
