-- *****************************************************************
-- IP-SOURCE-GUARD-MIB:  
-- ****************************************************************

MGMT-IP-SOURCE-GUARD-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SM<PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    ;

mgmtIpSourceGuardMib MODULE-IDENTITY
    LAST-UPDATED "201412080000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IP source guard MIB"
    REVISION    "201412080000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 64 }


mgmtIpSourceGuardMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMib 1 }

mgmtIpSourceGuardCapabilities OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibObjects 1 }

mgmtIpSourceGuardCapabilitiesStaticIpMask OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If FALSE, the IP mask of static binding table is only allowed to be
         configured as ***************."
    ::= { mgmtIpSourceGuardCapabilities 1 }

mgmtIpSourceGuardCapabilitiesStaticMacAddress OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If TRUE, the MAC address of static binding table is configurable."
    ::= { mgmtIpSourceGuardCapabilities 2 }

mgmtIpSourceGuardConfig OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibObjects 2 }

mgmtIpSourceGuardConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardConfig 1 }

mgmtIpSourceGuardConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of IP source guard. TRUE is to enable IP source guard and
         FALSE is to disable it."
    ::= { mgmtIpSourceGuardConfigGlobals 1 }

mgmtIpSourceGuardConfigInterface OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardConfig 2 }

mgmtIpSourceGuardConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpSourceGuardConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of IP source guard port configuration parameters."
    ::= { mgmtIpSourceGuardConfigInterface 1 }

mgmtIpSourceGuardConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTIpSourceGuardConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters."
    INDEX       { mgmtIpSourceGuardConfigInterfaceIfIndex }
    ::= { mgmtIpSourceGuardConfigInterfaceTable 1 }

MGMTIpSourceGuardConfigInterfaceEntry ::= SEQUENCE {
    mgmtIpSourceGuardConfigInterfaceIfIndex            MGMTInterfaceIndex,
    mgmtIpSourceGuardConfigInterfaceMode               TruthValue,
    mgmtIpSourceGuardConfigInterfaceDynamicEntryCount  Unsigned32
}

mgmtIpSourceGuardConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpSourceGuardConfigInterfaceEntry 1 }

mgmtIpSourceGuardConfigInterfaceMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port mode of IP source guard. TURE is to enable IP source guard on the
         port and FALSE is to disable it on the port."
    ::= { mgmtIpSourceGuardConfigInterfaceEntry 2 }

mgmtIpSourceGuardConfigInterfaceDynamicEntryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The dynamic entry count is the max number of dynamic entries allowed on
         the port."
    ::= { mgmtIpSourceGuardConfigInterfaceEntry 3 }

mgmtIpSourceGuardConfigStatic OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardConfig 3 }

mgmtIpSourceGuardConfigStaticTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpSourceGuardConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the static binding table of IP source
         guard."
    ::= { mgmtIpSourceGuardConfigStatic 1 }

mgmtIpSourceGuardConfigStaticEntry OBJECT-TYPE
    SYNTAX      MGMTIpSourceGuardConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpSourceGuardConfigStaticIfIndex,
                  mgmtIpSourceGuardConfigStaticVlanId,
                  mgmtIpSourceGuardConfigStaticIpAddress,
                  mgmtIpSourceGuardConfigStaticIpMask }
    ::= { mgmtIpSourceGuardConfigStaticTable 1 }

MGMTIpSourceGuardConfigStaticEntry ::= SEQUENCE {
    mgmtIpSourceGuardConfigStaticIfIndex     MGMTInterfaceIndex,
    mgmtIpSourceGuardConfigStaticVlanId      MGMTVlan,
    mgmtIpSourceGuardConfigStaticIpAddress   IpAddress,
    mgmtIpSourceGuardConfigStaticIpMask      IpAddress,
    mgmtIpSourceGuardConfigStaticMacAddress  MacAddress,
    mgmtIpSourceGuardConfigStaticAction      MGMTRowEditorState
}

mgmtIpSourceGuardConfigStaticIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpSourceGuardConfigStaticEntry 1 }

mgmtIpSourceGuardConfigStaticVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN ID."
    ::= { mgmtIpSourceGuardConfigStaticEntry 2 }

mgmtIpSourceGuardConfigStaticIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IP address."
    ::= { mgmtIpSourceGuardConfigStaticEntry 3 }

mgmtIpSourceGuardConfigStaticIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned network mask."
    ::= { mgmtIpSourceGuardConfigStaticEntry 4 }

mgmtIpSourceGuardConfigStaticMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned MAC Address.
         
         This object is only available if the capability object
         'mgmtIpSourceGuardCapabilitiesStaticMacAddress' is True."
    ::= { mgmtIpSourceGuardConfigStaticEntry 5 }

mgmtIpSourceGuardConfigStaticAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpSourceGuardConfigStaticEntry 100 }

mgmtIpSourceGuardConfigStaticTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardConfigStatic 2 }

mgmtIpSourceGuardConfigStaticTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 1 }

mgmtIpSourceGuardConfigStaticTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID."
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 2 }

mgmtIpSourceGuardConfigStaticTableRowEditorIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned IP address."
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 3 }

mgmtIpSourceGuardConfigStaticTableRowEditorIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned network mask."
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 4 }

mgmtIpSourceGuardConfigStaticTableRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned MAC Address.
         
         This object is only available if the capability object
         'mgmtIpSourceGuardCapabilitiesStaticMacAddress' is True."
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 5 }

mgmtIpSourceGuardConfigStaticTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpSourceGuardConfigStaticTableRowEditor 100 }

mgmtIpSourceGuardStatus OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibObjects 3 }

mgmtIpSourceGuardStatusDynamicTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpSourceGuardStatusDynamicEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table provided dynamic binding table of IP source guard."
    ::= { mgmtIpSourceGuardStatus 1 }

mgmtIpSourceGuardStatusDynamicEntry OBJECT-TYPE
    SYNTAX      MGMTIpSourceGuardStatusDynamicEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpSourceGuardStatusDynamicIfIndex,
                  mgmtIpSourceGuardStatusDynamicVlanId,
                  mgmtIpSourceGuardStatusDynamicIpAddress }
    ::= { mgmtIpSourceGuardStatusDynamicTable 1 }

MGMTIpSourceGuardStatusDynamicEntry ::= SEQUENCE {
    mgmtIpSourceGuardStatusDynamicIfIndex     MGMTInterfaceIndex,
    mgmtIpSourceGuardStatusDynamicVlanId      MGMTVlan,
    mgmtIpSourceGuardStatusDynamicIpAddress   IpAddress,
    mgmtIpSourceGuardStatusDynamicMacAddress  MacAddress
}

mgmtIpSourceGuardStatusDynamicIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpSourceGuardStatusDynamicEntry 1 }

mgmtIpSourceGuardStatusDynamicVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN ID."
    ::= { mgmtIpSourceGuardStatusDynamicEntry 2 }

mgmtIpSourceGuardStatusDynamicIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Learned IP address."
    ::= { mgmtIpSourceGuardStatusDynamicEntry 3 }

mgmtIpSourceGuardStatusDynamicMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Learned MAC Address."
    ::= { mgmtIpSourceGuardStatusDynamicEntry 4 }

mgmtIpSourceGuardControl OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibObjects 4 }

mgmtIpSourceGuardControlTranslate OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardControl 1 }

mgmtIpSourceGuardControlTranslateTranslateDynamicToStatic OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Translate all the current dynamic entries to static ones. Set it as
         TRUE to do the action."
    ::= { mgmtIpSourceGuardControlTranslate 1 }

mgmtIpSourceGuardMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMib 2 }

mgmtIpSourceGuardMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibConformance 1 }

mgmtIpSourceGuardMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpSourceGuardMibConformance 2 }

mgmtIpSourceGuardCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpSourceGuardCapabilitiesStaticIpMask,
                  mgmtIpSourceGuardCapabilitiesStaticMacAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 1 }

mgmtIpSourceGuardConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpSourceGuardConfigGlobalsMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 2 }

mgmtIpSourceGuardConfigInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpSourceGuardConfigInterfaceIfIndex,
                  mgmtIpSourceGuardConfigInterfaceMode,
                  mgmtIpSourceGuardConfigInterfaceDynamicEntryCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 3 }

mgmtIpSourceGuardConfigStaticTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpSourceGuardConfigStaticIfIndex,
                  mgmtIpSourceGuardConfigStaticVlanId,
                  mgmtIpSourceGuardConfigStaticIpAddress,
                  mgmtIpSourceGuardConfigStaticIpMask,
                  mgmtIpSourceGuardConfigStaticMacAddress,
                  mgmtIpSourceGuardConfigStaticAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 4 }

mgmtIpSourceGuardConfigStaticTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpSourceGuardConfigStaticTableRowEditorIfIndex,
                  mgmtIpSourceGuardConfigStaticTableRowEditorVlanId,
                  mgmtIpSourceGuardConfigStaticTableRowEditorIpAddress,
                  mgmtIpSourceGuardConfigStaticTableRowEditorIpMask,
                  mgmtIpSourceGuardConfigStaticTableRowEditorMacAddress,
                  mgmtIpSourceGuardConfigStaticTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 5 }

mgmtIpSourceGuardStatusDynamicInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpSourceGuardStatusDynamicIfIndex,
                  mgmtIpSourceGuardStatusDynamicVlanId,
                  mgmtIpSourceGuardStatusDynamicIpAddress,
                  mgmtIpSourceGuardStatusDynamicMacAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 6 }

mgmtIpSourceGuardControlTranslateInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpSourceGuardControlTranslateTranslateDynamicToStatic }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpSourceGuardMibGroups 7 }

mgmtIpSourceGuardMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpSourceGuardCapabilitiesInfoGroup,
                       mgmtIpSourceGuardConfigGlobalsInfoGroup,
                       mgmtIpSourceGuardConfigInterfaceInfoGroup,
                       mgmtIpSourceGuardConfigStaticTableInfoGroup,
                       mgmtIpSourceGuardConfigStaticTableRowEditorInfoGroup,
                       mgmtIpSourceGuardStatusDynamicInfoGroup,
                       mgmtIpSourceGuardControlTranslateInfoGroup }

    ::= { mgmtIpSourceGuardMibCompliances 1 }

END
