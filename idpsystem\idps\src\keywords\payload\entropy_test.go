package payload

import (
	"fmt"
	"math"
	"testing"

	"github.com/google/gonids"
)

func TestEntropyCalculation(t *testing.T) {
	tests := []struct {
		name     string
		data     []byte
		expected float64
		epsilon  float64
		ranges   bool
	}{
		{"all same byte", []byte("aaaaaaa"), 0.0, 1e-6, false},
		{"equal distribution 4 vals", []byte("abcdabcd"), 2.0, 1e-6, false},
		{"empty data", []byte{}, 0.0, 1e-6, false},
		{"mixed data", []byte("aaabbcc"), 0.0, 8.0, true}, // (3/7)*log2(3/7) + (2/7)*log2(2/7) + (2/7)*log2(2/7) negated
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entropy := calculateEntropy(tt.data)
			if tt.ranges {
				if !(entropy > tt.expected && entropy <= tt.epsilon) {
					t.<PERSON>("calculateEntropy() for %q:Entropy should be between %v and %v", tt.name, tt.expected, tt.epsilon)
				}
			} else {
				if !(math.Abs(entropy-tt.expected) < tt.epsilon) {
					t.Errorf("calculateEntropy() for %q: Entropy should be %v", tt.name, tt.expected)
				}
			}
		})
	}
}

func TestSCDetectEntropyMatch(t *testing.T) {
	type testCase struct {
		name     string
		data     []byte
		ctx      entropyData
		expected bool
	}

	testCases := []testCase{
		{
			name: "Exact match",
			data: []byte("abcdabcd"), // entropy 2.0
			ctx: entropyData{
				Value: detectFloatData{arg1: 2.0, mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "Greater than match",
			data: []byte("abcdabcd"), // entropy 2.0
			ctx: entropyData{
				Value: detectFloatData{arg1: 1.9, mode: detectFloatModeGt},
			},
			expected: true,
		},
		{
			name: "Greater than fail",
			data: []byte("abcdabcd"), // entropy 2.0
			ctx: entropyData{
				Value: detectFloatData{arg1: 2.0, mode: detectFloatModeGt},
			},
			expected: false,
		},
		{
			name: "Less than match",
			data: []byte("aaaa"), // entropy 0.0
			ctx: entropyData{
				Value: detectFloatData{arg1: 0.1, mode: detectFloatModeLt},
			},
			expected: true,
		},
		{
			name: "With offset",
			data: []byte("xxxabcdabcd"), // entropy of "abcdabcd" is 2.0
			ctx: entropyData{
				Offset: 3,
				Value:  detectFloatData{arg1: 2.0, mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "With nbytes",
			data: []byte("abcdxxxx"), // entropy of "abcd" is 2.0
			ctx: entropyData{
				Nbytes: 4,
				Value:  detectFloatData{arg1: 2.0, mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "With offset and nbytes",
			data: []byte("xxabcdyy"), // entropy of "abcd" is 2.0
			ctx: entropyData{
				Offset: 2,
				Nbytes: 4,
				Value:  detectFloatData{arg1: 2.0, mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "Offset too large",
			data: []byte("abc"),
			ctx: entropyData{
				Offset: 5,
				Value:  detectFloatData{arg1: 0.0, mode: detectFloatModeEqual},
			},
			expected: false,
		},
		{
			name: "Nbytes too large",
			data: []byte("abc"),
			ctx: entropyData{
				Nbytes: 5, // Nbytes is relative to data[startIndex:]
				Value:  detectFloatData{arg1: 0.0, mode: detectFloatModeEqual},
			},
			expected: false,
		},
		{
			name: "Nbytes makes empty slice after offset",
			data: []byte("abcdef"),
			ctx: entropyData{
				Offset: 3, // "def"
				Nbytes: 0, // Uses remaining length from offset
				Value:  detectFloatData{arg1: calculateEntropy([]byte("def")), mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "Nbytes positive but makes slice smaller",
			data: []byte("abcdef"),
			ctx: entropyData{
				Offset: 1, // "bcdef"
				Nbytes: 2, // "bc"
				Value:  detectFloatData{arg1: calculateEntropy([]byte("bc")), mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "Empty data, expect 0 entropy",
			data: []byte{},
			ctx: entropyData{
				Value: detectFloatData{arg1: 0.0, mode: detectFloatModeEqual},
			},
			expected: true,
		},
		{
			name: "Nil data",
			data: nil,
			ctx: entropyData{
				Value: detectFloatData{arg1: 0.0, mode: detectFloatModeEqual},
			},
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if got := detectEntropyMatch(tc.data, tc.ctx); got != tc.expected {
				t.Errorf("SCDetectEntropyMatch() = %v, want %v", got, tc.expected)
			}
		})
	}
}

func TestEntropy(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{

		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";entropy:2.0;sid:1;)`,
			expected: true,
			packet:   []byte("abcdabcd"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";entropy:>1.9;sid:1;)`,
			expected: true,
			packet:   []byte("abcdabcd"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";entropy:>2.0;sid:1;)`,
			expected: false,
			packet:   []byte("abcdabcd"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"aa";entropy:<0.1;sid:1;)`,
			expected: true,
			packet:   []byte("aaaa"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"bc";entropy:offset 3,value = 2.0;sid:1;)`,
			expected: true,
			packet:   []byte("xxxabcdabcd"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"bc";entropy:bytes 4,value = 2.0;sid:1;)`,
			expected: true,
			packet:   []byte("abcdxxxx"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"bc";entropy:bytes 4,offset 2,value = 2.0;sid:1;)`,
			expected: true,
			packet:   []byte("xxabcdyy"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"bc";entropy:offset 5,value = 0.0;sid:1;)`,
			expected: false,
			packet:   []byte("abc"),
		},
		{
			content:  fmt.Sprintf(`alert icmp any any -> any any (msg:"test";content:"bc";entropy:bytes 0,offset 3,value = %v;sid:1;)`, calculateEntropy([]byte("def"))),
			expected: true,
			packet:   []byte("abcdef"),
		},
		{
			content:  fmt.Sprintf(`alert icmp any any -> any any (msg:"test";content:"bc";entropy:bytes 2,offset 1,value = %v;sid:1;)`, calculateEntropy([]byte("bc"))),
			expected: true,
			packet:   []byte("abcdef"),
		},
		{
			content:  fmt.Sprintf(`alert icmp any any -> any any (msg:"test";entropy:%v;sid:1;)`, 0),
			expected: true,
			packet:   []byte(""),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestEntropy:%v", index+1), func(t *testing.T) {

			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
