import { ProTable } from "@ant-design/pro-components";
import React from "react";
import dayjs from "dayjs";
import { v4 } from "uuid";

const columns = [
  {
    title: "Rule_name",
    dataIndex: "rulename",
    key: "rulename",
  },
  {
    title: "Id",
    dataIndex: "id",
    key: "id",
  },
  {
    title: "Interface",
    dataIndex: "inInterface",
    key: "inInterface",
  },
  {
    title: "Type",
    dataIndex: "type",
    key: "type",
  },
  {
    title: "Protocol",
    dataIndex: "protocol",
    key: "protocol",
  },
  {
    title: "Source",
    children: [
      {
        title: "IPAddress",
        dataIndex: "srcip",
        key: "srcip",
      },
      {
        title: "Port",
        dataIndex: "srcPort",
        key: "srcPort",
      },
    ],
  },
  {
    title: "Destination",
    children: [
      {
        title: "IPAddress",
        dataIndex: "destip",
        key: "destip",
      },
      {
        title: "Port",
        dataIndex: "destPort",
        key: "destPort",
      },
    ],
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
  },
  {
    title: "Timestamp",
    dataIndex: "timestamp",
    key: "timestamp",
    render: (data) => {
      return dayjs(data).format("YYYY/MM/DD HH:mm:ss");
    },
  },
];

const IdpsEventTable = ({ data, id }) => {
  return (
    <ProTable
      columns={columns}
      cardProps={{ bodyStyle: { paddingInline: 5, paddingBlock: 0 } }}
      bordered
      rowKey={() => v4()}
      size="small"
      dataSource={data?.event || []}
      pagination={{
        position: ["bottomCenter"],
        showQuickJumper: true,
        size: "default",
        total: data?.event?.length,
        defaultPageSize: 10,
        pageSizeOptions: [10, 15, 20, 25],
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} items`,
      }}
      options={{
        search: false,
        fullScreen: false,
        setting: false,
        reload: false,
        density: false,
      }}
      search={false}
      scroll={{
        x: 1100,
      }}
      dateFormatter="string"
    />
  );
};

export default IdpsEventTable;
