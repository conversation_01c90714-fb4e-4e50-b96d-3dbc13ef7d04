package ip

import (
	_ "embed"
	"fmt"
	"mnms/idpsystem/idps/src/protocol"
	"net"
	"runtime"
	"strings"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/oschwald/geoip2-golang"
)

//go:embed GeoLite2-Country.mmdb
var b []byte

type geoip struct {
	db      *geoip2.Reader
	ipLayer protocol.IPLayer
	geomap  map[string]struct{}
	verify  func(net.IP, net.IP, *geoip2.Reader, map[string]struct{}) bool
}
type geoipflag uint

const (
	both geoipflag = iota
	anyip
	src
	dst
)

var geomap = map[string]geoipflag{
	"both": both,
	"any":  anyip,
	"src":  src,
	"dst":  dst,
}

func NewGeoip() *geoip {
	g := &geoip{ipLayer: protocol.NewIpLayer()}
	return g
}

func (g *geoip) Close() error {
	return g.db.Close()
}

func (g *geoip) SetUp(s string) error {
	arr := strings.Split(s, ",")
	if len(arr) < 2 {
		return fmt.Errorf("format error:%v", s)
	}

	geo := arr[0]
	if n, ok := geomap[geo]; ok {
		f, err := createFunc(n)
		if err != nil {
			return err
		}
		g.verify = f
	} else {
		return fmt.Errorf("unknown geoip: %v", geo)
	}
	arr = arr[1:]
	m := make(map[string]struct{})
	for _, v := range arr {
		m[v] = struct{}{}
		if v == "UK" {
			m["GB"] = struct{}{}
		}
	}
	g.geomap = m
	db, err := geoip2.FromBytes(b)
	if err != nil {
		return err
	}
	g.db = db
	runtime.SetFinalizer(g, func(g *geoip) {
		_ = g.Close()
	})
	return nil
}

func (g *geoip) Match(packet gopacket.Packet) bool {
	ver, net, err := g.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		if g.verify != nil && g.db != nil {
			return g.verify(v4.SrcIP, v4.DstIP, g.db, g.geomap)
		}
	case protocol.IPV6:
		v6 := net.(*layers.IPv6)
		if g.verify != nil && g.db != nil {
			return g.verify(v6.SrcIP, v6.DstIP, g.db, g.geomap)
		}
	}
	return false
}

func createFunc(g geoipflag) (func(net.IP, net.IP, *geoip2.Reader, map[string]struct{}) bool, error) {
	switch g {
	case both:
		return func(src, dst net.IP, db *geoip2.Reader, geomap map[string]struct{}) bool {
			if !verify(src, db, geomap) {
				return false
			}
			if !verify(dst, db, geomap) {
				return false
			}
			return true
		}, nil
	case anyip:
		return func(src, dst net.IP, db *geoip2.Reader, geomap map[string]struct{}) bool {
			if verify(src, db, geomap) {
				return true
			}
			if verify(dst, db, geomap) {
				return true
			}
			return false
		}, nil
	case src:
		return func(src, dst net.IP, db *geoip2.Reader, geomap map[string]struct{}) bool {
			return verify(src, db, geomap)
		}, nil
	case dst:
		return func(src, dst net.IP, db *geoip2.Reader, geomap map[string]struct{}) bool {

			return verify(dst, db, geomap)
		}, nil
	}
	return nil, fmt.Errorf("unknown geoip: %v", g)
}

func verify(ip net.IP, db *geoip2.Reader, geomap map[string]struct{}) bool {
	record, err := db.Country(ip)
	if err != nil {
		return false
	}
	if _, ok := geomap[record.Country.IsoCode]; ok {
		return true
	}
	return false
}
