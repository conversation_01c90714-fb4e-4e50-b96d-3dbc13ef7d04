-- *****************************************************************
-- EEE-MIB:  
-- ****************************************************************

MGMT-EEE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtEeeMib MODULE-IDENTITY
    LAST-UPDATED "201407240000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Energy Efficient Ethernet(EEE). "
    REVISION    "201407240000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 74 }


MGMTEeePreference ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the types of optimization preferences, either
         maximum power savings or low traffic latency."
    SYNTAX      INTEGER { latency(0), power(1) }

MGMTEeeQueueType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the types of egress port queues."
    SYNTAX      INTEGER { normal(0), urgent(1) }

MGMTEeeStatusType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the feature status."
    SYNTAX      INTEGER { no(0), yes(1), notSupported(2) }

mgmtEeeMibObjects OBJECT IDENTIFIER
    ::= { mgmtEeeMib 1 }

mgmtEeeCapabilities OBJECT IDENTIFIER
    ::= { mgmtEeeMibObjects 1 }

mgmtEeeCapabilitiesGlobals OBJECT IDENTIFIER
    ::= { mgmtEeeCapabilities 1 }

mgmtEeeCapabilitiesGlobalsOptimizationPreferences OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether device supports optimization preferences, true means
         supported. false means not supported."
    ::= { mgmtEeeCapabilitiesGlobals 1 }

mgmtEeeCapabilitiesInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTEeeCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to interface capabilities"
    ::= { mgmtEeeCapabilities 2 }

mgmtEeeCapabilitiesInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTEeeCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of capability parameters"
    INDEX       { mgmtEeeCapabilitiesInterfaceIfIndex }
    ::= { mgmtEeeCapabilitiesInterfaceTable 1 }

MGMTEeeCapabilitiesInterfaceEntry ::= SEQUENCE {
    mgmtEeeCapabilitiesInterfaceIfIndex          MGMTInterfaceIndex,
    mgmtEeeCapabilitiesInterfaceMaxEgressQueues  MGMTUnsigned8,
    mgmtEeeCapabilitiesInterfaceEEE              TruthValue
}

mgmtEeeCapabilitiesInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtEeeCapabilitiesInterfaceEntry 1 }

mgmtEeeCapabilitiesInterfaceMaxEgressQueues OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of supported egress port queues."
    ::= { mgmtEeeCapabilitiesInterfaceEntry 2 }

mgmtEeeCapabilitiesInterfaceEEE OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether interface supports EEE(IEEE 802.3az). true means EEE
         supported. false means not supported."
    ::= { mgmtEeeCapabilitiesInterfaceEntry 3 }

mgmtEeeConfig OBJECT IDENTIFIER
    ::= { mgmtEeeMibObjects 2 }

mgmtEeeConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtEeeConfig 1 }

mgmtEeeConfigGlobalsOptimizationPreferences OBJECT-TYPE
    SYNTAX      MGMTEeePreference
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "EEE optimization preferences, either maximum power saving or low
         traffic latency."
    ::= { mgmtEeeConfigGlobals 1 }

mgmtEeeConfigInterface OBJECT IDENTIFIER
    ::= { mgmtEeeConfig 2 }

mgmtEeeConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTEeeConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure EEE configurations for a specific
         interface."
    ::= { mgmtEeeConfigInterface 1 }

mgmtEeeConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTEeeConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of EEE configurable parameters"
    INDEX       { mgmtEeeConfigInterfaceParamIfIndex }
    ::= { mgmtEeeConfigInterfaceParamTable 1 }

MGMTEeeConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtEeeConfigInterfaceParamIfIndex    MGMTInterfaceIndex,
    mgmtEeeConfigInterfaceParamEnableEEE  TruthValue
}

mgmtEeeConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtEeeConfigInterfaceParamEntry 1 }

mgmtEeeConfigInterfaceParamEnableEEE OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable EEE (IEEE 802.3az) feature at a interface. true is to advertize
         EEE(IEEE 802.3az) capabilities to partner device. false is to disable
         it."
    ::= { mgmtEeeConfigInterfaceParamEntry 2 }

mgmtEeeConfigInterfaceQueueTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTEeeConfigInterfaceQueueEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure egress port queue type, whether urgent
         queue or normal queue. We can configure more than one egress queues as
         urgent queues. Queues configured as urgent, en-queued data will be
         transmitted with minimum latency. Queue configured as normal, en-queued
         data will be transmitted with latency depending upon traffic
         utilization."
    ::= { mgmtEeeConfigInterface 2 }

mgmtEeeConfigInterfaceQueueEntry OBJECT-TYPE
    SYNTAX      MGMTEeeConfigInterfaceQueueEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has set of egress queues"
    INDEX       { mgmtEeeConfigInterfaceQueueIfIndex,
                  mgmtEeeConfigInterfaceQueueIndex }
    ::= { mgmtEeeConfigInterfaceQueueTable 1 }

MGMTEeeConfigInterfaceQueueEntry ::= SEQUENCE {
    mgmtEeeConfigInterfaceQueueIfIndex          MGMTInterfaceIndex,
    mgmtEeeConfigInterfaceQueueIndex            Integer32,
    mgmtEeeConfigInterfaceQueueEgressQueueType  MGMTEeeQueueType
}

mgmtEeeConfigInterfaceQueueIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtEeeConfigInterfaceQueueEntry 1 }

mgmtEeeConfigInterfaceQueueIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Egress port queue index."
    ::= { mgmtEeeConfigInterfaceQueueEntry 2 }

mgmtEeeConfigInterfaceQueueEgressQueueType OBJECT-TYPE
    SYNTAX      MGMTEeeQueueType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Egress port queue is urgent queue or normal queue."
    ::= { mgmtEeeConfigInterfaceQueueEntry 3 }

mgmtEeeStatus OBJECT IDENTIFIER
    ::= { mgmtEeeMibObjects 3 }

mgmtEeeStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTEeeStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to Energy Efficient Ethernet interface status"
    ::= { mgmtEeeStatus 1 }

mgmtEeeStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTEeeStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of status parameters"
    INDEX       { mgmtEeeStatusInterfaceIfIndex }
    ::= { mgmtEeeStatusInterfaceTable 1 }

MGMTEeeStatusInterfaceEntry ::= SEQUENCE {
    mgmtEeeStatusInterfaceIfIndex      MGMTInterfaceIndex,
    mgmtEeeStatusInterfacePartnerEEE   MGMTEeeStatusType,
    mgmtEeeStatusInterfaceRxPowerSave  MGMTEeeStatusType
}

mgmtEeeStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtEeeStatusInterfaceEntry 1 }

mgmtEeeStatusInterfacePartnerEEE OBJECT-TYPE
    SYNTAX      MGMTEeeStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether link partner advertising EEE(IEEE 802.3az)
         capabilities."
    ::= { mgmtEeeStatusInterfaceEntry 2 }

mgmtEeeStatusInterfaceRxPowerSave OBJECT-TYPE
    SYNTAX      MGMTEeeStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether interfcae rx path currently in power save state."
    ::= { mgmtEeeStatusInterfaceEntry 3 }

mgmtEeeMibConformance OBJECT IDENTIFIER
    ::= { mgmtEeeMib 2 }

mgmtEeeMibCompliances OBJECT IDENTIFIER
    ::= { mgmtEeeMibConformance 1 }

mgmtEeeMibGroups OBJECT IDENTIFIER
    ::= { mgmtEeeMibConformance 2 }

mgmtEeeCapabilitiesGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeCapabilitiesGlobalsOptimizationPreferences }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 1 }

mgmtEeeCapabilitiesInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeCapabilitiesInterfaceIfIndex,
                  mgmtEeeCapabilitiesInterfaceMaxEgressQueues,
                  mgmtEeeCapabilitiesInterfaceEEE }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 2 }

mgmtEeeConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeConfigGlobalsOptimizationPreferences }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 3 }

mgmtEeeConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeConfigInterfaceParamIfIndex,
                  mgmtEeeConfigInterfaceParamEnableEEE }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 4 }

mgmtEeeConfigInterfaceQueueTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeConfigInterfaceQueueIfIndex,
                  mgmtEeeConfigInterfaceQueueIndex,
                  mgmtEeeConfigInterfaceQueueEgressQueueType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 5 }

mgmtEeeStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtEeeStatusInterfaceIfIndex,
                  mgmtEeeStatusInterfacePartnerEEE,
                  mgmtEeeStatusInterfaceRxPowerSave }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtEeeMibGroups 6 }

mgmtEeeMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtEeeCapabilitiesGlobalsInfoGroup,
                       mgmtEeeCapabilitiesInterfaceInfoGroup,
                       mgmtEeeConfigGlobalsInfoGroup,
                       mgmtEeeConfigInterfaceParamTableInfoGroup,
                       mgmtEeeConfigInterfaceQueueTableInfoGroup,
                       mgmtEeeStatusInterfaceTableInfoGroup }

    ::= { mgmtEeeMibCompliances 1 }

END
