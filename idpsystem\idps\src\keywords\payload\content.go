package payload

import (
	"mnms/idpsystem/idps/spm"

	"github.com/google/gonids"
)

const contentValueMax = 1024 * 1024

type contentFlag uint

const (
	noCase contentFlag = 1 << iota
	distance
	within
	offset
	depth
	contentNegated
	startsWith
	endsWith
	isdataAt
	offsetVar
	depthVar
	distanceVar
	withinVar
	withinNext
	distanceNext
	fastpPattern
	fastpPatternOnly
	fastpPatternChop
	replace
)
const relativeNext contentFlag = withinNext | distanceNext

func newContent(v *gonids.Content) *content {
	c := &content{
		content:    v.Pattern,
		contentLen: uint16(len(v.Pattern)),
	}
	return c
}

type content struct {
	content    []byte
	contentLen uint16
	nocase     bool
	offset     uint16
	flags      contentFlag
	depth      uint16
	distance   int32
	within     int32
	spm        spm.Algorithm
	replace    *string
	replaceLen uint16
}

func detectContentID(d *Detect, p *Packet) matchFlag {
	ct, _ := d.data.(*content)
	prev_offset := uint32(0)
	prev_buffer_offset := p.bufferOffset
	for {
		depth := p.bufferLen
		offset := uint32(0)

		if ct.flags&distance == distance || ct.flags&within == within {
			offset = prev_buffer_offset
			dist := int(ct.distance)
			if ct.flags&distance == distance {
				if ct.flags&distanceVar == distanceVar {
					dist = int(p.byteValues[ct.distance])
				}
				if dist < 0 && uint32(abs(dist)) > offset {
					offset = 0
				} else {
					offset += uint32(dist)
				}
			}
			if ct.flags&within == within {
				if ct.flags&withinVar > 0 {
					if depth > uint32(int32(prev_buffer_offset)+int32(p.byteValues[ct.within])+int32(dist)) {
						depth = uint32(int32(prev_buffer_offset) + int32(p.byteValues[ct.within]) + int32(dist))
					}

				} else {
					if depth > uint32(int32(prev_buffer_offset)+ct.within+int32(dist)) {
						depth = uint32(int32(prev_buffer_offset) + ct.within + int32(dist))
					}

				}

			}
			if ct.flags&depthVar > 0 {
				if uint32(p.byteValues[ct.depth])+prev_offset < depth {
					depth = prev_buffer_offset + uint32(p.byteValues[ct.depth])
				}
			} else {
				if ct.depth != 0 {
					if uint32(ct.depth)+prev_buffer_offset < depth {
						depth = prev_buffer_offset + uint32(ct.depth)
					}
				}
			}
			if uint32(ct.offset) > offset {
				offset = uint32(ct.offset)
			}

		} else {
			if ct.flags&depthVar > 0 {
				depth = uint32(p.byteValues[ct.depth])
			} else {
				if ct.depth != 0 {
					depth = uint32(ct.depth)
				}
			}
			if ct.flags&offsetVar > 0 {
				offset = uint32(p.byteValues[ct.offset])
			} else {
				offset = uint32(ct.offset)
			}
			prev_buffer_offset = 0
		}

		if (ct.flags & (distanceVar | offsetVar | depthVar)) > 0 {
			depth += offset
		}

		if prev_offset != 0 {
			offset = prev_offset
		}
		if depth > p.bufferLen {
			depth = p.bufferLen
		}

		if offset >= depth || depth == 0 {
			if ct.flags&contentNegated == contentNegated {
				return matched(d, p)
			} else {
				return noMatch
			}
		}

		sbuffer := p.buffer[offset:]
		sbuffer_len := depth - offset
		var found *spm.ScanResult
		if ct.flags&endsWith == endsWith && depth < p.bufferLen {
			found = nil
		} else if uint32(ct.contentLen) > sbuffer_len {
			found = nil
		} else {
			res := ct.spm.Scan(sbuffer[:sbuffer_len])
			if res.To != 0 {
				found = &res
			}
		}
		if found == nil {
			if !(ct.flags&contentNegated == contentNegated) {
				if ct.flags&(distance|within) == 0 {
					return noMatchDiscontinue
				}
				return noMatch
			} else {
				return matched(d, p)
			}
		}

		match_offset := uint32(found.To) + offset

		if ct.flags&contentNegated == contentNegated {
			if ct.flags&endsWith == endsWith && sbuffer_len != match_offset {
				return matched(d, p)
			}
			return noMatch
		}
		p.bufferOffset = match_offset

		if ct.flags&endsWith == 0 || match_offset == p.bufferLen {
			if ct.flags&replace > 0 {
				p.replace = replaceAddToList(p.replace, found.Payload[found.To-ct.contentLen:], ct)
			}

			if d.next == nil {
				return match
			}
			r := detectEngineContentInspectionInternal(d.next, p)
			if r == match {
				return match
			} else if r == noMatchDiscontinue {
				return noMatchDiscontinue
			}
		}
		prev_offset = match_offset - uint32(ct.contentLen) + 1
	}
}
