package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestOffset(t *testing.T) {
	var tests = []struct {
		title    string
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;sid:1;)`,
			expected: true,
			packet:   []byte("1456abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:5;sid:1;)`,
			expected: true,
			packet:   []byte("112785abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:5;sid:1;)`,
			expected: false,
			packet:   []byte("1127abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"Host: one.example.org"; offset:20; depth:41; sid:1;)`,
			expected: true,
			packet: []byte("GET /one/ HTTP/1.1\r\n" +
				"Host: one.example.org\r\n" +
				"\r\n\r\n" +
				"GET /two/ HTTP/1.1\r\n" +
				"Host: two.example.org\r\n" +
				"\r\n\r\n"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
