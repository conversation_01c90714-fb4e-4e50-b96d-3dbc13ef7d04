import {
  Confi<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  InputNumber,
  Button,
  Form,
  Popover,
  Flex,
  DatePicker,
  notification,
} from "antd";
import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { ProTable } from "@ant-design/pro-components";
import {
  eventLogSelector,
  RequestEventlog,
} from "../../features/eventLog/eventLogSlice";
import ExportData from "../../components/exportData/ExportData";
import { useTheme } from "antd-style";
import { v4 as uuidv4 } from "uuid";
import { FilterOutlined } from "@ant-design/icons";
const { RangePicker } = DatePicker;

const LogPage = () => {
  const token = useTheme();
  const { eventLogData, fetching } = useSelector(eventLogSelector);
  const [inputSearch, setInputSearch] = useState("");
  const columns = [
    {
      title: "Timestamp",
      dataIndex: "Timestamp",
      key: "Timestamp",
      width: 250,
      render: (data) => {
        // RFC3339 format dayjs will judge wrong
        const tmp = data.replace("Z", "");
        return dayjs(tmp).format("YYYY/MM/DD HH:mm:ss");
      },
      sorter: (a, b) => (a.Timestamp > b.Timestamp ? 1 : -1),
    },
    {
      title: "Hostname",
      dataIndex: "Hostname",
      key: "Hostname",
      width: 150,
      sorter: (a, b) => (a.Hostname > b.Hostname ? 1 : -1),
    },
    {
      title: "Facility",
      width: 100,
      dataIndex: "Facility",
      key: "Facility",
      sorter: (a, b) => (a.Hostname > b.Hostname ? 1 : -1),
    },
    {
      title: "Severity",
      dataIndex: "Severity",
      key: "Severity",
      width: 100,
      sorter: (a, b) => (a.Severity > b.Severity ? 1 : -1),
    },
    {
      title: "Priority",
      dataIndex: "Priority",
      key: "Priority",
      width: 100,
      sorter: (a, b) => (a.Priority > b.Priority ? 1 : -1),
    },
    {
      title: "Appname",
      dataIndex: "Appname",
      key: "Appname",
      width: 100,
      sorter: (a, b) => (a.Appname > b.Appname ? 1 : -1),
    },
    {
      title: "Message",
      dataIndex: "Message",
      key: "Message",
      width: 450,
      render: (data) => {
        return (
          <Typography.Paragraph
            ellipsis={{
              rows: 5,
            }}
            style={{ overflow: "auto", maxHeight: "250px" }}
          >
            {data}
          </Typography.Paragraph>
        );
      },
    },
  ];

  const handleRefreshClick = () => {
    dispatch(
      RequestEventlog({
        number: 100,
      })
    );
  };
  const recordAfterfiltering = (dataSource) => {
    return dataSource.filter((row) => {
      let rec = columns.map((element) => {
        return row[element.dataIndex]
          ?.toString()
          ?.toLowerCase()
          ?.includes(inputSearch?.toLowerCase());
      });
      return rec.includes(true);
    });
  };

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(
      RequestEventlog({
        start: "",
        end: "",
        number: 100,
      })
    );
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <ProTable
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          loading={fetching}
          headerTitle="Log List"
          rowKey={() => uuidv4()}
          columns={columns}
          dataSource={recordAfterfiltering(eventLogData)}
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: recordAfterfiltering(eventLogData).length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          scroll={{
            x: 1100,
          }}
          toolbar={{
            search: {
              onSearch: (value) => {
                setInputSearch(value);
              },
            },
            actions: [
              <LogPageFilter />,
              <ExportData
                Columns={columns}
                DataSource={eventLogData}
                title="Syslog_List"
                key={"syslog"}
              />,
            ],
          }}
          options={{
            reload: () => {
              handleRefreshClick();
            },
            fullScreen: false,
          }}
          search={false}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "syslog-table",
            persistenceType: "localStorage",
          }}
        />
      </Col>
    </Row>
  );
};

export default LogPage;

export const LogPageFilter = () => {
  const [open, setOpen] = useState(false);
  const [count, setCount] = useState(100);
  const [valueDate, setValueDate] = useState(null);
  const dispatch = useDispatch();

  const hide = () => {
    setCount(100);
    setValueDate(null);
    setOpen(false);
  };

  const handleOpenChange = (newOpen) => {
    setCount(100);
    setValueDate(null);
    setOpen(newOpen);
  };

  const handleOkClick = () => {
    try {
      dispatch(
        RequestEventlog({
          start:
            valueDate === null
              ? ""
              : dayjs(valueDate[0]).format("YYYY/MM/DD HH:mm:ss"),
          end:
            valueDate === null
              ? ""
              : dayjs(valueDate[1]).format("YYYY/MM/DD HH:mm:ss"),
          number: count,
        })
      );
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    } finally {
      hide();
    }
  };

  const content = (
    <Form layout="vertical">
      <Form.Item label="count">
        <InputNumber
          value={count}
          onChange={(v) => setCount(v)}
          min={-1}
          style={{
            width: "100%",
          }}
        />
      </Form.Item>
      <Form.Item label="date range">
        <RangePicker
          value={valueDate}
          onChange={(d, ds) => setValueDate(d)}
          format="YYYY/MM/DD HH:mm:ss"
          showTime
          disabledDate={(currentDate) => {
            return currentDate && currentDate > dayjs().endOf("day");
          }}
        />
      </Form.Item>

      <Flex gap={10} justify="flex-end">
        <Button onClick={() => hide()}>cancel</Button>
        <Button onClick={() => handleOkClick()}>ok</Button>
      </Flex>
    </Form>
  );

  return (
    <Popover
      placement="bottom"
      title="filter log"
      content={content}
      trigger="click"
      open={open}
      onOpenChange={handleOpenChange}
    >
      <Button icon={<FilterOutlined />} />
    </Popover>
  );
};
