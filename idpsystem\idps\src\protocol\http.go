package protocol

import (
	"errors"

	"github.com/antlabs/httparser"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

//var Layerhttp = gopacket.RegisterLayerType(int(httpProto), gopacket.LayerTypeMetadata{Name: httpProto.String(), Decoder: gopacket.DecodeFunc(decodeHttp)})

type http struct {
	layers.BaseLayer // Stores the packet bytes and payload (Modbus PDU) bytes .
}

/*
func (h *http) LayerType() gopacket.LayerType {
	return Layerhttp
}*/

func decodeHttp(data []byte) (*http, error) {
	h := &http{}
	err := h.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return h, nil
}

func (h *http) DecodeFromBytes(data []byte) error {
	l := len(data)
	if l == 0 {
		return errors.New("http packet error")
	}
	bodylen := 0
	var payload []byte
	setting := httparser.Setting{
		Body: func(_ *httparser.Parser, buf []byte) {
			bodylen = len(buf)
			payload = buf
		},
	}
	p := httparser.New(httparser.BOTH)
	success, err := p.Execute(&setting, data)
	if err != nil {
		return err
	}
	if success != l {
		return errors.New("http packet error")
	}
	h.BaseLayer = layers.BaseLayer{Contents: data[:(len(data) - bodylen)], Payload: payload}
	return nil
}

/*
// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (h *http) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}

func (h *http) Payload() []byte {
	return h.BaseLayer.Payload
}*/

type httpParser struct {
}

func (p *httpParser) Parse(pack gopacket.Packet) bool {
	tp := NewTcpParser()
	b := tp.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeHttp(tp.GetTcp().Payload)
	return err == nil
}
