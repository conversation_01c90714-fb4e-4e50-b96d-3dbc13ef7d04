# Model Context Protocol

Please refer to [MCP official](https://github.com/modelcontextprotocol) for a full introduction.

## Integrate With NIMBL

For now, we have embedded an MCP SSE server within NIMBL. As long as NIMBL is started, the MCP SSE endpoint will be available at:  
`/api/v1/mcp/sse`

> **Note:** We do not apply bearer token authentication yet, but we plan to add it soon.

For more details on the MCP implementation, please refer to the [mcp.go](./mcp.go) source file.

---

### Test NIMBL MCP SSE Server

There are multiple tools available to help you test the MCP SSE server:

#### MCP Inspector
The official MCP Inspector (built with Node.js) can be used to test the available tools without involving AI.  
Visit the [Inspector GitHub repository](https://github.com/modelcontextprotocol/inspector) for guidelines.

#### Claude Desktop, Cursor IDE
These tools allow you to integrate MCP with an AI agent. Follow the user guide to set up the MCP server correctly, then ask AI questions (for example, "how many devices we have") to trigger a tool call through MCP.

For example, to configure Cursor IDE, create a file at `.cursor/mcp.json` with the following content:

```json
{
    "mcpServers": {
        "nimbl": {
            "url": "http://localhost:27182/api/v1/mcp/sse"
        }
    }
}
```

After saving the configuration, open Cursor IDE settings and enable the MCP tab for the NIMBL server. You can now ask AI questions, and the system will trigger the corresponding MCP tool calls.

---

By following the guidance above, you can effectively integrate and test the MCP SSE server within your NIMBL environment.