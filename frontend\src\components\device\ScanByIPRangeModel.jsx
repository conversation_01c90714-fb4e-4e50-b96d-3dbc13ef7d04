import { Form, Input, Modal } from "antd";
import React from "react";

const CIDR_PATTERN = /^([0-9]{1,3}\.){3}[0-9]{1,3}\/([0-9]|[1-2][0-9]|3[0-2])$/;

const ScanByIPRangeModel = ({ open, onCancel, onOk }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="add device by IP Range (CIDR)"
      okText="ok"
      cancelText="Cancel"
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="scan_ip_range_form"
        style={{
          maxWidth: 400,
        }}
        form={form}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="cidr"
          label="IP Range (CIDR)"
          rules={[
            {
              required: true,
              message: "Missing IP Range (CIDR)!",
            },
            {
              pattern: CIDR_PATTERN,
              message: "input should be IP Range (CIDR)!",
            },
          ]}
        >
          <Input placeholder="eg. xxx.xxx.xxx.xxx/xx" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ScanByIPRangeModel;
