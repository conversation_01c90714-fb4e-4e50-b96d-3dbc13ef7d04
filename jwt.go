package mnms

import (
	"context"
	"fmt"
	"net/http"
	"net/smtp"
	"sync"
	"time"

	"mnms/encrypt"

	"github.com/go-chi/jwtauth/v5"
	"github.com/lestrrat-go/jwx/v2/jwt"
	"github.com/qeof/q"
)

// jwtTokenAuth is a global variable for JWT authentication
var jwtTokenAuth = jwtauth.New("HS256", encrypt.Secret, nil)

// temprary token
var tempararyUrlToken = jwtauth.New("HS256", []byte("mnmstemparayurl"), nil)

// GetToken get a internal token that can be used by CLI and node
func GetToken(name string) (string, error) {
	var token string
	var err error

	if len(token) == 0 {
		// generate special token for CLI and node
		_, token, err = jwtTokenAuth.Encode(map[string]any{
			"user":      name,
			"timestamp": time.Now().Format(time.RFC3339),
		})
		if err != nil {
			return "", err
		}
	}
	return token, nil
}

func parseJWT(tokenString string) (map[string]any, error) {
	t, err := jwtTokenAuth.Decode(tokenString)
	if err != nil {
		return nil, err
	}
	claims, err := t.AsMap(context.Background())
	if err != nil {
		return nil, err
	}
	return claims, nil
}

// generateJWT generates user's JWT token
func generateJWT(user, password string) (string, error) {

	// Validate password
	if user == "agent" && QC.Kind == "nms" {
		if password != AdminDefaultPassword {
			return "", fmt.Errorf("invalid user or password")
		}
	} else {
		err := validUserPassword(user, password)
		if err != nil {
			return "", err
		}
	}

	// token will expire in 30 days
	_, token, err := jwtTokenAuth.Encode(map[string]any{
		"user":      user,
		"timestamp": time.Now().Format(time.RFC3339),
		"exp":       time.Now().Add(time.Hour * 24 * 30).Unix(),
	})

	if err != nil {
		return "", err
	}

	return token, nil
}

// JWTVerifyToken verifies the token
func JWTVerifyToken(ja *jwtauth.JWTAuth, tokenString string) (jwt.Token, error) {
	// Decode & verify the token
	token, err := ja.Decode(tokenString)
	if err != nil {
		return token, err
	}

	if token == nil {
		return nil, fmt.Errorf("no token")
	}

	if err := jwt.Validate(token); err != nil {
		q.Q("jwt.Validate fail", err)
		return token, err
	}

	// Valid!
	return token, nil
}

// JWTRenewAgentToken renews the agent token
func JWTRenewAgentToken(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token, _, err := jwtauth.FromContext(r.Context())
		if err != nil {
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return
		}

		// token should be valiated before?
		if token == nil || jwt.Validate(token) != nil {
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		// pass
		if QC.Kind != "nms" {
			next.ServeHTTP(w, r)
			return
		}
		prefix := r.URL.Query().Get("prefix")
		devid := r.URL.Query().Get("devid")
		if prefix != "agent" {
			next.ServeHTTP(w, r)
			return
		}

		if _, ok := token.Get("reason"); ok {
			handleOldVersionToken(devid)
		} else {
			if !handleNewVersionToken(token, devid) {
				http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
				return
			}
		}

		next.ServeHTTP(w, r)
	})
}

// Global map to track notified devices
var notifiedDevices = make(map[string]bool)
var notifiedDevicesMutex sync.Mutex

func handleOldVersionToken(devid string) {
	notifiedDevicesMutex.Lock()
	defer notifiedDevicesMutex.Unlock()

	if _, ok := notifiedDevices[devid]; !ok {
		q.Q(devid, "agentclient accessing with old token")
		// SendSyslog(LOG_INFO, "agent token", fmt.Sprintf("%s agentclient accessing NMS with old token", devid))
		notifiedDevices[devid] = true
	}
}

func handleNewVersionToken(token jwt.Token, devid string) bool {
	user, ok := token.Get("user")
	if !ok || user != "agent" {
		return false
	}

	exp := token.Expiration()
	if time.Now().After(exp.Add(-24 * time.Hour)) {
		renewToken(devid)
	}
	return true
}

func renewToken(devid string) {
	SendSyslog(LOG_INFO, "agent token", fmt.Sprintf("%s token will expire in 1 day, send log and refresh token", devid))
	cmd := "agent token refresh " + devid
	ci := CmdInfo{
		Timestamp:   time.Now().Format(time.RFC3339),
		Command:     cmd,
		NoOverwrite: false,
		All:         false,
		NoSyslog:    false,
		Kind:        "auto-auth",
		Client:      "",
		Tag:         "",
		DevId:       devid,
	}

	QC.CmdMutex.Lock()
	if c, ok := QC.CmdData[cmd]; !ok {
		QC.CmdData[cmd] = ci
	} else {
		if c.Status == "running" {
			q.Q("command is running, skip")
		} else {
			QC.CmdData[cmd] = ci
		}
	}
	q.Q("renew token", cmd)
	QC.CmdMutex.Unlock()
}

func hasRequiredRole(userRole, requiredRole string) (bool, string) {
	roleHierarchy := map[string]int{
		MNMSUserRole:      1,
		MNMSSuperUserRole: 2,
		MNMSAdminRole:     3,
		MNMSAgentRole:     3, // Agent has the same level as Admin
	}

	userRoleLevel, userRoleExists := roleHierarchy[userRole]
	requiredRoleLevel, requiredRoleExists := roleHierarchy[requiredRole]

	if !userRoleExists {
		return false, fmt.Sprintf("unknown user role: %s", userRole)
	}

	if !requiredRoleExists {
		return false, fmt.Sprintf("unknown required role: %s", requiredRole)
	}

	if userRoleLevel < requiredRoleLevel {
		return false, fmt.Sprintf("user role %s does not meet required role %s", userRole, requiredRole)
	}

	return true, ""
}

// JWTAuthenticatorRole is a authentication middleware to enforce access from the
// Verifier middleware request context values. The JWTAuthenticatorRole sends a 401 Unauthorized
// response for any unverified tokens and passes the good ones through. It's just fine
// until you decide to write something similar and customize your client response.
func JWTAuthenticatorRole(role string, next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		token, _, err := jwtauth.FromContext(r.Context())

		if err != nil {
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return
		}

		if token == nil || jwt.Validate(token) != nil {
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}
		// check is agent token
		_, ok := token.Get("reason")
		if ok {
			if role == MNMSSuperUserRole {
				next.ServeHTTP(w, r)
				return
			}
		}

		// check is admin
		emailRaw, ok := token.Get("user")
		if !ok {
			q.Q("no user", token)
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}
		userString, ok := emailRaw.(string)
		if !ok {
			q.Q("user is not string")
			http.Error(w, http.StatusText(http.StatusUnauthorized), http.StatusUnauthorized)
			return
		}

		u, err := GetUserConfig(userString)
		// q.Q(u)

		if err != nil {
			q.Q("GetUserConfig fail", err)
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return
		}

		if ok, errMsg := hasRequiredRole(u.Role, role); !ok {
			q.Q(errMsg)
			http.Error(w, errMsg, http.StatusUnauthorized)
			return
		}

		// Token is authenticated, pass it through
		next.ServeHTTP(w, r)
	})
}

// SendGMail sends email using gmail
func SendGMail(to, subject, body string) error {

	from := "<EMAIL>"
	pass := "atop0130"
	msg := "Subject: " + subject + "\r\n" + "\r\n" + body + "\r\n"
	err := smtp.SendMail("smtp.gmail.com:587", smtp.PlainAuth("", from, pass, "smtp.gmail.com"), from, []string{to}, []byte(msg))
	if err != nil {
		return err
	}
	return nil
}
