import { render, fireEvent, screen } from '@testing-library/react';
import Chang<PERSON><PERSON><PERSON><PERSON> from '../components/comman/ChangBaseURL';
import { describe, expect, it } from 'vitest';

describe('ChangBaseURL', () => {
  it('should render input and save button', () => {
    render(<ChangBaseURL />);

    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeInTheDocument();

    const saveButton = screen.getByRole('button', { name: 'save' });
    expect(saveButton).toBeInTheDocument();
  });

  it('should update input value when changed', () => {
    render(<ChangBaseURL />);

    const inputElement = screen.getByRole('textbox');

    fireEvent.change(inputElement, { target: { value: 'http://localhost:27182' } });

    expect(inputElement.value).toBe('http://localhost:27182');
  });
  
});
