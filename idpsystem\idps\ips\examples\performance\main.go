package main

import (
	"flag"
	"fmt"
	"log"

	"mnms/idpsystem/idps/ips"
	"os"
	"os/signal"
	"time"

	"math/rand"

	"github.com/google/gonids"
)

var out = false

func main() {
	count := 0

	show := flag.Bool("show", false, "show info detected")
	flag.IntVar(&count, "count", 100, "rules count")
	flag.BoolVar(&out, "o", false, "out file")

	flag.Parse()
	ipsys, err := ips.NewIps()
	if err != nil {
		panic(err)
	}
	if *show {
		if v, ok := ipsys.(ips.MatchEventer); ok {
			v.RegisterMatchEvent(event())
		}
	}

	err = ipsys.Start()
	if err != nil {
		panic(err)
	}
	log.Print("add rule")

	err = addRule(ipsys, count)
	if err != nil {
		panic(err)
	}

	log.Print("Build Rules")
	err = ipsys.Build()
	if err != nil {
		panic(err)
	}

	log.Print("apply rule")
	err = ipsys.ApplyRules()
	if err != nil {
		panic(err)
	}

	log.Print("start detect")
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	<-c
}

func event() func(ips.Event) {
	v := func(message ips.Event) {
		fmt.Printf("%v\n", message)
	}
	return v
}

func addRule(f ips.Ipser, count int) error {
	s := []string{}
	for i := 1; i <= count; i++ {
		time.Sleep(time.Nanosecond)
		_, c, err := roundByte()
		if err != nil {
			return err
		}
		off := rand.Intn(12)
		v := fmt.Sprintf(`pass ip any any -> any any (msg:"test";content:"%v";offset:%v;sid:%v;)`, string(c), off, uint32(i+1000))
		r, err := gonids.ParseRule(v)
		if err != nil {
			return err
		}
		err = f.AddGonidsRule(r)
		if err != nil {
			return err
		}
		s = append(s, v)
	}
	if out {
		f, err := os.OpenFile("test_rule.rules", os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return err
		}
		defer f.Close()
		for _, v := range s {
			_, err = f.WriteString(v + "\n")
			if err != nil {
				return err
			}
		}

	}

	return nil
}

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func roundByte() (uint, []byte, error) {
	rand.NewSource(time.Now().UnixNano())
	length := 10
	bufl := rand.Intn(length + 1)
	if bufl == 0 {
		bufl++
	}

	buf := make([]byte, bufl)
	l := uint(0)
	for i := 0; i < len(buf); i++ {
		index := rand.Intn(len(letterRunes))
		buf[i] = byte(letterRunes[index])
	}

	l = uint(rand.Intn(len(buf) + 1))
	return l, buf, nil
}
