import { Bad<PERSON>, <PERSON>, <PERSON>, Tooltip, Typography } from "antd";
import React from "react";
import { Flexbox } from "react-layout-kit";
import { useThemeStore } from "../../utils/themes/useStore";
import { useSelector } from "react-redux";
import { serverStatusSelector } from "../../features/comman/serverStatusSlice";

const ServerStatus = ({ isDashboardPage }) => {
  const { baseURL } = useThemeStore();
  const { serverOnlineStatus } = useSelector(serverStatusSelector);
  return (
    <Tooltip title={baseURL}>
      <Flexbox direction="horizontal" gap={10} align="center" height="100%">
        {serverOnlineStatus ? (
          <Badge color="green" className="cutomBadge" status="processing" />
        ) : (
          <Badge status="error" className="cutomBadge" />
        )}
        {isDashboardPage ? (
          <Row>
            <Col xs={{ span: 0 }} sm={{ span: 24 }}>
              <Typography.Text>{baseURL}</Typography.Text>
            </Col>
          </Row>
        ) : (
          <Typography.Text>{baseURL}</Typography.Text>
        )}
      </Flexbox>
    </Tooltip>
  );
};

export default ServerStatus;
