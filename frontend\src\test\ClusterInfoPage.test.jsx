import { render, waitFor } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import ClusterInfoPage from "../pages/clusterInfo/ClusterInfoPage";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { RequestClusterInfo, RootClusterInfo } from "../features/clusterInfo/clusterInfoSlice";
import { loginUser } from "../features/auth/userAuthSlice";

describe("ClusterInfoPage", () => {

  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("Should handle RequestClusterInfo correctly when successful", async () => {
    await store.dispatch(RequestClusterInfo());
    const updatedState = store.getState().clusterInfoData;
    expect(updatedState.fetching).toEqual(false);
  });

  it("should display the cluster information table", () => {
    const clusterInfoData = [
      {
        name: "Cluster 1",
        num_devices: 10,
        num_cmds: 20,
        num_logs_received: 100,
        num_logs_sent: 80,
        start: **********,
        now: **********,
        num_goroutines: 50,
        ip_addresses: ["***********", "***********"],
      },
      {
        name: "Cluster 2",
        num_devices: 5,
        num_cmds: 15,
        num_logs_received: 50,
        num_logs_sent: 40,
        start: **********,
        now: **********,
        num_goroutines: 30,
        ip_addresses: ["***********", "***********"],
      },
    ];

    render(
      <Provider store={store}>
        <ClusterInfoPage />
      </Provider>,
      { initialState: { clusterInfoSlice: { clusterInfoData } } }
    );

  });

  it("Request Cluster Info API - Success with valid data", async () => {
    await store.dispatch(RequestClusterInfo({ }));
    const updatedState = store.getState().clusterInfoData;
    expect(updatedState.clusterInfoData.name).to.not.equal("");
    expect(updatedState.clusterInfoData.num_devices).to.not.equal("");
    expect(updatedState.clusterInfoData.num_cmds).to.not.equal("");
    expect(updatedState.clusterInfoData.num_logs_received).to.not.equal("");
    expect(updatedState.clusterInfoData.num_logs_sent).to.not.equal("");
    expect(updatedState.clusterInfoData.start).to.not.equal("");
    expect(updatedState.clusterInfoData.now).to.not.equal("");
    expect(updatedState.clusterInfoData.num_goroutines).to.not.equal("");
    expect(updatedState.clusterInfoData.ip_addresses).to.not.equal("");
  });

  it("Root Cluster Info API - Success with valid data", async () => {
    await store.dispatch(RootClusterInfo({ }));
    const updatedState = store.getState().clusterInfoData;
    expect(updatedState.rootInfoData.num_devices).to.not.equal("");
    expect(updatedState.rootInfoData.num_clients).to.not.equal("");
    expect(updatedState.rootInfoData.name).to.not.equal("");
    expect(updatedState.rootInfoData.port).to.not.equal("");
    expect(updatedState.rootInfoData.is_root).to.not.equal("");
    expect(updatedState.rootInfoData.syslog_local_path).to.not.equal("");
    expect(updatedState.rootInfoData.syslog_file_size).to.not.equal("");
    expect(updatedState.rootInfoData.syslog_compress).to.not.equal("");
    expect(updatedState.rootInfoData.mqtt_broker_addr).to.not.equal("");
    expect(updatedState.rootInfoData.syslog_server_addr).to.not.equal("");
    expect(updatedState.rootInfoData.trap_server_addr).to.not.equal("");
    expect(updatedState.rootInfoData.cmd_interval).to.not.equal("");
    expect(updatedState.rootInfoData.register_interval).to.not.equal("");
    expect(updatedState.rootInfoData.gwd_interval).to.not.equal("");
    expect(updatedState.rootInfoData.snmp_options).to.not.equal("");
  });
});

