package test

import (
	"fmt"
	"mnms/idpsystem/idps/detect"
	"testing"

	"github.com/google/gonids"
)

type testNetLinkerID struct {
	value bool
	id    map[int]struct{}
}

func (t *testNetLinkerID) Pass() {
	t.value = true
}

func (t *testNetLinkerID) Drop() {
	t.value = true
}

func (t *testNetLinkerID) Alert(d detect.InfoMatched) {
	t.value = true
	t.id[d.Id] = struct{}{}
}

func (t *testNetLinkerID) Default() {

}
func (t *testNetLinkerID) checkIDExisted(id int) bool {
	_, ok := t.id[id]
	return ok

}
func (t *testNetLinkerID) checkIDNotExisted(id int) bool {
	_, ok := t.id[id]
	return !ok

}
func newTestNetLinkerID() *testNetLinkerID {
	return &testNetLinkerID{id: map[int]struct{}{}}
}

func TestPayload(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (content:"abc"; content:"d"; distance:0; within:1; sid:1;)`},
			expected: captured,
			packet:   []byte("abcabcd"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (content:"abc"; nocase; content:"d"; distance:0; within:1; sid:1;)`},
			expected: captured,
			packet:   []byte("abcaBcd"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (content:"aBc"; nocase; content:"abca"; distance:-10; within:4; sid:1;)`},
			expected: captured,
			packet:   []byte("abcaBcd"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule: []string{`alert ip any any -> any any (content:"this"; content:"is"; within:6; content:"big"; within:8;
        content:"string"; within:8; sid:1;)`},
			expected: captured,
			packet:   []byte("now this is is big big string now"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule: []string{`alert ip any any -> any any  (msg:"dummy"; 
        content:"this"; content:"is"; within:9; content:"big"; within:12;
        content:"string"; within:8; sid:1;)`},
			expected: captured,
			packet:   []byte("now this is is is big big big string now"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule: []string{`alert ip any any -> any any  (msg:"dummy";
        content:"now"; content:"this"; content:"is"; within:12; content:"big"; within:8;
        content:"string"; within:8; sid:1;)`},
			expected: captured,
			packet:   []byte("this this now is is     big string now"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule: []string{`alert ip any any -> any any  (msg:"dummy";
          content:"thus"; offset:8; content:"is"; within:6; content:"big"; within:8; sid:1;)`},
			expected: captured,
			packet:   []byte("         thus thus is a big"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule: []string{`alert ip any any -> any any  (msg:"dummy";
           content:"fix"; content:"this"; within:6; content:!"and"; distance:0; sid:1;)`},
			expected: captured,
			packet:   []byte("we need to fix this and yes fix this now"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestAbsent(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:10;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:20;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:<20;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:<=20;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:>30;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:10<>15;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; bsize:10<>21;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		/*{ //error example when using bsize without udp.hdr ipv4.hdr
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";  bsize:10<>21;content:"|11|"; offset:9; depth:1; sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},*/
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestBsize(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr;absent;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("XYZ_klm_1234abcd_XYZ_klm_5678abcd"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr;absent: or_else; content:"|00 08|"; offset:4; depth:2;sid:2;)`},
			expected: captured,
			packet:   []byte(""),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestByteMath(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
	}{
		{
			rule: []string{`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;
				 byte_math: bytes 1, offset 1, oper +, rvalue extracted_val, result var;
				 byte_test: 2, =, var, 13;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:1;)`,
				`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;
				 byte_math: bytes 1, offset 1, oper +, rvalue extracted_val, result var;
				 byte_test: 2, !=, var, 13;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:2;)`,
				`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;
				 byte_math: bytes 1, offset 1, oper +, rvalue extracted_val, result var;
				 byte_test: 2, <, var, 15;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:3;)`},
			existed:    []int{1, 3},
			notexisted: []int{2},
			packet: []byte{0x38, 0x35, 0x6d, 0x00, 0x00, 0x01,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x6d, 0x00, 0x01, 0x00},
			ip:   []string{"127.0.0.1", "127.0.0.1"},
			port: []uint16{10, 20},
			link: newTestNetLinkerID(),
		},

		{
			rule: []string{`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;;
				 byte_math: bytes 1, offset -1, oper +, rvalue extracted_val, result var, relative;;
				 byte_test: 2, =, var, 13;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:1;)`,
				`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;
				 byte_math: bytes 1, offset -1, oper +,  rvalue extracted_val, result var, relative;
				 byte_test: 2, !=, var, 13;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:2;)`,
				`alert ip any any -> any any (byte_extract: 1, 0, extracted_val, relative;
				 byte_math: bytes 1, offset -1, oper +, rvalue extracted_val, result var, relative;
				 byte_test: 2, <, var, 15;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:3;)`},
			existed:    []int{1, 3},
			notexisted: []int{2},
			packet: []byte{0x38, 0x35, 0x6d, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x70, 0x00, 0x01, 0x00},
			ip:   []string{"127.0.0.1", "127.0.0.1"},
			port: []uint16{10, 20},
			link: newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}

func TestByteJump(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
	}{
		{
			rule: []string{`alert ip any any -> any any (content:"abc"; byte_jump:2,0,relative,string,dec; content:"xyz"; within:3;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:1;)`},
			existed:    []int{1},
			notexisted: []int{0},
			packet:     []byte("abc03abcxyz"),
			ip:         []string{"127.0.0.1", "127.0.0.1"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
		{
			rule: []string{`alert ip any any -> any any (content:"abc"; byte_jump:2,0,relative,string,dec; content:"xyz"; within:3;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:1;)`},
			existed:    []int{1},
			notexisted: []int{0},
			packet:     []byte("abc03abc03abcxyz"),
			ip:         []string{"127.0.0.1", "127.0.0.1"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
		{
			rule: []string{`alert ip any any -> any any (content:"abc"; byte_jump:2,0,relative,string,dec; content:"xyz"; within:3; isdataat:!1,relative;
				 msg:"Byte extract and byte math with byte test verification";
				 sid:1;)`},
			existed:    []int{1},
			notexisted: []int{0},
			packet:     []byte("abc03abc03abcxyz"),
			ip:         []string{"127.0.0.1", "127.0.0.1"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}

func TestRpc(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
	}{
		{
			rule: []string{
				`alert ip any any -> any any (msg:"RPC Get Port Call"; rpc:100000, 2, 3; sid:1;)`,
				`alert ip any any -> any any (msg:"RPC Get Port Call"; rpc:100000, 2, *; sid:2;)`,
				`alert ip any any -> any any (msg:"RPC Get Port Call"; rpc:100000, *, 3; sid:3;)`,
				`alert ip any any -> any any (msg:"RPC Get Port Call"; rpc:100000, *, *; sid:4;)`,
				`alert ip any any -> any any (msg:"RPC Get XXX Call.. no "
				                                      "matc"; rpc:123456, *, 3; sid:5;)`},
			existed:    []int{1, 2, 3, 4},
			notexisted: []int{5},
			packet: []byte{0x64, 0xb2, 0xb3, 0x75,
				/* Message type: Call (0) */
				0x00, 0x00, 0x00, 0x00,
				/* RPC Version (2) */
				0x00, 0x00, 0x00, 0x02,
				/* Program portmap (100000) */
				0x00, 0x01, 0x86, 0xa0,
				/* Program version (2) */
				0x00, 0x00, 0x00, 0x02,
				/* Program procedure (3) = GETPORT */
				0x00, 0x00, 0x00, 0x03,
				/* AUTH_NULL */
				0x00, 0x00, 0x00, 0x00,
				/* Length 0 */
				0x00, 0x00, 0x00, 0x00,
				/* VERIFIER NULL */
				0x00, 0x00, 0x00, 0x00,
				/* Length 0 */
				0x00, 0x00, 0x00, 0x00,
				/* Program portmap */
				0x00, 0x01, 0x86, 0xa2,
				/* Version 2 */
				0x00, 0x00, 0x00, 0x02,
				/* Proto UDP */
				0x00, 0x00, 0x00, 0x11,
				/* Port 0 */
				0x00, 0x00, 0x00, 0x00},
			ip:   []string{"127.0.0.1", "127.0.0.1"},
			port: []uint16{10, 20},
			link: newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}
