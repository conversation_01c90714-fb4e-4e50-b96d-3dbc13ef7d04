-- *****************************************************************
-- RIP-MIB:  
-- ****************************************************************

MGMT-RIP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtRipMib MODULE-IDENTITY
    LAST-UPDATED "201806110000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the RIP MIB."
    REVISION    "201806110000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 152 }


MGMTRipAuthType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates RIP authentication type."
    SYNTAX      INTEGER { nullAuth(0), simplePasswordAuth(1),
                          md5Auth(2) }

MGMTRipDbProtoSubType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the protocol sub-type of the RIP database
         entry."
    SYNTAX      INTEGER { static(0), normal(1), default(2),
                          redistribute(3), interface(4), unknown(5) }

MGMTRipDbProtoType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the protocol type of the RIP database entry."
    SYNTAX      INTEGER { rip(0), connected(1), static(2), ospf(3),
                          unknown(4) }

MGMTRipGlobalVerType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The global RIP version."
    SYNTAX      INTEGER { default(0), v1(1), v2(2) }

MGMTRipIntfReceiveVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement reception on the interface."
    SYNTAX      INTEGER { none(0), v1(1), v2(2), both(3),
                          notSpecified(4) }

MGMTRipIntfSendVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement transmission on the interface."
    SYNTAX      INTEGER { v1(1), v2(2), both(3), notSpecified(4) }

MGMTRipOffsetListDirection ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The direction to add the offset to routing metric update."
    SYNTAX      INTEGER { in(0), out(1) }

MGMTRipSplitHorizonMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The split horizon mode."
    SYNTAX      INTEGER { splitHorizon(0), poisonedReverse(1),
                          disabled(2) }

mgmtRipMibObjects OBJECT IDENTIFIER
    ::= { mgmtRipMib 1 }

mgmtRipCapabilities OBJECT IDENTIFIER
    ::= { mgmtRipMibObjects 1 }

mgmtRipCapabilitiesMinRedistDefMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP redistributed default metric"
    ::= { mgmtRipCapabilities 1 }

mgmtRipCapabilitiesMaxRedistDefMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP redistributed default metric"
    ::= { mgmtRipCapabilities 2 }

mgmtRipCapabilitiesMinRedistSpecificMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP redistributed specific metric value."
    ::= { mgmtRipCapabilities 3 }

mgmtRipCapabilitiesMaxRedistSpecificMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP redistributed specific metric value."
    ::= { mgmtRipCapabilities 4 }

mgmtRipCapabilitiesIsOspfRedistributedSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if OSPF redistributed is supported or not"
    ::= { mgmtRipCapabilities 5 }

mgmtRipCapabilitiesMinTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP timers value"
    ::= { mgmtRipCapabilities 6 }

mgmtRipCapabilitiesMaxTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP timers value"
    ::= { mgmtRipCapabilities 7 }

mgmtRipCapabilitiesMinAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP administrative distance value"
    ::= { mgmtRipCapabilities 8 }

mgmtRipCapabilitiesMaxAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP administrative distance value"
    ::= { mgmtRipCapabilities 9 }

mgmtRipCapabilitiesSimplePwdLenMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value of simple password string length"
    ::= { mgmtRipCapabilities 10 }

mgmtRipCapabilitiesKeyChainNameLenMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value of key chain name length"
    ::= { mgmtRipCapabilities 11 }

mgmtRipCapabilitiesOffsetListMaxCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum count of the RIP offset-list"
    ::= { mgmtRipCapabilities 12 }

mgmtRipCapabilitiesMinOffsetListMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP offset-list metric value."
    ::= { mgmtRipCapabilities 13 }

mgmtRipCapabilitiesMaxOffsetListMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP offset-list specific metric value."
    ::= { mgmtRipCapabilities 14 }

mgmtRipCapabilitiesNetworkSegmentMaxCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum count of the RIP count of the RIP network segments"
    ::= { mgmtRipCapabilities 15 }

mgmtRipCapabilitiesNeighborMaxCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum count of the RIP neighbors"
    ::= { mgmtRipCapabilities 16 }

mgmtRipConfig OBJECT IDENTIFIER
    ::= { mgmtRipMibObjects 2 }

mgmtRipConfigRouter OBJECT IDENTIFIER
    ::= { mgmtRipConfig 1 }

mgmtRipConfigRouterRouterMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "RIP router mode."
    ::= { mgmtRipConfigRouter 1 }

mgmtRipConfigRouterVersion OBJECT-TYPE
    SYNTAX      MGMTRipGlobalVerType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "RIP version support."
    ::= { mgmtRipConfigRouter 2 }

mgmtRipConfigRouterUpdateTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (5..2147483)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The timer interval (in seconds) between the router sends the complete
         routing table to all neighboring RIP routers."
    ::= { mgmtRipConfigRouter 3 }

mgmtRipConfigRouterInvalidTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (5..2147483)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The invalid timer is the number of seconds after which a route will be
         marked invalid."
    ::= { mgmtRipConfigRouter 4 }

mgmtRipConfigRouterGarbageCollectionTimer OBJECT-TYPE
    SYNTAX      Unsigned32 (5..2147483)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The garbage collection timer is the number of seconds after which a
         route will be deleted."
    ::= { mgmtRipConfigRouter 5 }

mgmtRipConfigRouterRedistDefaultMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The RIP default redistributed metric.It is used when the metric value
         isn't specificed for the redistributed protocol type."
    ::= { mgmtRipConfigRouter 6 }

mgmtRipConfigRouterConnectedRedistEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate if the router redistribute the directly connected routes with
         RIP not enabled into the RIP domain or not."
    ::= { mgmtRipConfigRouter 7 }

mgmtRipConfigRouterConnectedRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value of connected interfaces redistribution is a
         specific configured value or not."
    ::= { mgmtRipConfigRouter 8 }

mgmtRipConfigRouterConnectedRedistMetricVal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the connected interfaces. The field is
         significant only when the argument 'ConnectedRedistIsSpecificMetric' is
         TRUE. If the specific metric setting is removed while the connected
         redistributed mode is enabled, the router will updates the original
         connected redistributed routes with metric value 16 before updates to
         the new metric value."
    ::= { mgmtRipConfigRouter 9 }

mgmtRipConfigRouterStaticRedistEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate if the router redistribute the static routes intothe RIP
         domain or not."
    ::= { mgmtRipConfigRouter 10 }

mgmtRipConfigRouterStaticRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value if static route redistribution is a specific
         configured value or not."
    ::= { mgmtRipConfigRouter 11 }

mgmtRipConfigRouterStaticRedistMetricVal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the static routes. The field is
         significant only when the argument 'StaticRedistIsSpecificMetric' is
         TRUE. If the specific metric setting is removed while the static
         redistributed mode is enabled, the router will updates the original
         static redistributed routes with metric value 16 before updates to the
         new metric value"
    ::= { mgmtRipConfigRouter 12 }

mgmtRipConfigRouterOspfRedistEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate if the router redistribute the OSPF routes into the RIP domain
         or not. The field is significant only when the OSPF protocol is
         supported on the device."
    ::= { mgmtRipConfigRouter 13 }

mgmtRipConfigRouterOspfRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value if RIP route redistribution is a specific
         configured value or not.The field is significant only when the OSPF
         protocol is supported on the device."
    ::= { mgmtRipConfigRouter 14 }

mgmtRipConfigRouterOspfRedistMetricVal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the RIP routes. The field is
         significant only when the OSPF protocol is supported on the device and
         argument 'OspfRedistIsSpecificMetric' is TRUE. If the specific metric
         setting is removed while the OSPF redistributed mode is enabled, the
         router will updates the original OSPF redistributed routes with metric
         value 16 before updates to the new metric value"
    ::= { mgmtRipConfigRouter 15 }

mgmtRipConfigRouterDefaultRouteRedist OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The RIP default route redistribution."
    ::= { mgmtRipConfigRouter 16 }

mgmtRipConfigRouterDefPassiveIntf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure all interfaces as passive-interface by default."
    ::= { mgmtRipConfigRouter 17 }

mgmtRipConfigRouterAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The RIP administrative distance."
    ::= { mgmtRipConfigRouter 18 }

mgmtRipConfigNetworkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipConfigNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP network configuration table. It is used to specify the RIP
         enabled interface(s). When RIP is enabled on the specific interface(s),
         the router can provide the network information to the other RIP routers
         via those interfaces."
    ::= { mgmtRipConfig 2 }

mgmtRipConfigNetworkEntry OBJECT-TYPE
    SYNTAX      MGMTRipConfigNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the RIP enabled interface(s)."
    INDEX       { mgmtRipConfigNetworkNetwork,
                  mgmtRipConfigNetworkIpSubnetMaskLength }
    ::= { mgmtRipConfigNetworkTable 1 }

MGMTRipConfigNetworkEntry ::= SEQUENCE {
    mgmtRipConfigNetworkNetwork             IpAddress,
    mgmtRipConfigNetworkIpSubnetMaskLength  Integer32,
    mgmtRipConfigNetworkAction              MGMTRowEditorState
}

mgmtRipConfigNetworkNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtRipConfigNetworkEntry 1 }

mgmtRipConfigNetworkIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtRipConfigNetworkEntry 2 }

mgmtRipConfigNetworkAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigNetworkEntry 100 }

mgmtRipConfigNetworkTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRipConfig 3 }

mgmtRipConfigNetworkTableRowEditorNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtRipConfigNetworkTableRowEditor 1 }

mgmtRipConfigNetworkTableRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtRipConfigNetworkTableRowEditor 2 }

mgmtRipConfigNetworkTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigNetworkTableRowEditor 100 }

mgmtRipConfigRouterInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP router interface configuration table."
    ::= { mgmtRipConfig 4 }

mgmtRipConfigRouterInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTRipConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each router interface has a set of parameters."
    INDEX       { mgmtRipConfigRouterInterfaceIfIndex }
    ::= { mgmtRipConfigRouterInterfaceTable 1 }

MGMTRipConfigRouterInterfaceEntry ::= SEQUENCE {
    mgmtRipConfigRouterInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtRipConfigRouterInterfacePassiveInterface  TruthValue
}

mgmtRipConfigRouterInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtRipConfigRouterInterfaceEntry 1 }

mgmtRipConfigRouterInterfacePassiveInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the interface as RIP passive-interface."
    ::= { mgmtRipConfigRouterInterfaceEntry 2 }

mgmtRipConfigNeighborTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipConfigNeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP neighbor connection table. It is used to configure the RIP
         router to send RIP updates to specific neighbors using the unicast,
         broadcast, or network IP address after update timer expiration."
    ::= { mgmtRipConfig 5 }

mgmtRipConfigNeighborEntry OBJECT-TYPE
    SYNTAX      MGMTRipConfigNeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the neighbor address."
    INDEX       { mgmtRipConfigNeighborIpv4Addr }
    ::= { mgmtRipConfigNeighborTable 1 }

MGMTRipConfigNeighborEntry ::= SEQUENCE {
    mgmtRipConfigNeighborIpv4Addr  IpAddress,
    mgmtRipConfigNeighborAction    MGMTRowEditorState
}

mgmtRipConfigNeighborIpv4Addr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IPv4 address."
    ::= { mgmtRipConfigNeighborEntry 1 }

mgmtRipConfigNeighborAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigNeighborEntry 100 }

mgmtRipConfigNeighborTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRipConfig 6 }

mgmtRipConfigNeighborTableRowEditorIpv4Addr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The IPv4 address."
    ::= { mgmtRipConfigNeighborTableRowEditor 1 }

mgmtRipConfigNeighborTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigNeighborTableRowEditor 100 }

mgmtRipConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP interface configuration table."
    ::= { mgmtRipConfig 7 }

mgmtRipConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTRipConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters."
    INDEX       { mgmtRipConfigInterfaceIfIndex }
    ::= { mgmtRipConfigInterfaceTable 1 }

MGMTRipConfigInterfaceEntry ::= SEQUENCE {
    mgmtRipConfigInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtRipConfigInterfaceSendVersion       MGMTRipIntfSendVersion,
    mgmtRipConfigInterfaceRecvVersion       MGMTRipIntfReceiveVersion,
    mgmtRipConfigInterfaceSplitHorizonMode  MGMTRipSplitHorizonMode,
    mgmtRipConfigInterfaceAuthType          MGMTRipAuthType,
    mgmtRipConfigInterfaceKeyChainName      MGMTDisplayString,
    mgmtRipConfigInterfaceIsEncrypted       TruthValue,
    mgmtRipConfigInterfaceSimplePwd         MGMTDisplayString
}

mgmtRipConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtRipConfigInterfaceEntry 1 }

mgmtRipConfigInterfaceSendVersion OBJECT-TYPE
    SYNTAX      MGMTRipIntfSendVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement transmission on the interface."
    ::= { mgmtRipConfigInterfaceEntry 2 }

mgmtRipConfigInterfaceRecvVersion OBJECT-TYPE
    SYNTAX      MGMTRipIntfReceiveVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement reception on the interface."
    ::= { mgmtRipConfigInterfaceEntry 3 }

mgmtRipConfigInterfaceSplitHorizonMode OBJECT-TYPE
    SYNTAX      MGMTRipSplitHorizonMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The split horizon mode."
    ::= { mgmtRipConfigInterfaceEntry 4 }

mgmtRipConfigInterfaceAuthType OBJECT-TYPE
    SYNTAX      MGMTRipAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication type."
    ::= { mgmtRipConfigInterfaceEntry 5 }

mgmtRipConfigInterfaceKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key chain used by MD5 authentication."
    ::= { mgmtRipConfigInterfaceEntry 6 }

mgmtRipConfigInterfaceIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the simple password is encrypted or not. TRUE means
         the simple password is encrypted. FALSE means the simple password is
         plain text."
    ::= { mgmtRipConfigInterfaceEntry 7 }

mgmtRipConfigInterfaceSimplePwd OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The simple password."
    ::= { mgmtRipConfigInterfaceEntry 8 }

mgmtRipConfigOffsetListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipConfigOffsetListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP offset-list configuration table."
    ::= { mgmtRipConfig 8 }

mgmtRipConfigOffsetListEntry OBJECT-TYPE
    SYNTAX      MGMTRipConfigOffsetListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each offset-list entry has a set of parameters."
    INDEX       { mgmtRipConfigOffsetListIfIndex,
                  mgmtRipConfigOffsetListRipOffsetListDirection }
    ::= { mgmtRipConfigOffsetListTable 1 }

MGMTRipConfigOffsetListEntry ::= SEQUENCE {
    mgmtRipConfigOffsetListIfIndex                 MGMTInterfaceIndex,
    mgmtRipConfigOffsetListRipOffsetListDirection  MGMTRipOffsetListDirection,
    mgmtRipConfigOffsetListAccessListName          MGMTDisplayString,
    mgmtRipConfigOffsetListOffsetMetric            MGMTUnsigned8,
    mgmtRipConfigOffsetListAction                  MGMTRowEditorState
}

mgmtRipConfigOffsetListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtRipConfigOffsetListEntry 1 }

mgmtRipConfigOffsetListRipOffsetListDirection OBJECT-TYPE
    SYNTAX      MGMTRipOffsetListDirection
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The direction to add the offset to routing metric update."
    ::= { mgmtRipConfigOffsetListEntry 2 }

mgmtRipConfigOffsetListAccessListName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Access-list name."
    ::= { mgmtRipConfigOffsetListEntry 3 }

mgmtRipConfigOffsetListOffsetMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The offset to incoming or outgoing routing metric."
    ::= { mgmtRipConfigOffsetListEntry 4 }

mgmtRipConfigOffsetListAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigOffsetListEntry 100 }

mgmtRipConfigOffsetListTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRipConfig 9 }

mgmtRipConfigOffsetListTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtRipConfigOffsetListTableRowEditor 1 }

mgmtRipConfigOffsetListTableRowEditorRipOffsetListDirection OBJECT-TYPE
    SYNTAX      MGMTRipOffsetListDirection
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The direction to add the offset to routing metric update."
    ::= { mgmtRipConfigOffsetListTableRowEditor 2 }

mgmtRipConfigOffsetListTableRowEditorAccessListName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Access-list name."
    ::= { mgmtRipConfigOffsetListTableRowEditor 3 }

mgmtRipConfigOffsetListTableRowEditorOffsetMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The offset to incoming or outgoing routing metric."
    ::= { mgmtRipConfigOffsetListTableRowEditor 4 }

mgmtRipConfigOffsetListTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRipConfigOffsetListTableRowEditor 100 }

mgmtRipStatus OBJECT IDENTIFIER
    ::= { mgmtRipMibObjects 3 }

mgmtRipStatusGeneral OBJECT IDENTIFIER
    ::= { mgmtRipStatus 1 }

mgmtRipStatusGeneralRouterModeIsEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RIP router mode."
    ::= { mgmtRipStatusGeneral 1 }

mgmtRipStatusGeneralVersion OBJECT-TYPE
    SYNTAX      MGMTRipGlobalVerType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the global rip version. By default, the router sends
         RIPv2 and accepts both RIPv1 and RIPv2. When the router receive either
         version of REQUESTS or triggered updates packets, it replies with the
         appropriate version. Be aware that the RIP network class configuration
         when RIPv1 is involved in the topology. RIPv1 uses classful routing,
         the subnet information is not included in the routing updates. The
         limitation makes it impossible to have different-sized subnets inside
         of the same network class. In other words, all subnets in a network
         class must have the same size."
    ::= { mgmtRipStatusGeneral 2 }

mgmtRipStatusGeneralUpdateTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The timer interval (in seconds) between the router sends the complete
         routing table to all neighboring RIP routers"
    ::= { mgmtRipStatusGeneral 3 }

mgmtRipStatusGeneralInvalidTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The invalid timer is the number of seconds after which a route will be
         marked invalid."
    ::= { mgmtRipStatusGeneral 4 }

mgmtRipStatusGeneralGarbageCollectionTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The garbage collection timer is the number of seconds after which a
         route will be deleted."
    ::= { mgmtRipStatusGeneral 5 }

mgmtRipStatusGeneralNextUpdateTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies when the next round of updates will be sent out from this
         router in seconds."
    ::= { mgmtRipStatusGeneral 6 }

mgmtRipStatusGeneralRedistDefMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the default metric value of redistributed routes."
    ::= { mgmtRipStatusGeneral 7 }

mgmtRipStatusGeneralIsRedistributeConnected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the connected route is redistributed or not."
    ::= { mgmtRipStatusGeneral 8 }

mgmtRipStatusGeneralIsRedistributeStatic OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the static route is redistributed or not."
    ::= { mgmtRipStatusGeneral 9 }

mgmtRipStatusGeneralIsRedistributeOSPF OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the OSPF route is redistributed or not."
    ::= { mgmtRipStatusGeneral 10 }

mgmtRipStatusGeneralAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates administrative distance value"
    ::= { mgmtRipStatusGeneral 11 }

mgmtRipStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The RIP interface status information table."
    ::= { mgmtRipStatus 2 }

mgmtRipStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTRipStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents RIP the active interface status
         information."
    INDEX       { mgmtRipStatusInterfaceIfIndex }
    ::= { mgmtRipStatusInterfaceTable 1 }

MGMTRipStatusInterfaceEntry ::= SEQUENCE {
    mgmtRipStatusInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtRipStatusInterfaceSendVersion         MGMTRipIntfSendVersion,
    mgmtRipStatusInterfaceRecvVersion         MGMTRipIntfReceiveVersion,
    mgmtRipStatusInterfaceTriggeredUpdate     TruthValue,
    mgmtRipStatusInterfaceIsPassiveInterface  TruthValue,
    mgmtRipStatusInterfaceAuthType            MGMTRipAuthType,
    mgmtRipStatusInterfaceKeyChainName        MGMTDisplayString
}

mgmtRipStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtRipStatusInterfaceEntry 1 }

mgmtRipStatusInterfaceSendVersion OBJECT-TYPE
    SYNTAX      MGMTRipIntfSendVersion
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement transmission on the interface."
    ::= { mgmtRipStatusInterfaceEntry 2 }

mgmtRipStatusInterfaceRecvVersion OBJECT-TYPE
    SYNTAX      MGMTRipIntfReceiveVersion
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The RIP version for the advertisement reception on the interface."
    ::= { mgmtRipStatusInterfaceEntry 3 }

mgmtRipStatusInterfaceTriggeredUpdate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the interface enable triggered update or not."
    ::= { mgmtRipStatusInterfaceEntry 4 }

mgmtRipStatusInterfaceIsPassiveInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates if the passive-interface is active on the interface or
         not."
    ::= { mgmtRipStatusInterfaceEntry 5 }

mgmtRipStatusInterfaceAuthType OBJECT-TYPE
    SYNTAX      MGMTRipAuthType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The authentication type."
    ::= { mgmtRipStatusInterfaceEntry 6 }

mgmtRipStatusInterfaceKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the interface is associate with a specific key-chain
         name."
    ::= { mgmtRipStatusInterfaceEntry 7 }

mgmtRipStatusDbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipStatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The RIP database information table."
    ::= { mgmtRipStatus 3 }

mgmtRipStatusDbEntry OBJECT-TYPE
    SYNTAX      MGMTRipStatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a routing entry in
         the RIP database."
    INDEX       { mgmtRipStatusDbNetworkAddress,
                  mgmtRipStatusDbNetworkPrefixSize,
                  mgmtRipStatusDbNextHop }
    ::= { mgmtRipStatusDbTable 1 }

MGMTRipStatusDbEntry ::= SEQUENCE {
    mgmtRipStatusDbNetworkAddress     IpAddress,
    mgmtRipStatusDbNetworkPrefixSize  Integer32,
    mgmtRipStatusDbNextHop            IpAddress,
    mgmtRipStatusDbType               MGMTRipDbProtoType,
    mgmtRipStatusDbSubType            MGMTRipDbProtoSubType,
    mgmtRipStatusDbMetric             MGMTUnsigned8,
    mgmtRipStatusDbExternalMetric     Unsigned32,
    mgmtRipStatusDbIsSelfInterface    TruthValue,
    mgmtRipStatusDbSourceAddress      IpAddress,
    mgmtRipStatusDbTag                Unsigned32,
    mgmtRipStatusDbUptime             Unsigned32
}

mgmtRipStatusDbNetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The destination IP address of the route."
    ::= { mgmtRipStatusDbEntry 1 }

mgmtRipStatusDbNetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The destination network prefix size of the route."
    ::= { mgmtRipStatusDbEntry 2 }

mgmtRipStatusDbNextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The first gateway along the route to the destination."
    ::= { mgmtRipStatusDbEntry 3 }

mgmtRipStatusDbType OBJECT-TYPE
    SYNTAX      MGMTRipDbProtoType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The protocol type of the route."
    ::= { mgmtRipStatusDbEntry 4 }

mgmtRipStatusDbSubType OBJECT-TYPE
    SYNTAX      MGMTRipDbProtoSubType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The protocol sub-type of the route."
    ::= { mgmtRipStatusDbEntry 5 }

mgmtRipStatusDbMetric OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The metric of the route."
    ::= { mgmtRipStatusDbEntry 6 }

mgmtRipStatusDbExternalMetric OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The field is significant only when the route is redistributed from
         other protocol type, for example, OSPF. This indicates the metric value
         from the original redistributed source."
    ::= { mgmtRipStatusDbEntry 7 }

mgmtRipStatusDbIsSelfInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This indicates the route is generated from one of the local interfaces
         or not."
    ::= { mgmtRipStatusDbEntry 8 }

mgmtRipStatusDbSourceAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The learning source IP address of the route. This indicates the route
         is learned an IP address. The field is significant only when the route
         isn't generated from the local interfaces."
    ::= { mgmtRipStatusDbEntry 9 }

mgmtRipStatusDbTag OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The tag of the route. It is used to provide a method of separating
         'internal' RIP routes, which may have been imported from an EGP
         (Exterior gateway protocol) or another IGP (Interior gateway protocol).
         For example, routes imported from OSPF can have a route tag value which
         the other routing protocols can use to prevent advertising the same
         route back to the original protocol routing domain."
    ::= { mgmtRipStatusDbEntry 10 }

mgmtRipStatusDbUptime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time field is significant only when the route is learned from the
         neighbors. When the route destination is reachable (its metric value
         less than 16), the time field means the invalid time of the route. When
         the route destination is unreachable (its metric value great than 16),
         the time field means the garbage-collection time of the route."
    ::= { mgmtRipStatusDbEntry 11 }

mgmtRipStatusPeerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRipStatusPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is RIP peer table. It is used to provide the RIP peer information."
    ::= { mgmtRipStatus 4 }

mgmtRipStatusPeerEntry OBJECT-TYPE
    SYNTAX      MGMTRipStatusPeerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the information for a RIP peer."
    INDEX       { mgmtRipStatusPeerIpv4Addr }
    ::= { mgmtRipStatusPeerTable 1 }

MGMTRipStatusPeerEntry ::= SEQUENCE {
    mgmtRipStatusPeerIpv4Addr        IpAddress,
    mgmtRipStatusPeerVersion         Integer32,
    mgmtRipStatusPeerLastUpdateTime  Unsigned32,
    mgmtRipStatusPeerRecvBadPackets  Unsigned32,
    mgmtRipStatusPeerRecvBadRoutes   Unsigned32
}

mgmtRipStatusPeerIpv4Addr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IPv4 address."
    ::= { mgmtRipStatusPeerEntry 1 }

mgmtRipStatusPeerVersion OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The RIP version number in the header of the last RIP packet received
         from the neighbor."
    ::= { mgmtRipStatusPeerEntry 2 }

mgmtRipStatusPeerLastUpdateTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time duration in seconds from the time the last RIP packet received
         from the neighbor to now."
    ::= { mgmtRipStatusPeerEntry 3 }

mgmtRipStatusPeerRecvBadPackets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of RIP response packets from the neighbor discarded as
         invalid."
    ::= { mgmtRipStatusPeerEntry 4 }

mgmtRipStatusPeerRecvBadRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of routes from the neighbor that were ignored because they
         were invalid."
    ::= { mgmtRipStatusPeerEntry 5 }

mgmtRipControl OBJECT IDENTIFIER
    ::= { mgmtRipMibObjects 4 }

mgmtRipControlGlobals OBJECT IDENTIFIER
    ::= { mgmtRipControl 1 }

mgmtRipControlGlobalsReloadProcess OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set true to reload RIP process."
    ::= { mgmtRipControlGlobals 1 }

mgmtRipMibConformance OBJECT IDENTIFIER
    ::= { mgmtRipMib 2 }

mgmtRipMibCompliances OBJECT IDENTIFIER
    ::= { mgmtRipMibConformance 1 }

mgmtRipMibGroups OBJECT IDENTIFIER
    ::= { mgmtRipMibConformance 2 }

mgmtRipCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipCapabilitiesMinRedistDefMetric,
                  mgmtRipCapabilitiesMaxRedistDefMetric,
                  mgmtRipCapabilitiesMinRedistSpecificMetric,
                  mgmtRipCapabilitiesMaxRedistSpecificMetric,
                  mgmtRipCapabilitiesIsOspfRedistributedSupported,
                  mgmtRipCapabilitiesMinTimer,
                  mgmtRipCapabilitiesMaxTimer,
                  mgmtRipCapabilitiesMinAdminDistance,
                  mgmtRipCapabilitiesMaxAdminDistance,
                  mgmtRipCapabilitiesSimplePwdLenMax,
                  mgmtRipCapabilitiesKeyChainNameLenMax,
                  mgmtRipCapabilitiesOffsetListMaxCount,
                  mgmtRipCapabilitiesMinOffsetListMetric,
                  mgmtRipCapabilitiesMaxOffsetListMetric,
                  mgmtRipCapabilitiesNetworkSegmentMaxCount,
                  mgmtRipCapabilitiesNeighborMaxCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 1 }

mgmtRipConfigRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigRouterRouterMode,
                  mgmtRipConfigRouterVersion,
                  mgmtRipConfigRouterUpdateTimer,
                  mgmtRipConfigRouterInvalidTimer,
                  mgmtRipConfigRouterGarbageCollectionTimer,
                  mgmtRipConfigRouterRedistDefaultMetric,
                  mgmtRipConfigRouterConnectedRedistEnabled,
                  mgmtRipConfigRouterConnectedRedistIsSpecificMetric,
                  mgmtRipConfigRouterConnectedRedistMetricVal,
                  mgmtRipConfigRouterStaticRedistEnabled,
                  mgmtRipConfigRouterStaticRedistIsSpecificMetric,
                  mgmtRipConfigRouterStaticRedistMetricVal,
                  mgmtRipConfigRouterOspfRedistEnabled,
                  mgmtRipConfigRouterOspfRedistIsSpecificMetric,
                  mgmtRipConfigRouterOspfRedistMetricVal,
                  mgmtRipConfigRouterDefaultRouteRedist,
                  mgmtRipConfigRouterDefPassiveIntf,
                  mgmtRipConfigRouterAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 2 }

mgmtRipConfigNetworkTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigNetworkNetwork,
                  mgmtRipConfigNetworkIpSubnetMaskLength,
                  mgmtRipConfigNetworkAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 3 }

mgmtRipConfigNetworkTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigNetworkTableRowEditorNetwork,
                  mgmtRipConfigNetworkTableRowEditorIpSubnetMaskLength,
                  mgmtRipConfigNetworkTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 4 }

mgmtRipConfigRouterInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigRouterInterfaceIfIndex,
                  mgmtRipConfigRouterInterfacePassiveInterface }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 5 }

mgmtRipConfigNeighborTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigNeighborIpv4Addr,
                  mgmtRipConfigNeighborAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 6 }

mgmtRipConfigNeighborTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigNeighborTableRowEditorIpv4Addr,
                  mgmtRipConfigNeighborTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 7 }

mgmtRipConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigInterfaceIfIndex,
                  mgmtRipConfigInterfaceSendVersion,
                  mgmtRipConfigInterfaceRecvVersion,
                  mgmtRipConfigInterfaceSplitHorizonMode,
                  mgmtRipConfigInterfaceAuthType,
                  mgmtRipConfigInterfaceKeyChainName,
                  mgmtRipConfigInterfaceIsEncrypted,
                  mgmtRipConfigInterfaceSimplePwd }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 8 }

mgmtRipConfigOffsetListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigOffsetListIfIndex,
                  mgmtRipConfigOffsetListRipOffsetListDirection,
                  mgmtRipConfigOffsetListAccessListName,
                  mgmtRipConfigOffsetListOffsetMetric,
                  mgmtRipConfigOffsetListAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 9 }

mgmtRipConfigOffsetListTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipConfigOffsetListTableRowEditorIfIndex,
                  mgmtRipConfigOffsetListTableRowEditorRipOffsetListDirection,
                  mgmtRipConfigOffsetListTableRowEditorAccessListName,
                  mgmtRipConfigOffsetListTableRowEditorOffsetMetric,
                  mgmtRipConfigOffsetListTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 10 }

mgmtRipStatusGeneralTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipStatusGeneralRouterModeIsEnabled,
                  mgmtRipStatusGeneralVersion,
                  mgmtRipStatusGeneralUpdateTimer,
                  mgmtRipStatusGeneralInvalidTimer,
                  mgmtRipStatusGeneralGarbageCollectionTimer,
                  mgmtRipStatusGeneralNextUpdateTime,
                  mgmtRipStatusGeneralRedistDefMetric,
                  mgmtRipStatusGeneralIsRedistributeConnected,
                  mgmtRipStatusGeneralIsRedistributeStatic,
                  mgmtRipStatusGeneralIsRedistributeOSPF,
                  mgmtRipStatusGeneralAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 11 }

mgmtRipStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipStatusInterfaceIfIndex,
                  mgmtRipStatusInterfaceSendVersion,
                  mgmtRipStatusInterfaceRecvVersion,
                  mgmtRipStatusInterfaceTriggeredUpdate,
                  mgmtRipStatusInterfaceIsPassiveInterface,
                  mgmtRipStatusInterfaceAuthType,
                  mgmtRipStatusInterfaceKeyChainName }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 12 }

mgmtRipStatusDbTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipStatusDbNetworkAddress,
                  mgmtRipStatusDbNetworkPrefixSize,
                  mgmtRipStatusDbNextHop, mgmtRipStatusDbType,
                  mgmtRipStatusDbSubType, mgmtRipStatusDbMetric,
                  mgmtRipStatusDbExternalMetric,
                  mgmtRipStatusDbIsSelfInterface,
                  mgmtRipStatusDbSourceAddress, mgmtRipStatusDbTag,
                  mgmtRipStatusDbUptime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 13 }

mgmtRipStatusPeerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipStatusPeerIpv4Addr, mgmtRipStatusPeerVersion,
                  mgmtRipStatusPeerLastUpdateTime,
                  mgmtRipStatusPeerRecvBadPackets,
                  mgmtRipStatusPeerRecvBadRoutes }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 14 }

mgmtRipControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRipControlGlobalsReloadProcess }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRipMibGroups 15 }

mgmtRipMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtRipCapabilitiesInfoGroup,
                       mgmtRipConfigRouterTableInfoGroup,
                       mgmtRipConfigNetworkTableInfoGroup,
                       mgmtRipConfigNetworkTableRowEditorInfoGroup,
                       mgmtRipConfigRouterInterfaceTableInfoGroup,
                       mgmtRipConfigNeighborTableInfoGroup,
                       mgmtRipConfigNeighborTableRowEditorInfoGroup,
                       mgmtRipConfigInterfaceTableInfoGroup,
                       mgmtRipConfigOffsetListTableInfoGroup,
                       mgmtRipConfigOffsetListTableRowEditorInfoGroup,
                       mgmtRipStatusGeneralTableInfoGroup,
                       mgmtRipStatusInterfaceTableInfoGroup,
                       mgmtRipStatusDbTableInfoGroup,
                       mgmtRipStatusPeerTableInfoGroup,
                       mgmtRipControlGlobalsInfoGroup }

    ::= { mgmtRipMibCompliances 1 }

END
