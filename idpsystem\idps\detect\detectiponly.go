package detect

import (
	"fmt"

	"github.com/zmap/go-iptree/iptree"
)

func newsigNumArray(size uint32) *sigNumArray {
	size = size/8 + 1
	return &sigNumArray{numArray: make([]uint8, size), size: size}
}

type sigNumArray struct {
	numArray []uint8
	size     uint32
}

func (s *sigNumArray) copy() *sigNumArray {
	arr := make([]uint8, 0, s.size)
	arr = append(arr, s.numArray...)

	return &sigNumArray{numArray: arr, size: s.size}
}
func (s *sigNumArray) updateBitarray(info *iPInfo) {
	tmp := uint8(1 << (info.signum % 8))
	if info.negated {
		s.numArray[info.signum/8] &= ^tmp

	} else {
		s.numArray[info.signum/8] |= tmp
	}
}

type iPOnlyEngine struct {
	ipSrc      *iPInfo
	ipDst      *iPInfo
	ipv4Src    *iptree.IPTree
	ipv4Dst    *iptree.IPTree
	ipv6Src    *iptree.IPTree
	ipv6Dst    *iptree.IPTree
	signatures []*signature
	sigArray   []uint32
	maxidx     uint32
}

func newIPOnlyEngine() *iPOnlyEngine {
	i := &iPOnlyEngine{
		ipv4Src:    iptree.New(),
		ipv4Dst:    iptree.New(),
		ipv6Src:    iptree.New(),
		ipv6Dst:    iptree.New(),
		signatures: newsignatures(defaultsize),
	}
	return i
}
func (i *iPOnlyEngine) build() error {
	for _, s := range i.signatures {
		err := i.addSignature(s)
		if err != nil {
			return err
		}
	}
	err := i.prepare()
	if err != nil {
		return err
	}
	return nil
}

func (i *iPOnlyEngine) loadSignature(s *signature) error {
	i.signatures = append(i.signatures, s)
	return nil
}

func (i *iPOnlyEngine) addSignature(s *signature) error {
	signum := i.trackSigNum(s.interid)
	iPOnlyListSetSigNum(s.net.srcipinfos, signum)
	iPOnlyListSetSigNum(s.net.dstipinfos, signum)
	i.ipSrc = iPOnlyItemInsert(i.ipSrc, s.net.srcipinfos)
	i.ipDst = iPOnlyItemInsert(i.ipDst, s.net.dstipinfos)
	if signum > i.maxidx {
		i.maxidx = signum
	}
	return nil
}
func iPOnlyListSetSigNum(head *iPInfo, num uint32) {
	for head != nil {
		head.signum = num
		head = head.next
	}

}

func iPOnlyItemInsert(head, item *iPInfo) *iPInfo {
	if head == nil {
		return item
	}
	if item == nil {
		return head
	}
	cur := item
	for cur != nil {
		next := cur.next
		cur.next = nil
		head = iPOnlyItemInsertReal(head, cur)
		cur = next
	}

	return head
}
func iPOnlyItemInsertReal(head, item *iPInfo) *iPInfo {
	if item == nil {
		return head
	}
	item.next = head
	return item
}

func (i *iPOnlyEngine) trackSigNum(num uint32) uint32 {
	i.sigArray = append(i.sigArray, num)
	return uint32(len(i.sigArray)) - 1
}

func (i *iPOnlyEngine) prepare() error {
	iPOnlyListQSort(&i.ipSrc)
	iPOnlyListQSort(&i.ipDst)

	for src := i.ipSrc; src != nil; src = src.next {
		if src.family == AFInet {
			err := i.updateTree(i.ipv4Src, src)
			if err != nil {
				return err
			}
		} else if src.family == AFInet6 {
			err := i.updateTree(i.ipv6Src, src)
			if err != nil {
				return err
			}
		} else {
			return fmt.Errorf("src ip shoulad be ipv4 or ipv6,err:%v", src.String())
		}
	}

	for dst := i.ipDst; dst != nil; dst = dst.next {
		if dst.family == AFInet {
			err := i.updateTree(i.ipv4Dst, dst)
			if err != nil {
				return err
			}
		} else if dst.family == AFInet6 {
			err := i.updateTree(i.ipv6Dst, dst)
			if err != nil {
				return err
			}
		} else {
			return fmt.Errorf("dst ip shoulad be ipv4 or ipv6,err:%v", dst.String())
		}
	}
	return nil
}
func (i *iPOnlyEngine) updateTree(tree *iptree.IPTree, item *iPInfo) error {
	data, _, err := tree.GetByString(item.String())
	if err != nil {
		return err
	}
	if data == nil {
		sna := newsigNumArray(i.maxidx)
		sna.updateBitarray(item)
		err = tree.R.SetCIDR(item.String(), sna)
		if err != nil {
			return err
		}
	} else {
		osna := data.(*sigNumArray)
		sna := osna.copy()
		sna.updateBitarray(item)
		tree.R.SetCIDR(item.String(), sna)
	}

	return nil
}

func (iponly *iPOnlyEngine) matchPacket(detctx *DetectEngineCtx, netlinker NetLinker, p *Packet) {
	var src *sigNumArray
	var dst *sigNumArray
	if p.family == AFInet {
		v, b, _ := iponly.ipv4Src.GetByString(p.srcip)
		if b {
			src = v.(*sigNumArray)
		}

		v, b, _ = iponly.ipv4Dst.GetByString(p.dstip)
		if b {
			dst = v.(*sigNumArray)
		}
	}
	if p.family == AFInet6 {
		v, b, _ := iponly.ipv6Src.GetByString(p.srcip)
		if b {
			src = v.(*sigNumArray)
		}

		v, b, _ = iponly.ipv6Dst.GetByString(p.dstip)
		if b {
			dst = v.(*sigNumArray)
		}

	}
	if src == nil || dst == nil {
		return
	}
	for u := uint32(0); u < src.size; u++ {
		bitarray := dst.numArray[u] & src.numArray[u]
		if bitarray == 0 {
			continue
		}
		for i := uint8(0); i < 8; i, bitarray = i+1, bitarray>>1 {
			if bitarray&0x01 > 0 {
				s, _ := detctx.manageSignature.FindSignatures(int(iponly.sigArray[u*8+uint32(i)]))
				if s == nil {
					continue
				}
				iponly.match(detctx, netlinker, s, p)
			}
		}

	}
}

func (i *iPOnlyEngine) match(detctx *DetectEngineCtx, netlinker NetLinker, s *signature, p *Packet) {
	if s.DetectPort(p) && s.DetectProto(p) {
		matched(detctx, netlinker, s, p)
	}
}

type detectIpOnlyEngineCtx struct {
	tempEngine *iPOnlyEngine
	insEngine  *iPOnlyEngine
}

func newDetectIpOnlyEngineCtx() *detectIpOnlyEngineCtx {
	return &detectIpOnlyEngineCtx{}
}

func (d *detectIpOnlyEngineCtx) Build() error {
	if d.tempEngine != nil {
		return d.tempEngine.build()
	}
	return nil
}
func (d *detectIpOnlyEngineCtx) Apply() {
	if d.tempEngine != nil {
		d.insEngine = d.tempEngine
		d.tempEngine = nil
	}
}

func (d *detectIpOnlyEngineCtx) LoadSignature(s *signature) error {
	if d.tempEngine == nil {
		d.tempEngine = newIPOnlyEngine()
	}
	return d.tempEngine.loadSignature(s)
}

func (d *detectIpOnlyEngineCtx) Verify(detctx *DetectEngineCtx, netlinker NetLinker, p *Packet) {
	if d.insEngine == nil {
		return
	}
	d.insEngine.matchPacket(detctx, netlinker, p)
}
func iPOnlyListQSort(info **iPInfo) {
	curr := *info
	l := 0
	temp := make([]*iPInfo, 0)
	for curr != nil {
		l++
		temp = append(temp, curr)
		curr = curr.next
	}
	quickSortIPInfo(temp, 0, l-1)
	*info = temp[0]
	for i := range l - 1 {
		temp[i].next = temp[i+1]
	}
	temp[l-1].next = nil
}
