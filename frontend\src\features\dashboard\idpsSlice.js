import { createSlice, createSelector } from "@reduxjs/toolkit";

const idpsSlice = createSlice({
  name: "idpsSlice",
  initialState: { selectedRecordDate: new Date().toLocaleDateString("fr-CA") },
  reducers: {
    setSelectedRecordDate: (state, { payload }) => {
      state.selectedRecordDate = payload;
    },
  },
});

export const { setSelectedRecordDate } = idpsSlice.actions;

export const idpsSelector = createSelector(
  (state) => state.idps,
  ({ selectedRecordDate }) => ({
    selectedRecordDate,
  })
);

export default idpsSlice;
