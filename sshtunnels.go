package mnms

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"

	"github.com/qeof/q"
	"golang.org/x/crypto/ssh"
)

type SshDeviceAutoRunResponse struct {
	Status     string `json:"status"`
	RootHost   string `json:"root_host"`
	SshPort    int    `json:"ssh_port"`
	ListenPort int    `json:"listen_port"`
	Message    string `json:"message"`
}

type SshConnection struct {
	SshConn    *ssh.ServerConn `json:"-"`
	RemoteAddr string          `json:"remote_addr"`
	ListenPort int             `json:"listen_port"`
	DevMac     string          `json:"dev_mac"`
}

func init() {
	QC.SshServerPort = 6422
	QC.SshConnections = make(map[int]SshConnection)
	QC.SshServerKeyPath = "nimbl_ssh_host_rsa_key"
}

func getPrivateKey() (ssh.Signer, error) {
	// Attempt to read the private key from the file
	pkey, err := os.ReadFile(QC.SshServerKeyPath)
	if err == nil {
		// Parse the private key if it exists
		privateKey, err := ssh.ParsePrivateKey(pkey)
		if err != nil {
			return nil, err
		}
		q.Q("read private key from file")
		return privateKey, nil
	}

	// If the file does not exist or there was an error reading it, generate a new key
	q.Q("could not read private key from file, generating new key")
	private, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		return nil, err
	}

	// Convert the RSA key into a format that the ssh package can use
	privateKey, err := ssh.NewSignerFromKey(private)
	if err != nil {
		return nil, err
	}

	// Convert the RSA private key to PEM format
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(private),
	})

	// Save the PEM-encoded private key to a file
	err = os.WriteFile(QC.SshServerKeyPath, privateKeyPEM, 0600)
	if err != nil {
		return nil, err
	}

	return privateKey, nil
}

func RunSSHServer() error {
	q.Q("RunSSHServer")

	privateKey, err := getPrivateKey()
	if err != nil {
		q.Q("error: could not get private key", err)
		return err
	}

	config := &ssh.ServerConfig{
		NoClientAuth: true,
		NoClientAuthCallback: func(c ssh.ConnMetadata) (*ssh.Permissions, error) {
			id := c.User()
			QC.DevMutex.Lock()
			defer QC.DevMutex.Unlock()
			_, ok := QC.DevData[id]
			if !ok {
				return nil, fmt.Errorf("invalid username, please register device first and connect with device id")
			}
			return nil, nil
		},
	}

	config.AddHostKey(privateKey)

	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", QC.SshServerPort))
	if err != nil {
		fmt.Println("error: cannot run ssh server", QC.SshServerPort)
		DoExit(1)
	}
	defer listener.Close()

	for {
		nConn, err := listener.Accept()
		if err != nil {
			q.Q("failed to accept incoming connection: ", err)
			continue
		}
		q.Q(nConn.RemoteAddr().String())

		// Before use, a handshake must be performed on the incoming net.Conn.
		sshConn, chans, reqs, err := ssh.NewServerConn(nConn, config)
		if err != nil {
			q.Q("failed to handshake: ", err)
			continue
		}

		var forwardListener net.Listener
		// The incoming Request channel must be serviced.
		// go ssh.DiscardRequests(reqs)
		go func() {
			// handle tcpip-forward and cancel-tcpip-forward requests
			for req := range reqs {
				if req.Type == "tcpip-forward" {
					// parse the request payload
					var r struct {
						Address string
						Port    uint32
					}
					err = ssh.Unmarshal(req.Payload, &r)
					if err != nil {
						q.Q("could not unmarshal payload: ", err)
						req.Reply(false, nil)
						continue
					}
					// host a forwardListener on the requested port
					forwardListener, err = net.Listen("tcp", fmt.Sprintf(":%d", r.Port))
					if err != nil {
						q.Q("could not listen on port: ", err)
						req.Reply(false, nil)
						continue
					}

					// Add the connection to the map
					addConnection(int(r.Port), SshConnection{
						SshConn:    sshConn,
						ListenPort: int(r.Port),
						RemoteAddr: nConn.RemoteAddr().String(),
						DevMac:     sshConn.User(),
					})

					// forward all incoming connections back to the client
					go func() {
						defer func() {
							if forwardListener != nil {
								forwardListener.Close()
							}
						}()

						for {
							if forwardListener == nil {
								break
							}

							conn, err := forwardListener.Accept()
							if err != nil {
								if opErr, ok := err.(*net.OpError); ok && opErr.Err.Error() == "use of closed network connection" {
									q.Q("forwardListener closed, exiting accept loop.")
									break
								}
								q.Q("could not accept connection: ", err)
								continue
							}

							// open a new channel to the client
							channel, _, err := sshConn.OpenChannel("forwarded-tcpip", ssh.Marshal(struct {
								Address        string
								Port           uint32
								OriginatorIP   string
								OriginatorPort uint32
							}{
								Address:        r.Address,
								Port:           r.Port,
								OriginatorIP:   conn.RemoteAddr().(*net.TCPAddr).IP.String(),
								OriginatorPort: uint32(conn.RemoteAddr().(*net.TCPAddr).Port),
							}))
							if err != nil {
								q.Q("could not open channel: ", err)
								conn.Close()
								continue
							}

							// start copying data
							go func() {
								_, err := io.Copy(channel, conn)
								if err != nil {
									channel.Close()
									conn.Close()
								}
							}()

							go func() {
								_, err := io.Copy(conn, channel)
								if err != nil {
									channel.Close()
									conn.Close()
								}
							}()
						}
					}()
					req.Reply(true, nil)
				}
				// reject all other requests
				req.Reply(false, nil)
			}
		}()

		go func() {
			for {
				_, ok := <-chans
				if !ok {
					if forwardListener != nil {
						deleteConnection(int(forwardListener.Addr().(*net.TCPAddr).Port))
						forwardListener.Close()
						q.Q("chans closed")
					}
					break
				}
			}
		}()

		// Service the incoming Channel channel.
		// serve reverse connection only
		go func(<-chan ssh.NewChannel) {
			for newChannel := range chans {
				q.Q("newChannel.ChannelType", newChannel.ChannelType())
				// reject all channel types, only allow reverse connections which are handled by the reqs goroutine
				newChannel.Reject(ssh.Prohibited, "only reverse connections are allowed")
			}
		}(chans)
	}
}

func addConnection(port int, conn SshConnection) {
	QC.SshConnectionsMutex.Lock()
	defer QC.SshConnectionsMutex.Unlock()
	QC.SshConnections[port] = conn
}

func deleteConnection(port int) {
	QC.SshConnectionsMutex.Lock()
	defer QC.SshConnectionsMutex.Unlock()
	delete(QC.SshConnections, port)
}

func closeConnection(port int) error {
	conn, ok := QC.SshConnections[port]
	if !ok {
		return fmt.Errorf("connection not found")
	}

	err := conn.SshConn.Close()
	if err != nil {
		return err
	}

	deleteConnection(port)
	return nil
}

func randomOpenPort() (int, error) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		return 0, err
	}

	addrParts := strings.Split(listener.Addr().String(), ":")
	port, err := strconv.Atoi(addrParts[len(addrParts)-1])
	if err != nil {
		return 0, err
	}

	listener.Close()

	return port, nil
}

func AutoRunSshInfo() *SshDeviceAutoRunResponse {
	if !QC.IsRoot {
		return &SshDeviceAutoRunResponse{
			Status:  "error",
			Message: "this command requires root service",
		}
	}

	listenPort, err := randomOpenPort()
	if err != nil {
		return &SshDeviceAutoRunResponse{
			Status:  "error",
			Message: "could not open port",
		}
	}

	return &SshDeviceAutoRunResponse{
		Status: "ok",
		// RootHost:   "localhost", fill later
		SshPort:    QC.SshServerPort,
		ListenPort: listenPort,
		Message:    "",
	}
}

func LookupRootHost(nmsUrl string) (string, error) {
	if QC.IsRoot {
		return "", fmt.Errorf("error: this command requires nms service")
	}

	// maybe I can use the RootURL from QC in root as the ssh host
	// if rootsvc's root url is empty, use nmssvc's root url
	// if nmssvc's root url is loopback, use the nmsURL passed in which comes from the device because this means nmssvc and rootsvc are on the same machine

	// here we only check the nmssvc's root url, if goes wrong, we use the full solution above
	parsedURL, err := url.Parse(QC.RootURL)
	if err != nil {
		return "", fmt.Errorf("error: could not parse root url")
	}
	// check root host
	rHost := parsedURL.Hostname()
	// if host is empty, error
	if len(rHost) == 0 {
		return "", fmt.Errorf("error: root host is empty")
	}
	// if host is loopback, use mnmshost
	ips, err := net.LookupIP(rHost)
	if err != nil {
		return "", fmt.Errorf("error: could not lookup root host")
	}
	for _, ip := range ips {
		if ip.IsLoopback() {
			parsedURL, err := url.Parse(nmsUrl)
			if err != nil {
				return "", fmt.Errorf("error: could not parse nms url")
			}
			rHost = parsedURL.Hostname()
		}
	}
	return rHost, nil
}

/*
ssh tunnel close [port]
ssh tunnel fetch [port]
ssh tunnels list
*/
func SshCmd(cmdinfo *CmdInfo) *CmdInfo {
	if !QC.IsRoot {
		cmdinfo.Status = "error: this command requires root service"
		return cmdinfo
	}

	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "ssh tunnel close") {
		ws := strings.Fields(cmd)
		if len(ws) < 4 {
			cmdinfo.Status = "error: invalid number of arguments"
			return cmdinfo
		} else if len(ws) > 4 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}

		port := ws[3]
		p, err := strconv.Atoi(port)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		err = closeConnection(p)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}

		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "ssh tunnel fetch") {
		ws := strings.Fields(cmd)
		if len(ws) < 4 {
			cmdinfo.Status = "error: invalid number of arguments"
			return cmdinfo
		} else if len(ws) > 4 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}

		port := ws[3]
		p, err := strconv.Atoi(port)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		err = checkConnection("localhost", p)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "ssh tunnels list") {
		ws := strings.Fields(cmd)
		if len(ws) > 3 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		QC.SshConnectionsMutex.Lock()
		defer QC.SshConnectionsMutex.Unlock()
		jsonBytes, err := json.Marshal(QC.SshConnections)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		cmdinfo.Result = string(jsonBytes)
		return cmdinfo
	}

	cmdinfo.Status = "error: unknown command"
	return cmdinfo
}

func checkConnection(serverIP string, port int) error {
	urls := []string{
		fmt.Sprintf("https://%s:%d", serverIP, port),
		fmt.Sprintf("http://%s:%d", serverIP, port),
	}

	fetched := false
	for _, url := range urls {
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
		client := &http.Client{Transport: tr}
		resp, err := client.Get(url)
		if err != nil {
			q.Q("Error fetching URL %s: %v\n", url, err)
			continue
		}

		if resp.StatusCode == http.StatusOK {
			fetched = true
			resp.Body.Close()
			break
		} else {
			q.Q("Error fetching URL %s: %v\n", url, resp.Status)
		}
		resp.Body.Close()
	}

	if !fetched {
		return fmt.Errorf("connection failed")
	}
	return nil
}
