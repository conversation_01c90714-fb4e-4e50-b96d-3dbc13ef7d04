// devicequery/database.go
package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strings"
	"time"

	_ "github.com/glebarez/go-sqlite" // Pure Go SQLite driver
)

var db *sql.DB

// initDB initializes the SQLite database connection and creates the table.
func initDB(dbPath string) error {
	var err error
	db, err = sql.Open("sqlite", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	if err = db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Successfully connected to SQLite database.")
	return createDevicesTable()
}

// createDevicesTable dynamically creates the 'devices' table based on the Device struct.
func createDevicesTable() error {
	var device Device
	val := reflect.ValueOf(device)
	typ := val.Type()

	var columns []string
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		if jsonTag := field.Tag.Get("json"); jsonTag == "-" { // Skip fields like 'Timestamp' not directly from JSON struct tag for column creation
			continue
		}

		colName := field.Name
		var colType string

		switch field.Type.Kind() {
		case reflect.String:
			colType = "TEXT"
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			colType = "INTEGER"
		case reflect.Float32, reflect.Float64:
			colType = "REAL"
		case reflect.Bool:
			colType = "INTEGER" // Store bools as 0 or 1
		default:
			// Check specific types after kind
			if field.Type == reflect.TypeOf(time.Time{}) { // This is for our processed Timestamp field
				colType = "TEXT" // Storing as RFC3339 string
			} else if field.Name == "Timestamp" { // Explicitly handle the 'Timestamp' field in the struct if not caught by TypeOf
				colType = "TEXT"
			} else if field.Type == reflect.TypeOf(json.RawMessage{}) {
				colType = "TEXT" // Storing JSON as string
			} else {
				log.Printf("Warning: Unsupported type for column %s: %s (%s). Defaulting to TEXT.", colName, field.Type.Kind(), field.Type.String())
				colType = "TEXT"
			}
		}

		if colName == "MAC" {
			columns = append(columns, fmt.Sprintf("%s %s PRIMARY KEY", colName, colType))
		} else {
			columns = append(columns, fmt.Sprintf("%s %s", colName, colType))
		}
	}

	query := fmt.Sprintf("CREATE TABLE IF NOT EXISTS devices (%s);", strings.Join(columns, ", "))
	log.Printf("Creating table with SQL: %s", query)

	_, err := db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create devices table: %w", err)
	}
	log.Println("Devices table created or already exists.")
	return nil
}

// getTableSchemaForLLM generates a string representation of the table schema.
func getTableSchemaForLLM() (string, error) {
	rows, err := db.Query("PRAGMA table_info(devices);")
	if err != nil {
		return "", fmt.Errorf("failed to query table_info: %w", err)
	}
	defer rows.Close()

	var schemaParts []string
	schemaParts = append(schemaParts, "Table: devices")
	schemaParts = append(schemaParts, "Columns (column names are case-sensitive as listed):")
	for rows.Next() {
		var cid int
		var name string
		var colType string
		var notnull int
		var dflt_value sql.NullString
		var pk int
		if err := rows.Scan(&cid, &name, &colType, &notnull, &dflt_value, &pk); err != nil {
			return "", fmt.Errorf("failed to scan table_info row: %w", err)
		}
		pkStr := ""
		if pk == 1 {
			pkStr = " (PRIMARY KEY)"
		}
		description := ""
		switch name {
		case "Timestamp":
			description = " (Device event time, stored as RFC3339 UTC string, e.g., '2024-09-10T04:56:57Z'. Query using SQLite datetime() function: datetime(Timestamp) > datetime('YYYY-MM-DDTHH:MM:SSZ'))"
		case "TimestampUnixStr":
			description = " (Original Unix epoch timestamp as a string from source data, e.g., '1725944217')"
		case "IsOnline", "IsDHCP", "Lock":
			description = " (Boolean: 1 for true, 0 for false. Query as IsOnline = 1 or IsOnline = 0)"
		case "Capabilities", "DeviceErrors":
			description = " (Stored as a JSON string. Use LIKE for simple search or json_extract for specific values, e.g., json_extract(Capabilities, '$.gwd') = 1 or json_extract(Capabilities, '$.gwd') IS TRUE for boolean true within JSON)"
		case "MAC":
			description = " (Device MAC address, PRIMARY KEY)"
		}
		schemaParts = append(schemaParts, fmt.Sprintf("  - %s (%s)%s%s", name, colType, pkStr, description))
	}
	if err = rows.Err(); err != nil {
		return "", fmt.Errorf("error iterating table_info rows: %w", err)
	}
	return strings.Join(schemaParts, "\n"), nil
}

// insertDevices inserts a slice of Device objects into the database.
func insertDevices(devices []Device) error {
	if len(devices) == 0 {
		log.Println("No devices to insert.")
		return nil
	}

	var sampleDevice Device
	valType := reflect.TypeOf(sampleDevice)
	var columnNames []string
	var placeholders []string

	for i := 0; i < valType.NumField(); i++ {
		field := valType.Field(i)
		if jsonTag := field.Tag.Get("json"); jsonTag == "-" {
			continue
		}
		columnNames = append(columnNames, field.Name)
		placeholders = append(placeholders, "?")
	}

	stmtSQL := fmt.Sprintf("INSERT OR REPLACE INTO devices (%s) VALUES (%s)",
		strings.Join(columnNames, ", "),
		strings.Join(placeholders, ", "))

	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(stmtSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %w", err)
	}
	defer stmt.Close()

	for _, device := range devices {
		v := reflect.ValueOf(device)
		var args []interface{}
		for i := 0; i < valType.NumField(); i++ {
			structField := valType.Field(i)
			if jsonTag := structField.Tag.Get("json"); jsonTag == "-" {
				continue
			}

			fieldVal := v.Field(i)
			fieldName := structField.Name

			if fieldName == "Timestamp" { // This is our time.Time field
				t := fieldVal.Interface().(time.Time)
				if t.IsZero() { // Handle unparsed or zero timestamps
					args = append(args, nil) // Store as SQL NULL
				} else {
					args = append(args, t.Format(time.RFC3339))
				}
			} else if fieldVal.Type() == reflect.TypeOf(json.RawMessage{}) {
				rawMsg := fieldVal.Interface().(json.RawMessage)
				if rawMsg == nil || string(rawMsg) == "null" || len(rawMsg) == 0 {
					args = append(args, nil)
				} else {
					args = append(args, string(rawMsg))
				}
			} else {
				args = append(args, fieldVal.Interface())
			}
		}
		_, err := stmt.Exec(args...)
		if err != nil {
			log.Printf("Failed to insert/replace device MAC %s: %v. SQL: %s, Args: %+v", device.MAC, err, stmtSQL, args)
		}
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	log.Printf("Successfully inserted/replaced %d devices.", len(devices))
	return nil
}

// executeSQLQuery is the function that the LLM tool will call.
func executeSQLQuery(query string) (string, error) {
	log.Printf("LLM Tool: Executing SQL: %s", query)

	trimmedQuery := strings.TrimSpace(strings.ToUpper(query))
	if !strings.HasPrefix(trimmedQuery, "SELECT") {
		log.Printf("LLM Tool: Blocked non-SELECT query: %s", query)
		return "Error: Only SELECT queries are allowed by this tool. Please rephrase your request if you intended to retrieve data.", nil
	}

	rows, err := db.Query(query)
	if err != nil {
		log.Printf("LLM Tool: SQL execution error: %v for query: %s", err, query)
		return fmt.Sprintf("Error executing SQL query: %v. The query was: %s", err, query), nil
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		log.Printf("LLM Tool: Error getting columns: %v", err)
		return fmt.Sprintf("Error getting columns: %v", err), nil
	}

	var results []string
	results = append(results, strings.Join(columns, " | "))

	columnValues := make([]interface{}, len(columns))
	valuePointers := make([]interface{}, len(columns))
	for i := range columnValues {
		valuePointers[i] = &columnValues[i]
	}

	rowCount := 0
	for rows.Next() {
		rowCount++
		err = rows.Scan(valuePointers...)
		if err != nil {
			log.Printf("LLM Tool: Error scanning row: %v", err)
			return fmt.Sprintf("Error scanning row: %v", err), nil
		}

		var rowStr []string
		for i, colVal := range columnValues {
			colName := columns[i]

			if colVal == nil {
				rowStr = append(rowStr, "NULL")
			} else {
				switch v := colVal.(type) {
				case []byte: // TEXT columns often come as []byte
					s := string(v)
					if colName == "Timestamp" {
						parsedTime, timeErr := time.Parse(time.RFC3339, s)
						if timeErr == nil {
							rowStr = append(rowStr, parsedTime.Format("2006-01-02 15:04:05 UTC"))
						} else {
							rowStr = append(rowStr, s)
						}
					} else {
						rowStr = append(rowStr, s)
					}
				case int64:
					if colName == "IsOnline" || colName == "IsDHCP" || colName == "Lock" {
						if v == 1 {
							rowStr = append(rowStr, "true")
						} else {
							rowStr = append(rowStr, "false")
						}
					} else {
						rowStr = append(rowStr, fmt.Sprintf("%d", v))
					}
				case float64:
					rowStr = append(rowStr, fmt.Sprintf("%f", v))
				case string: // For columns already string
					if colName == "Timestamp" {
						parsedTime, timeErr := time.Parse(time.RFC3339, v)
						if timeErr == nil {
							rowStr = append(rowStr, parsedTime.Format("2006-01-02 15:04:05 UTC"))
						} else {
							rowStr = append(rowStr, v)
						}
					} else {
						rowStr = append(rowStr, v)
					}
				default:
					rowStr = append(rowStr, fmt.Sprintf("%v", colVal))
				}
			}
		}
		results = append(results, strings.Join(rowStr, " | "))
		if len(results) > 51 { // Header + 50 data rows
			results = append(results, fmt.Sprintf("... (and %d more matching rows, output truncated)", rowCount-50)) // Approximation
			break
		}
	}

	if err = rows.Err(); err != nil {
		log.Printf("LLM Tool: Error after iterating rows: %v", err)
		return fmt.Sprintf("Error after iterating rows: %v", err), nil
	}

	if rowCount == 0 {
		return "No results found for the query.", nil
	}
	return strings.Join(results, "\n"), nil
}

func closeDB() {
	if db != nil {
		db.Close()
		log.Println("Database connection closed.")
	}
}
