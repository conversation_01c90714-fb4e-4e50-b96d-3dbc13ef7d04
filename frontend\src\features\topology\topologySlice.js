import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";
import {
  ParseJsonToTopoData,
  getAllTopologyData,
  getTopologyClient,
  getTopologyDataByClient,
} from "../../utils/comman/dataMapping";

const prevNmsTopoNodesData = JSON.parse(
  localStorage.getItem("prevNmsTopoNodesData")
);

export const getTopologyData = createAsyncThunk(
  "newTopologySlice/getTopologyData",
  async (_, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/topology", {});
      const data = await response.data;
      if (response.status === 200) {
        console.log("topology data1", data);
        return data;
      } else {
        return thunkAPI.rejectWithValue(data);
      }
    } catch (e) {
      if (e.response && e.response.data !== "") {
        return thunkAPI.rejectWithValue(e.response.data);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const newTopologySlice = createSlice({
  name: "newTopologySlice",
  initialState: {
    isEditMode: false,
    gPhysics: false,
    clientsData: [],
    topologyData: {},
    reqClient: "All Network Service",
    gData: { nodes: [], edges: [] },
    savedNodes: prevNmsTopoNodesData === null ? [] : prevNmsTopoNodesData,
  },
  reducers: {
    setIsEditMode: (state, { payload }) => {
      state.isEditMode = payload;
    },
    setGPhysics: (state, { payload }) => {
      state.gPhysics = payload;
    },
    saveNodesPosition: (state, { payload }) => {
      state.savedNodes = [...state.savedNodes, ...payload];
      const arrayUniqueByKey = [
        ...new Map(state.savedNodes.map((item) => [item["id"], item])).values(),
      ];
      localStorage.setItem(
        "prevNmsTopoNodesData",
        JSON.stringify(arrayUniqueByKey)
      );
    },
    getGraphDataOnClientChange: (state, { payload }) => {
      state.reqClient = payload;
      const { nodes, links } =
        payload === "All Network Service"
          ? getAllTopologyData(state.topologyData)
          : getTopologyDataByClient(state.topologyData, payload);
      state.gData = { nodes, edges: links };
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getTopologyData.fulfilled, (state, { payload }) => {
      state.topologyData = ParseJsonToTopoData(payload);
      state.clientsData = [
        "All Network Service",
        ...getTopologyClient(ParseJsonToTopoData(payload)),
      ];
      const { nodes, links } =
        state.reqClient === "All Network Service"
          ? getAllTopologyData(ParseJsonToTopoData(payload))
          : getTopologyDataByClient(
              ParseJsonToTopoData(payload),
              state.reqClient
            );
      state.gData = { nodes, edges: links };
    });
  },
});

export const {
  setIsEditMode,
  setGPhysics,
  saveNodesPosition,
  getGraphDataOnClientChange,
} = newTopologySlice.actions;

export const newTopologySelector = createSelector(
  (state) => state.newTopology,
  ({ isEditMode, gPhysics, gData, savedNodes, clientsData, reqClient }) => ({
    isEditMode,
    gPhysics,
    gData,
    savedNodes,
    clientsData,
    reqClient,
  })
);

export default newTopologySlice;
