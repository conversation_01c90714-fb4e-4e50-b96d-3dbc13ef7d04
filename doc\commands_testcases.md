
 #                                     UnitTest and Intigration testcases

//-------------------------------------Beep------------------------------------------
## Beep command: 

### TestCase:TestBeepCmd_InvalidFormat
    Purpose: Ensures the command fails when the format is incorrect (no MAC or too many arguments).
    Input:
     example: beep 11-22-33-44-55-66 extra
              beep

    Expected Result : "error:invalid command arguments"
    Outcome: Passed

### TestCase:TestBeepCmd_InvalidMAC
    Purpose: Simulates a failure in MAC validation.
    Input:
     example:beep 11-GG-FF-22-33-44

    Expected Result :"error:invalid MAC address"
    Outcome: Passed

### TestCase:TestBeepCmd_UnsupportedModel 
    Purpose: Ensures the command fails when command run on unsupported model
    Input: 
          "beep 11-22-33-44-55-66"  //Rejects a command for a device with an unsupported model (e.g., "EHG2408")

    Expected Result : "error:does not support beep"
    Outcome: Passed

### Testcase:TestBeepCmd_WithAgent  //Verify the Capabilities: map[string]bool{"agent": true,}
   Purpose: Validates command generation when device has "agent" capability.
    Input:
         "beep 11-22-33-44-55-66"
    Expected Result : "agent beep mac-address"
    Outcome: Passed

### Testcase:TestBeepCmd_WithoutAgent  //Verify the Capabilities: map[string]bool{"gwd": true,}
  Purpose: Validates command generation when device has "gwd" capability.
    Input:
           "beep 11-22-33-44-55-66"

    Expected Result: "gwd beep mac-address"
    Outcome: Passed

### TestCase:TestBeepCmd_Integration_Success  //Based on Capabilities: map[string]bool{"agent": true,}
Purpose: Validates command generation when device has "agent" capability with intigration success.
    Input: 
           "beep 11-22-33-44-55-66"
    Expected Result : "agent beep mac-address"
    Outcome: Passed

### TestCase:TestBeepCmd_Integration_DeviceNotFound
    Purpose: Simulates case where MAC is valid but no device is found.
    Input: 
      example: beep 11-22-33-44-55-77

    Expected Result: "error: device not found"
    Outcome: Passed

### TestCase:TestBeepCmd_DBError
Purpose: Validates command of database checking with available database
    Input :  
      example: beep 11-22-33-44-55-77  // Let us assume this device does not exit in Devdata Base
  
    Expected Result: "error: device not found"
    Outcome: Passed

### TestCase:TestBeepCmd_MACCaseSensitivity
Purpose: Validates command generation when device has passing mac sensitivity
    Input:
      example: beep 11:22:33:44:55:66 or beep 11-22-33-dd-ff-ee

    Expected result: "agent beep mac-address" or "gwd beep mac-address"
    Outcome: Passed

//--------------------------------------Reset---------------------------------------------------

## Reset Command:
### TestCase: TestResetCmd_InvalidFormat
Purpose: Ensures the command fails when the format is incorrect (no MAC or too many arguments).
    Input Command:
               "reset" or "reset 11-22-33-44-55-66 extra"

    Expected Result: "error:invalid command arguments"
    Outcome: Passed

### TestCase: TestResetCmd_InvalidMAC
 Purpose: Simulates a failure in MAC validation.
    Input Command: "reset invalid-mac"
      Example: reset 11-GG-FF-22-33-44

    Expected Result: "error:invalid MAC address"
    Outcome: Passed

### TestCase: TestResetCmd_DeviceNotFound
Purpose: Simulates case where MAC is valid but no device is found.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-77 // Device with this MAC is not present

    Expected Result: "error: device not found"
    Outcome: Passed

### TestCase: TestResetCmd_DeviceLocked
Purpose: Simulates case where MAC is locked or not.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 //Device is found but is currently unlocked

    Expected Result: "agent reset mac-address"
    Outcome: Passed

### TestCase: TestResetCmd_WithAgent
Purpose: Validates command generation when device has "agent" capability.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // Device has capabilities map[string]bool{"agent": true}

    Expected Result: "agent reset mac-address"
    Outcome: Passed

### TestCase: TestResetCmd_WithoutAgent
Purpose: Validates command generation when device has "gwd" capability.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // Device has capabilities map[string]bool{"gwd": true}

    Expected Result: "gwd reset mac-address"
    Outcome: Passed

### TestCase: TestResetCmd_EHG2408RequiresAgent
Purpose: Ensures model EHG2408 fails if only gwd (no agent) is present.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // Device has capabilities map[string]bool{"gwd": true}

    Expected Result: "error:"must required agent"
    Outcome: Passed

### TestCase:TestResetCmd_Integration_AgentSuccess
Purpose: Full flow validation for agent-enabled device.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // Device has capabilities map[string]bool{"agent": true}

    Expected Result: "agent reset mac-address"
    Outcome: Passed

### TestCase:TestResetCmd_Integration_GWDSuccess
Purpose: Full flow validation for gwd-enabled device.
    Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // Device has capabilities map[string]bool{"gwd": true}

    Expected Result: "gwd reset mac-address"
    Outcome: Passed

### TestCase: TestResetCmd_Integration_ClientMismatch
Purpose: Full flow validation for client name on the device.
  Input Command: "reset mac-address"
    Example: reset 11-22-33-44-55-66 // ScannedBy: "client1", but clinet :client2 in cmdinfo passing

    Expected Result: "error:not scanned by the same client"
    Outcome: Passed

//--------------------------------------------Config Save---------------------------------------------
## Config Save

### TestCase: TestConfigSaveCmd_InvalidFormat
   Purpose: Ensures the command fails when the format is incorrect (no MAC or too many arguments).
   Input: No arguments or too many arguments
          "config save" or "config save 11-22-33-44-55-66 extra"
    Expected Result: Returns an error containing "invalid command arguments".
    Outcome: Passed

### TestCase: TestConfigSaveCmd_InvalidMAC
Purpose: Simulates a failure in MAC validation.
Input:
      config save 11-22-33-44-55-GG
Expected Result: Returns an error with status: "invalid MAC Address".
Outcome: Passed

### TestCase: TestConfigSaveCmd_DeviceNotFound
Purpose: Simulates case where MAC is valid but no device is found.
Input:
      config save 11-22-33-44-55-77
Expected Result: Returns an error with status: "device not found".
Outcome: Passed

### TestCase: TestConfigSaveCmd_AgentSuccess
Purpose: Tests successful command generation when device supports the agent capability.
Input:
      config save 11-22-33-44-55-66
Expected Result:
Command: "agent config save 11-22-33-44-55-66"
DevId: matches MAC
No error
Outcome: Passed

### TestCase: TestConfigSaveCmd_SwitchSuccess
Purpose: Tests fallback to switch config save if device has gwd capability.
Input:
      config save 11-22-33-44-55-66
Expected Result:
Command: "switch config save 11-22-33-44-55-66"
No error
Outcome: Passed

### TestCase: TestConfigSaveCmd_EHG2408RequiresAgent
Purpose: Ensures model EHG2408 fails if only gwd (no agent) is present.
Input:
    config save 11-22-33-44-55-66
Expected Result:
Error: "must required agent"
Status: contains "must required agent"
Outcome: Passed

// Integration Test Cases
### TestCase: TestConfigSaveCmd_Integration_AgentSuccess
Purpose: Full flow validation for agent-enabled device.
Input:
      config save 11-22-33-44-55-66
Expected Result:
Command: "agent config save 11-22-33-44-55-66"
DevId equals input MAC
No error
Outcome: Passed

### TestCase: TestConfigSaveCmd_Integration_SwitchSuccess
Purpose: Full flow validation for switch (GWD) device.
Input:
      config save 11-22-33-44-55-66
Expected Result:
Command: "switch config save <mac>"
No error
Outcome: Passed

//------------------------------------------Config User----------------------------------------
## Config user
### TestCase: TestConfigUserCmd_InvalidArguments
Purpose: Validates command argument count and structure.
Input:
    Missing args ("config user") Incomplete args (missing password) Extra args (more than 5 args)
    config user 11-22-33-44-55-66 admin default extra

Expected Result:
Returns an error with "invalid command arguments" in the result status.
Outcome: Passed

### TestCase: TestConfigUserCmd_InvalidMAC
Purpose: Simulates failure during MAC address validation.
Input:
      config user 11-22-33-44-55-77 admin default
Expected Result:
Returns an error with status: "invalid MAC Address".
Outcome: Passed

### TestCase: TestConfigUserCmd_ShortUsername
Purpose: Ensures the username meets minimum length requirements.
Input:
      config user 11-22-33-44-55-66 sasi default
Expected Result:
Result status contains: "username must be more than 5 characters".
Outcome: Passed

### TestCase: TestConfigUserCmd_ShortPassword
Purpose: Ensures the password meets minimum length requirements.
Input:
      config user 11-22-33-44-55-66 admin sasi12
Expected Result:
Result status contains: "password must be more than 7 characters".
Outcome: Passed

### TestCase: TestConfigUserCmd_Valid
Purpose: Validates a properly formatted and functional config user command.
Input:
      config user 11-22-33-44-55-66 admin default
Expected Result:
No error
Result command matches: "config user <mac> username password"
Outcome: Passed

### TestCase: TestConfigUserCmd_DeviceNotFound
Purpose: Simulates scenario where the device is not present.
Input:
      config user 11-22-33-44-55-77 admin default
Expected Result:
Returns an error with status: "device not found".
Outcome: Passed

### TestCase: TestConfigUserAuthCmd_Success
Purpose: Verifies authentication logic and insertAndPublish interaction.
Input:
      config user 11-22-33-44-55-66 admin default
Mocked Functions:
insertAndPublish checks if correct username & password are set
Expected Result:
Result status: "ok"
Outcome: Passed

// Integration Test
### TestCase: TestConfigUser_Integration_Success
Purpose: Full command flow validation — parsing, validation, and authentication.
Input:
      config user 11-22-33-44-55-66 admin default
Expected Result:
No error in validation
Authenticated result has:
Command: "config user <mac> validuser validpass123"
DevId: matches MAC
Status: "ok"
Outcome: Passed

//----------------------------------MtdErase------------------------------------------------
## MtdErase
### TestCase: TestMtdEraseCmd_InvalidFormat
Purpose: Validates argument count and structure.
Input: No arguments: "mtderase" or too many arguments: "mtderase <mac> extra"
    mtderase 
    mtderase 11-22-33-44-55-66 extra

Expected Result:
Error with status: "invalid command arguments"
Outcome: Passed

### TestCase: TestMtdEraseCmd_InvalidMAC
Purpose: Verifies handling of invalid MAC addresses.
Input:
      mtderase 11-22-33-44-55-GG
Expected Result:
Error with status: "invalid MAC Address"
Outcome: Passed

### TestCase: TestMtdEraseCmd_DeviceNotFound
Purpose: Handles scenarios when the device is missing.
Input:
      mtderase 11-22-33-44-55-77
Expected Result:
Error with status: "device not found"
Outcome: Passed
### TestCase: TestMtdEraseCmd_DeviceLocked
Purpose: Checks for lock status on the device (mocked unlocked).
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
Command should include "agent mtderase", indicating success under unlocked condition.
Outcome: Passed

### TestCase: TestMtdEraseCmd_WithAgent
Purpose: Validates command generation when device has "agent" capability.
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
No error. Result command should start with: "agent mtderase"
Outcome: Passed

### TestCase: TestMtdEraseCmd_EHG2408RequiresAgent
Purpose: Special case where device model is EHG2408, which requires "agent" even if it has other capabilities.
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
Error with message and status: "must required agent"
Outcome: Passed

### TestCase: TestMtdEraseCmd_WithoutAgent
Purpose: Normal case for devices with only "gwd" capability.
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
No error. Result command should start with: "gwd mtderase"
Outcome: Passed

//Integration Tests
### TestCase: TestMtdEraseCmd_Integration_AgentSuccess
Purpose: End-to-end validation for a device with "agent" capability.
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
No error. Result command should start with: "agent mtderase"
Outcome: Passed

### TestCase: TestMtdEraseCmd_Integration_GWDSuccess
Purpose: End-to-end validation for a device with only "gwd" capability.
Input:
      mtderase 11-22-33-44-55-66
Expected Result:
No error. Result command should start with: "gwd mtderase"
Outcome: Passed

### TestCase: TestMtdEraseCmd_Integration_ClientMismatch
Purpose: Ensures command fails when CmdInfo.Client does not match DevInfo.ScannedBy.
Input:
      mtderase 11-22-33-44-55-66  
Expected Result:
Error with status: "not scanned by the same client"
Outcome: Passed

//---------------------------------------Network Setting------------------------------------
## Network Setting
### TestCase: TestConfigNetworkCmd_InvalidArguments
Purpose: Validate that the command parser identifies and rejects malformed or incomplete config network set commands.
Input:
    config network set
		config network set 11-22-33-44-55-66 *************
		config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0 extra

Expected Result:
An error must be returned. result.Status should contain the phrase: "invalid command arguments".
Outcome: Passed

### TestCase: TestConfigNetworkCmd_InvalidMAC
Purpose: Ensure that the command fails when an invalid or unrecognized MAC address is passed.
Input:
      config network set 11-22-33-44-55-GG ************* ************* ************* *********** hostname 0
Expected Result:
An error must be returned. result.Status should contain: "invalid MAC Address".
Outcome: Passed

### TestCase: TestConfigNetworkCmd_DeviceLocked
Purpose: Confirm the command handling behaves correctly based on device lock status, simulating an unlocked device.
Input:
      config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
No device lock error should occur. result.Command should begin with: "agent config network set".
Outcome: Passed

### TestCase: TestConfigNetworkCmd_InvalidIPs
Purpose: Validate that invalid IP address components (current IP, new IP, mask, gateway) are correctly rejected.
Input:
      config network set 11-22-33-44-55-66 192.168.1.400 192.168.1111.101 255.255.256.0 256.168.1.1 hostname 0

Expected Result:
An error must be returned. result.Status must contain:
"error: IP Address: <invalid value> - Invalid" depending on the field.
Outcome: Passed

### TestCase: TestConfigNetworkCmd_InvalidDHCP
Purpose: Ensure DHCP field accepts only valid values (0 or 1), and invalid values are rejected.
Input:
config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname one 
Expected Result:
result.Status should contain the message: "invalid DHCP value".
Outcome: Passed

### TestCase: TestConfigNetworkCmd_AgentSuccess
Purpose: Verify successful command generation when a device has the "agent" capability and all inputs are valid.
Input:
    config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
No error. result.Command should start with "agent config network set" and include the new IP, hostname, and DHCP value.
Outcome: Passed

### TestCase: TestConfigNetworkCmd_GWDSuccess
Purpose: Validate successful command generation for a device with only "gwd" capability and valid parameters.
Input:
      config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
No error. result.Command should start with "gwd config network set" and include current and new IPs, mask, gateway, and hostname.
Outcome: Passed

### TestCase: TestConfigNetworkCmd_EHG2408RequiresAgent
Purpose: Enforce that model EHG2408 must support the "agent" capability even if it has "gwd".
Input:
      config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
An error must be returned. Both err and result.Status should contain: "must required agent".
Outcome: Passed

### TestCase: TestConfigNetworkCmd_Integration_AgentSuccess
Purpose: End-to-end validation for a device with "agent" capability and full valid input set.
Input:
    config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
No error. result.Command should equal:
"agent config network set 11-22-33-44-55-66 ************* ************* *********** hostname 1"
Outcome: Passed

### TestCase: TestConfigNetworkCmd_Integration_GWDSuccess
Purpose: End-to-end validation for a device with "gwd" capability using all valid arguments.
Input:
      config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname 0
Expected Result:
No error. result.Command should equal:
"gwd config network set 11-22-33-44-55-66 ************* ************* ************* *********** hostname"
Outcome: Passed

//------------------------------------------Syslog Configuration Setting-----------------------------
## Syslog Configuration Setting

### TestCase: TestConfigSyslogSetCmd_ArgumentValidation
Purpose: Validate the argument count for the config syslog set command.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 extra
      config syslog set 11-22-33-44-55-66 1 ************* 514

Expected Result:
Valid command should pass without errors.
Missing or extra arguments should trigger proper error messages.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_StatusValidation
Purpose: Validate the status argument (0 or 1) of the config syslog set command.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Valid statuses (0, 1) should pass.
Invalid values (e.g., 2, "enable", "") should return "invalid syslog status value" error.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_IPValidation
Purpose: Validate the IP address argument in the config syslog set command.
Input:
      config syslog set 11-22-33-44-55-66 1 192.168.1.256 514 6 1 

Expected Result:
Properly formatted IPv4 addresses should pass.
Invalid IPs (e.g., 256.x.x.x, incomplete, or non-IP strings) should fail with Invalid error mentioning the IP.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_PortValidation
Purpose: Validate the port number for the syslog server.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Valid port numbers (1–65535) should pass.
Invalid ports (e.g., 0, 65536, non-numeric) should return "invalid server port" error.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_LogLevelValidation
Purpose: Validate the syslog log level argument.
input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Valid levels (0–7) should pass.
Invalid levels (e.g., -1, 8, non-numeric) should return "invalid log level" error.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_LogToFlashValidation
Purpose: Validate the LogToFlash flag in the config syslog set command.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Valid values (0 or 1) should pass.
Other values (e.g., 2, "true", "") should return "invalid LogToFlash value" error.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_AgentPath
Purpose: Integration test to verify command routing through agent capability path.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Command should be prefixed with "agent" and executed successfully.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_SNMPPath
Purpose: Integration test to verify command routing through SNMP (non-agent) path.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Command should be prefixed with "snmp" and executed successfully.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_EHG2408RequiresAgent
Purpose: Special integration test for model EHG2408 to verify agent requirement.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Command must fail with "must required agent" error if agent capability is missing.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_EmptyFields
Purpose: Validate behavior when mandatory fields (MAC, status, IP, port, etc.) are empty.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 

Expected Result:
Each missing field should trigger appropriate error messages.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_CommandInjection
Purpose: Security test to validate protection against command injection.
Input:
     config syslog set 11-22-33-44-55-66 1 ************* 514 6 1 

Expected Result:
Malicious inputs in MAC, status, IP, and port fields should trigger validation errors.
Outcome: Passed

### TestCase: TestConfigSyslogSetCmd_ConcurrentAccess
Purpose: Concurrency test to ensure thread-safe handling of multiple parallel syslog configuration commands.
Input:
      config syslog set 11-22-33-44-55-66 1 ************* 514 6 1

Expected Result:
All 100 parallel executions should complete without error or data race issues.
Outcome: Passed

//------------------------------------Syslog Configuration Get------------------------------------
## Syslog Configuration Get

### TestCase: TestConfigSyslogGetCmd_ArgumentValidation
Purpose: Verify command argument count validation.
Input:
"config syslog get 11-22-33-44-55-66" (valid)
"config syslog get" (missing MAC)
"config syslog get 11-22-33-44-55-66 extra" (extra argument)

Expected Output:

No error for valid command.
Error indicating invalid argument count for missing or extra arguments.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_MACValidation
Purpose: Validate MAC address format in command.
Input:
"config syslog get 11-22-33-44-55-66" (valid MAC)
"config syslog get invalid-mac" (invalid MAC)
"config syslog get " (empty MAC)

Expected Output:
No error for valid MAC.
Error for invalid or empty MAC.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_AgentPath
Purpose: Confirm command routing for “agent” devices.
Input:
"config syslog get 11-22-33-44-55-66" for a device marked as agent.

Expected Output:
Command prefix changed to "agent config syslog get 11-22-33-44-55-66".
No error.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_SNMPPath
Purpose: Confirm command routing for “gwd” (SNMP) devices.
Input:
"config syslog get 11-22-33-44-55-66" for a device marked as gwd.

Expected Output:
Command prefix changed to "snmp config syslog get 11-22-33-44-55-66".
No error.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_EmptyCommand
Purpose: Validate handling of empty command strings.
Input:
"" (empty string)

Expected Output:
Error indicating invalid command arguments.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_CommandInjection
Purpose: Prevent command injection attacks.
Input:
"config syslog get 11-22-33-44-55-66; rm -rf /"
"config syslog get $(rm -rf /)"

Expected Output:
Error rejecting the injected command strings.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_ConcurrentAccess
Purpose: Ensure thread safety under concurrent validation.
Input:
100 parallel validations of "config syslog get 11-22-33-44-55-66"

Expected Output:
All validations succeed with no errors or race conditions.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_Integration_AgentSuccess
Purpose: Verify full integration path for agent devices.
Input:
"config syslog get 11-22-33-44-55-66" on an agent device.

Expected Output:
Command returned as "agent config syslog get 11-22-33-44-55-66".
No error.
Outcome: Passed

### TestCase: TestConfigSyslogGetCmd_Integration_SNMPSuccess
Purpose: Verify full integration path for SNMP devices.
Input:
"config syslog get 11-22-33-44-55-66" on a gwd device.

Expected Output:
Command returned as "snmp config syslog get 11-22-33-44-55-66".
No error.
Outcome: Passed

//-------------------------------------Firmware Update--------------------------------------
## Firmware Update
// 1. Unit Test Cases
### TestCase: TestFWUpdateCmd_ArgumentValidation
Purpose: Validate correct number of arguments for the firmware update command.

Valid command
Input: firmware update 11-22-33-44-55-66 firmware.bin
Expected: No error
Outcome: Passed

Missing MAC and file
Input: firmware update
Expected: Error: expected 4 but got 2
Outcome: Passed

Missing file
Input: firmware update 11-22-33-44-55-66
Expected: Error: expected 4 but got 3
Outcome: Passed

Extra arguments
Input: firmware update 11-22-33-44-55-66 firmware.bin extra
Expected: Error: expected 4 but got 5
Outcome: Passed

### TestCase: TestFWUpdateCmd_MACValidation
Purpose: Ensure the MAC address format is valid and recognized.

Valid MAC
Input: firmware update 11-22-33-44-55-66 firmware.bin
Expected: No error
Outcome: Passed

Invalid MAC
Input: firmware update invalid-mac firmware.bin
Expected: Error: invalid MAC address
Outcome: Passed

Empty MAC
Input: firmware update firmware.bin
Expected: Error: invalid MAC address
Outcome: Passed

### TestCase: TestFWUpdateCmd_DeviceLookup
Purpose: Verify behavior when the device MAC is not found in the device database.
Input: firmware update 11-22-33-44-55-66 firmware.bin

Expected: Error: device not found
Outcome:Passed

//Path Validation Tests
### TestCase: TestFWUpdateCmd_AgentPath
Purpose: Confirm firmware update command for devices with "agent" capability is correctly handled.
Input: Agent device, firmware update 11-22-33-44-55-66 firmware.bin

Expected: Command prefixed with agent
Outcome: Passed

### TestCase: TestFWUpdateCmd_GWDPath
Purpose: Confirm firmware update command for devices with "gwd" capability is correctly handled.
Input: GWD device, firmware update 11-22-33-44-55-66 firmware.bin

Expected: Command prefixed with gwd
Outcome:Passed

### TestCase: TestFWUpdateCmd_EHG2408RequiresAgent
Purpose: Ensure EHG2408 model devices require "agent" capability, not just "gwd".
Input: EHG2408 with only gwd capability

Expected: Error indicating must required agent
Outcome:Passed

// Edge Case Tests
### TestCase: TestFWUpdateCmd_FileNameValidation
Purpose: Validate allowed file names and extensions for firmware files.
Valid filename
Input: firmware update 11-22-33-44-55-66 firmware.bin

Expected: No error
Outcome: Passed
Valid versioned filename
Input: firmware update 11-22-33-44-55-66 fw_v1.2.3.bin
Expected: No error
Outcome: Passed

//Security Tests
### TestCase: TestFWUpdateCmd_CommandInjection
Purpose: Prevent command injection attacks by validating inputs.
Injected MAC
Input: firmware update 11-22-33-44-55-66; rm -rf / firmware.bin

Expected: Error due to injection attempt
Outcome: Passed

Injected filename
Input: firmware update 11-22-33-44-55-66 firmware.bin; shutdown
Expected: Error due to injection attempt
Outcome: Passed

// Integration Test Cases
### TestCase: TestFWUpdateCmd_Integration_AgentSuccess
Purpose: Verify full workflow for agent-capable devices processes the command correctly.
Input: Agent device, firmware update 11-22-33-44-55-66 firmware.bin

Expected: Command prefixed with agent and no error
Outcome: Passed

### TestCase: TestFWUpdateCmd_Integration_GWDSuccess
Purpose: Verify full workflow for gwd-capable devices processes the command correctly.
Input: GWD device, firmware update 11-22-33-44-55-66 firmware.bin

Expected: Command prefixed with gwd and no error
Outcome: Passed

// Concurrency Tests
### TestCase: TestFWUpdateCmd_ConcurrentAccess
Purpose: Ensure thread safety and correct behavior under concurrent command validations.
Input: 100 concurrent firmware update commands for the same MAC with different filenames
      firmware update 11-22-33-44-55-66 firmware.bin

Expected: No errors or race conditions during concurrent execution
Outcome: Passed

### TestCase: TestSwitchCmd_ArgumentValidation
Purpose: Verify correct handling of various argument formats and error conditions for switch commands.
Input:

"switch" (missing arguments)
"switch config save" (invalid format)
"switch config save 11-22-33-44-55-66 extra" (extra arguments)

Expected: Appropriate error messages for invalid commands
Outcome: Passed

### TestCase: TestSwitchCmd_MACValidation
Purpose: Validate MAC address format handling in switch commands.
Input:

"switch 11-22-33-44-55-66 show ip" (valid MAC)
"switch invalid-mac show ip" (invalid MAC)
"switch show ip" (empty MAC)

Expected:

Valid MAC: Command proceeds

Invalid/Empty MAC: "invalid MAC Address" error
Outcome: Passed

### TestCase: TestSwitchCmd_DeviceLookup
Purpose: Verify device existence checking functionality.
Input: "switch 11-22-33-44-55-77 show ip" (non-existent device)
Expected: "device not found" error
Outcome: Passed

### TestCase: TestSwitchCmd_EHG2408Restrictions
Purpose: Validate EHG2408 model-specific command restrictions.
Input:

"switch 11-22-33-44-55-66 snmp enable"
"switch 11-22-33-44-55-66 no snmp"
"switch 11-22-33-44-55-66 snmp trap"
"switch 11-22-33-44-55-66 show snmp trap"

Expected: "must required agent" error for all SNMP-related commands
Outcome: Passed

### TestCase: TestSwitchCmd_CommandConversion
Purpose: Verify command conversion and error handling.
Input:

"switch 11-22-33-44-55-66 show ip"
"switch 11-22-33-44-55-66 configure terminal"

Expected: Connection attempt errors (TCP dial failures)
Outcome: Passed

### TestCase: TestSwitchCmd_SendSwitchErrors
Purpose: Verify error propagation from SendSwitch function.
Input: "switch 11-22-33-44-55-66 show ip" (with mocked error)
Expected: "switch cli not available" error
Outcome: Passed

### TestCase: TestSwitchCmd_ConvertErrors
Purpose: Verify error handling in command conversion.
Input: "switch 11-22-33-44-55-66 invalid-command"
Expected: "invalid command" error
Outcome: Passed

### TestCase: TestValidateSnmpEnableDisable
Purpose: Validate correct command conversion for SNMP enable/disable operations on agent-capable devices.
Input:

"snmp enable 11-22-33-44-55-66"
"snmp disable 11-22-33-44-55-66"

Expected:

Enable → "agent snmp enable 11-22-33-44-55-66 1"

Disable → "agent snmp enable 11-22-33-44-55-66 0"
Outcome: Passed

### TestCase: TestValidateSnmpTrapCommands
Purpose: Verify proper handling of SNMP trap server management commands.
Input:

"snmp trap add 11-22-33-44-55-66 ************* 162 public"
"snmp trap del 11-22-33-44-55-66 ************* 162 public"
"snmp trap get 11-22-33-44-55-66"

Expected: Correct conversion to agent command format
Outcome: Passed

### TestCase: TestValidateSnmpSyslogCommands
Purpose: Test syslog configuration command validation.
Input:

"snmp config syslog set 11-22-33-44-55-66 1 10.0.0.1 514 6 1"
"snmp config syslog get 11-22-33-44-55-66"

Expected: Successful command parsing without errors
Outcome: Passed

### TestCase: TestValidateSnmpOptions
Purpose: Validate SNMP option setting commands.
Input: "snmp options 161 3 2c 30"
Expected: Successful command parsing
Outcome: Passed

### TestCase: TestValidateSnmpCommunityCommands
Purpose: Test community string management commands.
Input:

"snmp communities 11-22-33-44-55-66"
"snmp update community 11-22-33-44-55-66 public private"

Expected: Successful command parsing
Outcome: Passed

### TestCase: TestValidateLegacySnmpCommand
Purpose: Verify backward compatibility with legacy SNMP command format.
Input: "snmp ***********"
Expected: Successful command parsing
Outcome: Passed

### TestCase: TestValidateSnmpNonAgentDevice
Purpose: Validate command conversion for non-agent devices.
Input:

"snmp enable 11-22-33-44-55-66" → "switch 11-22-33-44-55-66 snmp enable"
"snmp disable 11-22-33-44-55-66" → "switch 11-22-33-44-55-66 no snmp enable"
"snmp trap add..." → switch command format

Expected: Proper conversion to switch CLI commands
Outcome: Passed

### TestCase: TestValidateSnmpEdgeCases
Purpose: Verify handling of boundary values in commands.
Input:

Port extremes (1, 65535)
Log level extremes (0, 7)

Expected: Successful processing of valid edge cases
Outcome: Passed

### TestCase: TestCommandParsing
Purpose: Validate basic command parsing and error handling.
Input:

Empty command
Invalid command
Short command

Expected: Appropriate error messages
Outcome: Passed

### TestCase: TestDeviceCapabilities_Agent
Purpose: Verify agent-specific command handling.
Input: "snmp enable 11-22-33-44-55-66" on agent device
Expected: Proper agent command format conversion
Outcome: Passed

### TestCase: TestDeviceCapabilities_GWD
Purpose: Verify non-agent device command handling.
Input: "snmp enable 11-22-33-44-55-66" on GWD device
Expected: Proper switch command format conversion
Outcome: Passed

### TestCase: TestDeviceLocking
Purpose: Validate error handling for locked devices.
Input: SNMP command on locked device
Expected: Appropriate error response
Outcome: Passed

### TestCase: TestInvalidNetworkParams
Purpose: Verify input validation for network parameters.
Input:

Invalid IP (256.168.1.1)
Invalid ports (0, 65536)

Expected: Error messages for invalid inputs
Outcome: Passed

### TestCase: TestMissingFields
Purpose: Validate required field checking.
Input:

Missing MAC
Missing IP
Missing community string

Expected: Appropriate error messages
Outcome: Passed

### TestCase: TestUnknownSubcommand
Purpose: Verify handling of invalid subcommands.
Input: "snmp invalid 11-22-33-44-55-66"
Expected: Error for unknown subcommand
Outcome: Passed

### TestCase: TestEndToEndSNMPFlow
Purpose: Validate complete SNMP configuration workflow.
Steps:

Enable SNMP
Add trap server
Verify configuration
Expected: Successful execution of all steps
Outcome: Passed

### TestCase: TestGwdBeepCmd_DeviceNotFound
Purpose: Verify error handling when attempting to beep a non-existent device.
Input: "gwd beep 11-22-33-44-55-77" (non-existent MAC)
Expected: "error: device not found"
Outcome: Passed

### TestCase: TestGwdBeepCmd_UnsupportedModelEHG2408
Purpose: Validate beep command rejection for unsupported EHG2408 model.
Input: "gwd beep 11-22-33-44-55-66" on EHG2408 device
Expected: "error: EHG2408 device does not support beep"
Outcome: Passed

### TestCase: TestGwdBeepCmd_UnsupportedModelEHG65
Purpose: Validate beep command rejection for unsupported EHG65 model.
Input: "gwd beep 11-22-33-44-55-66" on EHG65 device
Expected: "error: EHG65 device does not support beep"
Outcome: Passed

### TestCase: TestGwdBeepCmd_ValidCommandDifferentMAC
Purpose: Verify device lookup failure for different MAC address.
Input: "gwd beep AA-BB-CC-DD-EE-FF"
Expected: "error: device not found"
Outcome: Passed

### TestCase: TestGwdBeepCmd_InvalidCommandFormat
Purpose: Validate argument count validation.
Input: "gwd beep" (missing MAC)
Expected: "error: invalid command arguments, expected 3 but got 2"
Outcome: Passed

### TestCase: TestGwdBeepCmd_InvalidMACFormat
Purpose: Verify MAC address format validation.
Input: "gwd beep 11-22-33-44-55-6G" (invalid MAC)
Expected: "error: invalid MAC Address"
Outcome: Passed

### TestCase: TestGwdResetCmd_DeviceNotFound
Purpose: Verify reset command failure for non-existent device.
Input: "gwd reset 11-22-33-44-55-77"
Expected: "error: device not found"
Outcome: Passed

### TestCase: TestGwdResetCmd_DifferentValidMAC
Purpose: Validate device lookup with different MAC format.
Input: "gwd reset AA-BB-CC-DD-EE-FF"
Expected: "error: device not found"
Outcome: Passed

### TestCase: TestGwdMtdEraseCmd_DeviceNotFound
Purpose: Verify MTD erase command failure for non-existent device.
Input: "gwd mtderase AA-BB-CC-DD-EE-FF"
Expected: "error: device not found"
Outcome: Passed

### TestCase: TestGwdMtdEraseCmd_InvalidMACFormat
Purpose: Validate MAC format checking in MTD erase command.
Input: "gwd mtderase 11-22-33-44-55-GG"
Expected: "error: invalid MAC Address"
Outcome: Passed

### TestCase: TestGwdConfigNetworkCmd_InvalidInputs
Purpose: Comprehensive network configuration input validation.
Test Cases:

Too few arguments

Invalid current IP (300.0.50.1)

Invalid new IP (300.0.50.2)

Invalid netmask (255.255.300.0)

Invalid gateway (300.0.50.1)

Expected: Appropriate error messages for each invalid case
Outcome: Passed

### TestCase: TestGwdFirmwareCmd_RealErrorCases
Purpose: Verify firmware update error scenarios.
Test Cases:

Device not found

Device locked

Invalid URL format

Unsupported protocol (FTP)

Expected: Appropriate error/status messages
Outcome: Passed

### TestCase: TestGwdFirmwareCmd_RealEdgeCases
Purpose: Validate firmware command edge cases.
Test Cases:

Too few arguments

Incomplete MAC (11-22-33)

Invalid MAC format (contains 'HH')

Expected: Appropriate format validation errors
Outcome: Passed