## Anomaly services dashboard
We should have a UI that list all anomaly services and their statistics.
**GET /api/v1/anomaly/statistics**
Response: 
I think frontend can display [since, clientname] and a [settings] button. the settings button can show the settings of the service.
Fields in the settings panel:
- db_settings: 
  - Host: string
  - Port: int
  - User: string
  - Password: string
- detect: bool (turn on/off anomaly auto detection)
- pull_interval_mins: int (anomaly auto detection interval)
- distance: (score) float64, should be between 0.1 and 0.9

Others fields are not editable and do not display in the UI.

```json
[
    {
        "since": "2024-03-05T17:26:44+08:00",
        "total_messages": 0,
        "anomaly_messages": 0,
        "clientname": "an1",
        "settings": {
            "db_settings": {
                "Host": "localhost",
                "Port": 5432,
                "User": "postgres",
                "Password": "nimblpass",
                "Database": "embeddings",
                "VectorDimension": {
                    "vectors": 1536
                }
            },
            "last_error": "",
            "root": "http://localhost:27182",
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0aW1lc3RhbXAiOiIyMDI0LTAzLTA1VDE3OjI2OjQ0KzA4OjAwIiwidXNlciI6ImFkbWluIn0.3l7xG8yck2BaZu975cOJtm3fhkwoxy64J-MTWV-wkkE",
            "score": 0.4,
            "pull_interval_mins": 5,
            "detect": true
        }
    }
]
```
To set the settings, frontend should POST /api/v1/anomaly/settings with the settings data.
Body example: just values need to change omit the rest
```json
{
		"client":"service_name", "settings":{
		"db_settings": {
			"host": "localhost",
			"port": 5432,
			"user": "postgres",
			"password": "password",
		},
		"pull_interval": 60,
		"detect":true,
		}
}
```

Anomaly services UI should provide a way that allow user input a file URL to detetct anomalies.
Use this API to ask specific anomaly service to detect anomalies in a file.  
POST /api/v1/anomaly/detect  

	body: {"url": "file://rawlog.log", "distance":0.4, "client":"anomaly_service1"}
	- url : The file that consis of syslog messages
	- distance : distance threshold for anomaly detection (optional, default 0.4)
	- client : name of anomaly service to be used (optional, default random)

	response:
		anomaly analyse "file://rawlog.log 0.4
	send logs to anomaly service for analysis


## Vector Table
Have a table can show all vectors
Can modify vector's data, especially 'normal' field and reason field.

- To modify vector's data, frontend should GET /api/v1/anomaly/vectors?id={vector's id} to get the vector's data, then modify the data and POST /api/v1/anomaly/vectors to update the vector's data.

endpoint: 
**GET /api/v1/anomaly/vectors**
Query Parameters:
// id: int64
// client: service's name
// page: int  default 1
// pageSize: int default 10
// count: int default 0 (0 means all)
// sortColumn: string default id
// sortDirection: string default asc

ex: http://localhost:27182/api/v1/anomaly/vectors?client=an1&pageSize=3&page=2

**POST /api/v1/anomaly/vectors**
request body:
client: anomaly services name, can get from GET /api/v1/anomaly/statistics 
```json
{
  "client": "an1",
  "item":{
    "id": 123,
    "data": {
      "normal": false,
      "reason": "authentication failure",
      "text": "authentication failure; logname= uid=0 euid=0 tty=NODEVssh ruser= rhost=61-220-159-99.hinet-ip.hinet.net  user=root"
    },
  }
}
```

// id is optional, if id is not set, insert a new vector
// Response:
// {"data":{"id":2},"error":"","success":true}



## Reports
GET /api/v1/anomaly/report?source={regex}  filter report.Source match regex
GET /api/v1/anomaly/report    Get all reports