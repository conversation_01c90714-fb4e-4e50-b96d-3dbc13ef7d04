-- *****************************************************************
-- PSEC-MIB:  
-- ****************************************************************

MGMT-PSEC-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPsecUserBitmaskType FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    MGMTVlanOrZero FROM MGMT-TC
    ;

mgmtPsecMib MODULE-IDENTITY
    LAST-UPDATED "201801260000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MIB for Port Security"
    REVISION    "201801260000Z"
    DESCRIPTION
        "Added support for static and sticky MACs"
    REVISION    "201610240000Z"
    DESCRIPTION
        "Restructured entire MIB in a non-backward-compatible way"
    REVISION    "201606020000Z"
    DESCRIPTION
        "Support SNMP trap"
    REVISION    "201412100000Z"
    DESCRIPTION
        "Remove user of DHCP snooping"
    REVISION    "201412080000Z"
    DESCRIPTION
        "Add users in status port table"
    REVISION    "201410130000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 66 }


MGMTPsecMacType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The MAC type determines how the entry is learned."
    SYNTAX      INTEGER { dynamic(0), static(1), sticky(2) }

MGMTPsecViolationMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The violation mode determines what should happen when a limit is
         exceeded."
    SYNTAX      INTEGER { protect(0), restrict(1), shutdown(2) }

mgmtPsecMibObjects OBJECT IDENTIFIER
    ::= { mgmtPsecMib 1 }

mgmtPsecCapabilities OBJECT IDENTIFIER
    ::= { mgmtPsecMibObjects 1 }

mgmtPsecCapabilitiesUsers OBJECT-TYPE
    SYNTAX      MGMTPsecUserBitmaskType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bitmask indicating the internal modules (a.k.a. users) included in
         this flavor of the application."
    ::= { mgmtPsecCapabilities 1 }

mgmtPsecCapabilitiesPoolSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of Port Security-controlled <VLAN, MAC> entries."
    ::= { mgmtPsecCapabilities 2 }

mgmtPsecCapabilitiesLimitMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum limit on number of <VLAN, MAC> entries a given interface
         can be configured to."
    ::= { mgmtPsecCapabilities 3 }

mgmtPsecCapabilitiesLimitMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum limit on number of <VLAN, MAC> entries a given interface
         can be configured to."
    ::= { mgmtPsecCapabilities 4 }

mgmtPsecCapabilitiesViolateLimitMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum number of violating <VLAN, MAC> entries a given interface
         can be configured to."
    ::= { mgmtPsecCapabilities 5 }

mgmtPsecCapabilitiesViolateLimitMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of violating <VLAN, MAC> entries a given interface
         can be configured to."
    ::= { mgmtPsecCapabilities 6 }

mgmtPsecCapabilitiesAgeTimeMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum allowed age time value."
    ::= { mgmtPsecCapabilities 7 }

mgmtPsecCapabilitiesAgeTimeMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum allowed age time value."
    ::= { mgmtPsecCapabilities 8 }

mgmtPsecCapabilitiesHoldTimeMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum allowed hold time value."
    ::= { mgmtPsecCapabilities 9 }

mgmtPsecCapabilitiesHoldTimeMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum allowed hold time value."
    ::= { mgmtPsecCapabilities 10 }

mgmtPsecConfig OBJECT IDENTIFIER
    ::= { mgmtPsecMibObjects 2 }

mgmtPsecConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtPsecConfig 1 }

mgmtPsecConfigGlobalsMain OBJECT IDENTIFIER
    ::= { mgmtPsecConfigGlobals 1 }

mgmtPsecConfigGlobalsMainEnableAging OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When TRUE, learned, forwarding entries are subject to aging - FALSE if
         not."
    ::= { mgmtPsecConfigGlobalsMain 1 }

mgmtPsecConfigGlobalsMainAgingPeriodSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If aging is enabled (with enableAging), this is the aging period in
         seconds. Valid range is defined by capabilities' ageTimeMin and
         ageTimeMax members."
    ::= { mgmtPsecConfigGlobalsMain 2 }

mgmtPsecConfigGlobalsMainHoldTimeSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Denies entries are held in a blocking state for this amount of time,
         measured in seconds. Valid range is defined by capabilities'
         holdTimeMin and holdTimeMax members."
    ::= { mgmtPsecConfigGlobalsMain 3 }

mgmtPsecConfigInterfaces OBJECT IDENTIFIER
    ::= { mgmtPsecConfig 2 }

mgmtPsecConfigInterfacesTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPsecConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-interface configuration."
    ::= { mgmtPsecConfigInterfaces 1 }

mgmtPsecConfigInterfacesEntry OBJECT-TYPE
    SYNTAX      MGMTPsecConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the port-security configuration for one interface."
    INDEX       { mgmtPsecConfigInterfacesIfIndex }
    ::= { mgmtPsecConfigInterfacesTable 1 }

MGMTPsecConfigInterfacesEntry ::= SEQUENCE {
    mgmtPsecConfigInterfacesIfIndex        MGMTInterfaceIndex,
    mgmtPsecConfigInterfacesEnabled        TruthValue,
    mgmtPsecConfigInterfacesLimit          Unsigned32,
    mgmtPsecConfigInterfacesViolateLimit   Unsigned32,
    mgmtPsecConfigInterfacesViolationMode  MGMTPsecViolationMode,
    mgmtPsecConfigInterfacesSticky         TruthValue
}

mgmtPsecConfigInterfacesIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecConfigInterfacesEntry 1 }

mgmtPsecConfigInterfacesEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether port-security is enabled for this interface. "
    ::= { mgmtPsecConfigInterfacesEntry 2 }

mgmtPsecConfigInterfacesLimit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Maximum number of MAC addresses allowed on this interface. Valid range
         is given by capabilities' limitMin and limitMax members."
    ::= { mgmtPsecConfigInterfacesEntry 3 }

mgmtPsecConfigInterfacesViolateLimit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Maximum number of violating MAC addresses allowed on this interface.
         This is only used when violationMode is 'restrict'. Valid range is
         given by capabilities' violateLimitMin and violateLimitMax members."
    ::= { mgmtPsecConfigInterfacesEntry 4 }

mgmtPsecConfigInterfacesViolationMode OBJECT-TYPE
    SYNTAX      MGMTPsecViolationMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action to take if number of MAC addresses exceeds the limit. protect(0)
         Do nothing, except disallowing further clients. restrict(1) Keep
         recording the violating MAC addresses, which are kept until
         holdTimeSecs expires. shutdown(2) Shut-down the interface."
    ::= { mgmtPsecConfigInterfacesEntry 5 }

mgmtPsecConfigInterfacesSticky OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to automatically convert dynamic entries to sticky, which
         will survive link changes."
    ::= { mgmtPsecConfigInterfacesEntry 6 }

mgmtPsecConfigInterfacesMacTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPsecConfigInterfacesMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of static and sticky MAC addresses to be applied to MAC table
         (forwarding database)"
    ::= { mgmtPsecConfigInterfaces 2 }

mgmtPsecConfigInterfacesMacEntry OBJECT-TYPE
    SYNTAX      MGMTPsecConfigInterfacesMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configurating for one MAC address"
    INDEX       { mgmtPsecConfigInterfacesMacIfIndex,
                  mgmtPsecConfigInterfacesMacVlanId,
                  mgmtPsecConfigInterfacesMacMacAddress }
    ::= { mgmtPsecConfigInterfacesMacTable 1 }

MGMTPsecConfigInterfacesMacEntry ::= SEQUENCE {
    mgmtPsecConfigInterfacesMacIfIndex     MGMTInterfaceIndex,
    mgmtPsecConfigInterfacesMacVlanId      MGMTVlan,
    mgmtPsecConfigInterfacesMacMacAddress  MacAddress,
    mgmtPsecConfigInterfacesMacMacType     MGMTPsecMacType,
    mgmtPsecConfigInterfacesMacAction      MGMTRowEditorState
}

mgmtPsecConfigInterfacesMacIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecConfigInterfacesMacEntry 1 }

mgmtPsecConfigInterfacesMacVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID used for indexing."
    ::= { mgmtPsecConfigInterfacesMacEntry 2 }

mgmtPsecConfigInterfacesMacMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The MAC address for which this entry applies."
    ::= { mgmtPsecConfigInterfacesMacEntry 3 }

mgmtPsecConfigInterfacesMacMacType OBJECT-TYPE
    SYNTAX      MGMTPsecMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how to add entry. dynamic(0) It is strongly recommended not
         to use this interface to add dynamic entries. static(1) Add statically
         to MAC table (forwarding database). sticky(2) Like dynamic, but kept
         across link-changes. It is strongly recommended not to use this
         interface to add sticky entries."
    ::= { mgmtPsecConfigInterfacesMacEntry 4 }

mgmtPsecConfigInterfacesMacAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPsecConfigInterfacesMacEntry 1000 }

mgmtPsecConfigInterfacesMacTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtPsecConfigInterfaces 3 }

mgmtPsecConfigInterfacesMacTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecConfigInterfacesMacTableRowEditor 1 }

mgmtPsecConfigInterfacesMacTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN ID used for indexing."
    ::= { mgmtPsecConfigInterfacesMacTableRowEditor 2 }

mgmtPsecConfigInterfacesMacTableRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The MAC address for which this entry applies."
    ::= { mgmtPsecConfigInterfacesMacTableRowEditor 3 }

mgmtPsecConfigInterfacesMacTableRowEditorMacType OBJECT-TYPE
    SYNTAX      MGMTPsecMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how to add entry. dynamic(0) It is strongly recommended not
         to use this interface to add dynamic entries. static(1) Add statically
         to MAC table (forwarding database). sticky(2) Like dynamic, but kept
         across link-changes. It is strongly recommended not to use this
         interface to add sticky entries."
    ::= { mgmtPsecConfigInterfacesMacTableRowEditor 4 }

mgmtPsecConfigInterfacesMacTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPsecConfigInterfacesMacTableRowEditor 1000 }

mgmtPsecStatus OBJECT IDENTIFIER
    ::= { mgmtPsecMibObjects 3 }

mgmtPsecStatusGlobals OBJECT IDENTIFIER
    ::= { mgmtPsecStatus 1 }

mgmtPsecStatusGlobalsMain OBJECT IDENTIFIER
    ::= { mgmtPsecStatusGlobals 1 }

mgmtPsecStatusGlobalsMainTotalMacCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of MAC addresses in the system managed by Port Security."
    ::= { mgmtPsecStatusGlobalsMain 1 }

mgmtPsecStatusGlobalsMainCurMacCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of currently unused MAC addresses in the system managed by Port
         Security. The number of used MAC addresses can then be found by
         subtracting curMacCnt from totalMacCnt."
    ::= { mgmtPsecStatusGlobalsMain 2 }

mgmtPsecStatusGlobalsNotification OBJECT IDENTIFIER
    ::= { mgmtPsecStatusGlobals 2 }

mgmtPsecStatusGlobalsNotificationPoolDepleted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if Port Security has run out of free <VLAN, MAC>-entries, FALSE if
         at least one is yet free."
    ::= { mgmtPsecStatusGlobalsNotification 1 }

mgmtPsecStatusInterfaces OBJECT IDENTIFIER
    ::= { mgmtPsecStatus 2 }

mgmtPsecStatusInterfacesTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPsecStatusInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-interface status."
    ::= { mgmtPsecStatusInterfaces 1 }

mgmtPsecStatusInterfacesEntry OBJECT-TYPE
    SYNTAX      MGMTPsecStatusInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the port-security status for one interface."
    INDEX       { mgmtPsecStatusInterfacesIfIndex }
    ::= { mgmtPsecStatusInterfacesTable 1 }

MGMTPsecStatusInterfacesEntry ::= SEQUENCE {
    mgmtPsecStatusInterfacesIfIndex        MGMTInterfaceIndex,
    mgmtPsecStatusInterfacesUsers          MGMTPsecUserBitmaskType,
    mgmtPsecStatusInterfacesMacCount       Unsigned32,
    mgmtPsecStatusInterfacesViolateCount   Unsigned32,
    mgmtPsecStatusInterfacesLimitReached   TruthValue,
    mgmtPsecStatusInterfacesSecLearning    TruthValue,
    mgmtPsecStatusInterfacesCpuCopying     TruthValue,
    mgmtPsecStatusInterfacesLinkIsUp       TruthValue,
    mgmtPsecStatusInterfacesStpDiscarding  TruthValue,
    mgmtPsecStatusInterfacesHwAddFailed    TruthValue,
    mgmtPsecStatusInterfacesSwAddFailed    TruthValue,
    mgmtPsecStatusInterfacesSticky         TruthValue
}

mgmtPsecStatusInterfacesIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecStatusInterfacesEntry 1 }

mgmtPsecStatusInterfacesUsers OBJECT-TYPE
    SYNTAX      MGMTPsecUserBitmaskType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bitmask indicating the port-security users that are currently enabled
         on this interface."
    ::= { mgmtPsecStatusInterfacesEntry 2 }

mgmtPsecStatusInterfacesMacCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of MAC addresses currently assigned to this interface. This
         includes violateCount."
    ::= { mgmtPsecStatusInterfacesEntry 3 }

mgmtPsecStatusInterfacesViolateCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current number of violating MAC addresses. Only counts when
         violationMode is 'restrict'. This keeps track of the current number of
         violating MAC addresses in the MAC table. These MAC addresses time out
         (according to holdTimeSecs)"
    ::= { mgmtPsecStatusInterfacesEntry 4 }

mgmtPsecStatusInterfacesLimitReached OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the limit is reached on the interface, FALSE otherwise."
    ::= { mgmtPsecStatusInterfacesEntry 5 }

mgmtPsecStatusInterfacesSecLearning OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if secure learning is enabled on the interface, FALSE otherwise.
         Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 6 }

mgmtPsecStatusInterfacesCpuCopying OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if CPU copying is enabled on the interface, FALSE otherwise.
         Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 7 }

mgmtPsecStatusInterfacesLinkIsUp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if interface link is up, FALSE otherwise. Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 8 }

mgmtPsecStatusInterfacesStpDiscarding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if at least one STP MSTI instance is discarding on the interface,
         FALSE otherwise. Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 9 }

mgmtPsecStatusInterfacesHwAddFailed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if H/W add of a MAC address failed on this interface, FALSE
         otherwise. Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 10 }

mgmtPsecStatusInterfacesSwAddFailed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if S/W add of a MAC address failed on this interface, FALSE
         otherwise. Mainly for debugging."
    ::= { mgmtPsecStatusInterfacesEntry 11 }

mgmtPsecStatusInterfacesSticky OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if interface is configured as sticky, that is, learned MAC
         addresses will survive link changes."
    ::= { mgmtPsecStatusInterfacesEntry 12 }

mgmtPsecStatusInterfacesNotificationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPsecStatusInterfacesNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-interface notification status."
    ::= { mgmtPsecStatusInterfaces 2 }

mgmtPsecStatusInterfacesNotificationEntry OBJECT-TYPE
    SYNTAX      MGMTPsecStatusInterfacesNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the port-security notification status for one
         interface."
    INDEX       { mgmtPsecStatusInterfacesNotificationIfIndex }
    ::= { mgmtPsecStatusInterfacesNotificationTable 1 }

MGMTPsecStatusInterfacesNotificationEntry ::= SEQUENCE {
    mgmtPsecStatusInterfacesNotificationIfIndex              MGMTInterfaceIndex,
    mgmtPsecStatusInterfacesNotificationTotalViolateCount    Unsigned32,
    mgmtPsecStatusInterfacesNotificationShutDown             TruthValue,
    mgmtPsecStatusInterfacesNotificationLatestViolatingVlan  MGMTVlanOrZero,
    mgmtPsecStatusInterfacesNotificationLatestViolatingMac   MacAddress
}

mgmtPsecStatusInterfacesNotificationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecStatusInterfacesNotificationEntry 1 }

mgmtPsecStatusInterfacesNotificationTotalViolateCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of violating MAC addresses. Only counts when violationMode
         is 'restrict'. This keeps on counting up, whereas violateCount counts
         the actual number of violating MAC addresses"
    ::= { mgmtPsecStatusInterfacesNotificationEntry 2 }

mgmtPsecStatusInterfacesNotificationShutDown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the interface is shut down as a result of a violation when
         violationMode is 'shutdown', FALSE otherwise. Do a 'shutdown/no
         shutdown' or a port-security configuration change on the interface to
         re-open it."
    ::= { mgmtPsecStatusInterfacesNotificationEntry 3 }

mgmtPsecStatusInterfacesNotificationLatestViolatingVlan OBJECT-TYPE
    SYNTAX      MGMTVlanOrZero
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Holds the VLAN ID of the latest violating host. Used when violationMode
         is 'restrict' or 'shutdown'. This field and latestViolatingMac are only
         valid if this field is non-zero"
    ::= { mgmtPsecStatusInterfacesNotificationEntry 4 }

mgmtPsecStatusInterfacesNotificationLatestViolatingMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Holds the MAC address of the latest violating host. Used when
         violationMode is 'restrict' or 'shutdown'."
    ::= { mgmtPsecStatusInterfacesNotificationEntry 5 }

mgmtPsecStatusInterfacesMacTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPsecStatusInterfacesMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of port-security controlled entries in the MAC table (forwarding
         database)"
    ::= { mgmtPsecStatusInterfaces 3 }

mgmtPsecStatusInterfacesMacEntry OBJECT-TYPE
    SYNTAX      MGMTPsecStatusInterfacesMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the status for one MAC address"
    INDEX       { mgmtPsecStatusInterfacesMacIfIndex,
                  mgmtPsecStatusInterfacesMacVlanId,
                  mgmtPsecStatusInterfacesMacMacAddress }
    ::= { mgmtPsecStatusInterfacesMacTable 1 }

MGMTPsecStatusInterfacesMacEntry ::= SEQUENCE {
    mgmtPsecStatusInterfacesMacIfIndex           MGMTInterfaceIndex,
    mgmtPsecStatusInterfacesMacVlanId            MGMTVlan,
    mgmtPsecStatusInterfacesMacMacAddress        MacAddress,
    mgmtPsecStatusInterfacesMacPort              MGMTInterfaceIndex,
    mgmtPsecStatusInterfacesMacCreationTime      MGMTDisplayString,
    mgmtPsecStatusInterfacesMacChangedTime       MGMTDisplayString,
    mgmtPsecStatusInterfacesMacAgeHoldTime       Unsigned32,
    mgmtPsecStatusInterfacesMacViolating         MGMTUnsigned8,
    mgmtPsecStatusInterfacesMacBlocked           MGMTUnsigned8,
    mgmtPsecStatusInterfacesMacKeptBlocked       MGMTUnsigned8,
    mgmtPsecStatusInterfacesMacCpuCopying        MGMTUnsigned8,
    mgmtPsecStatusInterfacesMacAgeFrameSeen      MGMTUnsigned8,
    mgmtPsecStatusInterfacesMacUsersForward      MGMTPsecUserBitmaskType,
    mgmtPsecStatusInterfacesMacUsersBlock        MGMTPsecUserBitmaskType,
    mgmtPsecStatusInterfacesMacUsersKeepBlocked  MGMTPsecUserBitmaskType,
    mgmtPsecStatusInterfacesMacMacType           MGMTPsecMacType
}

mgmtPsecStatusInterfacesMacIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtPsecStatusInterfacesMacEntry 1 }

mgmtPsecStatusInterfacesMacVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID used for indexing."
    ::= { mgmtPsecStatusInterfacesMacEntry 2 }

mgmtPsecStatusInterfacesMacMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The MAC address for which this entry applies."
    ::= { mgmtPsecStatusInterfacesMacEntry 3 }

mgmtPsecStatusInterfacesMacPort OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port on which this MAC address is learned."
    ::= { mgmtPsecStatusInterfacesMacEntry 4 }

mgmtPsecStatusInterfacesMacCreationTime OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..24))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC address was originally added at this time."
    ::= { mgmtPsecStatusInterfacesMacEntry 5 }

mgmtPsecStatusInterfacesMacChangedTime OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..24))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC address was last changed at this time (due to e.g. aging)."
    ::= { mgmtPsecStatusInterfacesMacEntry 6 }

mgmtPsecStatusInterfacesMacAgeHoldTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Down-counter indicating time left of aging period or hold time measured
         in seconds. 0 means that aging is disabled."
    ::= { mgmtPsecStatusInterfacesMacEntry 7 }

mgmtPsecStatusInterfacesMacViolating OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if this MAC address is violating the limit and therefore is
         blocked. This can only be set when violationMode is set to 'restrict'.
         Non-violating MAC addresses may be blocked or kept blocked if other
         user modules are enabled on this interface. MAC addresses may be
         blocked for other reasons."
    ::= { mgmtPsecStatusInterfacesMacEntry 8 }

mgmtPsecStatusInterfacesMacBlocked OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if this MAC address is blocked from forwarding. Such MAC addresses
         are subject to hold-time timeout."
    ::= { mgmtPsecStatusInterfacesMacEntry 9 }

mgmtPsecStatusInterfacesMacKeptBlocked OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if this MAC address is kept blocked from forwarding. Only internal
         user modules can set a MAC address to be kept blocking until further
         notice. MAC addresses in this state are not subject to hold-time
         expiration."
    ::= { mgmtPsecStatusInterfacesMacEntry 10 }

mgmtPsecStatusInterfacesMacCpuCopying OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if CPU copying is enabled for this MAC address due to aging.
         Mainly used for debugging."
    ::= { mgmtPsecStatusInterfacesMacEntry 11 }

mgmtPsecStatusInterfacesMacAgeFrameSeen OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if an age frame was seen during aging of this MAC address. Mainly
         used for debugging."
    ::= { mgmtPsecStatusInterfacesMacEntry 12 }

mgmtPsecStatusInterfacesMacUsersForward OBJECT-TYPE
    SYNTAX      MGMTPsecUserBitmaskType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bitmask indicating the user modules that have marked this MAC address
         as forwarding."
    ::= { mgmtPsecStatusInterfacesMacEntry 13 }

mgmtPsecStatusInterfacesMacUsersBlock OBJECT-TYPE
    SYNTAX      MGMTPsecUserBitmaskType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bitmask indicating the user modules that have marked this MAC address
         as blocking (wins over forward)."
    ::= { mgmtPsecStatusInterfacesMacEntry 14 }

mgmtPsecStatusInterfacesMacUsersKeepBlocked OBJECT-TYPE
    SYNTAX      MGMTPsecUserBitmaskType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bitmask indicating the user modules that wish this MAC address to be
         kept blocked (wins over block)."
    ::= { mgmtPsecStatusInterfacesMacEntry 15 }

mgmtPsecStatusInterfacesMacMacType OBJECT-TYPE
    SYNTAX      MGMTPsecMacType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates how entry is learned. dynamic(0) Learned when packets are
         received on the secure port. static(1) Configured by the user.
         sticky(2) Learned like dynamic addresses, but persist through switch
         reboots and link changes."
    ::= { mgmtPsecStatusInterfacesMacEntry 16 }

mgmtPsecControl OBJECT IDENTIFIER
    ::= { mgmtPsecMibObjects 4 }

mgmtPsecControlGlobals OBJECT IDENTIFIER
    ::= { mgmtPsecControl 1 }

mgmtPsecControlGlobalsMacClear OBJECT IDENTIFIER
    ::= { mgmtPsecControlGlobals 1 }

mgmtPsecControlGlobalsMacClearSpecificIfindex OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When TRUE, this structure's ifindex field is searched for matching MAC
         addresses to remove. When FALSE, all ports are searched for MAC
         addresses to remove."
    ::= { mgmtPsecControlGlobalsMacClear 1 }

mgmtPsecControlGlobalsMacClearIfindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When specificIfindex is TRUE, this member holds the interface (must be
         of type port) on which to search for MAC addresses to remove."
    ::= { mgmtPsecControlGlobalsMacClear 2 }

mgmtPsecControlGlobalsMacClearSpecificVlan OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When TRUE, this structure's vlanId field is searched for MAC addresses
         to remove. When FALSE, all VLANs are searched for MAC addresses to
         remove."
    ::= { mgmtPsecControlGlobalsMacClear 3 }

mgmtPsecControlGlobalsMacClearVlanId OBJECT-TYPE
    SYNTAX      MGMTVlanOrZero
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When specificVlan is TRUE, this member holds the VLAN ID on which to
         search for MAC addresses to remove."
    ::= { mgmtPsecControlGlobalsMacClear 4 }

mgmtPsecControlGlobalsMacClearSpecificMac OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When TRUE, this structure's macAddress field holds the MAC address to
         remove. When FALSE, all MAC addresses are searched for MAC addresses to
         remove."
    ::= { mgmtPsecControlGlobalsMacClear 5 }

mgmtPsecControlGlobalsMacClearMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When specificMac is TRUE, this member holds the MAC address to remove."
    ::= { mgmtPsecControlGlobalsMacClear 6 }

mgmtPsecControlInterfaces OBJECT IDENTIFIER
    ::= { mgmtPsecControl 2 }

mgmtPsecTrap OBJECT IDENTIFIER
    ::= { mgmtPsecMibObjects 6 }

mgmtPsecTrapGlobals OBJECT IDENTIFIER
    ::= { mgmtPsecTrap 1 }

mgmtPsecTrapGlobalsMain NOTIFICATION-TYPE
    OBJECTS     { mgmtPsecStatusGlobalsNotificationPoolDepleted }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
         has been updated."

    ::= { mgmtPsecTrapGlobals 1 }

mgmtPsecTrapInterfaces OBJECT IDENTIFIER
    ::= { mgmtPsecTrap 2 }

mgmtPsecTrapInterfacesAdd NOTIFICATION-TYPE
    OBJECTS     { mgmtPsecStatusInterfacesNotificationIfIndex,
                  mgmtPsecStatusInterfacesNotificationTotalViolateCount,
                  mgmtPsecStatusInterfacesNotificationShutDown,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingVlan,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingMac }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been added. The index(es) and value(s)
         of the row is included in the trap."

    ::= { mgmtPsecTrapInterfaces 1 }

mgmtPsecTrapInterfacesMod NOTIFICATION-TYPE
    OBJECTS     { mgmtPsecStatusInterfacesNotificationIfIndex,
                  mgmtPsecStatusInterfacesNotificationTotalViolateCount,
                  mgmtPsecStatusInterfacesNotificationShutDown,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingVlan,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingMac }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
          has been updated."

    ::= { mgmtPsecTrapInterfaces 2 }

mgmtPsecTrapInterfacesDel NOTIFICATION-TYPE
    OBJECTS     { mgmtPsecStatusInterfacesNotificationIfIndex }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been deleted. The index(es) of the
         row is included in the trap."

    ::= { mgmtPsecTrapInterfaces 3 }

mgmtPsecMibConformance OBJECT IDENTIFIER
    ::= { mgmtPsecMib 2 }

mgmtPsecMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPsecMibConformance 1 }

mgmtPsecMibGroups OBJECT IDENTIFIER
    ::= { mgmtPsecMibConformance 2 }

mgmtPsecCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecCapabilitiesUsers,
                  mgmtPsecCapabilitiesPoolSize,
                  mgmtPsecCapabilitiesLimitMin,
                  mgmtPsecCapabilitiesLimitMax,
                  mgmtPsecCapabilitiesViolateLimitMin,
                  mgmtPsecCapabilitiesViolateLimitMax,
                  mgmtPsecCapabilitiesAgeTimeMin,
                  mgmtPsecCapabilitiesAgeTimeMax,
                  mgmtPsecCapabilitiesHoldTimeMin,
                  mgmtPsecCapabilitiesHoldTimeMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 1 }

mgmtPsecConfigGlobalsMainInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecConfigGlobalsMainEnableAging,
                  mgmtPsecConfigGlobalsMainAgingPeriodSecs,
                  mgmtPsecConfigGlobalsMainHoldTimeSecs }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 2 }

mgmtPsecConfigInterfacesTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecConfigInterfacesIfIndex,
                  mgmtPsecConfigInterfacesEnabled,
                  mgmtPsecConfigInterfacesLimit,
                  mgmtPsecConfigInterfacesViolateLimit,
                  mgmtPsecConfigInterfacesViolationMode,
                  mgmtPsecConfigInterfacesSticky }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 3 }

mgmtPsecConfigInterfacesMacTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecConfigInterfacesMacIfIndex,
                  mgmtPsecConfigInterfacesMacVlanId,
                  mgmtPsecConfigInterfacesMacMacAddress,
                  mgmtPsecConfigInterfacesMacMacType,
                  mgmtPsecConfigInterfacesMacAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 4 }

mgmtPsecConfigInterfacesMacTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecConfigInterfacesMacTableRowEditorIfIndex,
                  mgmtPsecConfigInterfacesMacTableRowEditorVlanId,
                  mgmtPsecConfigInterfacesMacTableRowEditorMacAddress,
                  mgmtPsecConfigInterfacesMacTableRowEditorMacType,
                  mgmtPsecConfigInterfacesMacTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 5 }

mgmtPsecStatusGlobalsMainInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecStatusGlobalsMainTotalMacCnt,
                  mgmtPsecStatusGlobalsMainCurMacCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 6 }

mgmtPsecStatusGlobalsNotificationInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecStatusGlobalsNotificationPoolDepleted }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 7 }

mgmtPsecStatusInterfacesTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecStatusInterfacesIfIndex,
                  mgmtPsecStatusInterfacesUsers,
                  mgmtPsecStatusInterfacesMacCount,
                  mgmtPsecStatusInterfacesViolateCount,
                  mgmtPsecStatusInterfacesLimitReached,
                  mgmtPsecStatusInterfacesSecLearning,
                  mgmtPsecStatusInterfacesCpuCopying,
                  mgmtPsecStatusInterfacesLinkIsUp,
                  mgmtPsecStatusInterfacesStpDiscarding,
                  mgmtPsecStatusInterfacesHwAddFailed,
                  mgmtPsecStatusInterfacesSwAddFailed,
                  mgmtPsecStatusInterfacesSticky }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 8 }

mgmtPsecStatusInterfacesNotificationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecStatusInterfacesNotificationIfIndex,
                  mgmtPsecStatusInterfacesNotificationTotalViolateCount,
                  mgmtPsecStatusInterfacesNotificationShutDown,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingVlan,
                  mgmtPsecStatusInterfacesNotificationLatestViolatingMac }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 9 }

mgmtPsecStatusInterfacesMacTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecStatusInterfacesMacIfIndex,
                  mgmtPsecStatusInterfacesMacVlanId,
                  mgmtPsecStatusInterfacesMacMacAddress,
                  mgmtPsecStatusInterfacesMacPort,
                  mgmtPsecStatusInterfacesMacCreationTime,
                  mgmtPsecStatusInterfacesMacChangedTime,
                  mgmtPsecStatusInterfacesMacAgeHoldTime,
                  mgmtPsecStatusInterfacesMacViolating,
                  mgmtPsecStatusInterfacesMacBlocked,
                  mgmtPsecStatusInterfacesMacKeptBlocked,
                  mgmtPsecStatusInterfacesMacCpuCopying,
                  mgmtPsecStatusInterfacesMacAgeFrameSeen,
                  mgmtPsecStatusInterfacesMacUsersForward,
                  mgmtPsecStatusInterfacesMacUsersBlock,
                  mgmtPsecStatusInterfacesMacUsersKeepBlocked,
                  mgmtPsecStatusInterfacesMacMacType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 10 }

mgmtPsecControlGlobalsMacClearInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPsecControlGlobalsMacClearSpecificIfindex,
                  mgmtPsecControlGlobalsMacClearIfindex,
                  mgmtPsecControlGlobalsMacClearSpecificVlan,
                  mgmtPsecControlGlobalsMacClearVlanId,
                  mgmtPsecControlGlobalsMacClearSpecificMac,
                  mgmtPsecControlGlobalsMacClearMacAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPsecMibGroups 11 }

mgmtPsecTrapGlobalsMainInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtPsecTrapGlobalsMain }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtPsecMibGroups 12 }

mgmtPsecTrapInterfacesAddInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtPsecTrapInterfacesAdd }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtPsecMibGroups 13 }

mgmtPsecTrapInterfacesModInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtPsecTrapInterfacesMod }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtPsecMibGroups 14 }

mgmtPsecTrapInterfacesDelInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtPsecTrapInterfacesDel }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtPsecMibGroups 15 }

mgmtPsecMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPsecCapabilitiesInfoGroup,
                       mgmtPsecConfigGlobalsMainInfoGroup,
                       mgmtPsecConfigInterfacesTableInfoGroup,
                       mgmtPsecConfigInterfacesMacTableInfoGroup,
                       mgmtPsecConfigInterfacesMacTableRowEditorInfoGroup,
                       mgmtPsecStatusGlobalsMainInfoGroup,
                       mgmtPsecStatusGlobalsNotificationInfoGroup,
                       mgmtPsecStatusInterfacesTableInfoGroup,
                       mgmtPsecStatusInterfacesNotificationTableInfoGroup,
                       mgmtPsecStatusInterfacesMacTableInfoGroup,
                       mgmtPsecControlGlobalsMacClearInfoGroup,
                       mgmtPsecTrapGlobalsMainInfoGroup,
                       mgmtPsecTrapInterfacesAddInfoGroup,
                       mgmtPsecTrapInterfacesModInfoGroup,
                       mgmtPsecTrapInterfacesDelInfoGroup }

    ::= { mgmtPsecMibCompliances 1 }

END
