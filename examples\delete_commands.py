import requests
import json

headers = {
        'Content-Type': 'application/json'
}

post_data = {
        'user': 'admin',
        'password':'default'
}

json_payload = json.dumps(post_data)

url = 'http://localhost:27182/api/v1/login'
response=requests.post(url,headers=headers, data=json_payload)
json_res= json.loads(response.text)
token=json_res['token']

headers = {
        'Authorization': 'Bearer ' + token
}

url='http://localhost:27182/api/v1/commands'


cmds='[{"edit":"delete","kind":"usercommand","timestamp":"2024-02-12T10:22:31-08:00","command":"debug log on","result":"","status":"","name":"client1","nooverwrite":false,"all":false,"nosyslog":false,"client":"","devid":"","tag":""},{"edit":"delete","kind":"usercommand","timestamp":"2024-02-12T10:22:25-08:00","command":"scan gwd","result":"","status":"","name":"client1","nooverwrite":false,"all":false,"nosyslog":false,"client":"","devid":"","tag":""},{"edit":"delete","kind":"usercommand","timestamp":"2024-02-12T10:22:23-08:00","command":"scan snmp","result":"","status":"","name":"client1","nooverwrite":false,"all":false,"nosyslog":false,"client":"","devid":"","tag":""}]'


url='http://localhost:27182/api/v1/commands'
response=requests.post(url,headers=headers,data=cmds)
json_res=json.loads(response.text)
print(json_res)

