package mnms

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	_ "github.com/glebarez/go-sqlite" // Pure Go SQLite driver
	"github.com/qeof/q"
)

// CallToll

func CallTool(ctx context.Context, toolName string, args map[string]any) string {
	// TODO: implement
	switch toolName {
	case "nimbl_command":
		// Call the nimbl_command function
		return nimblCommand(args)
	case "sql_query_devices":
		// Call the sql_query_devices function
		return sqlQueryDevices(args)
	case "device_chunk_summary":
		return deviceChunk(args)
	default:
		return fmt.Sprintf("Unknown tool: %s", toolName)
	}
}

// Function metadata for the MCP functions endpoint
type ToolMetadata struct {
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Parameters  map[string]any `json:"parameters"`
}

var db *sql.DB

func init() {
	// Initialize the database connection
	if err := initDB("devices.db"); err != nil {
		q.Q("Failed to initialize database: %v", err)
	}
}

// initDB initializes the SQLite database connection and creates the table.
func initDB(dbPath string) error {
	var err error
	db, err = sql.Open("sqlite", dbPath)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	if err = db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	q.Q("Successfully connected to SQLite database.")
	return createDevicesTable()
}

// createDevicesTable dynamically creates the 'devices' table based on the Device struct.
func createDevicesTable() error {
	var device DevInfo
	val := reflect.ValueOf(device)
	typ := val.Type()

	var columns []string
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		if jsonTag := field.Tag.Get("json"); jsonTag == "-" { // Skip fields
			continue
		}

		colName := field.Name
		var colType string

		switch field.Type.Kind() {
		case reflect.String:
			colType = "TEXT"
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			colType = "INTEGER"
		case reflect.Float32, reflect.Float64:
			colType = "REAL"
		case reflect.Bool:
			colType = "INTEGER" // Store bools as 0 or 1
		default:
			// Check specific types after kind
			if field.Type == reflect.TypeOf(time.Time{}) { // This is for our processed Timestamp field
				colType = "TEXT" // Storing as RFC3339 string
			} else if field.Name == "Timestamp" { // Explicitly handle the 'Timestamp' field in the struct if not caught by TypeOf
				colType = "TEXT"
			} else if field.Type == reflect.TypeOf(json.RawMessage{}) {
				colType = "TEXT" // Storing JSON as string
			} else if field.Type.Kind() == reflect.Slice || field.Type.Kind() == reflect.Map {
				colType = "TEXT" // Store as JSON string
			} else {
				q.Q("Warning: Unsupported type for column %s: %s (%s). Defaulting to TEXT.", colName, field.Type.Kind(), field.Type.String())
				colType = "TEXT"
			}
		}

		if colName == "MAC" {
			columns = append(columns, fmt.Sprintf("%s %s PRIMARY KEY", colName, colType))
		} else {
			columns = append(columns, fmt.Sprintf("%s %s", colName, colType))
		}
	}

	query := fmt.Sprintf("CREATE TABLE IF NOT EXISTS devices (%s);", strings.Join(columns, ", "))
	q.Q("Creating table with SQL: %s", query)

	_, err := db.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to create devices table: %w", err)
	}
	q.Q("Devices table created or already exists.")
	return nil
}

func getDeviceTableSchemaForLLM() (string, error) {
	QC.DevMutex.Lock()
	if err := createDevicesTable(); err != nil {
		return "", fmt.Errorf("failed to create devices table before schema fetch: %w", err)
	}
	QC.DevMutex.Unlock()

	rows, err := db.Query("PRAGMA table_info(devices);")
	if err != nil {
		return "", fmt.Errorf("failed to query table_info: %w", err)
	}
	defer rows.Close()

	var schemaParts []string
	schemaParts = append(schemaParts, "Table: devices")
	schemaParts = append(schemaParts, "Columns (column names are case-sensitive as listed):")
	for rows.Next() {
		var cid int
		var name string
		var colType string
		var notnull int
		var dflt_value sql.NullString
		var pk int
		if err := rows.Scan(&cid, &name, &colType, &notnull, &dflt_value, &pk); err != nil {
			return "", fmt.Errorf("failed to scan table_info row: %w", err)
		}
		pkStr := ""
		if pk == 1 {
			pkStr = " (PRIMARY KEY)"
		}
		description := ""
		switch name {
		case "Timestamp":
			description = " (Original Unix epoch timestamp as a string from source data, e.g., '1725944217'). Query using SQLite datetime() function: datetime(TimestampUnixStr) > datetime('YYYY-MM-DDTHH:MM:SSZ'))"
		case "IsOnline", "IsDHCP", "Lock":
			description = " (Boolean: 1 for true, 0 for false. Query as IsOnline = 1 or IsOnline = 0)"
		case "Capabilities", "DeviceErrors":
			description = " (Stored as a JSON string. Use LIKE for simple search or json_extract for specific values, e.g., json_extract(Capabilities, '$.gwd') = 1 or json_extract(Capabilities, '$.gwd') IS TRUE for boolean true within JSON)"
		case "Mac":
			description = " (Device MAC address, PRIMARY KEY)"
		}
		schemaParts = append(schemaParts, fmt.Sprintf("  - %s (%s)%s%s", name, colType, pkStr, description))
	}
	if err = rows.Err(); err != nil {
		return "", fmt.Errorf("error iterating table_info rows: %w", err)
	}
	return strings.Join(schemaParts, "\n"), nil
}

// executeSQLQuery is the function that the LLM tool will call.
func executeSQLQuery(query string) (string, error) {
	q.Q("Executing SQL: %s", query)

	trimmedQuery := strings.TrimSpace(strings.ToUpper(query))
	if !strings.HasPrefix(trimmedQuery, "SELECT") {
		q.Q("Blocked non-SELECT query: %s", query)
		return "Error: Only SELECT queries are allowed by this tool. Please rephrase your request if you intended to retrieve data.", nil
	}

	rows, err := db.Query(query)
	if err != nil {
		q.Q("SQL execution error: %v for query: %s", err, query)
		return fmt.Sprintf("Error executing SQL query: %v. The query was: %s", err, query), nil
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		q.Q("Error getting columns: %v", err)
		return fmt.Sprintf("Error getting columns: %v", err), nil
	}

	var results []string
	results = append(results, strings.Join(columns, " | "))

	columnValues := make([]any, len(columns))
	valuePointers := make([]any, len(columns))
	for i := range columnValues {
		valuePointers[i] = &columnValues[i]
	}

	rowCount := 0
	for rows.Next() {
		rowCount++
		err = rows.Scan(valuePointers...)
		if err != nil {
			q.Q("Error scanning row: %v", err)
			return fmt.Sprintf("Error scanning row: %v", err), nil
		}

		var rowStr []string
		for i, colVal := range columnValues {
			colName := columns[i]

			if colVal == nil {
				rowStr = append(rowStr, "NULL")
			} else {
				switch v := colVal.(type) {
				case []byte: // TEXT columns often come as []byte
					s := string(v)
					if colName == "Timestamp" {
						if sec, err := strconv.ParseInt(s, 10, 64); err == nil {
							// format however you like:
							rowStr = append(rowStr, time.Unix(sec, 0).UTC().Format("2006-01-02 15:04:05 UTC"))
						} else {
							rowStr = append(rowStr, s)
						}
					} else {
						rowStr = append(rowStr, s)
					}
				case int64:
					if colName == "IsOnline" || colName == "IsDHCP" || colName == "Lock" {
						if v == 1 {
							rowStr = append(rowStr, "true")
						} else {
							rowStr = append(rowStr, "false")
						}
					} else {
						rowStr = append(rowStr, fmt.Sprintf("%d", v))
					}
				case float64:
					rowStr = append(rowStr, fmt.Sprintf("%f", v))
				case string: // For columns already string
					if colName == "Timestamp" {
						if sec, err := strconv.ParseInt(v, 10, 64); err == nil {
							rowStr = append(rowStr, time.Unix(sec, 0).UTC().Format("2006-01-02 15:04:05 UTC"))
						} else {
							rowStr = append(rowStr, v)
						}
					} else {
						rowStr = append(rowStr, v)
					}
				default:
					rowStr = append(rowStr, fmt.Sprintf("%v", colVal))
				}
			}
		}
		results = append(results, strings.Join(rowStr, " | "))
		if len(results) > 51 { // Header + 50 data rows
			results = append(results, fmt.Sprintf("... (and %d more matching rows, output truncated)", rowCount-50)) // Approximation
			break
		}
	}

	if err = rows.Err(); err != nil {
		q.Q("Error after iterating rows: %v", err)
		return fmt.Sprintf("Error after iterating rows: %v", err), nil
	}

	if rowCount == 0 {
		return "No results found for the query.", nil
	}
	return strings.Join(results, "\n"), nil
}

func closeDB() {
	if db != nil {
		db.Close()
		q.Q("Database connection closed.")
	}
}

func insertDevicesToDB(devices []DevInfo) error {
	if len(devices) == 0 {
		log.Println("No devices to insert.")
		return nil
	}

	var sampleDevice DevInfo
	valType := reflect.TypeOf(sampleDevice)
	var columnNames []string
	var placeholders []string

	for i := 0; i < valType.NumField(); i++ {
		field := valType.Field(i)
		if jsonTag := field.Tag.Get("json"); jsonTag == "-" {
			continue
		}
		columnNames = append(columnNames, field.Name)
		placeholders = append(placeholders, "?")
	}

	stmtSQL := fmt.Sprintf("INSERT OR REPLACE INTO devices (%s) VALUES (%s)",
		strings.Join(columnNames, ", "),
		strings.Join(placeholders, ", "))

	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(stmtSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement: %w", err)
	}
	defer stmt.Close()

	for _, device := range devices {
		v := reflect.ValueOf(device)
		var args []any
		for i := range valType.NumField() {
			structField := valType.Field(i)
			if jsonTag := structField.Tag.Get("json"); jsonTag == "-" {
				continue
			}

			fieldVal := v.Field(i)

			if fieldVal.Type() == reflect.TypeOf(json.RawMessage{}) {
				rawMsg := fieldVal.Interface().(json.RawMessage)
				if rawMsg == nil || string(rawMsg) == "null" || len(rawMsg) == 0 {
					args = append(args, nil)
				} else {
					args = append(args, string(rawMsg))
				}
			} else if structField.Type.Kind() == reflect.Slice ||
				structField.Type.Kind() == reflect.Map ||
				(structField.Type.Kind() == reflect.Struct && structField.Type != reflect.TypeOf(time.Time{})) {
				b, err := json.Marshal(fieldVal.Interface())
				if err != nil {
					args = append(args, nil)
				} else {
					args = append(args, string(b))
				}
			} else {
				args = append(args, fieldVal.Interface())
			}
		}
		_, err := stmt.Exec(args...)
		if err != nil {
			log.Printf("Failed to insert/replace device MAC %s: %v. SQL: %s, Args: %+v", device.Mac, err, stmtSQL, args)
		}
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	q.Q("Successfully inserted/replaced %d devices.", len(devices))
	return nil
}

func helpText() string {
	/*
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help mtderase", "Erase target device mtd and restore default settings.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help beep", "Beep target device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help reset", "Reset/Reboot target device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help gwd", "Configure GWD commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help scan", "Use different protocol to scan all devices.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help config", "Configure device setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help switch", "Use target device CLI configuration commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help snmp", "Use snmp get/set.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help syslog", "Log service commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help debug", "Configure debug setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help firmware", "Upgrade firmware.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help mqtt", "Use mqtt to publish/subscribe topic.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help opcua", "Opcua setting.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help util", "Utilities commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help wg", "Wireguard commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help agent", "Use agent to control device.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help anomaly", "Anomaly detection commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help idps", "IDPS commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help msg", "Msg commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help ssh", "SSH commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help firewall", "Firewall commands.")
		msg = msg + fmt.Sprintf("\t%-15s %-15s\n", "help service", "Service commands.")
	*/
	msg := HelpCmd("help mtderase")
	msg = msg + HelpCmd("help beep") + "\n"
	msg = msg + HelpCmd("help reset") + "\n"
	msg = msg + HelpCmd("help gwd") + "\n"
	msg = msg + HelpCmd("help scan") + "\n"
	msg = msg + HelpCmd("help config") + "\n"
	// msg = msg + HelpCmd("help switch") + "\n"
	// msg = msg + HelpCmd("help snmp") + "\n"
	// msg = msg + HelpCmd("help syslog") + "\n"
	// msg = msg + HelpCmd("help debug") + "\n"
	// msg = msg + HelpCmd("help firmware") + "\n"
	// msg = msg + HelpCmd("help mqtt") + "\n"
	// msg = msg + HelpCmd("help opcua") + "\n"
	// msg = msg + HelpCmd("help util") + "\n"
	// msg = msg + HelpCmd("help wg") + "\n"
	// msg = msg + HelpCmd("help agent") + "\n"
	// msg = msg + HelpCmd("help anomaly") + "\n"
	// msg = msg + HelpCmd("help idps") + "\n"
	msg = msg + HelpCmd("help msg") + "\n"
	// msg = msg + HelpCmd("help ssh") + "\n"
	// msg = msg + HelpCmd("help firewall") + "\n"
	// msg = msg + HelpCmd("help service") + "\n"

	return msg
}

// SupportedTools returns a list of supported commands
func SupportedTools() ([]ToolMetadata, error) {
	tools := []ToolMetadata{
		// {
		// 	Name: "nimbl_command",
		// 	Description: `Given a user’s natural-language request, look up the corresponding Nimbl CLI command in the documentation and
		// 	return only the exact command invocation (including flags and arguments) that fulfills the request.
		// 	Do not include any extra text or explanation. Here is the document:` + helpText(),
		// 	Parameters: mcp.ToolInputSchema{
		// 		Properties: map[string]any{
		// 			"command": map[string]any{
		// 				"type":        "string",
		// 				"description": "The command to run on Nimbl.",
		// 			},
		// 			"kind": map[string]any{
		// 				"type":        "string",
		// 				"description": "Optional: The kind of command to run. Issue 'root' to run a command for Root service. Default is empty.",
		// 			},
		// 			"client": map[string]any{
		// 				"type":        "string",
		// 				"description": "Optional: The client to run the command on. Default is empty.",
		// 			},
		// 			"nosyslog": map[string]any{
		// 				"type":        "boolean",
		// 				"description": "Optional: If true, the command will not be logged to syslog. Default is false.",
		// 			},
		// 			"nooverwrite": map[string]any{
		// 				"type":        "boolean",
		// 				"description": "Optional: If true, the command will not overwrite the existing command. Default is false.",
		// 			},
		// 		},
		// 		Required: []string{"command"},
		// 		Type:     "object",
		// 	},
		// },
		{
			Name: "sql_query_devices",
			Description: `Your primary function is to translate user's natural language questions about devices into valid SQL SELECT queries.
			Given a user’s natural-language request, look up the corresponding SQL query in the documentation and
			return only the exact SQL query that fulfills the request. Only use SELECT statements.`,
			Parameters: map[string]any{
				"properties": map[string]any{
					"sql_query": map[string]any{
						"type": "string",
						"description": `The SQL query to run on the devices database. 
						Example: 'SELECT MAC, IPAddress FROM devices WHERE IsOnline = 1;'. 
						Column names are case-sensitive. 
						String values in WHERE clauses should be in double quotes.`,
					},
				},
				"required": []string{"sql_query"},
				"type":     "object",
			},
		},
		{
			Name: "device_chunk_summary",
			Description: `Returns a JSON object with this schema:
			{
				"devices":     [ ...array of device objects... ],  // list of DevInfo entries
				"offset":      <integer>,                       // current chunk index (0-based)
				"total_chunks":<integer>,                       // total number of chunks available
				"remaining":    <integer>                       // number of chunks remaining after this one
			}
			The LLM must summarize the 'devices' data and include:
			- totals (all/online/offline)
			- model-name counts (online only)
			- firmware/application version breakdown (online only)
			- kernel version breakdown (online only)
			- IP subnet distribution and gateway usage (online only)
			- scan and topology protocol usage (online only)
			- list of offline devices with MAC, last-seen timestamp, and model`,
			Parameters: map[string]any{
				"properties": map[string]any{
					"chunk_size": map[string]any{
						"type": "integer",
						"description": `Optional: The size of each chunk of devices to process. 
						Default is 5. 
						Must be a positive integer.
						Highly recommended to set this value to 10 or less for better performance or not to reach the context size limit.`,
					},
					"offset": map[string]any{
						"type":        "integer",
						"description": `Optional: The index of the chunk to return. Default is 0.`,
					},
				},
				"required": []string{},
				"type":     "object",
			},
		},
	}

	return tools, nil
}

func integerConvert(v any) (int, error) {
	if i, ok := v.(int); ok {
		return i, nil
	}
	if i, ok := v.(float64); ok {
		return int(i), nil
	}
	if i, ok := v.(string); ok {
		return strconv.Atoi(i)
	}
	return 0, fmt.Errorf("cannot convert %v to integer", v)
}

func nimblCommand(args map[string]any) string {
	if len(args) < 1 {
		return "Error: expected at least 1 argument, got 0"
	}
	var cmdinfo CmdInfo
	if _, ok := args["command"]; !ok {
		return "Error: missing required argument: command"
	}
	cmdinfo.Command = args["command"].(string)
	if _, ok := args["kind"]; ok {
		cmdinfo.Kind = args["kind"].(string)
	}
	if _, ok := args["client"]; ok {
		cmdinfo.Client = args["client"].(string)
	}
	if _, ok := args["nosyslog"]; ok {
		if boolValue, err := strconv.ParseBool(args["nosyslog"].(string)); err != nil {
			q.Q("Error parsing nosyslog: %v", err)
			cmdinfo.NoSyslog = false
		} else {
			cmdinfo.NoSyslog = boolValue
		}
	}
	if _, ok := args["nooverwrite"]; ok {
		if boolValue, err := strconv.ParseBool(args["nooverwrite"].(string)); err != nil {
			q.Q("Error parsing nooverwrite: %v", err)
			cmdinfo.NoOverwrite = false
		} else {
			cmdinfo.NoOverwrite = boolValue
		}
	}

	// same steps as HandleCommands
	_cmdinfo, err := ValidateCommands(&cmdinfo)
	if err != nil {
		return fmt.Sprintf("Error: %v", err)
	}
	cmds := []CmdInfo{*_cmdinfo}
	err = UpdateCmds(cmds)
	if err != nil {
		return fmt.Sprintf("Error: %v", err)
	}

	return fmt.Sprintf("Command `%s` sent to Nimbl successed, please verify the result later", cmdinfo.Command)
}

func sqlQueryDevices(args map[string]any) string {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	// insert devices to database
	err := createDevicesTable()
	if err != nil {
		return fmt.Sprintf("Error: failed to create devices table: %v", err)
	}
	devs := []DevInfo{}
	for _, dev := range QC.DevData {
		devs = append(devs, dev)
	}
	err = insertDevicesToDB(devs)
	if err != nil {
		return fmt.Sprintf("Error: failed to insert devices to database: %v", err)
	}
	q.Q("Devices inserted to database.")

	// defer wipe out database
	defer func() {
		// drop table
		_, err := db.Exec("DROP TABLE IF EXISTS devices;")
		if err != nil {
			q.Q("Failed to drop devices table: %v", err)
		}
		q.Q("Devices table dropped.")
	}()

	// parse required query
	query, ok := args["sql_query"].(string)
	if !ok || query == "" {
		return "Error: missing required argument: sql_query"
	}

	res, err := executeSQLQuery(query)
	if err != nil {
		return fmt.Sprintf("Error: failed to execute SQL query: %v", err)
	}

	q.Q("SQL query result: ", res)
	return res
}

func deviceChunk(args map[string]any) string {
	chunkSize := 5
	if size, ok := args["chunk_size"]; ok {
		if s, err := integerConvert(size); err != nil {
			q.Q("Error parsing chunk_size: %v", err)
			chunkSize = 5
		} else {
			chunkSize = s
		}
	}

	if chunkSize <= 0 {
		return "Error: chunk_size must be a positive integer"
	}

	offset := 0
	if off, ok := args["offset"]; ok {
		if s, err := integerConvert(off); err != nil {
			q.Q("Error parsing offset: %v", err)
			offset = 0
		} else {
			offset = s
		}
	}
	if offset < 0 {
		return "Error: offset must be a non-negative integer"
	}

	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()

	if len(QC.DevData) == 0 {
		return "Error: no devices found"
	}
	devs := []DevInfo{}
	for _, dev := range QC.DevData {
		devs = append(devs, dev)
	}

	// chunk devices
	var chunks [][]DevInfo
	for i := 0; i < len(devs); i += chunkSize {
		end := i + chunkSize
		if end > len(devs) {
			end = len(devs)
		}
		chunks = append(chunks, devs[i:end])
	}

	// return the offset chunk
	if offset >= len(chunks) {
		return "Error: offset is out of range"
	}
	chunk := chunks[offset]
	if len(chunk) == 0 {
		return "Error: no devices in the chunk"
	}

	// wrap chunk in metadata object
	summary := map[string]any{
		"devices":      chunk,
		"offset":       offset,
		"total_chunks": len(chunks),
		"remaining":    len(chunks) - offset - 1,
	}
	res, err := json.Marshal(summary)

	if err != nil {
		return fmt.Sprintf("Error: failed to marshal devices to JSON: %v", err)
	}

	// return the result
	return string(res)
}
