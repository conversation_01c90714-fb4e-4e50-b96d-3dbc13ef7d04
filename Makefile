.PHONY: default

EXE=

ifeq ($(OS),Windows_NT)
EXE=.exe
endif

RELEASE_DIR = release
LINUX_DIR = $(RELEASE_DIR)/linux_amd64
WINDOWS_DIR = $(RELEASE_DIR)/windows_amd64
GO_BUILD_FLAGS = -ldflags "-X main.Version=$(VERSION) -s -w"

define go-build
- mkdir -p $(2)
GOOS=$(1) go build $(GO_BUILD_FLAGS) -o $(2)/$(3) $(4)
chmod +x $(2)/$(3)
endef

.PHONY: frontend wgclient default bbrootsvc bbctl bbnmssvc bblogsvc bbidpsvc dev devcmds code-assist

# default make to build bb_svc with frontend
BBCMDS=frontend bbrootsvc bbctl bblogsvc bbnmssvc bbidpsvc
default: $(BBCMDS)

# can use make dev to build bb_svc without building frontend, for that we shold have atleast one time frontend build
# so we can have dist folder 
DEVCMDS=bbrootsvc bbctl bblogsvc bbnmssvc bbidpsvc	
devcmds: $(DEVCMDS)

dev: 
	if [ ! -d "./dist" ]; then\
    	make;\
	else\
		make devcmds;\
	fi
	

lint:
	go vet 
	revive
	golangci-lint run
	staticcheck

frontend:
	VERSION=$(VERSION) DISTRIBUTED=true make -C frontend

wgclient:
	make -C wgclient

bbrootsvc:
	make -C bbrootsvc

bbctl:
	make -C bbctl

bblogsvc:
	make -C bblogsvc

bbnmssvc:
	make -C bbnmssvc


bbidpsvc:
	make -C bbidpsvc

code-assist:
	make -C code-assist

install_doc:
	mkdir -p $(RELEASE_DIR)
	go doc -all > $(RELEASE_DIR)/API.txt
	cp doc/README.md doc/authentication.md CHANGELOG.md COPYRIGHT.txt LICENSE.txt doc/MANIFESTForLinux.md doc/MANIFESTForWindows.md $(RELEASE_DIR)


release_build_get-machine-id:
	$(call go-build,linux,$(LINUX_DIR),get-machine-id,get-machine-id/main.go)
	$(call go-build,windows,$(WINDOWS_DIR),get-machine-id.exe,get-machine-id/main.go)


.PHONY: release
release: release_build_bb release_build_get-machine-id install_doc
	cd $(RELEASE_DIR); rm -f bbnim*.zip; \
	rm -f bbnim_windows_amd64_$(VERSION).zip; \
	rm -f bbnim_linux_amd64_$(VERSION).zip; \
	mv MANIFESTForWindows.md windows_amd64/MANIFESTF.md; \
	mv MANIFESTForLinux.md linux_amd64/MANIFESTF.md; \
	upx -9 windows_amd64/bb*.exe windows_amd64/get-machine-id.exe; \
	zip -r bbnim_windows_amd64_$(VERSION).zip *.txt *.pdf CHANGELOG.md README.md authentication.md windows_amd64; \
	zip -r bbnim_linux_amd64_$(VERSION).zip *.txt *.pdf CHANGELOG.md README.md authentication.md linux_amd64

# You should do <NAME_EMAIL>:bbtechhive/mnms_installation in ..
# before building release_build_bb
WCGO_CFLAGS="-I$(HOME)/mnms_installation/windows/include/hs -I$(HOME)/mnms_installation/pcre2-windows/include"
WCGO_LDFLAGS="-L$(HOME)/mnms_installation/windows -lhs -L$(HOME)/mnms_installation/pcre2-windows/lib -lpcre2-8"
.PHONY: release_build_bb
release_build_bb:
	for target in $(BBCMDS); do \
		echo building $$target with version $(VERSION); \
		GOOS=linux go build -ldflags "-X main.Version=$(VERSION) -s -w" -o release/linux_amd64/$$target $$target/*.go; \
		CGO_CFLAGS=$(WCGO_CFLAGS)  CGO_LDFLAGS=$(WCGO_LDFLAGS) \
		GOOS=windows GOARCH=amd64 CGO_ENABLED=1 CXX=x86_64-w64-mingw32-g++ CC=x86_64-w64-mingw32-gcc go build $(GO_BUILD_FLAGS) -o $(WINDOWS_DIR)/$$target.exe $$target/*.go; \
	done

.PHONY: clean
clean:
	# make -C frontend clean	
	make -C bbrootsvc clean
	make -C bbctl clean
	make -C bbnmssvc clean	
	make -C bblogsvc clean
	make -C bbidpsvc clean
	make -C code-assist clean
	rm -rf release
