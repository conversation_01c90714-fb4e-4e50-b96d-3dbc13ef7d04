-- *****************************************************************
-- FRER-MIB:  
-- ****************************************************************

MGMT-FRER-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    ;

mgmtFrerMib MODULE-IDENTITY
    LAST-UPDATED "202009020000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MIB for Frame Replication and Elimination for Reliability,
         FRER."
    REVISION    "202009020000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 157 }


MGMTFrerFrerMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines wether FRER runs in generation or recovery
         mode."
    SYNTAX      INTEGER { generation(0), recovery(1) }

MGMTFrerOperState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This operational state of a FRER instance"
    SYNTAX      INTEGER { disabled(0), active(1), internalError(2) }

MGMTFrerRecoveryAlg ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the chosen recovery algorithm."
    SYNTAX      INTEGER { vector(0), match(1) }

mgmtFrerMibObjects OBJECT IDENTIFIER
    ::= { mgmtFrerMib 1 }

mgmtFrerCapabilities OBJECT IDENTIFIER
    ::= { mgmtFrerMibObjects 1 }

mgmtFrerCapabilitiesInstanceMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of creatable FRER instances.This value is 0 if FRER is
         not supported on this platform."
    ::= { mgmtFrerCapabilities 1 }

mgmtFrerCapabilitiesMstreamMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of creatable member streams."
    ::= { mgmtFrerCapabilities 2 }

mgmtFrerCapabilitiesCstreamMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of creatable compound streams."
    ::= { mgmtFrerCapabilities 3 }

mgmtFrerCapabilitiesResetTimeoutMsecMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum value to configure for reset timer (in milliseconds)."
    ::= { mgmtFrerCapabilities 4 }

mgmtFrerCapabilitiesResetTimeoutMsecMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value to configure for reset timer (in milliseconds)."
    ::= { mgmtFrerCapabilities 5 }

mgmtFrerCapabilitiesHistorylenMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum history length (when using vector recovery algorithm)."
    ::= { mgmtFrerCapabilities 6 }

mgmtFrerCapabilitiesHistorylenMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum history length (when using vector recovery algorithm)."
    ::= { mgmtFrerCapabilities 7 }

mgmtFrerCapabilitiesLaErrDifferenceMin OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed sequence number distance used in latent error
         detection."
    ::= { mgmtFrerCapabilities 8 }

mgmtFrerCapabilitiesLaErrDifferenceMax OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed sequence number distance used in latent error
         detection."
    ::= { mgmtFrerCapabilities 9 }

mgmtFrerCapabilitiesLaErrPeriodMsecMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum configurable latent error period in milliseconds."
    ::= { mgmtFrerCapabilities 10 }

mgmtFrerCapabilitiesLaErrPeriodMsecMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum configurable latent error period in milliseconds."
    ::= { mgmtFrerCapabilities 11 }

mgmtFrerCapabilitiesLaErrPathsMin OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed number of paths used in latent error detection."
    ::= { mgmtFrerCapabilities 12 }

mgmtFrerCapabilitiesLaErrPathsMax OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed number of paths used in latent error detection.."
    ::= { mgmtFrerCapabilities 13 }

mgmtFrerCapabilitiesLaErrResetPeriodMsecMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum configurable latent error reset period in milliseconds."
    ::= { mgmtFrerCapabilities 14 }

mgmtFrerCapabilitiesLaErrResetPeriodMsecMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum configurable latent error reset period in milliseconds."
    ::= { mgmtFrerCapabilities 15 }

mgmtFrerConfig OBJECT IDENTIFIER
    ::= { mgmtFrerMibObjects 2 }

mgmtFrerConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the FRER instance configuration table."
    ::= { mgmtFrerConfig 1 }

mgmtFrerConfigEntry OBJECT-TYPE
    SYNTAX      MGMTFrerConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a FRER instance"
    INDEX       { mgmtFrerConfigId }
    ::= { mgmtFrerConfigTable 1 }

MGMTFrerConfigEntry ::= SEQUENCE {
    mgmtFrerConfigId                    Integer32,
    mgmtFrerConfigMode                  MGMTFrerFrerMode,
    mgmtFrerConfigFrerVlan              MGMTUnsigned16,
    mgmtFrerConfigEgressPorts           MGMTPortList,
    mgmtFrerConfigAlgorithm             MGMTFrerRecoveryAlg,
    mgmtFrerConfigHistoryLen            Unsigned32,
    mgmtFrerConfigResetTimeoutMsec      Unsigned32,
    mgmtFrerConfigTakeNoSequence        TruthValue,
    mgmtFrerConfigIndividualRecovery    TruthValue,
    mgmtFrerConfigTerminate             TruthValue,
    mgmtFrerConfigLaErrDetection        TruthValue,
    mgmtFrerConfigLaErrDifference       Unsigned32,
    mgmtFrerConfigLaErrPeriodMsec       Unsigned32,
    mgmtFrerConfigLaErrPaths            Unsigned32,
    mgmtFrerConfigLaErrResetPeriodMsec  Unsigned32,
    mgmtFrerConfigAdminActive           TruthValue,
    mgmtFrerConfigStreamId0             Integer32,
    mgmtFrerConfigStreamId1             Integer32,
    mgmtFrerConfigStreamId2             Integer32,
    mgmtFrerConfigStreamId3             Integer32,
    mgmtFrerConfigStreamId4             Integer32,
    mgmtFrerConfigStreamId5             Integer32,
    mgmtFrerConfigStreamId6             Integer32,
    mgmtFrerConfigStreamId7             Integer32,
    mgmtFrerConfigAction                MGMTRowEditorState
}

mgmtFrerConfigId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerConfigEntry 1 }

mgmtFrerConfigMode OBJECT-TYPE
    SYNTAX      MGMTFrerFrerMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mode that this instance is running in."
    ::= { mgmtFrerConfigEntry 2 }

mgmtFrerConfigFrerVlan OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID that the streams identified by stream_ids get classified
         to."
    ::= { mgmtFrerConfigEntry 3 }

mgmtFrerConfigEgressPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of Egress ports."
    ::= { mgmtFrerConfigEntry 4 }

mgmtFrerConfigAlgorithm OBJECT-TYPE
    SYNTAX      MGMTFrerRecoveryAlg
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The chosen recovery algorithm."
    ::= { mgmtFrerConfigEntry 5 }

mgmtFrerConfigHistoryLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The vector recovery algorithm's history length."
    ::= { mgmtFrerConfigEntry 6 }

mgmtFrerConfigResetTimeoutMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The reset time in milliseconds of the recovery algorithm."
    ::= { mgmtFrerConfigEntry 7 }

mgmtFrerConfigTakeNoSequence OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls latent error detection enabledness.Indicates whether
         individual recovery is enabled on this FRER intance.When individual
         recovery is enabled, each member flow will run the selected recovery
         algorithm before passing it to the compound recovery, allowing for
         detecting a sender that sends the same R-Tag sequence number over and
         over again (see e.g. 802.1CB, Annex C.10)."
    ::= { mgmtFrerConfigEntry 8 }

mgmtFrerConfigIndividualRecovery OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether we are an end system that terminates recovery and
         removes the R-tag before the frames are egressing."
    ::= { mgmtFrerConfigEntry 9 }

mgmtFrerConfigTerminate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether we are an end system that terminates recovery and
         removes the R-tag before the frames are egressing."
    ::= { mgmtFrerConfigEntry 10 }

mgmtFrerConfigLaErrDetection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls latent error detection enabledness."
    ::= { mgmtFrerConfigEntry 11 }

mgmtFrerConfigLaErrDifference OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the maximum distance between the number of discarded packets
         and the number of member streams multiplied by the number of passed
         packets. Any larger difference will trigger the detection of a latent
         error by the latent error test function."
    ::= { mgmtFrerConfigEntry 12 }

mgmtFrerConfigLaErrPeriodMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of milliseconds between running the latent error test
         function."
    ::= { mgmtFrerConfigEntry 13 }

mgmtFrerConfigLaErrPaths OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of paths that the latent error detection function operates
         on."
    ::= { mgmtFrerConfigEntry 14 }

mgmtFrerConfigLaErrResetPeriodMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of milliseconds between running the latent error reset
         function."
    ::= { mgmtFrerConfigEntry 15 }

mgmtFrerConfigAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether this instance is active or not."
    ::= { mgmtFrerConfigEntry 16 }

mgmtFrerConfigStreamId0 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 0"
    ::= { mgmtFrerConfigEntry 50 }

mgmtFrerConfigStreamId1 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 1"
    ::= { mgmtFrerConfigEntry 51 }

mgmtFrerConfigStreamId2 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 2"
    ::= { mgmtFrerConfigEntry 52 }

mgmtFrerConfigStreamId3 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 3"
    ::= { mgmtFrerConfigEntry 53 }

mgmtFrerConfigStreamId4 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 4"
    ::= { mgmtFrerConfigEntry 54 }

mgmtFrerConfigStreamId5 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 5"
    ::= { mgmtFrerConfigEntry 55 }

mgmtFrerConfigStreamId6 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 6"
    ::= { mgmtFrerConfigEntry 56 }

mgmtFrerConfigStreamId7 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 7"
    ::= { mgmtFrerConfigEntry 57 }

mgmtFrerConfigAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtFrerConfigEntry 100 }

mgmtFrerConfigRowEditor OBJECT IDENTIFIER
    ::= { mgmtFrerConfig 2 }

mgmtFrerConfigRowEditorId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerConfigRowEditor 1 }

mgmtFrerConfigRowEditorMode OBJECT-TYPE
    SYNTAX      MGMTFrerFrerMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mode that this instance is running in."
    ::= { mgmtFrerConfigRowEditor 2 }

mgmtFrerConfigRowEditorFrerVlan OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID that the streams identified by stream_ids get classified
         to."
    ::= { mgmtFrerConfigRowEditor 3 }

mgmtFrerConfigRowEditorEgressPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of Egress ports."
    ::= { mgmtFrerConfigRowEditor 4 }

mgmtFrerConfigRowEditorAlgorithm OBJECT-TYPE
    SYNTAX      MGMTFrerRecoveryAlg
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The chosen recovery algorithm."
    ::= { mgmtFrerConfigRowEditor 5 }

mgmtFrerConfigRowEditorHistoryLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The vector recovery algorithm's history length."
    ::= { mgmtFrerConfigRowEditor 6 }

mgmtFrerConfigRowEditorResetTimeoutMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The reset time in milliseconds of the recovery algorithm."
    ::= { mgmtFrerConfigRowEditor 7 }

mgmtFrerConfigRowEditorTakeNoSequence OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls latent error detection enabledness.Indicates whether
         individual recovery is enabled on this FRER intance.When individual
         recovery is enabled, each member flow will run the selected recovery
         algorithm before passing it to the compound recovery, allowing for
         detecting a sender that sends the same R-Tag sequence number over and
         over again (see e.g. 802.1CB, Annex C.10)."
    ::= { mgmtFrerConfigRowEditor 8 }

mgmtFrerConfigRowEditorIndividualRecovery OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether we are an end system that terminates recovery and
         removes the R-tag before the frames are egressing."
    ::= { mgmtFrerConfigRowEditor 9 }

mgmtFrerConfigRowEditorTerminate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether we are an end system that terminates recovery and
         removes the R-tag before the frames are egressing."
    ::= { mgmtFrerConfigRowEditor 10 }

mgmtFrerConfigRowEditorLaErrDetection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls latent error detection enabledness."
    ::= { mgmtFrerConfigRowEditor 11 }

mgmtFrerConfigRowEditorLaErrDifference OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies the maximum distance between the number of discarded packets
         and the number of member streams multiplied by the number of passed
         packets. Any larger difference will trigger the detection of a latent
         error by the latent error test function."
    ::= { mgmtFrerConfigRowEditor 12 }

mgmtFrerConfigRowEditorLaErrPeriodMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of milliseconds between running the latent error test
         function."
    ::= { mgmtFrerConfigRowEditor 13 }

mgmtFrerConfigRowEditorLaErrPaths OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of paths that the latent error detection function operates
         on."
    ::= { mgmtFrerConfigRowEditor 14 }

mgmtFrerConfigRowEditorLaErrResetPeriodMsec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of milliseconds between running the latent error reset
         function."
    ::= { mgmtFrerConfigRowEditor 15 }

mgmtFrerConfigRowEditorAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether this instance is active or not."
    ::= { mgmtFrerConfigRowEditor 16 }

mgmtFrerConfigRowEditorStreamId0 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 0"
    ::= { mgmtFrerConfigRowEditor 50 }

mgmtFrerConfigRowEditorStreamId1 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 1"
    ::= { mgmtFrerConfigRowEditor 51 }

mgmtFrerConfigRowEditorStreamId2 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 2"
    ::= { mgmtFrerConfigRowEditor 52 }

mgmtFrerConfigRowEditorStreamId3 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 3"
    ::= { mgmtFrerConfigRowEditor 53 }

mgmtFrerConfigRowEditorStreamId4 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 4"
    ::= { mgmtFrerConfigRowEditor 54 }

mgmtFrerConfigRowEditorStreamId5 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 5"
    ::= { mgmtFrerConfigRowEditor 55 }

mgmtFrerConfigRowEditorStreamId6 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 6"
    ::= { mgmtFrerConfigRowEditor 56 }

mgmtFrerConfigRowEditorStreamId7 OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Stream identity list element 7"
    ::= { mgmtFrerConfigRowEditor 57 }

mgmtFrerConfigRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtFrerConfigRowEditor 100 }

mgmtFrerStatus OBJECT IDENTIFIER
    ::= { mgmtFrerMibObjects 3 }

mgmtFrerStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created FRER instance status."
    ::= { mgmtFrerStatus 1 }

mgmtFrerStatusEntry OBJECT-TYPE
    SYNTAX      MGMTFrerStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created FRER instance status."
    INDEX       { mgmtFrerStatusId }
    ::= { mgmtFrerStatusTable 1 }

MGMTFrerStatusEntry ::= SEQUENCE {
    mgmtFrerStatusId                               Integer32,
    mgmtFrerStatusOperState                        MGMTFrerOperState,
    mgmtFrerStatusLatentError                      TruthValue,
    mgmtFrerStatusWarningNone                      TruthValue,
    mgmtFrerStatusWarningSteamNotFound             TruthValue,
    mgmtFrerStatusWarningNoIngressPorts            TruthValue,
    mgmtFrerStatusWarningStreamControlFail         TruthValue,
    mgmtFrerStatusWarningStreamOwnedBySomeoneElse  TruthValue,
    mgmtFrerStatusWarningIngressEgressOverlap      TruthValue,
    mgmtFrerStatusWarningEgressPortCnt             TruthValue,
    mgmtFrerStatusWarningIngressNoLink             TruthValue,
    mgmtFrerStatusWarningEgressNoLink              TruthValue,
    mgmtFrerStatusWarningVlanMembership            TruthValue,
    mgmtFrerStatusWarningStpBlocked                TruthValue,
    mgmtFrerStatusWarningMstpBlocked               TruthValue
}

mgmtFrerStatusId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerStatusEntry 1 }

mgmtFrerStatusOperState OBJECT-TYPE
    SYNTAX      MGMTFrerOperState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Operational state of this FRER instance."
    ::= { mgmtFrerStatusEntry 2 }

mgmtFrerStatusLatentError OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If latent error detection is enabled and a SIGNAL_LATENT_ERROR is
         raised, this member will become true."
    ::= { mgmtFrerStatusEntry 3 }

mgmtFrerStatusWarningNone OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "No warnings found."
    ::= { mgmtFrerStatusEntry 4 }

mgmtFrerStatusWarningSteamNotFound OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the matching streams doesn't exist."
    ::= { mgmtFrerStatusEntry 5 }

mgmtFrerStatusWarningNoIngressPorts OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the streams don't have an ingress port."
    ::= { mgmtFrerStatusEntry 6 }

mgmtFrerStatusWarningStreamControlFail OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Failed to take over at least one of the streams."
    ::= { mgmtFrerStatusEntry 7 }

mgmtFrerStatusWarningStreamOwnedBySomeoneElse OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the streams is owned by someone else."
    ::= { mgmtFrerStatusEntry 8 }

mgmtFrerStatusWarningIngressEgressOverlap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "There is an overlap between ingress and egress ports."
    ::= { mgmtFrerStatusEntry 9 }

mgmtFrerStatusWarningEgressPortCnt OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "In generation mode, only one egress port is specified."
    ::= { mgmtFrerStatusEntry 10 }

mgmtFrerStatusWarningIngressNoLink OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the ingress ports doesn't have link."
    ::= { mgmtFrerStatusEntry 11 }

mgmtFrerStatusWarningEgressNoLink OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the egress ports doesn't have link."
    ::= { mgmtFrerStatusEntry 12 }

mgmtFrerStatusWarningVlanMembership OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the egress ports is not member of the FRER VLAN."
    ::= { mgmtFrerStatusEntry 13 }

mgmtFrerStatusWarningStpBlocked OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the egress ports is blocked by STP."
    ::= { mgmtFrerStatusEntry 14 }

mgmtFrerStatusWarningMstpBlocked OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "At least one of the egress ports is blocked by MSTP."
    ::= { mgmtFrerStatusEntry 15 }

mgmtFrerStatusNotificationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerStatusNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created FRER instance notification status."
    ::= { mgmtFrerStatus 2 }

mgmtFrerStatusNotificationEntry OBJECT-TYPE
    SYNTAX      MGMTFrerStatusNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created FRER instance notification status."
    INDEX       { mgmtFrerStatusNotificationId }
    ::= { mgmtFrerStatusNotificationTable 1 }

MGMTFrerStatusNotificationEntry ::= SEQUENCE {
    mgmtFrerStatusNotificationId           Integer32,
    mgmtFrerStatusNotificationLatentError  TruthValue
}

mgmtFrerStatusNotificationId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerStatusNotificationEntry 1 }

mgmtFrerStatusNotificationLatentError OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If latent error detection is enabled and a SIGNAL_LATENT_ERROR is
         raised, this member will become true."
    ::= { mgmtFrerStatusNotificationEntry 2 }

mgmtFrerStatusStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created FRER instance status."
    ::= { mgmtFrerStatus 3 }

mgmtFrerStatusStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTFrerStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created FRER instance status."
    INDEX       { mgmtFrerStatusStatisticsId,
                  mgmtFrerStatusStatisticsIfIndex,
                  mgmtFrerStatusStatisticsStreamId }
    ::= { mgmtFrerStatusStatisticsTable 1 }

MGMTFrerStatusStatisticsEntry ::= SEQUENCE {
    mgmtFrerStatusStatisticsId               Integer32,
    mgmtFrerStatusStatisticsIfIndex          MGMTInterfaceIndex,
    mgmtFrerStatusStatisticsStreamId         Integer32,
    mgmtFrerStatusStatisticsOutOfOrder       Counter64,
    mgmtFrerStatusStatisticsRogue            Counter64,
    mgmtFrerStatusStatisticsPassed           Counter64,
    mgmtFrerStatusStatisticsDiscarded        Counter64,
    mgmtFrerStatusStatisticsLost             Counter64,
    mgmtFrerStatusStatisticsTagless          Counter64,
    mgmtFrerStatusStatisticsRecoveryResets   Counter64,
    mgmtFrerStatusStatisticsLaErrResets      Counter64,
    mgmtFrerStatusStatisticsGenrationResets  Counter64
}

mgmtFrerStatusStatisticsId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerStatusStatisticsEntry 1 }

mgmtFrerStatusStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtFrerStatusStatisticsEntry 2 }

mgmtFrerStatusStatisticsStreamId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The Stream ID"
    ::= { mgmtFrerStatusStatisticsEntry 3 }

mgmtFrerStatusStatisticsOutOfOrder OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyOutOfOrderPackets"
    ::= { mgmtFrerStatusStatisticsEntry 5 }

mgmtFrerStatusStatisticsRogue OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyRoguePackets"
    ::= { mgmtFrerStatusStatisticsEntry 6 }

mgmtFrerStatusStatisticsPassed OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyPassedPackets"
    ::= { mgmtFrerStatusStatisticsEntry 7 }

mgmtFrerStatusStatisticsDiscarded OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyDiscardedPackets"
    ::= { mgmtFrerStatusStatisticsEntry 8 }

mgmtFrerStatusStatisticsLost OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyLostPackets"
    ::= { mgmtFrerStatusStatisticsEntry 9 }

mgmtFrerStatusStatisticsTagless OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyTaglessPackets"
    ::= { mgmtFrerStatusStatisticsEntry 10 }

mgmtFrerStatusStatisticsRecoveryResets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyResets"
    ::= { mgmtFrerStatusStatisticsEntry 11 }

mgmtFrerStatusStatisticsLaErrResets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqRcvyLatentErrorResets (S/W counted)"
    ::= { mgmtFrerStatusStatisticsEntry 12 }

mgmtFrerStatusStatisticsGenrationResets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "frerCpsSeqGenResets (S/W counted)"
    ::= { mgmtFrerStatusStatisticsEntry 13 }

mgmtFrerControl OBJECT IDENTIFIER
    ::= { mgmtFrerMibObjects 4 }

mgmtFrerControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of FRER clear commands."
    ::= { mgmtFrerControl 1 }

mgmtFrerControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTFrerControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created FRER clear command."
    INDEX       { mgmtFrerControlStatisticsClearId }
    ::= { mgmtFrerControlStatisticsClearTable 1 }

MGMTFrerControlStatisticsClearEntry ::= SEQUENCE {
    mgmtFrerControlStatisticsClearId     Integer32,
    mgmtFrerControlStatisticsClearClear  TruthValue
}

mgmtFrerControlStatisticsClearId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerControlStatisticsClearEntry 2 }

mgmtFrerControlStatisticsClearClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the counters of an FRER instance."
    ::= { mgmtFrerControlStatisticsClearEntry 3 }

mgmtFrerControlClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFrerControlClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the FRER instance control table."
    ::= { mgmtFrerControl 2 }

mgmtFrerControlClearEntry OBJECT-TYPE
    SYNTAX      MGMTFrerControlClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents dynamic control elements an FRER
         instance."
    INDEX       { mgmtFrerControlClearId }
    ::= { mgmtFrerControlClearTable 1 }

MGMTFrerControlClearEntry ::= SEQUENCE {
    mgmtFrerControlClearId          Integer32,
    mgmtFrerControlClearReset       TruthValue,
    mgmtFrerControlClearLaErrClear  TruthValue
}

mgmtFrerControlClearId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The FRER instance ID"
    ::= { mgmtFrerControlClearEntry 2 }

mgmtFrerControlClearReset OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If this FRER instance is in generation mode, this member is used to
         reset thesequence number of the sequence generator.If this FRER
         instance is in recovery mode, this member is used to reset therecovery
         function. It resets both possible individual recovery functionsand the
         compound recovery functions. A value of false has no effect."
    ::= { mgmtFrerControlClearEntry 3 }

mgmtFrerControlClearLaErrClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear a sticky latent error.If this FRER instance is in generation
         mode, this member has no effect.If this FRER instance is in recovery
         mode, but latent error detection isdisabled, this member has no
         effect.A value of false has no effect."
    ::= { mgmtFrerControlClearEntry 4 }

mgmtFrerTrap OBJECT IDENTIFIER
    ::= { mgmtFrerMibObjects 6 }

mgmtFrerTrapAdd NOTIFICATION-TYPE
    OBJECTS     { mgmtFrerStatusNotificationId,
                  mgmtFrerStatusNotificationLatentError }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been added. The index(es) and value(s)
         of the row is included in the trap."

    ::= { mgmtFrerTrap 1 }

mgmtFrerTrapMod NOTIFICATION-TYPE
    OBJECTS     { mgmtFrerStatusNotificationId,
                  mgmtFrerStatusNotificationLatentError }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
          has been updated."

    ::= { mgmtFrerTrap 2 }

mgmtFrerTrapDel NOTIFICATION-TYPE
    OBJECTS     { mgmtFrerStatusNotificationId }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been deleted. The index(es) of the
         row is included in the trap."

    ::= { mgmtFrerTrap 3 }

mgmtFrerMibConformance OBJECT IDENTIFIER
    ::= { mgmtFrerMib 2 }

mgmtFrerMibCompliances OBJECT IDENTIFIER
    ::= { mgmtFrerMibConformance 1 }

mgmtFrerMibGroups OBJECT IDENTIFIER
    ::= { mgmtFrerMibConformance 2 }

mgmtFrerCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerCapabilitiesInstanceMax,
                  mgmtFrerCapabilitiesMstreamMax,
                  mgmtFrerCapabilitiesCstreamMax,
                  mgmtFrerCapabilitiesResetTimeoutMsecMin,
                  mgmtFrerCapabilitiesResetTimeoutMsecMax,
                  mgmtFrerCapabilitiesHistorylenMin,
                  mgmtFrerCapabilitiesHistorylenMax,
                  mgmtFrerCapabilitiesLaErrDifferenceMin,
                  mgmtFrerCapabilitiesLaErrDifferenceMax,
                  mgmtFrerCapabilitiesLaErrPeriodMsecMin,
                  mgmtFrerCapabilitiesLaErrPeriodMsecMax,
                  mgmtFrerCapabilitiesLaErrPathsMin,
                  mgmtFrerCapabilitiesLaErrPathsMax,
                  mgmtFrerCapabilitiesLaErrResetPeriodMsecMin,
                  mgmtFrerCapabilitiesLaErrResetPeriodMsecMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 1 }

mgmtFrerConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerConfigId, mgmtFrerConfigMode,
                  mgmtFrerConfigFrerVlan, mgmtFrerConfigEgressPorts,
                  mgmtFrerConfigAlgorithm, mgmtFrerConfigHistoryLen,
                  mgmtFrerConfigResetTimeoutMsec,
                  mgmtFrerConfigTakeNoSequence,
                  mgmtFrerConfigIndividualRecovery,
                  mgmtFrerConfigTerminate,
                  mgmtFrerConfigLaErrDetection,
                  mgmtFrerConfigLaErrDifference,
                  mgmtFrerConfigLaErrPeriodMsec,
                  mgmtFrerConfigLaErrPaths,
                  mgmtFrerConfigLaErrResetPeriodMsec,
                  mgmtFrerConfigAdminActive, mgmtFrerConfigStreamId0,
                  mgmtFrerConfigStreamId1, mgmtFrerConfigStreamId2,
                  mgmtFrerConfigStreamId3, mgmtFrerConfigStreamId4,
                  mgmtFrerConfigStreamId5, mgmtFrerConfigStreamId6,
                  mgmtFrerConfigStreamId7, mgmtFrerConfigAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 2 }

mgmtFrerConfigRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerConfigRowEditorId,
                  mgmtFrerConfigRowEditorMode,
                  mgmtFrerConfigRowEditorFrerVlan,
                  mgmtFrerConfigRowEditorEgressPorts,
                  mgmtFrerConfigRowEditorAlgorithm,
                  mgmtFrerConfigRowEditorHistoryLen,
                  mgmtFrerConfigRowEditorResetTimeoutMsec,
                  mgmtFrerConfigRowEditorTakeNoSequence,
                  mgmtFrerConfigRowEditorIndividualRecovery,
                  mgmtFrerConfigRowEditorTerminate,
                  mgmtFrerConfigRowEditorLaErrDetection,
                  mgmtFrerConfigRowEditorLaErrDifference,
                  mgmtFrerConfigRowEditorLaErrPeriodMsec,
                  mgmtFrerConfigRowEditorLaErrPaths,
                  mgmtFrerConfigRowEditorLaErrResetPeriodMsec,
                  mgmtFrerConfigRowEditorAdminActive,
                  mgmtFrerConfigRowEditorStreamId0,
                  mgmtFrerConfigRowEditorStreamId1,
                  mgmtFrerConfigRowEditorStreamId2,
                  mgmtFrerConfigRowEditorStreamId3,
                  mgmtFrerConfigRowEditorStreamId4,
                  mgmtFrerConfigRowEditorStreamId5,
                  mgmtFrerConfigRowEditorStreamId6,
                  mgmtFrerConfigRowEditorStreamId7,
                  mgmtFrerConfigRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 3 }

mgmtFrerStatusTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerStatusId, mgmtFrerStatusOperState,
                  mgmtFrerStatusLatentError,
                  mgmtFrerStatusWarningNone,
                  mgmtFrerStatusWarningSteamNotFound,
                  mgmtFrerStatusWarningNoIngressPorts,
                  mgmtFrerStatusWarningStreamControlFail,
                  mgmtFrerStatusWarningStreamOwnedBySomeoneElse,
                  mgmtFrerStatusWarningIngressEgressOverlap,
                  mgmtFrerStatusWarningEgressPortCnt,
                  mgmtFrerStatusWarningIngressNoLink,
                  mgmtFrerStatusWarningEgressNoLink,
                  mgmtFrerStatusWarningVlanMembership,
                  mgmtFrerStatusWarningStpBlocked,
                  mgmtFrerStatusWarningMstpBlocked }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 4 }

mgmtFrerStatusNotificationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerStatusNotificationId,
                  mgmtFrerStatusNotificationLatentError }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 5 }

mgmtFrerStatusStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerStatusStatisticsId,
                  mgmtFrerStatusStatisticsIfIndex,
                  mgmtFrerStatusStatisticsStreamId,
                  mgmtFrerStatusStatisticsOutOfOrder,
                  mgmtFrerStatusStatisticsRogue,
                  mgmtFrerStatusStatisticsPassed,
                  mgmtFrerStatusStatisticsDiscarded,
                  mgmtFrerStatusStatisticsLost,
                  mgmtFrerStatusStatisticsTagless,
                  mgmtFrerStatusStatisticsRecoveryResets,
                  mgmtFrerStatusStatisticsLaErrResets,
                  mgmtFrerStatusStatisticsGenrationResets }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 6 }

mgmtFrerControlStatisticsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerControlStatisticsClearId,
                  mgmtFrerControlStatisticsClearClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 7 }

mgmtFrerControlClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFrerControlClearId, mgmtFrerControlClearReset,
                  mgmtFrerControlClearLaErrClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFrerMibGroups 8 }

mgmtFrerTrapAddInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtFrerTrapAdd }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtFrerMibGroups 9 }

mgmtFrerTrapModInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtFrerTrapMod }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtFrerMibGroups 10 }

mgmtFrerTrapDelInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtFrerTrapDel }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtFrerMibGroups 11 }

mgmtFrerMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtFrerCapabilitiesInfoGroup,
                       mgmtFrerConfigTableInfoGroup,
                       mgmtFrerConfigRowEditorInfoGroup,
                       mgmtFrerStatusTableInfoGroup,
                       mgmtFrerStatusNotificationTableInfoGroup,
                       mgmtFrerStatusStatisticsTableInfoGroup,
                       mgmtFrerControlStatisticsClearTableInfoGroup,
                       mgmtFrerControlClearTableInfoGroup,
                       mgmtFrerTrapAddInfoGroup,
                       mgmtFrerTrapModInfoGroup,
                       mgmtFrerTrapDelInfoGroup }

    ::= { mgmtFrerMibCompliances 1 }

END
