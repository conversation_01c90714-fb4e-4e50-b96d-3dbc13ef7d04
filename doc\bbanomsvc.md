# bbanomsvc

Anomaly detection service

Anomaly detection service is a distributed architecture service named `bbanomsvc`, designed to leverage the OpenAI API for obtaining embeddings of syslog messages and then searching for the closest vector in the database. Users have the capability to define the metadata of vectors, for instance, marking a vector as normal or anomaly.

`bbanomsvc` cannot operate independently; it must be used in conjunction with `bbrootsvc`. This means that for the anomaly service (`bbanomsvc`) to function correctly, the root service (`bbrootsvc`) must first be running and accessible. `bbanomsvc` relies on `bbrootsvc` for certain operational functionalities, such as coordinating services, managing configurations, or possibly distributing tasks among multiple instances of the anomaly service.

The anomaly detection is an advanced tool designed to seamlessly integrate with the `NIMBL`. The feature integrated with OpenAI's API. Through its API and UI, users can effortlessly feed logs for analysis, detect anomalies. 


## Quick start

**Step 1. Run root service**
Start root server and give it a name `root`
```bash
$ bbrootsvc -n root 
```
**Step 2. Run anomaly service**
Start anomaly service and give it a name `an1`, `bbanomsvc` have to register itself to the root service for further tasks. We
need to provide the root service URL to the anomaly service. (`-r`)
```bash
$ bbanomsvc -n an1 -r {root_service_url}
```
For example if you run the root service on the same machine:
```bash
$ bbanomsvc -n an1 -r http://localhost:27182
```

**Step 3. Start syslog service, replace the root service URL with the actual root service URL**
Optional, if you want to test auto-detection you can start `bblogsvc` also.
```bash
$ bblogsvc -n syslog1 -r http://localhost:27182
```


**Step 4. Start UI**
Optional, all anomaly tasks can be done with Nimbl CLI utility `bbctl` or API. For convenience we suggest to start the UI.
```
$ bbnimbl
```
You should be able to see the reports uploaded by `bbanomsvc` from our UI. The default behavior is to upload one report daily.

Optional steps: Run ollama  
If you want to use Ollama as the LLM server, you can start the Ollama server with specific model.  
```bash
$ ollama run llama3
```


**Step 5. Config bbanomsvc**

- Use OpenAI:  In default, `bbanomsvc` uses OpenAI as the LLM server and provides a default OpenAI api key for users to try out the service. The default key has a limit of 50 requests. If you want to use your own OpenAI key, you can set it up with the following command:
```bash
$ bbctl -cc [client] anomaly config llm open-ai {api-key}
# example: config an1's OpenAI key
$ bbctl -cc an1 anomaly config llm openai sh-xxkdjuexxxxxkdjdjs
```

- Use ollama: If you want to use Ollama as the LLM server, you can set it up with the following command:
```bash
$ bbctl -cc [client] anomaly config llm ollama {host} {port} {model}
# example, ollama is running on localhost:1 with model llama3
$ bbctl -cc an1 anomaly config llm ollama http://localhost 11434 llama3
```

Switch to ollama:  
```bash
$ bbctl -cc an1 anomaly config llm connect ollama
```

Swtich to OpenAI:  
```bash
$ bbctl -cc an1 anomaly config llm connect open-ai
```


## Start bbanomsvc

Here's a brief overview of how to ensure both services work together:

1. Start `bbrootsvc`: First, launch the root service by specifying its name and port. This service acts as a central node that `bbanomsvc` connects to for coordination and configuration.  

Example:  
```
$ bbrootsvc -n root -p 27182
```
This command starts the root service with the name `root` and listens on port `27182`.

The `bbanomsvc` actively uploads data to `bbrootsvc` and retrieves commands from `bbrootsvc` for execution. In other words, the communication is not bidirectional; only `bbanomsvc` initiates connections to `bbrootsvc` to send and receive information. This means that `bbanomsvc` can be located behind a firewall, as long as it can reach `bbrootsvc`.

This architecture allows for a secure and efficient setup where `bbanomsvc`, the anomaly service, operates as a client that connects to `bbrootsvc`, the root service, which acts as a server. The key points of this setup include:



* **Security**: Since `bbanomsvc` does not need to accept incoming connections, it can be more securely positioned within a network, behind firewalls or within demilitarized zones (DMZs), reducing its exposure to potential threats.
* **Efficiency**: `bbanomsvc` only needs to establish an outbound connection to `bbrootsvc`, simplifying network configurations and potentially reducing the need for complex NAT (Network Address Translation) setups.
* **Flexibility**: This setup allows `bbanomsvc` to be deployed in various environments, including cloud services, where it might not have a static IP address or be directly accessible from the outside.

To ensure proper communication and functionality: 



1. **Configure the Firewall**: Ensure that the firewall allows outbound connections from the `bbanomsvc` host to the `bbrootsvc` host on the specified port.
2. **Verify Connectivity**: Before starting `bbanomsvc`, verify that it can reach `bbrootsvc` using tools like ping or telnet (for connection testing) on the specified port.
3. **Start Services Correctly**: First, start `bbrootsvc` with the necessary parameters (e.g., name and port). Then, start `bbanomsvc` and ensure it's configured to connect to `bbrootsvc` with the correct URL and port.

This approach ensures that `bbanomsvc` can operate effectively, even in restricted or highly secure network environments, by maintaining a single, outgoing connection to `bbrootsvc`.


## OpenAI Key Configuration and Usage Limits

Our service includes a default OpenAI API key, intended to provide new users with a seamless introduction to our anomaly detection capabilities. This key enables users to experiment with the service's features and understand its potential impact on their operations. However, to ensure fair usage and to introduce users to the OpenAI ecosystem, the default key is subject to a usage limit. Users are allowed to make up to 50 OpenAI API requests, sufficient to explore the basic functionalities and assess the service's value.

Once the initial 50 requests are exhausted, or if users prefer to utilize their own OpenAI API keys from the outset to benefit from higher request limits or specific OpenAI features, they are required to undertake a simple configuration step. 

use the API to set up api key:
```
POST /api/v1/anomaly/openai/settings 

Request Body:

{"settings":{"api_key": "your_openai_key"}, "client": "anomaly_service1" }
```

use command to set up api key:
```bash
$ bbctl -cc [client_name] anomaly config llm open-ai {api-key}
```
This setup ensures that the service seamlessly transitions to use the user-provided OpenAI key, allowing for uninterrupted service usage. It is a straightforward process designed to minimize disruption and ensure users can leverage the full potential of the anomaly detection service without being hindered by initial usage limits.


## Detecting Anomalies in Syslog Message Files and Confirming Reports

The `bbanomsvc` service provides a powerful tool for analyzing syslog message files for potential anomalies. Utilizing both Command Line Interface (CLI) commands and RESTful API endpoints, users can submit syslog files for analysis and subsequently confirm the generated reports. This section outlines the process for detecting anomalies using the anomaly detect CLI command and the */anomaly/detect* API endpoint, and how to confirm the generated anomaly reports.


### Using the CLI to Analyze Syslog Messages

To execute a command, users should follow the `bbctl` command structure, which encapsulates the necessary parameters and directives for the system to process the request effectively. The general syntax for using `bbctl` to detect syslog messages for anomalies is as follows:
```
$ bbctl -cc [client_name] anomaly detect [url] [distance]
```
- **cc [client_name]**: Specifies the `bbanomsvc` name, directing `bbctl` to use a specific anomaly service profile for the command. This allows for flexible command execution under different configurations or environments.

- **anomaly detect**: The command directive that tells `bbctl` to perform an anomaly analysis on a syslog message file.



* [url]: The URL or path to the syslog message file that needs to be analyzed. This can be a path to a local file or a URL to a file accessible over the network. Or a `bbrootsvc`'s file URL for example `https://bbrootsvc:27182/file/api/v1/files/log1.log`.
* [distance]: Specifies the distance threshold for anomaly detection. This parameter allows users to define the sensitivity of the anomaly detection process. This is optional and defaults to 0.4 if not provided.

Example:
```
$ bbctl -cc an1 anomaly detect file://example.com/syslog.log 0.5
```
### Using the API to Analyze Syslog Messages

1. **Endpoint Overview**:

    The `_/anomaly/detect_` API endpoint allows for the submission of syslog message files for analysis via HTTP POST requests. The request body should include the following fields:

*  **url**: The URL or local path to the syslog message file. Or a `**bbrootsvc**`'s file URL for example `https://bbrootsvc:27182/file/api/v1/files/log1.log`.
*  **distance**: The threshold for detecting anomalies (optional, default is 0.4). Larger values indicate a higher tolerance for differences between syslog messages.
* **client**: The name of the anomaly service to use for the analysis.
2. **Request Example**:
```
POST /api/v1/anomaly/detect
Content-Type: application/json
{
"url": "file://path/to/syslog.log",
"distance": 0.5,
"client": "anomaly_service1"
}
```
* This request submits a local syslog file for analysis, specifying a distance threshold of 0.5.


### Confirming Anomaly Reports

After submitting a syslog message file for analysis, `bbanomsvc` will generate a report detailing any detected anomalies. Users can confirm these reports :

Use the `/api/v1/anomaly/reports` endpoint to retrieve the generated anomaly reports. Confirming a report may involve reviewing the reported anomalies and taking necessary actions, such as adjusting monitoring thresholds or investigating potential security incidents.

The API endpoint `/api/v1/anomaly/reports` supports query parameters that allow for filtering based on a regex pattern applied to the source field. This functionality enables users to search for reports that match specific patterns within the source of the report, enhancing the ability to find relevant data quickly.

Example API request:
```
GET /api/v1/anomaly/reports?source=regex_pattern
```


* **source=regex_pattern**:The **source** query parameter accepts a regex pattern (regex_pattern) that is used to filter the reports. Only reports with a source field that matches the regex pattern will be returned.

Example:
```
GET /api/v1/anomaly/reports?source=log1
```
This request retrieves all anomaly reports where the source of the report matches the regular expression log1. In practice, this means it will return reports related to any log file names that contain "log1".



* **Usage Scenario**: If your system has processed multiple log files and you wish to review reports specifically related to a file or set of files identifiable by a common pattern in their names (e.g., "log1"), you can use this source parameter to quickly filter and access those reports.

## Real-Time Anomalies Detection with Syslog Message Streams
The real-time anomalies detection feature in `bbanomsvc` allows users to monitor syslog message streams for anomalies as they occur. By leveraging the `bblogsvc` service to stream syslog messages to `bbanomsvc`, users can receive immediate alerts and insights into potential anomalies in their system logs.

### Workflow Overview
The following is a high-level overview of the data flow between the components:
1. Upstream Syslog Reception (`bblogsvc`):  
   - The `bblogsvc` service is configurared to receive syslog messages that require real-time anomaly detection.
 
2. Anomaly Detection (`bbanomsvc`):
   - Once the `bbanomsvc` service is started, it notifies the `bblogsvc` service to begin forwarding syslog messages to `bbanomsvc` for real-time anomaly detection.
   - `bbanomsvc` processes the incoming syslog messages in real-time, analyzing the data for potential anomalies.
3. Root Service (`bbrootsvc`):
   - After the syslog messages are processed by `bbanomsvc`, they are forwarded to the `bbrootsvc` for further processing or storage.

```mermaid
flowchart LR
    A[Upstream Syslog Reception - bblogsvc] -- syslog --> B[Anomaly Detection - bbanomsvc]
    B -- syslog --> C[Root Service - bbrootsvc]
```

We recommend setting up multiple syslog services and routing syslog messages to different syslog services based on their categories. This approach can help prevent message congestion and make maintenance easier.

### Configurations
- Enable: To enable/disable real-time anomaly detection, beware that detection might come with a fee depending on selected LLM service.
- Buffer size: Message queue buffering size.

Command line example:
```bash
$bbctl -cc anm1 anomaly config realtime enable
$bbctl -cc anm1 anomaly config realtime disable
$bbctl -cc anm1 anomaly config realtime buffer-size 30
```

### APIs
`bbrootsvc` provides APIs to manage real-time anomaly detection, 

- **Start Realtime Detection**: Start real-time anomaly detection.
POST /api/v1/anomaly/realtime/start
```json
{
		"logsvc":"log1",
		"anomsvc":"an1",
		"anom_syslog_addr":"syslog_addr"
}
```
Response:
Response a list of commands that are executed by the root service to start the real-time detection. Commands should have but not limited to :
1. `syslog config remote :4979` to configure the syslog service to forward syslog messages to the anomaly service.
2. `anomaly config realtime enable` to enable the real-time detection in the anomaly service.

logsvc: the log service name
anomsvc: the anomaly service name
anom_syslog_addr: the syslog address of the anomaly service, this field is optional, if anomsvc and logsvc are in the same machine, this field can be omitted.

After the real-time detection is started, the `bbanomsvc` will start to receive the syslog messages from the `bblogsvc` and process them in real-time.

- **Stop Realtime Detection**: Stop real-time anomaly detection.
POST /api/v1/anomaly/realtime/stop
```json
{
      "anomsvc":"an1"
}
```
Response:
Response is a `anomaly config realtime disable` command's result.

- **Configure Realtime Detection**: Configure the real-time detection.
POST /api/v1/anomaly/realtime/settings
```json
{
   "anomsvc":"an1",
   "settings":{
     "buffer_size": 300
   }
}
``

Response:
Response is a list of commands that are executed by the root service to configure the real-time detection. 

- **Get Realtime Detection configuration**: Get the real-time detection configuration.
GET /api/v1/anomaly/realtime/settings?anomsvc=an1

Response:
```json
{
    "settings": {
        "enabled": false,
        "syslog_addr": ":4979",
        "buffer_size": 30,
    }
}
```

### Syslog Message Rate Limiting and Anomaly Alert Configuration for `bbanomsvc`
To ensure the stability and reliability of the system, `bbanomsvc` implements rate limiting on the number of syslog messages it processes per minute, as well as the number of anomalies it can detect within the same time frame. If these thresholds are exceeded, `bbanomsvc` will generate alert messages and forward them to `bbrootsvc` for further processing.

For example syslog message rate limiting alert:
```
<3>Dec 22 11:38:35 bbanomsvc(rt1): Too many syslog coming (over 100 per min)
<3>Dec 22 11:38:35 bbanomsvc(rt1): Too many anomalies message (over 10 per min)

```

## Enhancing Anomaly Detection Accuracy with RAG Integration and Flexible User Classification Options

The anomaly detection service incorporates RAG (Retrieval-Augmented Generation) to enhance AI accuracy, initially forwarding unrecognized messages to OpenAI for evaluation. This method quickly provides detailed insights into potential anomalies by using OpenAI's advanced technology to assess if a message is an anomaly. 

Additionally, the system supports RAG data management to further refine AI performance. Users can export RAG data to a specified URL with the command` anomaly rag export [url] [filename]`, or import RAG data from a given URL using` anomaly rag import [url].` This functionality not only offers flexibility in managing anomaly detection through both automated insights and user-driven classification but also enhances the system's decision-making process by incorporating external knowledge through RAG.

Use case1: 

In this case all NIMBL services are running in the same machine, we can export or import file from local file system.

* **bbanomsvc **and **bbrootsvc **are running in the same machine
* **bbanomsvc **start with name (-n) anom1
* We want to put the RAG file to /usr/user1 and naming rag.csv
```
$ bbctl -cc anom1 anomaly rag export file:///usr/user1 rag.csv
```
Modify the output csv file the import to the anomaly service
```
$ bbctl -cc anom1 anomaly rag import file:///usr/user1/rag.csv
```


You can do same thing on UI.

## Setting Up the Large Language Model (LLM) Server
The `bbanomsvc` supports two LLM server, OpenAI and Ollama.

> :warning: Since different LLM servers process data in different dimensions, switching LLM servers will invalidate all current RAG data. **It is strongly recommended to backup the knowledge before proceeding with the replacement**.

To backup knowledge, use the following command:
```bash
$ bbctl -cc [client_name] anomaly knowledge export [fileurl] [filename]
# for example save the knowledge to /usr/user1/rag.csv
$ bbctl -cc an1 anomaly knowledge export file:///usr/user1 rag.csv
```

Before switch LLM server, you may want to check the LLM server's setting, to set the LLM server, use the following command:  
```bash
# set up openAI
$ bbctl -cc [client_name] anomaly config llm open-ai {api-key}
# set up ollama
$ bbctl -cc [client_name] anomaly config llm ollama {host} {port} {model}
```
For example:  
```bash
$ bbctl -cc an1 anomaly config llm open-ai sh-xxkdjuexxxxxkdjdjs
$ bbctl -cc an1 anomaly config llm ollama localhost 8000 llama2
```

To switch LLM server, use the following command:  
```bash
$ bbctl -cc [client_name] anomaly config llm active {open-ai|ollama}
```
For example, to switch to Ollama:   
```bash
$ bbctl -cc an1 anomaly config llm active ollama
```

