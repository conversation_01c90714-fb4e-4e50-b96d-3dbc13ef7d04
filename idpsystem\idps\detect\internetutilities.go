package detect

import (
	"encoding/binary"
	"fmt"
	"net"
	"strconv"
	"strings"

	"github.com/asaskevich/govalidator"
	regexp "github.com/wasilibs/go-re2"
)

type Adder interface {
	AddInfo(string, bool) error
}

func newIPInfo() *iPInfo {
	return &iPInfo{}
}

type iPInfo struct {
	family  int
	netmask int
	ipnet   *net.IPNet
	ip      net.IP
	ips     []uint32
	negated bool
	signum  uint32
	next    *iPInfo
}

func (i *iPInfo) AddInfo(ipstr string, n bool) error {
	cur := i
	for cur != nil {
		if cur.family == 0 {
			ipv := ipstr
			if govalidator.IsCIDR(ipv) {
				ip, net, err := net.ParseCIDR(ipv)
				if err != nil {
					return err
				}
				ipv = ip.String()
				cur.ipnet = net
			}
			if govalidator.IsIPv4(ipv) {
				cur.netmask = 32
				cur.family = AFInet
			} else if govalidator.IsIPv6(ipv) {
				cur.netmask = 128
				cur.family = AFInet6
			} else {
				return fmt.Errorf("invalid IP address: %s", ipv)
			}
			if cur.ipnet != nil {
				cur.netmask, _ = cur.ipnet.Mask.Size()
			}
			cur.ip = net.ParseIP(ipv)
			cur.ips, _ = IPToUint32Array(ipv)
			cur.negated = n
			return nil
		} else if cur.next == nil {
			cur.next = &iPInfo{family: 0}
		}
		cur = cur.next
	}
	return nil
}
func (i *iPInfo) String() string {
	if i.ipnet != nil {
		return i.ipnet.String()
	}
	return i.ip.String()
}

func CompareIPInfo(l *iPInfo, r *iPInfo) bool {
	if l.netmask == r.netmask {
		for i := 0; i < l.netmask/32 || i < 1; i++ {
			if r.ips[i] > l.ips[i] {
				return true
			} else if r.ips[i] < l.ips[i] {
				return false
			} else if r.ips[i] == l.ips[i] {
				continue
			}
		}
	} else {
		return r.netmask > l.netmask
	}
	return true
}

func newPortInfo() Adder {
	return &portInfo{}
}

type portInfo struct {
	port    string
	negated bool
	next    *portInfo
}

func (i *portInfo) AddInfo(port string, n bool) error {
	cur := i
	for cur != nil {
		if len(cur.port) == 0 {
			cur.port = port
			cur.negated = n
			return nil
		} else if cur.next == nil {
			cur.next = &portInfo{}
		}
		cur = cur.next
	}
	return nil
}

type empty struct{}

type parseingType int

const (
	iPType parseingType = 1 + iota
	portType
)

var (
	arrmatch  = regexp.MustCompile(`(!*)(\[)(.*)(\])`)
	iPv4      = regexp.MustCompile(`(((!*)(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}(/\d+)*)+)`)
	iPv6      = regexp.MustCompile(`((!*)(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|(([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)))(%.+)?)(/(12[0-8]|1[01][0-9]|[1-9]?[0-9]))?$`)
	regexport = regexp.MustCompile(`(([0-9]+:[0-9]+)+)|(([0-9]+:)+)|((!*[0-9]+)+)`)
	azAZRegex = regexp.MustCompile(`(\$*)[a-zA-Z\_]+`)
	total     = regexp.MustCompile(arrmatch.String() + `|` + iPv4.String() + `|` + iPv6.String() + `|` + regexport.String() + `|` + azAZRegex.String())
)

// parseIpAndPort
func parseIpAndPort(s string, infos Adder, negative bool, t parseingType) error {
	switch {
	case arrmatch.MatchString(s):
		v := arrmatch.FindAllStringSubmatch(s, -1)
		if len(v) != 1 {
			return fmt.Errorf("%v,%v", ErrorFormat, s)
		}
		if len(v[0]) != 5 {
			return fmt.Errorf("%v,%v", ErrorFormat, s)
		}
		//allneagtive := false
		vn := v[0][1] //! or ""
		if vn == "!" {
			negative = true
		}
		st := v[0][2] //"["
		if st != "[" {
			return fmt.Errorf("%v,%v", ErrorFormat, s)
		}
		vs := v[0][3]  //values
		end := v[0][4] //"]""
		if end != "]" {
			return fmt.Errorf("%v,%v", ErrorFormat, s)
		}
		var ps []string
		switch {
		case total.MatchString(vs):
			ps = strings.Split(vs, ",")
		default:
			return fmt.Errorf("%v,%v", ErrorFormat, v)
		}

		for _, v := range ps {
			if len(v) == 0 {
				continue
			}
			err := parseIpAndPort(v, infos, negative, t)
			if err != nil {
				return fmt.Errorf("%v,%v", ErrorFormat, v)
			}

		}
	case iPv4.MatchString(s):
		err := regexpIp(infos, s, negative)
		if err != nil {
			return err
		}
	case iPv6.MatchString(s):
		err := regexpIp(infos, s, negative)
		if err != nil {
			return err
		}
	case regexport.MatchString(s):
		err := regexpPort(infos, s, negative)
		if err != nil {
			return err
		}
	case azAZRegex.MatchString(s):
		switch t {
		case portType:
			switch s {
			case AnyPort:
				err := infos.AddInfo("0", negative)
				if err != nil {
					return err
				}
			default:
				return fmt.Errorf("parsing port:%v,%v", ErrorFormat, s)
			}
		case iPType:
			var ips []string
			switch s {
			case HomeNet:
				ips = configuration.homeIP
			case ExternalNet:
				ips = configuration.homeIP
				negative = !negative
			case AnyNet:
				ips = append(ips, configuration.anyIP...)
			default:
				return fmt.Errorf("parsing ip:%v,%v", ErrorFormat, s)
			}
			for _, ip := range ips {
				err := infos.AddInfo(ip, negative)
				if err != nil {
					return err
				}
			}
		}

	default:
		return fmt.Errorf("%v,%v", ErrorFormat, s)
	}
	return nil
}
func regexpIp(info Adder, s string, negate bool) error {
	if strings.HasPrefix(s, "!") {
		negate = !negate
		s = strings.TrimPrefix(s, "!")
	}
	return info.AddInfo(s, negate)
}
func regexpPort(info Adder, s string, negate bool) error {
	if strings.HasPrefix(s, "!") {
		negate = !negate
		s = strings.TrimPrefix(s, "!")
	}
	_, err := parsePortRange(s)
	if err != nil {
		return err
	}
	return info.AddInfo(s, negate)
}

var (
	portRangeR1 = regexp.MustCompile(`(\[*)([0-9]+)\:([0-9]+)(\]*)`)
	portRangeR2 = regexp.MustCompile(`(\[*)([0-9]+)(\:)(\]*)`)
	portRangeR3 = regexp.MustCompile(`(\[*)(\:)([0-9]+)(\]*)`)
	portRangeR4 = regexp.MustCompile(`(\[*)([0-9]+)(\]*)`)
	portnum     = regexp.MustCompile(`([0-9]+)`)
)

func parsePortRange(port string) ([]uint16, error) {
	switch {
	case portRangeR1.MatchString(port):
		vs := portnum.FindAllString(port, -1)
		if len(vs) > 2 {
			return []uint16{}, Errorportforamt
		}
		res := []uint16{0, 0}
		for index, v := range vs {
			i, err := strconv.ParseUint(v, 10, 16)
			if err != nil {
				return []uint16{}, err
			}
			res[index] = uint16(i)
			if index == (len(res) - 1) {
				if res[0] < res[1] {
					return res, nil
				} else {
					return res, fmt.Errorf("%v,%v", ErrorportRangeforamt, res)
				}

			}
		}
	case portRangeR2.MatchString(port):
		vs := portnum.FindAllString(port, -1)
		if len(vs) == 1 {
			res := []uint16{0, 0}
			i, err := strconv.ParseUint(vs[0], 10, 16)
			if err != nil {
				return []uint16{}, err
			}
			res[0] = uint16(i)
			res[1] = 65535
			return res, nil
		}

	case portRangeR3.MatchString(port):
		vs := portnum.FindAllString(port, -1)
		if len(vs) == 1 {
			res := []uint16{0, 0}
			i, err := strconv.ParseUint(vs[0], 10, 16)
			if err != nil {
				return []uint16{}, err
			}
			res[0] = 1
			res[1] = uint16(i)
			return res, nil
		}
	case portRangeR4.MatchString(port):
		i, err := strconv.ParseUint(port, 10, 16)
		if err != nil {
			return []uint16{}, err
		}
		return []uint16{uint16(i)}, nil
	case port == AnyPort:
		return []uint16{anyport}, nil
	}
	return []uint16{}, fmt.Errorf("%v,%v", Errorportforamt, port)
}

func IPToUint32Array(ipStr string) ([]uint32, error) {
	var result []uint32

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP: %s", ipStr)
	}

	if ip.To4() != nil {

		result = make([]uint32, 4)
		result[0] = binary.BigEndian.Uint32(ip.To4())
	} else {

		ip = ip.To16()
		for i := 0; i < 4; i++ {
			start := i * 4
			result = append(result, binary.BigEndian.Uint32(ip[start:start+4]))
		}
	}
	return result, nil
}

func Uint32ArrayToIP(arr []uint32) (net.IP, error) {
	if len(arr) == 1 {
		ip := make(net.IP, 4)
		binary.BigEndian.PutUint32(ip, arr[0])
		return ip, nil
	} else if len(arr) == 4 {
		ip := make(net.IP, 16)
		for i := 0; i < 4; i++ {
			start := i * 4
			binary.BigEndian.PutUint32(ip[start:start+4], arr[i])
		}
		return ip, nil
	} else {
		return nil, fmt.Errorf("invalid uint32 ")
	}
}

func quickSortIPInfo(arr []*iPInfo, low, high int) {
	if low < high {
		mid := partitionIPInfo(arr, low, high)
		quickSortIPInfo(arr, 0, mid-1)
		quickSortIPInfo(arr, mid+1, high)
	}
}
func partitionIPInfo(arr []*iPInfo, low, high int) int {
	pivot := arr[high]
	piovidx := high
	for low < high {
		for low < high && CompareIPInfo(arr[low], pivot) /*arr[low].Size() <= pivot.Size()*/ {
			low++
		}
		for low < high && CompareIPInfo(pivot, arr[high]) /*arr[high].Size() >= pivot.Size() */ {
			high--
		}
		if low < high {
			arr[low], arr[high] = arr[high], arr[low]
		}
	}
	arr[low], arr[piovidx] = arr[piovidx], arr[low]
	return low
}
