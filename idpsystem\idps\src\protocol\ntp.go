package protocol

import (
	"bytes"
	"encoding/binary"
	"errors"
	"time"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

//var LayerNtp = gopacket.RegisterLayerType(int(ntpProto), gopacket.LayerTypeMetadata{Name: ntpProto.String(), Decoder: gopacket.DecodeFunc(decodeNtp)})

type ntpParser struct {
}

func (p *ntpParser) Parse(pack gopacket.Packet) bool {
	up := udpParser{}
	b := up.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeNtp(up.GetUdp().Payload)
	return err == nil
}

type ntp struct {
	layers.BaseLayer
}

func decodeNtp(data []byte) (*ntp, error) {
	n := &ntp{}
	err := n.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return n, err
}

func (h *ntp) DecodeFromBytes(data []byte) error {
	hdr := new(header)
	recvReader := bytes.NewReader(data)
	err := binary.Read(recvReader, binary.BigEndian, hdr)
	if err != nil {
		return err
	}

	if hdr.getVersion() < version1 || hdr.getVersion() > version4 {
		return ErrInvalidProtocolVersion
	}

	switch hdr.getMode() {
	case server:
		if hdr.TransmitTime == ntpTime(0) {
			return ErrInvalidTransmitTime
		}
		if hdr.getLeap() == LeapNotInSync {
			return ErrInvalidLeapSecond
		}
		if hdr.Stratum == 0 {
			return ErrKissOfDeath
		}
		if hdr.Stratum >= maxStratum {
			return ErrInvalidStratum
		}
		time := hdr.TransmitTime.Time()
		freshness := time.Sub(hdr.ReceiveTime.Time())
		if freshness > maxPollInterval {
			return ErrServerClockFreshness
		}
		rootDelay := hdr.RootDelay.Duration()
		lambda := rootDelay/2 + hdr.RootDispersion.Duration()
		if lambda > maxDispersion {
			return ErrInvalidDispersion
		}
		if time.Before(hdr.ReceiveTime.Time()) {
			return ErrInvalidTime
		}

	case client:

		if hdr.getLeap() != LeapNoWarning && hdr.getLeap() != LeapNotInSync {
			return ErrInvalidLeapSecond
		}
		if hdr.Precision == 0 {
			return errors.New("error")
		}
	default:
		return ErrInvalidMode
	}

	h.BaseLayer = layers.BaseLayer{Contents: data, Payload: data}
	return nil
}

// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (h *ntp) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}

func (h *ntp) Payload() []byte {
	return h.BaseLayer.Payload
}

// The LeapIndicator is used to warn if a leap second should be inserted
// or deleted in the last minute of the current month.
type LeapIndicator uint8

const (
	// LeapNoWarning indicates no impending leap second.
	LeapNoWarning LeapIndicator = 0

	// LeapAddSecond indicates the last minute of the day has 61 seconds.
	LeapAddSecond = 1

	// LeapDelSecond indicates the last minute of the day has 59 seconds.
	LeapDelSecond = 2

	// LeapNotInSync indicates an unsynchronized leap second.
	LeapNotInSync = 3
)

// NTP modes. This package uses only client mode.
const (
	reserved mode = 0 + iota
	symmetricActive
	symmetricPassive
	client
	server
	broadcast
	controlMessage
	reservedPrivate
)

// Internal variables
var (
	ntpEpoch = time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)
)

// An ntpTimeShort is a 32-bit fixed-point (Q16.16) representation of the
// number of seconds elapsed.
type ntpTimeShort uint32

// Duration interprets the fixed-point ntpTimeShort as a number of elapsed
// seconds and returns the corresponding time.Duration value.
func (t ntpTimeShort) Duration() time.Duration {
	sec := uint64(t>>16) * nanoPerSec
	frac := uint64(t&0xffff) * nanoPerSec
	nsec := frac >> 16
	if uint16(frac) >= 0x8000 {
		nsec++
	}
	return time.Duration(sec + nsec)
}

type ntpTime uint64

// Duration interprets the fixed-point ntpTime as a number of elapsed seconds
// and returns the corresponding time.Duration value.
func (t ntpTime) Duration() time.Duration {
	sec := (t >> 32) * nanoPerSec
	frac := (t & 0xffffffff) * nanoPerSec
	nsec := frac >> 32
	if uint32(frac) >= 0x80000000 {
		nsec++
	}
	return time.Duration(sec + nsec)
}

// Time interprets the fixed-point ntpTime as an absolute time and returns
// the corresponding time.Time value.
func (t ntpTime) Time() time.Time {
	return ntpEpoch.Add(t.Duration())
}

type mode uint8
type version uint8

const (
	version0 version = iota
	version1
	version2
	version3
	version4
)
const (
	defaultNtpVersion = 4
	defaultNtpPort    = 123
	nanoPerSec        = 1000000000
	maxStratum        = 16
	defaultTimeout    = 5 * time.Second
	maxPollInterval   = (1 << 17) * time.Second
	maxDispersion     = 16 * time.Second
)

// header is an internal representation of an NTP packet header.
type header struct {
	LiVnMode       uint8 // Leap Indicator (2) + Version (3) + Mode (3)
	Stratum        uint8
	Poll           int8
	Precision      int8
	RootDelay      ntpTimeShort
	RootDispersion ntpTimeShort
	ReferenceID    uint32 // KoD code if Stratum == 0
	ReferenceTime  ntpTime
	OriginTime     ntpTime
	ReceiveTime    ntpTime
	TransmitTime   ntpTime
}

// getMode returns the mode value in the header.
func (h *header) getMode() mode {
	return mode(h.LiVnMode & 0x07)
}

// getLeap returns the leap indicator on the header.
func (h *header) getLeap() LeapIndicator {
	return LeapIndicator((h.LiVnMode >> 6) & 0x03)
}

// getMode returns the mode value in the header.
func (h *header) getVersion() version {
	return version((h.LiVnMode >> 3) & 0x07)
}

var (
	ErrAuthFailed             = errors.New("authentication failed")
	ErrInvalidAuthKey         = errors.New("invalid authentication key")
	ErrInvalidDispersion      = errors.New("invalid dispersion in response")
	ErrInvalidLeapSecond      = errors.New("invalid leap second in response")
	ErrInvalidMode            = errors.New("invalid mode in response")
	ErrInvalidProtocolVersion = errors.New("invalid protocol version requested")
	ErrInvalidStratum         = errors.New("invalid stratum in response")
	ErrInvalidTime            = errors.New("invalid time reported")
	ErrInvalidTransmitTime    = errors.New("invalid transmit time in response")
	ErrKissOfDeath            = errors.New("kiss of death received")
	ErrServerClockFreshness   = errors.New("server clock not fresh")
	ErrServerResponseMismatch = errors.New("server response didn't match request")
	ErrServerTickedBackwards  = errors.New("server clock ticked backwards")
)

// A Response contains time data, some of which is returned by the NTP server
// and some of which is calculated by this client.
type Response struct {
	// Time is the transmit time reported by the server just before it
	// responded to the client's NTP query. You should not use this value
	// for time synchronization purposes. Use the ClockOffset instead.
	Time time.Time

	// ClockOffset is the estimated offset of the local system clock relative
	// to the server's clock. Add this value to subsequent local system time
	// measurements in order to obtain a more accurate time.
	ClockOffset time.Duration

	// RTT is the measured round-trip-time delay estimate between the client
	// and the server.
	RTT time.Duration

	// Precision is the reported precision of the server's clock.
	Precision time.Duration

	// Stratum is the "stratum level" of the server. The smaller the number,
	// the closer the server is to the reference clock. Stratum 1 servers are
	// attached directly to the reference clock. A stratum value of 0
	// indicates the "kiss of death," which typically occurs when the client
	// issues too many requests to the server in a short period of time.
	Stratum uint8

	// ReferenceID is a 32-bit integer identifying the server or reference
	// clock. For stratum 1 servers, this is typically a meaningful
	// zero-padded ASCII-encoded string assigned to the clock. For stratum 2+
	// servers, this is a reference identifier for the server and is either
	// the server's IPv4 address or a hash of its IPv6 address. For
	// kiss-of-death responses (stratum 0), this is the ASCII-encoded "kiss
	// code".
	ReferenceID uint32

	// ReferenceTime is the time when the server's system clock was last
	// set or corrected.
	ReferenceTime time.Time

	// RootDelay is the server's estimated aggregate round-trip-time delay to
	// the stratum 1 server.
	RootDelay time.Duration

	// RootDispersion is the server's estimated maximum measurement error
	// relative to the stratum 1 server.
	RootDispersion time.Duration

	// RootDistance is an estimate of the total synchronization distance
	// between the client and the stratum 1 server.
	RootDistance time.Duration

	// Leap indicates whether a leap second should be added or removed from
	// the current month's last minute.
	Leap LeapIndicator

	// MinError is a lower bound on the error between the client and server
	// clocks. When the client and server are not synchronized to the same
	// clock, the reported timestamps may appear to violate the principle of
	// causality. In other words, the NTP server's response may indicate
	// that a message was received before it was sent. In such cases, the
	// minimum error may be useful.
	MinError time.Duration

	// KissCode is a 4-character string describing the reason for a
	// "kiss of death" response (stratum=0). For a list of standard kiss
	// codes, see https://tools.ietf.org/html/rfc5905#section-7.4.
	KissCode string

	// Poll is the maximum interval between successive NTP query messages to
	// the server.
	Poll time.Duration

	authErr error
}
