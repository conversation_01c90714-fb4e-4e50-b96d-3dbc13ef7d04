import { useEffect, useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import { theme as antdTheme } from "antd";
import { useTheme } from "antd-style";

const IdpsPieChart = ({ data = [] }) => {
  const { appearance } = useTheme();
  const { token } = antdTheme.useToken();
  const [pieChartData, setPieChartData] = useState({
    series: [0],
    options: {
      chart: {
        width: "100%",
        background: token.colorBgContainer,
        type: "pie",
      },
      theme: {
        mode: appearance,
      },
      legend: {
        position: "bottom",
      },
      plotOptions: {
        pie: {
          dataLabels: {
            offset: -15,
          },
        },
      },
      labels: ["no data"],
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          return opts.w.config.series[opts.seriesIndex];
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 150,
            },
            legend: {
              position: "bottom",
            },
          },
        },
      ],
    },
  });
  useEffect(() => {
    setPieChartData((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        theme: { mode: appearance },
        chart: { ...prev.options.chart, background: token.colorBgContainer },
      },
    }));
  }, [token, appearance]);

  useEffect(() => {
    setPieChartData((prev) => ({
      ...prev,
      series: data.length > 0 ? data.map((item) => item.counts) : [0],
      options: {
        ...prev.options,
        labels:
          data.length > 0
            ? data.map((item) => `${item.sid}(${item.action})(${item.counts}) `)
            : ["no data"],
      },
    }));
  }, [data]);

  return (
    <ReactApexChart
      options={pieChartData.options}
      series={pieChartData.series}
      type="pie"
      width="100%"
    />
  );
};
export default IdpsPieChart;
