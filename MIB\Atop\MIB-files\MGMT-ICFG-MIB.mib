-- *****************************************************************
-- ICFG-MIB:  
-- ****************************************************************

MGMT-ICFG-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    ;

mgmtIcfgMib MODULE-IDENTITY
    LAST-UPDATED "201605090000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of ICFG"
    REVISION    "201605090000Z"
    DESCRIPTION
        "Add support for allocated/free flash size"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 101 }


MGMTIcfgConfigStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of configuration."
    SYNTAX      INTEGER { none(0), success(1), inProgress(2),
                          errOtherInProcessing(3), errNoSuchFile(4),
                          errSameSrcDst(5), errPermissionDenied(6),
                          errLoadSrc(7), errSaveDst(8) }

MGMTIcfgConfigType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of configuration."
    SYNTAX      INTEGER { none(0), runningConfig(1), startupConfig(2),
                          configFile(3) }

MGMTIcfgReloadDefault ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of reload default."
    SYNTAX      INTEGER { none(0), default(1), defaultKeepIp(2) }

mgmtIcfgMibObjects OBJECT IDENTIFIER
    ::= { mgmtIcfgMib 1 }

mgmtIcfgStatus OBJECT IDENTIFIER
    ::= { mgmtIcfgMibObjects 3 }

mgmtIcfgStatusFileStatistics OBJECT IDENTIFIER
    ::= { mgmtIcfgStatus 1 }

mgmtIcfgStatusFileStatisticsNumberOfFiles OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of files in flash."
    ::= { mgmtIcfgStatusFileStatistics 1 }

mgmtIcfgStatusFileStatisticsTotalBytes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of bytes used by all files in flash."
    ::= { mgmtIcfgStatusFileStatistics 2 }

mgmtIcfgStatusFileStatisticsFlashSizeBytes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Flash file system size in bytes."
    ::= { mgmtIcfgStatusFileStatistics 3 }

mgmtIcfgStatusFileStatisticsFlashFreeBytes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Flash file system number of free bytes."
    ::= { mgmtIcfgStatusFileStatistics 4 }

mgmtIcfgStatusFileTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIcfgStatusFileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of status of files in flash."
    ::= { mgmtIcfgStatus 2 }

mgmtIcfgStatusFileEntry OBJECT-TYPE
    SYNTAX      MGMTIcfgStatusFileEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of file status."
    INDEX       { mgmtIcfgStatusFileFileNo }
    ::= { mgmtIcfgStatusFileTable 1 }

MGMTIcfgStatusFileEntry ::= SEQUENCE {
    mgmtIcfgStatusFileFileNo        Integer32,
    mgmtIcfgStatusFileFileName      MGMTDisplayString,
    mgmtIcfgStatusFileBytes         Unsigned32,
    mgmtIcfgStatusFileModifiedTime  MGMTDisplayString,
    mgmtIcfgStatusFileAttribute     MGMTDisplayString
}

mgmtIcfgStatusFileFileNo OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The number of File. The number starts from 1."
    ::= { mgmtIcfgStatusFileEntry 1 }

mgmtIcfgStatusFileFileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "File name."
    ::= { mgmtIcfgStatusFileEntry 2 }

mgmtIcfgStatusFileBytes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of bytes of the file."
    ::= { mgmtIcfgStatusFileEntry 3 }

mgmtIcfgStatusFileModifiedTime OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..39))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Last modified time of the file."
    ::= { mgmtIcfgStatusFileEntry 4 }

mgmtIcfgStatusFileAttribute OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..15))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "File attribute in the format of 'rw'. 'r' presents readable while 'w'
         presents writable."
    ::= { mgmtIcfgStatusFileEntry 5 }

mgmtIcfgStatusCopyConfig OBJECT IDENTIFIER
    ::= { mgmtIcfgStatus 3 }

mgmtIcfgStatusCopyConfigStatus OBJECT-TYPE
    SYNTAX      MGMTIcfgConfigStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The status indicates the status of current copy operation. none(0)
         means no copy operation. success(1) means copy operation is successful.
         inProgress(2) means current copy operation is in progress.
         errOtherInProcessing(3) means copy operation is failed due to other in
         processing. errNoSuchFile(4) means copy operation is failed due to file
         not existing. errSameSrcDst(5) means copy operation is failed due to
         the source and destination are the same. errPermissionDenied(6) means
         copy operation is failed due to the destination is not permitted to
         modify. errLoadSrc(7) means copy operation is failed due to the error
         to load source file. errSaveDst(8) means copy operation is failed due
         to the error to save or commit destination."
    ::= { mgmtIcfgStatusCopyConfig 1 }

mgmtIcfgControl OBJECT IDENTIFIER
    ::= { mgmtIcfgMibObjects 4 }

mgmtIcfgControlGlobals OBJECT IDENTIFIER
    ::= { mgmtIcfgControl 1 }

mgmtIcfgControlGlobalsReloadDefault OBJECT-TYPE
    SYNTAX      MGMTIcfgReloadDefault
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Reset system to default. none(0) is to do nothing. default(1) is to
         reset the whole system to default. defaultKeepIp(2) is to reset system
         to default, but keep IP address of VLAN 1."
    ::= { mgmtIcfgControlGlobals 1 }

mgmtIcfgControlGlobalsDeleteFile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Delete file in flash. The format is flash:filename. Where
         'default-config' is read-only and not allowed to be deleted."
    ::= { mgmtIcfgControlGlobals 2 }

mgmtIcfgControlCopyConfig OBJECT IDENTIFIER
    ::= { mgmtIcfgControl 2 }

mgmtIcfgControlCopyConfigCopy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action to do copy or not. true is to do the copy operation. false is to
         do nothing"
    ::= { mgmtIcfgControlCopyConfig 1 }

mgmtIcfgControlCopyConfigSourceConfigType OBJECT-TYPE
    SYNTAX      MGMTIcfgConfigType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source configuration type. none(0) means no configuration file.
         runningConfig(1) means running configuration. startupConfig(2) means
         startup configuration file in flash. configFile(3) is the configuration
         file specified in SourceConfigFile."
    ::= { mgmtIcfgControlCopyConfig 2 }

mgmtIcfgControlCopyConfigSourceConfigFile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source configuration file. If the configuration file is in flash then
         the format is flash:'filename'. If the configuration file is from tftp
         then the format is tftp://server[:port]/path-to-file."
    ::= { mgmtIcfgControlCopyConfig 3 }

mgmtIcfgControlCopyConfigDestinationConfigType OBJECT-TYPE
    SYNTAX      MGMTIcfgConfigType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source configuration type. none(0) means no configuration file.
         runningConfig(1) means running configuration. startupConfig(2) means
         startup configuration file in flash. configFile(3) is the configuration
         file specified in DestinationConfigFile."
    ::= { mgmtIcfgControlCopyConfig 4 }

mgmtIcfgControlCopyConfigDestinationConfigFile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Destination configuration file. If the configuration file is in flash
         then the format is flash:filename. If the configuration file is from
         tftp then the format is tftp://server[:port]/filename_with_path. Where
         'default-config' is read-only and not allowed to be deleted."
    ::= { mgmtIcfgControlCopyConfig 5 }

mgmtIcfgControlCopyConfigMerge OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This flag works only if DestinationConfigType is runningConfig(1). true
         is to merge the source configuration into the current running
         configuration. false is to replace the current running configuration
         with the source configuration."
    ::= { mgmtIcfgControlCopyConfig 6 }

mgmtIcfgMibConformance OBJECT IDENTIFIER
    ::= { mgmtIcfgMib 2 }

mgmtIcfgMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIcfgMibConformance 1 }

mgmtIcfgMibGroups OBJECT IDENTIFIER
    ::= { mgmtIcfgMibConformance 2 }

mgmtIcfgStatusFileStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIcfgStatusFileStatisticsNumberOfFiles,
                  mgmtIcfgStatusFileStatisticsTotalBytes,
                  mgmtIcfgStatusFileStatisticsFlashSizeBytes,
                  mgmtIcfgStatusFileStatisticsFlashFreeBytes }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIcfgMibGroups 1 }

mgmtIcfgStatusFileTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIcfgStatusFileFileNo,
                  mgmtIcfgStatusFileFileName, mgmtIcfgStatusFileBytes,
                  mgmtIcfgStatusFileModifiedTime,
                  mgmtIcfgStatusFileAttribute }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIcfgMibGroups 2 }

mgmtIcfgStatusCopyConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIcfgStatusCopyConfigStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIcfgMibGroups 3 }

mgmtIcfgControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIcfgControlGlobalsReloadDefault,
                  mgmtIcfgControlGlobalsDeleteFile }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIcfgMibGroups 4 }

mgmtIcfgControlCopyConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIcfgControlCopyConfigCopy,
                  mgmtIcfgControlCopyConfigSourceConfigType,
                  mgmtIcfgControlCopyConfigSourceConfigFile,
                  mgmtIcfgControlCopyConfigDestinationConfigType,
                  mgmtIcfgControlCopyConfigDestinationConfigFile,
                  mgmtIcfgControlCopyConfigMerge }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIcfgMibGroups 5 }

mgmtIcfgMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIcfgStatusFileStatisticsInfoGroup,
                       mgmtIcfgStatusFileTableInfoGroup,
                       mgmtIcfgStatusCopyConfigInfoGroup,
                       mgmtIcfgControlGlobalsInfoGroup,
                       mgmtIcfgControlCopyConfigInfoGroup }

    ::= { mgmtIcfgMibCompliances 1 }

END
