package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestBasicByteExtract(t *testing.T) {
	var tests = []struct {
		content  string
		expected byteExtract
	}{

		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one ;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           0,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, relative;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagRelative,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, multiplier 10;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagMultiplier,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: 10,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2,one, relative, multiplier 10;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagRelative | byteExtractFlagMultiplier,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: 10,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, big;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagEndian,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, little;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagEndian,
				byteEndian:      littleEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, dce;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagEndian,
				byteEndian:      endianDCE,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, string, hex;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagBase | byteExtractFlagString,
				base:            byteExtractBaseHex,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, string, oct;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagBase | byteExtractFlagString,
				base:            byteExtractBaseOct,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, string, dec;sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagBase | byteExtractFlagString,
				base:            byteExtractBaseDec,
				byteEndian:      bigEndian,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:4, 2, one, align 4;sid:1;)`,
			expected: byteExtract{
				name:       "one",
				nbytes:     4,
				offset:     2,
				flags:      byteExtractFlagAlign,
				alignValue: 4,
				byteEndian: bigEndian,

				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative,
				alignValue:      4,
				byteEndian:      bigEndian,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative, big";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative | byteExtractFlagEndian,
				alignValue:      4,
				byteEndian:      bigEndian,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative, dce";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative | byteExtractFlagEndian,
				alignValue:      4,
				byteEndian:      endianDCE,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative, little";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative | byteExtractFlagEndian,
				alignValue:      4,
				byteEndian:      littleEndian,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative, little, multiplier 2";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative | byteExtractFlagEndian | byteExtractFlagMultiplier,
				alignValue:      4,
				byteEndian:      littleEndian,
				multiplierValue: 2,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_extract:"4, 2, one, align 4, relative, little, multiplier 2";sid:1;)`,
			expected: byteExtract{
				name:            "one",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagAlign | byteExtractFlagRelative | byteExtractFlagEndian | byteExtractFlagMultiplier,
				alignValue:      4,
				byteEndian:      littleEndian,
				multiplierValue: 2,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";content:"one";
                                   byte_extract:4,0,two,string,hex;
                                   byte_test: 2,=,10, two;
                                   sid:1;)`,
			expected: byteExtract{
				name:            "two",
				nbytes:          4,
				offset:          2,
				flags:           byteExtractFlagString | byteExtractFlagBase,
				base:            byteExtractBaseHex,
				alignValue:      0,
				multiplierValue: byteExtractMultiplierDefault,
			},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			if v, ok := d.RetrieveData().(*byteExtract); ok {
				if v.name != test.expected.name {
					t.Error("name error: unexpected")
				}
				if v.nbytes != test.expected.nbytes {
					t.Error("nbytes error: unexpected")
				}
				if v.offset != test.expected.offset {
					t.Error("offset error: unexpected")
				}
				if v.flags != test.expected.flags {
					t.Error("flags error: unexpected")
				}
				if v.flags&byteExtractFlagEndian > 0 && v.byteEndian != test.expected.byteEndian {
					t.Error("byteEndian error: unexpected")
				}

				if v.alignValue != test.expected.alignValue {
					t.Error("alignValue error: unexpected")
				}
				if v.multiplierValue != test.expected.multiplierValue {
					t.Error("multiplierValue error: unexpected")
				}
			}
		})
	}
}

func TestExtract(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content: `alert ip any any -> any any (msg:"dummy";content:"|01 02 03 04|";byte_extract:1,2,one,string,dec,relative;` +
				`content:"|0C 0D 0E 0F|"; distance:one; sid:1;)`,
			expected: captured,
			packet: []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x35,
				0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,
				0x0E, 0x0F},
		},
		{
			content: `alert ip any any -> any any (msg:"dummy";content:"|01 02 03 04|";byte_extract:1,2,one,string,hex,relative;` +
				`content:"|0C 0D 0E 0F|"; distance:one; sid:1;)`,
			expected: captured,
			packet: []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x35,
				0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,
				0x0E, 0x0F},
		},
		{
			content: `alert ip any any -> any any (msg:"dummy";content:"|01 02 03 04|";
				        	byte_extract:1,2,one,string,dec,relative;
				        	content:"|06 35 07 08|"; offset:one; sid:1;)`,
			expected: captured,
			packet: []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x35,
				0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,
				0x0E, 0x0F},
		},
		{
			content: `alert ip any any -> any any (msg:"dummy";content:"|01 02 03 04|";
		        	byte_extract:1,2,one,string,dec,relative;
		        	content:"|03 04 05 06|"; depth:one; sid:1;)`,
			expected: captured,
			packet: []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x36,
				0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,
				0x0E, 0x0F},
		},
		{
			content: `alert ip any any -> any any (msg:"dummy";content:"|01 02 03 04|";
        byte_extract:1,2,one,string,dec,relative;
        content:"|09 0A 0B 0C|"; within:one; sid:1;)`,
			expected: captured,
			packet: []byte{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x36,
				0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D,
				0x0E, 0x0F},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
