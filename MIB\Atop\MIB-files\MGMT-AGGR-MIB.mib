-- *****************************************************************
-- AGGR-MIB:  
-- ****************************************************************

MGMT-AGGR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTPortStatusSpeed FROM MGMT-TC
    ;

mgmtAggrMib MODULE-IDENTITY
    LAST-UPDATED "201707310000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private mib of aggregation management"
    REVISION    "201707310000Z"
    DESCRIPTION
        "Added new aggregation modes: disabled, reserved, static, lacpActive,
         lacpPassive"
    REVISION    "201507070000Z"
    DESCRIPTION
        "Port speed is moved into the TC MIB"
    REVISION    "201411180000Z"
    DESCRIPTION
        "Added aggregation group status table"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 19 }


MGMTAggregationMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The aggregation mode."
    SYNTAX      INTEGER { disabled(0), reserved(1), static(2),
                          lacpActive(3), lacpPassive(4) }

mgmtAggrMibObjects OBJECT IDENTIFIER
    ::= { mgmtAggrMib 1 }

mgmtAggrConfig OBJECT IDENTIFIER
    ::= { mgmtAggrMibObjects 2 }

mgmtAggrConfigModeGlobals OBJECT IDENTIFIER
    ::= { mgmtAggrConfig 1 }

mgmtAggrConfigModeGlobalsSmacAddr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable the use of the Source MAC address, or false to
         disable. By default, Source MAC Address is enabled."
    ::= { mgmtAggrConfigModeGlobals 1 }

mgmtAggrConfigModeGlobalsDmacAddr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable the use of the Destination MAC address, or false
         to disable. By default, Destination MAC Address is disabled."
    ::= { mgmtAggrConfigModeGlobals 2 }

mgmtAggrConfigModeGlobalsSourceAndDestinationIpAddr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable the use of the IP address, or false to disable.
         By default, Destination MAC Address is enabled."
    ::= { mgmtAggrConfigModeGlobals 3 }

mgmtAggrConfigModeGlobalsTcpOrUdpSportAndDportNo OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable the use of the TCP/UDP Port Number, or false to
         disable. By default, TCP/UDP Port Number is enabled."
    ::= { mgmtAggrConfigModeGlobals 4 }

mgmtAggrConfigGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAggrConfigGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is static Link Aggregation Group configuration table. The
         index is Aggregration Group Identifier."
    ::= { mgmtAggrConfig 2 }

mgmtAggrConfigGroupEntry OBJECT-TYPE
    SYNTAX      MGMTAggrConfigGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtAggrConfigGroupAggrIndexNo }
    ::= { mgmtAggrConfigGroupTable 1 }

MGMTAggrConfigGroupEntry ::= SEQUENCE {
    mgmtAggrConfigGroupAggrIndexNo  MGMTInterfaceIndex,
    mgmtAggrConfigGroupPortMembers  MGMTPortList,
    mgmtAggrConfigGroupAggrMode     MGMTAggregationMode
}

mgmtAggrConfigGroupAggrIndexNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Link Aggregation Group Identifier."
    ::= { mgmtAggrConfigGroupEntry 1 }

mgmtAggrConfigGroupPortMembers OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The bitmap type containing the port members' list for this aggregation
         group."
    ::= { mgmtAggrConfigGroupEntry 2 }

mgmtAggrConfigGroupAggrMode OBJECT-TYPE
    SYNTAX      MGMTAggregationMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Aggregation group mode."
    ::= { mgmtAggrConfigGroupEntry 3 }

mgmtAggrStatus OBJECT IDENTIFIER
    ::= { mgmtAggrMibObjects 3 }

mgmtAggrStatusGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAggrStatusGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is Aggregation Group status table. The index is Aggregration
         Group Identifier."
    ::= { mgmtAggrStatus 3 }

mgmtAggrStatusGroupEntry OBJECT-TYPE
    SYNTAX      MGMTAggrStatusGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtAggrStatusGroupAggrIndexNo }
    ::= { mgmtAggrStatusGroupTable 1 }

MGMTAggrStatusGroupEntry ::= SEQUENCE {
    mgmtAggrStatusGroupAggrIndexNo      MGMTInterfaceIndex,
    mgmtAggrStatusGroupConfiguredPorts  MGMTPortList,
    mgmtAggrStatusGroupAggregatedPorts  MGMTPortList,
    mgmtAggrStatusGroupSpeed            MGMTPortStatusSpeed,
    mgmtAggrStatusGroupAggrMode         MGMTAggregationMode,
    mgmtAggrStatusGroupType             MGMTDisplayString
}

mgmtAggrStatusGroupAggrIndexNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Link Aggregation Group Identifier."
    ::= { mgmtAggrStatusGroupEntry 1 }

mgmtAggrStatusGroupConfiguredPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Configured member ports of the aggregation Group."
    ::= { mgmtAggrStatusGroupEntry 2 }

mgmtAggrStatusGroupAggregatedPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Aggregated member ports of the aggregation Group."
    ::= { mgmtAggrStatusGroupEntry 3 }

mgmtAggrStatusGroupSpeed OBJECT-TYPE
    SYNTAX      MGMTPortStatusSpeed
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Speed of the Aggregation Group."
    ::= { mgmtAggrStatusGroupEntry 4 }

mgmtAggrStatusGroupAggrMode OBJECT-TYPE
    SYNTAX      MGMTAggregationMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Aggregation group mode."
    ::= { mgmtAggrStatusGroupEntry 5 }

mgmtAggrStatusGroupType OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of the Aggregation Group."
    ::= { mgmtAggrStatusGroupEntry 6 }

mgmtAggrMibConformance OBJECT IDENTIFIER
    ::= { mgmtAggrMib 2 }

mgmtAggrMibCompliances OBJECT IDENTIFIER
    ::= { mgmtAggrMibConformance 1 }

mgmtAggrMibGroups OBJECT IDENTIFIER
    ::= { mgmtAggrMibConformance 2 }

mgmtAggrConfigModeGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAggrConfigModeGlobalsSmacAddr,
                  mgmtAggrConfigModeGlobalsDmacAddr,
                  mgmtAggrConfigModeGlobalsSourceAndDestinationIpAddr,
                  mgmtAggrConfigModeGlobalsTcpOrUdpSportAndDportNo }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAggrMibGroups 1 }

mgmtAggrConfigGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAggrConfigGroupAggrIndexNo,
                  mgmtAggrConfigGroupPortMembers,
                  mgmtAggrConfigGroupAggrMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAggrMibGroups 2 }

mgmtAggrStatusGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAggrStatusGroupAggrIndexNo,
                  mgmtAggrStatusGroupConfiguredPorts,
                  mgmtAggrStatusGroupAggregatedPorts,
                  mgmtAggrStatusGroupSpeed,
                  mgmtAggrStatusGroupAggrMode,
                  mgmtAggrStatusGroupType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAggrMibGroups 3 }

mgmtAggrMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtAggrConfigModeGlobalsInfoGroup,
                       mgmtAggrConfigGroupTableInfoGroup,
                       mgmtAggrStatusGroupTableInfoGroup }

    ::= { mgmtAggrMibCompliances 1 }

END
