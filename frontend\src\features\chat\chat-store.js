import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { persist } from "zustand/middleware";
import { v4 as uuidv4 } from "uuid";

const initialState = {
  messages: [],
  isLoading: false,
  error: null,
  selectedModel: null, // Default model
  isChatOpen: false,
  chatWidth: 450,
  session_id: null,
  llm_provider: null,
  sessions: [], // List of available sessions
  currentSession: null, // Current session details
  isNewSessionModalOpen: false,
};

/**
 * Create chat store with initial state and actions
 * @param {Function} set - Zustand set function
 * @returns {ChatState & ChatActions} Store with state and actions
 */
const createChatStore = (set) => ({
  ...initialState,

  toggleChat: () =>
    set((state) => {
      state.isChatOpen = !state.isChatOpen;
    }),
  setChatWidth: (width) =>
    set((state) => {
      state.chatWidth = Math.max(400, Math.min(800, width)); // Min 300px, Max 800px
    }),

  addMessage: (message) =>
    set((state) => {
      state.messages.push({
        ...message,
        id: uuidv4(),
        timestamp: new Date(),
      });
    }),

  updateMessage: (text, error = false) =>
    set((state) => {
      const lastMessage = state.messages[state.messages.length - 1];
      if (lastMessage && lastMessage.role === "assistant") {
        const updatedMessages = [...state.messages];
        updatedMessages[updatedMessages.length - 1] = {
          ...lastMessage,
          content: error ? `Error: ${text}` : text,
          isLoading: false,
        };
        state.messages = updatedMessages;
      }
    }),

  setMessageLoading: (isLoading) =>
    set((state) => {
      if (isLoading) {
        const loadingMessage = {
          id: uuidv4(),
          content: "Thinking...",
          role: "assistant",
          timestamp: new Date(),
          isLoading: true,
        };
        state.messages.push(loadingMessage);
      } else {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage && lastMessage.role === "assistant") {
          const updatedMessages = [...state.messages];
          updatedMessages[updatedMessages.length - 1] = {
            ...lastMessage,
            isLoading: false,
          };
          state.messages = updatedMessages;
        }
      }
    }),

  clearMessages: () =>
    set((state) => {
      state.messages = [];
    }),

  deleteMessage: (messageId) =>
    set((state) => {
      state.messages = state.messages.filter((msg) => msg.id !== messageId);
    }),

  setSessions: (sessions) =>
    set((state) => {
      state.sessions = sessions;
    }),

  setCurrentSession: (session) =>
    set((state) => {
      state.currentSession = session;
      state.session_id = session?.session_id || null;
      state.selectedModel = session?.model || null;
      state.llm_provider = session?.provider || null;
    }),

  setSessionID: (sessionId) =>
    set((state) => {
      state.session_id = sessionId;
    }),

  setSelectedModel: (model) =>
    set((state) => {
      state.selectedModel = model;
    }),

  setLLMProvider: (provider) =>
    set((state) => {
      state.llm_provider = provider;
    }),

  setLoading: (isLoading) =>
    set((state) => {
      state.isLoading = isLoading;
    }),

  setError: (error) =>
    set((state) => {
      state.error = error;
    }),

  toggleNewSessionModal: () =>
    set((state) => {
      state.isNewSessionModalOpen = !state.isNewSessionModalOpen;
    }),

  updateState: (updates) => set((state) => ({ ...state, ...updates })),
});

/**
 * Chat store for managing chat state and messages
 * @type {import("zustand").UseBoundStore<ChatState & ChatActions>}
 */
export const useChatStore = create(
  devtools(
    immer(
      persist(
        (...args) => ({
          ...createChatStore(...args),
        }),
        {
          name: "nimbl-chat",
          partialize: (state) => ({
            messages: state.messages,
            selectedModel: state.selectedModel,
            llm_provider: state.llm_provider,
            session_id: state.session_id,
            sessions: state.sessions,
            currentSession: state.currentSession,
          }),
        }
      )
    ),
    {
      name: "ChatStore",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);
