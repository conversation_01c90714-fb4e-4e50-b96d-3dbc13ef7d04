-- *****************************************************************
-- DHCP-SERVER-MIB:  
-- ****************************************************************

MGMT-DHCP-SERVER-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    ;

mgmtDhcpServerMib MODULE-IDENTITY
    LAST-UPDATED "201903110000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of DhcpServer"
    REVISION    "201903110000Z"
    DESCRIPTION
        "add support for dhcp server per port"
    REVISION    "201508240000Z"
    DESCRIPTION
        "revise fqdn as name"
    REVISION    "201411270000Z"
    DESCRIPTION
        "revise descriptions by Palle"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 109 }


MGMTDhcpServerBindingEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of binding."
    SYNTAX      INTEGER { none(0), automatic(1), manual(2),
                          expired(3) }

MGMTDhcpServerBindingStateEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the state of binding."
    SYNTAX      INTEGER { none(0), allocated(1), committed(2),
                          expired(3) }

MGMTDhcpServerClientIdentifierEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of client identifier."
    SYNTAX      INTEGER { none(0), name(1), mac(2) }

MGMTDhcpServerNetbiosNodeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of NetBIOS node."
    SYNTAX      INTEGER { nodeNone(0), nodeB(1), nodeP(2), nodeM(3),
                          nodeH(4) }

MGMTDhcpServerPoolEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of DHCP pool."
    SYNTAX      INTEGER { none(0), network(1), host(2) }

mgmtDhcpServerMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMib 1 }

mgmtDhcpServerConfig OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMibObjects 2 }

mgmtDhcpServerConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDhcpServerConfig 1 }

mgmtDhcpServerConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of DHCP server. true is to enable the functions of DHCP
         server and false is to disable it."
    ::= { mgmtDhcpServerConfigGlobals 1 }

mgmtDhcpServerConfigVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerConfigVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the table of DHCP server VLAN configuration. The index is VLAN
         ID."
    ::= { mgmtDhcpServerConfig 2 }

mgmtDhcpServerConfigVlanEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerConfigVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each VLAN has a set of parameters"
    INDEX       { mgmtDhcpServerConfigVlanIfIndex }
    ::= { mgmtDhcpServerConfigVlanTable 1 }

MGMTDhcpServerConfigVlanEntry ::= SEQUENCE {
    mgmtDhcpServerConfigVlanIfIndex  MGMTInterfaceIndex,
    mgmtDhcpServerConfigVlanMode     TruthValue
}

mgmtDhcpServerConfigVlanIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of VLAN."
    ::= { mgmtDhcpServerConfigVlanEntry 1 }

mgmtDhcpServerConfigVlanMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN mode of DHCP server. true is to enable DHCP server per VLAN and
         false is to disable it per VLAN."
    ::= { mgmtDhcpServerConfigVlanEntry 2 }

mgmtDhcpServerConfigExcludedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerConfigExcludedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is DHCP server excluded IP onfiguration table. The indexes
         are low IP and high IP address."
    ::= { mgmtDhcpServerConfig 3 }

mgmtDhcpServerConfigExcludedEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerConfigExcludedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcpServerConfigExcludedLowIpAddress,
                  mgmtDhcpServerConfigExcludedHighIpAddress }
    ::= { mgmtDhcpServerConfigExcludedTable 1 }

MGMTDhcpServerConfigExcludedEntry ::= SEQUENCE {
    mgmtDhcpServerConfigExcludedLowIpAddress   IpAddress,
    mgmtDhcpServerConfigExcludedHighIpAddress  IpAddress,
    mgmtDhcpServerConfigExcludedAction         MGMTRowEditorState
}

mgmtDhcpServerConfigExcludedLowIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Low IP address."
    ::= { mgmtDhcpServerConfigExcludedEntry 1 }

mgmtDhcpServerConfigExcludedHighIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "High IP address."
    ::= { mgmtDhcpServerConfigExcludedEntry 2 }

mgmtDhcpServerConfigExcludedAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigExcludedEntry 100 }

mgmtDhcpServerConfigExcludedIpTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtDhcpServerConfig 4 }

mgmtDhcpServerConfigExcludedIpTableRowEditorLowIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Low IP address."
    ::= { mgmtDhcpServerConfigExcludedIpTableRowEditor 1 }

mgmtDhcpServerConfigExcludedIpTableRowEditorHighIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "High IP address."
    ::= { mgmtDhcpServerConfigExcludedIpTableRowEditor 2 }

mgmtDhcpServerConfigExcludedIpTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigExcludedIpTableRowEditor 100 }

mgmtDhcpServerConfigPoolTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerConfigPoolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is DHCP server pool onfiguration table. The indexe is pool
         name."
    ::= { mgmtDhcpServerConfig 5 }

mgmtDhcpServerConfigPoolEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerConfigPoolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcpServerConfigPoolPoolName }
    ::= { mgmtDhcpServerConfigPoolTable 1 }

MGMTDhcpServerConfigPoolEntry ::= SEQUENCE {
    mgmtDhcpServerConfigPoolPoolName               MGMTDisplayString,
    mgmtDhcpServerConfigPoolPoolType               MGMTDhcpServerPoolEnum,
    mgmtDhcpServerConfigPoolIpv4Address            IpAddress,
    mgmtDhcpServerConfigPoolSubnetMask             IpAddress,
    mgmtDhcpServerConfigPoolSubnetBroadcast        IpAddress,
    mgmtDhcpServerConfigPoolLeaseDay               Unsigned32,
    mgmtDhcpServerConfigPoolLeaseHour              Unsigned32,
    mgmtDhcpServerConfigPoolLeaseMinute            Unsigned32,
    mgmtDhcpServerConfigPoolDomainName             MGMTDisplayString,
    mgmtDhcpServerConfigPoolDefaultRouter1         IpAddress,
    mgmtDhcpServerConfigPoolDefaultRouter2         IpAddress,
    mgmtDhcpServerConfigPoolDefaultRouter3         IpAddress,
    mgmtDhcpServerConfigPoolDefaultRouter4         IpAddress,
    mgmtDhcpServerConfigPoolDnsServer1             IpAddress,
    mgmtDhcpServerConfigPoolDnsServer2             IpAddress,
    mgmtDhcpServerConfigPoolDnsServer3             IpAddress,
    mgmtDhcpServerConfigPoolDnsServer4             IpAddress,
    mgmtDhcpServerConfigPoolNtpServer1             IpAddress,
    mgmtDhcpServerConfigPoolNtpServer2             IpAddress,
    mgmtDhcpServerConfigPoolNtpServer3             IpAddress,
    mgmtDhcpServerConfigPoolNtpServer4             IpAddress,
    mgmtDhcpServerConfigPoolNetbiosNodeType        MGMTDhcpServerNetbiosNodeEnum,
    mgmtDhcpServerConfigPoolNetbiosScope           MGMTDisplayString,
    mgmtDhcpServerConfigPoolNetbiosNameServer1     IpAddress,
    mgmtDhcpServerConfigPoolNetbiosNameServer2     IpAddress,
    mgmtDhcpServerConfigPoolNetbiosNameServer3     IpAddress,
    mgmtDhcpServerConfigPoolNetbiosNameServer4     IpAddress,
    mgmtDhcpServerConfigPoolNisDomainName          MGMTDisplayString,
    mgmtDhcpServerConfigPoolNisServer1             IpAddress,
    mgmtDhcpServerConfigPoolNisServer2             IpAddress,
    mgmtDhcpServerConfigPoolNisServer3             IpAddress,
    mgmtDhcpServerConfigPoolNisServer4             IpAddress,
    mgmtDhcpServerConfigPoolClientIdentifierType   MGMTDhcpServerClientIdentifierEnum,
    mgmtDhcpServerConfigPoolClientIdentifierName   MGMTDisplayString,
    mgmtDhcpServerConfigPoolClientIdentifierMac    MacAddress,
    mgmtDhcpServerConfigPoolClientHardwareAddress  MacAddress,
    mgmtDhcpServerConfigPoolClientName             MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorClassId1         MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorSpecificInfo1    MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorClassId2         MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorSpecificInfo2    MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorClassId3         MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorSpecificInfo3    MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorClassId4         MGMTDisplayString,
    mgmtDhcpServerConfigPoolVendorSpecificInfo4    MGMTDisplayString,
    mgmtDhcpServerConfigPoolReservedOnly           TruthValue,
    mgmtDhcpServerConfigPoolAction                 MGMTRowEditorState
}

mgmtDhcpServerConfigPoolPoolName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of DHCP pool."
    ::= { mgmtDhcpServerConfigPoolEntry 1 }

mgmtDhcpServerConfigPoolPoolType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerPoolEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of pool. none(0) means the pool type is not defined yet.
         network(1) means the pool defines a pool of IP addresses to service
         more than one DHCP client. host(2) means the pool services for a
         specific DHCP client identified by client identifier or hardware
         address."
    ::= { mgmtDhcpServerConfigPoolEntry 2 }

mgmtDhcpServerConfigPoolIpv4Address OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network number of the subnet. If the pool type is of network, the IP
         address can be any general IP address. If the pool type is of host, the
         IP address must be a unicast IP address."
    ::= { mgmtDhcpServerConfigPoolEntry 3 }

mgmtDhcpServerConfigPoolSubnetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Subnet Mask. DHCP option 1. Specify subnet mask of the DHCP address
         pool, excluding 0.0.0.0 and ***************."
    ::= { mgmtDhcpServerConfigPoolEntry 4 }

mgmtDhcpServerConfigPoolSubnetBroadcast OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Broadcast IP address in the subnet. DHCP option 28. Specify the
         broadcast address in use on the client's subnet."
    ::= { mgmtDhcpServerConfigPoolEntry 5 }

mgmtDhcpServerConfigPoolLeaseDay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of days of lease time. DHCP option 51, 58 and 59. The value
         range is 0-365. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolEntry 6 }

mgmtDhcpServerConfigPoolLeaseHour OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of hours of lease time. DHCP option 51, 58 and 59. The value
         range is 0-23. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolEntry 7 }

mgmtDhcpServerConfigPoolLeaseMinute OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of minutes of lease time. DHCP option 51, 58 and 59. The value
         range is 0-59. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolEntry 8 }

mgmtDhcpServerConfigPoolDomainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name. DHCP option 15. Specify domain name that client should use
         when resolving hostname via DNS."
    ::= { mgmtDhcpServerConfigPoolEntry 9 }

mgmtDhcpServerConfigPoolDefaultRouter1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 1."
    ::= { mgmtDhcpServerConfigPoolEntry 10 }

mgmtDhcpServerConfigPoolDefaultRouter2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 2."
    ::= { mgmtDhcpServerConfigPoolEntry 11 }

mgmtDhcpServerConfigPoolDefaultRouter3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 3."
    ::= { mgmtDhcpServerConfigPoolEntry 12 }

mgmtDhcpServerConfigPoolDefaultRouter4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 4."
    ::= { mgmtDhcpServerConfigPoolEntry 13 }

mgmtDhcpServerConfigPoolDnsServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 1."
    ::= { mgmtDhcpServerConfigPoolEntry 14 }

mgmtDhcpServerConfigPoolDnsServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 2."
    ::= { mgmtDhcpServerConfigPoolEntry 15 }

mgmtDhcpServerConfigPoolDnsServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 3."
    ::= { mgmtDhcpServerConfigPoolEntry 16 }

mgmtDhcpServerConfigPoolDnsServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 4."
    ::= { mgmtDhcpServerConfigPoolEntry 17 }

mgmtDhcpServerConfigPoolNtpServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 1."
    ::= { mgmtDhcpServerConfigPoolEntry 18 }

mgmtDhcpServerConfigPoolNtpServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 2."
    ::= { mgmtDhcpServerConfigPoolEntry 19 }

mgmtDhcpServerConfigPoolNtpServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 3."
    ::= { mgmtDhcpServerConfigPoolEntry 20 }

mgmtDhcpServerConfigPoolNtpServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 4."
    ::= { mgmtDhcpServerConfigPoolEntry 21 }

mgmtDhcpServerConfigPoolNetbiosNodeType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerNetbiosNodeEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of NetBIOS node. DHCP option 46. Specify NetBIOS node type option
         to allow Netbios over TCP/IP clients which are configurable to be
         configured as described in RFC 1001/1002. nodeNone(0) means the node
         type is not defined yet. nodeB(1) means the node type is type of B.
         nodeP(2) means the node type is type of P. nodeM(3) means the node type
         is type of M. nodeH(4) means the node type is type of H."
    ::= { mgmtDhcpServerConfigPoolEntry 22 }

mgmtDhcpServerConfigPoolNetbiosScope OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS scope. DHCP option 47. Specify the NetBIOS over TCP/IP scope
         parameter for the client as specified in RFC 1001/1002."
    ::= { mgmtDhcpServerConfigPoolEntry 23 }

mgmtDhcpServerConfigPoolNetbiosNameServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 1."
    ::= { mgmtDhcpServerConfigPoolEntry 24 }

mgmtDhcpServerConfigPoolNetbiosNameServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 2."
    ::= { mgmtDhcpServerConfigPoolEntry 25 }

mgmtDhcpServerConfigPoolNetbiosNameServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 3."
    ::= { mgmtDhcpServerConfigPoolEntry 26 }

mgmtDhcpServerConfigPoolNetbiosNameServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 4."
    ::= { mgmtDhcpServerConfigPoolEntry 27 }

mgmtDhcpServerConfigPoolNisDomainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS Domain Name. DHCP option 40. Specify the name of the client's NIS
         domain."
    ::= { mgmtDhcpServerConfigPoolEntry 28 }

mgmtDhcpServerConfigPoolNisServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 1."
    ::= { mgmtDhcpServerConfigPoolEntry 29 }

mgmtDhcpServerConfigPoolNisServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 2."
    ::= { mgmtDhcpServerConfigPoolEntry 30 }

mgmtDhcpServerConfigPoolNisServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 3."
    ::= { mgmtDhcpServerConfigPoolEntry 31 }

mgmtDhcpServerConfigPoolNisServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 4."
    ::= { mgmtDhcpServerConfigPoolEntry 32 }

mgmtDhcpServerConfigPoolClientIdentifierType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerClientIdentifierEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of client identifier. DHCP option 61. Specify client's unique
         identifier to be used when the pool is the type of host. none(0) means
         the client identifier type is not defined yet. name(1) means the client
         identifier type is other than hardware. mac(2) means the client
         identifier type is type of MAC address."
    ::= { mgmtDhcpServerConfigPoolEntry 33 }

mgmtDhcpServerConfigPoolClientIdentifierName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client identifier which type is other than hardware. DHCP option 61.
         Specify client's unique identifier to be used when the pool is the type
         of host. This takes effect only if ClientIdentifierType is defined
         name(1)."
    ::= { mgmtDhcpServerConfigPoolEntry 34 }

mgmtDhcpServerConfigPoolClientIdentifierMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client's MAC address. DHCP option 61. Specify client's unique
         identifier to be used when the pool is the type of host. This takes
         effect only if ClientIdentifierType is defined as mac(2)."
    ::= { mgmtDhcpServerConfigPoolEntry 35 }

mgmtDhcpServerConfigPoolClientHardwareAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client's hardware address. Specify client's hardware(MAC) address to be
         used when the pool is the type of host."
    ::= { mgmtDhcpServerConfigPoolEntry 36 }

mgmtDhcpServerConfigPoolClientName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client name. DHCP option 12. Specify the name of client to be used when
         the pool is the type of host."
    ::= { mgmtDhcpServerConfigPoolEntry 37 }

mgmtDhcpServerConfigPoolVendorClassId1 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolEntry 38 }

mgmtDhcpServerConfigPoolVendorSpecificInfo1 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolEntry 39 }

mgmtDhcpServerConfigPoolVendorClassId2 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolEntry 40 }

mgmtDhcpServerConfigPoolVendorSpecificInfo2 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolEntry 41 }

mgmtDhcpServerConfigPoolVendorClassId3 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolEntry 42 }

mgmtDhcpServerConfigPoolVendorSpecificInfo3 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolEntry 43 }

mgmtDhcpServerConfigPoolVendorClassId4 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolEntry 44 }

mgmtDhcpServerConfigPoolVendorSpecificInfo4 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolEntry 45 }

mgmtDhcpServerConfigPoolReservedOnly OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Whether to only hand out reserved addresses (TRUE) or not (FALSE)."
    ::= { mgmtDhcpServerConfigPoolEntry 46 }

mgmtDhcpServerConfigPoolAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigPoolEntry 100 }

mgmtDhcpServerConfigPoolTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtDhcpServerConfig 6 }

mgmtDhcpServerConfigPoolTableRowEditorPoolName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of DHCP pool."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 1 }

mgmtDhcpServerConfigPoolTableRowEditorPoolType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerPoolEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of pool. none(0) means the pool type is not defined yet.
         network(1) means the pool defines a pool of IP addresses to service
         more than one DHCP client. host(2) means the pool services for a
         specific DHCP client identified by client identifier or hardware
         address."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 2 }

mgmtDhcpServerConfigPoolTableRowEditorIpv4Address OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network number of the subnet. If the pool type is of network, the IP
         address can be any general IP address. If the pool type is of host, the
         IP address must be a unicast IP address."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 3 }

mgmtDhcpServerConfigPoolTableRowEditorSubnetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Subnet Mask. DHCP option 1. Specify subnet mask of the DHCP address
         pool, excluding 0.0.0.0 and ***************."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 4 }

mgmtDhcpServerConfigPoolTableRowEditorSubnetBroadcast OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Broadcast IP address in the subnet. DHCP option 28. Specify the
         broadcast address in use on the client's subnet."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 5 }

mgmtDhcpServerConfigPoolTableRowEditorLeaseDay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of days of lease time. DHCP option 51, 58 and 59. The value
         range is 0-365. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 6 }

mgmtDhcpServerConfigPoolTableRowEditorLeaseHour OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of hours of lease time. DHCP option 51, 58 and 59. The value
         range is 0-23. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 7 }

mgmtDhcpServerConfigPoolTableRowEditorLeaseMinute OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of minutes of lease time. DHCP option 51, 58 and 59. The value
         range is 0-59. Specify lease time that allows the client to request a
         lease time for the IP address. If all of LeaseDay, LeaseHour and
         LeaseMinute are 0's, then it means the lease time is infinite."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 8 }

mgmtDhcpServerConfigPoolTableRowEditorDomainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name. DHCP option 15. Specify domain name that client should use
         when resolving hostname via DNS."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 9 }

mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 1."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 10 }

mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 2."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 11 }

mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 3."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 12 }

mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default router 4."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 13 }

mgmtDhcpServerConfigPoolTableRowEditorDnsServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 1."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 14 }

mgmtDhcpServerConfigPoolTableRowEditorDnsServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 2."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 15 }

mgmtDhcpServerConfigPoolTableRowEditorDnsServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 3."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 16 }

mgmtDhcpServerConfigPoolTableRowEditorDnsServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DNS server 4."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 17 }

mgmtDhcpServerConfigPoolTableRowEditorNtpServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 1."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 18 }

mgmtDhcpServerConfigPoolTableRowEditorNtpServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 2."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 19 }

mgmtDhcpServerConfigPoolTableRowEditorNtpServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 3."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 20 }

mgmtDhcpServerConfigPoolTableRowEditorNtpServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NTP server 4."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 21 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosNodeType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerNetbiosNodeEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of NetBIOS node. DHCP option 46. Specify NetBIOS node type option
         to allow Netbios over TCP/IP clients which are configurable to be
         configured as described in RFC 1001/1002. nodeNone(0) means the node
         type is not defined yet. nodeB(1) means the node type is type of B.
         nodeP(2) means the node type is type of P. nodeM(3) means the node type
         is type of M. nodeH(4) means the node type is type of H."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 22 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosScope OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS scope. DHCP option 47. Specify the NetBIOS over TCP/IP scope
         parameter for the client as specified in RFC 1001/1002."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 23 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 1."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 24 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 2."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 25 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 3."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 26 }

mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NetBIOS name server 4."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 27 }

mgmtDhcpServerConfigPoolTableRowEditorNisDomainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS Domain Name. DHCP option 40. Specify the name of the client's NIS
         domain."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 28 }

mgmtDhcpServerConfigPoolTableRowEditorNisServer1 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 1."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 29 }

mgmtDhcpServerConfigPoolTableRowEditorNisServer2 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 2."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 30 }

mgmtDhcpServerConfigPoolTableRowEditorNisServer3 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 3."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 31 }

mgmtDhcpServerConfigPoolTableRowEditorNisServer4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "NIS server 4."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 32 }

mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerClientIdentifierEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of client identifier. DHCP option 61. Specify client's unique
         identifier to be used when the pool is the type of host. none(0) means
         the client identifier type is not defined yet. name(1) means the client
         identifier type is other than hardware. mac(2) means the client
         identifier type is type of MAC address."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 33 }

mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client identifier which type is other than hardware. DHCP option 61.
         Specify client's unique identifier to be used when the pool is the type
         of host. This takes effect only if ClientIdentifierType is defined
         name(1)."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 34 }

mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client's MAC address. DHCP option 61. Specify client's unique
         identifier to be used when the pool is the type of host. This takes
         effect only if ClientIdentifierType is defined as mac(2)."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 35 }

mgmtDhcpServerConfigPoolTableRowEditorClientHardwareAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client's hardware address. Specify client's hardware(MAC) address to be
         used when the pool is the type of host."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 36 }

mgmtDhcpServerConfigPoolTableRowEditorClientName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Client name. DHCP option 12. Specify the name of client to be used when
         the pool is the type of host."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 37 }

mgmtDhcpServerConfigPoolTableRowEditorVendorClassId1 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 38 }

mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo1 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 39 }

mgmtDhcpServerConfigPoolTableRowEditorVendorClassId2 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 40 }

mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo2 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 41 }

mgmtDhcpServerConfigPoolTableRowEditorVendorClassId3 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 42 }

mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo3 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 43 }

mgmtDhcpServerConfigPoolTableRowEditorVendorClassId4 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Class Identifier. DHCP option 60. Specify to be used by DHCP
         client to optionally identify the vendor type and configuration of a
         DHCP client. DHCP server will deliver the corresponding option 43
         specific information to the client that sends option 60 vendor class
         identifier."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 44 }

mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo4 OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..66))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vendor Specific Information. DHCP option 43. Specify vendor specific
         information corresponding to option 60 vendor class identifier.
         Therefore, the corresponding vendor class identifier must be defined
         before this specific information."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 45 }

mgmtDhcpServerConfigPoolTableRowEditorReservedOnly OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Whether to only hand out reserved addresses (TRUE) or not (FALSE)."
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 46 }

mgmtDhcpServerConfigPoolTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigPoolTableRowEditor 100 }

mgmtDhcpServerConfigReservedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerConfigReservedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is DHCP server reserved entries. The index is the name of the
         pool and the ip address"
    ::= { mgmtDhcpServerConfig 7 }

mgmtDhcpServerConfigReservedEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerConfigReservedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcpServerConfigReservedPoolName,
                  mgmtDhcpServerConfigReservedIpAddress }
    ::= { mgmtDhcpServerConfigReservedTable 1 }

MGMTDhcpServerConfigReservedEntry ::= SEQUENCE {
    mgmtDhcpServerConfigReservedPoolName   MGMTDisplayString,
    mgmtDhcpServerConfigReservedIpAddress  IpAddress,
    mgmtDhcpServerConfigReservedIfIndex    MGMTInterfaceIndex,
    mgmtDhcpServerConfigReservedAction     MGMTRowEditorState
}

mgmtDhcpServerConfigReservedPoolName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of DHCP pool."
    ::= { mgmtDhcpServerConfigReservedEntry 1 }

mgmtDhcpServerConfigReservedIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Reserved IP address."
    ::= { mgmtDhcpServerConfigReservedEntry 2 }

mgmtDhcpServerConfigReservedIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number for the interface for which the ip address is
         reserved."
    ::= { mgmtDhcpServerConfigReservedEntry 3 }

mgmtDhcpServerConfigReservedAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigReservedEntry 100 }

mgmtDhcpServerConfigReservedIpTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtDhcpServerConfig 8 }

mgmtDhcpServerConfigReservedIpTableRowEditorPoolName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of DHCP pool."
    ::= { mgmtDhcpServerConfigReservedIpTableRowEditor 1 }

mgmtDhcpServerConfigReservedIpTableRowEditorIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Reserved IP address."
    ::= { mgmtDhcpServerConfigReservedIpTableRowEditor 2 }

mgmtDhcpServerConfigReservedIpTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number for the interface for which the ip address is
         reserved."
    ::= { mgmtDhcpServerConfigReservedIpTableRowEditor 3 }

mgmtDhcpServerConfigReservedIpTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcpServerConfigReservedIpTableRowEditor 100 }

mgmtDhcpServerStatus OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMibObjects 3 }

mgmtDhcpServerStatusDeclinedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerStatusDeclinedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of IP addresses declined by DHCP client."
    ::= { mgmtDhcpServerStatus 1 }

mgmtDhcpServerStatusDeclinedEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerStatusDeclinedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a declined IP address."
    INDEX       { mgmtDhcpServerStatusDeclinedEntryNo }
    ::= { mgmtDhcpServerStatusDeclinedTable 1 }

MGMTDhcpServerStatusDeclinedEntry ::= SEQUENCE {
    mgmtDhcpServerStatusDeclinedEntryNo      Integer32,
    mgmtDhcpServerStatusDeclinedIpv4Address  IpAddress
}

mgmtDhcpServerStatusDeclinedEntryNo OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The number of entry. The number starts from 1."
    ::= { mgmtDhcpServerStatusDeclinedEntry 1 }

mgmtDhcpServerStatusDeclinedIpv4Address OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv4 address declined by DHCP client."
    ::= { mgmtDhcpServerStatusDeclinedEntry 2 }

mgmtDhcpServerStatusStatistics OBJECT IDENTIFIER
    ::= { mgmtDhcpServerStatus 2 }

mgmtDhcpServerStatusStatisticsDiscoverCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP DISCOVER messages received."
    ::= { mgmtDhcpServerStatusStatistics 1 }

mgmtDhcpServerStatusStatisticsOfferCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP OFFER messages sent."
    ::= { mgmtDhcpServerStatusStatistics 2 }

mgmtDhcpServerStatusStatisticsRequestCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP REQUEST messages received."
    ::= { mgmtDhcpServerStatusStatistics 3 }

mgmtDhcpServerStatusStatisticsAckCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP ACK messages sent."
    ::= { mgmtDhcpServerStatusStatistics 4 }

mgmtDhcpServerStatusStatisticsNakCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP NAK messages sent."
    ::= { mgmtDhcpServerStatusStatistics 5 }

mgmtDhcpServerStatusStatisticsDeclineCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP DECLINE messages received."
    ::= { mgmtDhcpServerStatusStatistics 6 }

mgmtDhcpServerStatusStatisticsReleaseCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP RELEASE messages received."
    ::= { mgmtDhcpServerStatusStatistics 7 }

mgmtDhcpServerStatusStatisticsInformCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of DHCP INFORM messages received."
    ::= { mgmtDhcpServerStatusStatistics 8 }

mgmtDhcpServerStatusBindingTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpServerStatusBindingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of binding data."
    ::= { mgmtDhcpServerStatus 3 }

mgmtDhcpServerStatusBindingEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpServerStatusBindingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has the binding data."
    INDEX       { mgmtDhcpServerStatusBindingIpAddress }
    ::= { mgmtDhcpServerStatusBindingTable 1 }

MGMTDhcpServerStatusBindingEntry ::= SEQUENCE {
    mgmtDhcpServerStatusBindingIpAddress             IpAddress,
    mgmtDhcpServerStatusBindingState                 MGMTDhcpServerBindingStateEnum,
    mgmtDhcpServerStatusBindingType                  MGMTDhcpServerBindingEnum,
    mgmtDhcpServerStatusBindingPoolName              MGMTDisplayString,
    mgmtDhcpServerStatusBindingServerId              IpAddress,
    mgmtDhcpServerStatusBindingVlanId                MGMTUnsigned16,
    mgmtDhcpServerStatusBindingSubnetMask            IpAddress,
    mgmtDhcpServerStatusBindingClientIdentifierType  MGMTDhcpServerClientIdentifierEnum,
    mgmtDhcpServerStatusBindingClientIdentifierName  MGMTDisplayString,
    mgmtDhcpServerStatusBindingClientIdentifierMac   MacAddress,
    mgmtDhcpServerStatusBindingMacAddress            MacAddress,
    mgmtDhcpServerStatusBindingLease                 MGMTDisplayString,
    mgmtDhcpServerStatusBindingTimeToExpire          MGMTDisplayString
}

mgmtDhcpServerStatusBindingIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IP address."
    ::= { mgmtDhcpServerStatusBindingEntry 1 }

mgmtDhcpServerStatusBindingState OBJECT-TYPE
    SYNTAX      MGMTDhcpServerBindingStateEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "State of binding. none(0) means the binding is not in use. allocated(1)
         means the binding is allocated to the new DHCP client who send
         DHCPDISCOVER. committed(2) means the binding is committed as the DHCP
         process is completed successfully. expired(3) means the lease of the
         binding expired."
    ::= { mgmtDhcpServerStatusBindingEntry 2 }

mgmtDhcpServerStatusBindingType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerBindingEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of binding. none(0) means the binding is not in use. automatic(1)
         means the binding is mapped to network-type pool. manual(2) means the
         binding is mapped to host-type pool. expired(3) means the lease of the
         binding expired."
    ::= { mgmtDhcpServerStatusBindingEntry 3 }

mgmtDhcpServerStatusBindingPoolName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Name of the pool that creates the binding."
    ::= { mgmtDhcpServerStatusBindingEntry 4 }

mgmtDhcpServerStatusBindingServerId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address of the DHCP server to service the binding."
    ::= { mgmtDhcpServerStatusBindingEntry 5 }

mgmtDhcpServerStatusBindingVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The VLAN where the binding works on."
    ::= { mgmtDhcpServerStatusBindingEntry 6 }

mgmtDhcpServerStatusBindingSubnetMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Subnet mask of the DHCP client."
    ::= { mgmtDhcpServerStatusBindingEntry 7 }

mgmtDhcpServerStatusBindingClientIdentifierType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerClientIdentifierEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of client identifier. DHCP option 61. Specify client's unique
         identifier to be used when the pool is the type of host. none(0) means
         the client identifier type is not defined yet. name(1) means the client
         identifier type is other than hardware. mac(2) means the client
         identifier type is type of MAC address."
    ::= { mgmtDhcpServerStatusBindingEntry 8 }

mgmtDhcpServerStatusBindingClientIdentifierName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Client identifier which type is other than hardware. DHCP option 61."
    ::= { mgmtDhcpServerStatusBindingEntry 9 }

mgmtDhcpServerStatusBindingClientIdentifierMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Client's MAC address. DHCP option 61."
    ::= { mgmtDhcpServerStatusBindingEntry 10 }

mgmtDhcpServerStatusBindingMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC address of the DHCP client."
    ::= { mgmtDhcpServerStatusBindingEntry 11 }

mgmtDhcpServerStatusBindingLease OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Lease time of the binding."
    ::= { mgmtDhcpServerStatusBindingEntry 12 }

mgmtDhcpServerStatusBindingTimeToExpire OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "remaining time to expire."
    ::= { mgmtDhcpServerStatusBindingEntry 13 }

mgmtDhcpServerControl OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMibObjects 4 }

mgmtDhcpServerControlStatistics OBJECT IDENTIFIER
    ::= { mgmtDhcpServerControl 1 }

mgmtDhcpServerControlStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear all statistics."
    ::= { mgmtDhcpServerControlStatistics 1 }

mgmtDhcpServerControlBinding OBJECT IDENTIFIER
    ::= { mgmtDhcpServerControl 2 }

mgmtDhcpServerControlBindingClearByIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear binding with the IP address. If 0.0.0.0 then do nothing."
    ::= { mgmtDhcpServerControlBinding 1 }

mgmtDhcpServerControlBindingClearByType OBJECT-TYPE
    SYNTAX      MGMTDhcpServerBindingEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear binding by binding type. If none(0) then do nothing."
    ::= { mgmtDhcpServerControlBinding 2 }

mgmtDhcpServerMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMib 2 }

mgmtDhcpServerMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMibConformance 1 }

mgmtDhcpServerMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcpServerMibConformance 2 }

mgmtDhcpServerConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigGlobalsMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 1 }

mgmtDhcpServerConfigVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigVlanIfIndex,
                  mgmtDhcpServerConfigVlanMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 2 }

mgmtDhcpServerConfigExcludedTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigExcludedLowIpAddress,
                  mgmtDhcpServerConfigExcludedHighIpAddress,
                  mgmtDhcpServerConfigExcludedAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 3 }

mgmtDhcpServerConfigExcludedIpTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtDhcpServerConfigExcludedIpTableRowEditorLowIpAddress,
                  mgmtDhcpServerConfigExcludedIpTableRowEditorHighIpAddress,
                  mgmtDhcpServerConfigExcludedIpTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 4 }

mgmtDhcpServerConfigPoolTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigPoolPoolName,
                  mgmtDhcpServerConfigPoolPoolType,
                  mgmtDhcpServerConfigPoolIpv4Address,
                  mgmtDhcpServerConfigPoolSubnetMask,
                  mgmtDhcpServerConfigPoolSubnetBroadcast,
                  mgmtDhcpServerConfigPoolLeaseDay,
                  mgmtDhcpServerConfigPoolLeaseHour,
                  mgmtDhcpServerConfigPoolLeaseMinute,
                  mgmtDhcpServerConfigPoolDomainName,
                  mgmtDhcpServerConfigPoolDefaultRouter1,
                  mgmtDhcpServerConfigPoolDefaultRouter2,
                  mgmtDhcpServerConfigPoolDefaultRouter3,
                  mgmtDhcpServerConfigPoolDefaultRouter4,
                  mgmtDhcpServerConfigPoolDnsServer1,
                  mgmtDhcpServerConfigPoolDnsServer2,
                  mgmtDhcpServerConfigPoolDnsServer3,
                  mgmtDhcpServerConfigPoolDnsServer4,
                  mgmtDhcpServerConfigPoolNtpServer1,
                  mgmtDhcpServerConfigPoolNtpServer2,
                  mgmtDhcpServerConfigPoolNtpServer3,
                  mgmtDhcpServerConfigPoolNtpServer4,
                  mgmtDhcpServerConfigPoolNetbiosNodeType,
                  mgmtDhcpServerConfigPoolNetbiosScope,
                  mgmtDhcpServerConfigPoolNetbiosNameServer1,
                  mgmtDhcpServerConfigPoolNetbiosNameServer2,
                  mgmtDhcpServerConfigPoolNetbiosNameServer3,
                  mgmtDhcpServerConfigPoolNetbiosNameServer4,
                  mgmtDhcpServerConfigPoolNisDomainName,
                  mgmtDhcpServerConfigPoolNisServer1,
                  mgmtDhcpServerConfigPoolNisServer2,
                  mgmtDhcpServerConfigPoolNisServer3,
                  mgmtDhcpServerConfigPoolNisServer4,
                  mgmtDhcpServerConfigPoolClientIdentifierType,
                  mgmtDhcpServerConfigPoolClientIdentifierName,
                  mgmtDhcpServerConfigPoolClientIdentifierMac,
                  mgmtDhcpServerConfigPoolClientHardwareAddress,
                  mgmtDhcpServerConfigPoolClientName,
                  mgmtDhcpServerConfigPoolVendorClassId1,
                  mgmtDhcpServerConfigPoolVendorSpecificInfo1,
                  mgmtDhcpServerConfigPoolVendorClassId2,
                  mgmtDhcpServerConfigPoolVendorSpecificInfo2,
                  mgmtDhcpServerConfigPoolVendorClassId3,
                  mgmtDhcpServerConfigPoolVendorSpecificInfo3,
                  mgmtDhcpServerConfigPoolVendorClassId4,
                  mgmtDhcpServerConfigPoolVendorSpecificInfo4,
                  mgmtDhcpServerConfigPoolReservedOnly,
                  mgmtDhcpServerConfigPoolAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 5 }

mgmtDhcpServerConfigPoolTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigPoolTableRowEditorPoolName,
                  mgmtDhcpServerConfigPoolTableRowEditorPoolType,
                  mgmtDhcpServerConfigPoolTableRowEditorIpv4Address,
                  mgmtDhcpServerConfigPoolTableRowEditorSubnetMask,
                  mgmtDhcpServerConfigPoolTableRowEditorSubnetBroadcast,
                  mgmtDhcpServerConfigPoolTableRowEditorLeaseDay,
                  mgmtDhcpServerConfigPoolTableRowEditorLeaseHour,
                  mgmtDhcpServerConfigPoolTableRowEditorLeaseMinute,
                  mgmtDhcpServerConfigPoolTableRowEditorDomainName,
                  mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter1,
                  mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter2,
                  mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter3,
                  mgmtDhcpServerConfigPoolTableRowEditorDefaultRouter4,
                  mgmtDhcpServerConfigPoolTableRowEditorDnsServer1,
                  mgmtDhcpServerConfigPoolTableRowEditorDnsServer2,
                  mgmtDhcpServerConfigPoolTableRowEditorDnsServer3,
                  mgmtDhcpServerConfigPoolTableRowEditorDnsServer4,
                  mgmtDhcpServerConfigPoolTableRowEditorNtpServer1,
                  mgmtDhcpServerConfigPoolTableRowEditorNtpServer2,
                  mgmtDhcpServerConfigPoolTableRowEditorNtpServer3,
                  mgmtDhcpServerConfigPoolTableRowEditorNtpServer4,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosNodeType,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosScope,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer1,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer2,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer3,
                  mgmtDhcpServerConfigPoolTableRowEditorNetbiosNameServer4,
                  mgmtDhcpServerConfigPoolTableRowEditorNisDomainName,
                  mgmtDhcpServerConfigPoolTableRowEditorNisServer1,
                  mgmtDhcpServerConfigPoolTableRowEditorNisServer2,
                  mgmtDhcpServerConfigPoolTableRowEditorNisServer3,
                  mgmtDhcpServerConfigPoolTableRowEditorNisServer4,
                  mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierType,
                  mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierName,
                  mgmtDhcpServerConfigPoolTableRowEditorClientIdentifierMac,
                  mgmtDhcpServerConfigPoolTableRowEditorClientHardwareAddress,
                  mgmtDhcpServerConfigPoolTableRowEditorClientName,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorClassId1,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo1,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorClassId2,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo2,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorClassId3,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo3,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorClassId4,
                  mgmtDhcpServerConfigPoolTableRowEditorVendorSpecificInfo4,
                  mgmtDhcpServerConfigPoolTableRowEditorReservedOnly,
                  mgmtDhcpServerConfigPoolTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 6 }

mgmtDhcpServerConfigReservedTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerConfigReservedPoolName,
                  mgmtDhcpServerConfigReservedIpAddress,
                  mgmtDhcpServerConfigReservedIfIndex,
                  mgmtDhcpServerConfigReservedAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 7 }

mgmtDhcpServerConfigReservedIpTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtDhcpServerConfigReservedIpTableRowEditorPoolName,
                  mgmtDhcpServerConfigReservedIpTableRowEditorIpAddress,
                  mgmtDhcpServerConfigReservedIpTableRowEditorIfIndex,
                  mgmtDhcpServerConfigReservedIpTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 8 }

mgmtDhcpServerStatusDeclinedTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerStatusDeclinedEntryNo,
                  mgmtDhcpServerStatusDeclinedIpv4Address }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 9 }

mgmtDhcpServerStatusStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerStatusStatisticsDiscoverCnt,
                  mgmtDhcpServerStatusStatisticsOfferCnt,
                  mgmtDhcpServerStatusStatisticsRequestCnt,
                  mgmtDhcpServerStatusStatisticsAckCnt,
                  mgmtDhcpServerStatusStatisticsNakCnt,
                  mgmtDhcpServerStatusStatisticsDeclineCnt,
                  mgmtDhcpServerStatusStatisticsReleaseCnt,
                  mgmtDhcpServerStatusStatisticsInformCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 10 }

mgmtDhcpServerStatusBindingTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerStatusBindingIpAddress,
                  mgmtDhcpServerStatusBindingState,
                  mgmtDhcpServerStatusBindingType,
                  mgmtDhcpServerStatusBindingPoolName,
                  mgmtDhcpServerStatusBindingServerId,
                  mgmtDhcpServerStatusBindingVlanId,
                  mgmtDhcpServerStatusBindingSubnetMask,
                  mgmtDhcpServerStatusBindingClientIdentifierType,
                  mgmtDhcpServerStatusBindingClientIdentifierName,
                  mgmtDhcpServerStatusBindingClientIdentifierMac,
                  mgmtDhcpServerStatusBindingMacAddress,
                  mgmtDhcpServerStatusBindingLease,
                  mgmtDhcpServerStatusBindingTimeToExpire }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 11 }

mgmtDhcpServerControlStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerControlStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 12 }

mgmtDhcpServerControlBindingInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpServerControlBindingClearByIp,
                  mgmtDhcpServerControlBindingClearByType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpServerMibGroups 13 }

mgmtDhcpServerMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcpServerConfigGlobalsInfoGroup,
                       mgmtDhcpServerConfigVlanTableInfoGroup,
                       mgmtDhcpServerConfigExcludedTableInfoGroup,
                       mgmtDhcpServerConfigExcludedIpTableRowEditorInfoGroup,
                       mgmtDhcpServerConfigPoolTableInfoGroup,
                       mgmtDhcpServerConfigPoolTableRowEditorInfoGroup,
                       mgmtDhcpServerConfigReservedTableInfoGroup,
                       mgmtDhcpServerConfigReservedIpTableRowEditorInfoGroup,
                       mgmtDhcpServerStatusDeclinedTableInfoGroup,
                       mgmtDhcpServerStatusStatisticsInfoGroup,
                       mgmtDhcpServerStatusBindingTableInfoGroup,
                       mgmtDhcpServerControlStatisticsInfoGroup,
                       mgmtDhcpServerControlBindingInfoGroup }

    ::= { mgmtDhcpServerMibCompliances 1 }

END
