package mnms

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"github.com/qeof/q"
)

func NewIdpsRePortInfo() IdpsRePortInfo {
	return IdpsRePortInfo{}
}

type IdpsRePortInfo struct {
	StartTime        string             `json:"start_time"`
	Rules            []IdpsRule         `json:"rules"`
	Event            []EventMessage     `json:"event"`
	RecordList       []RecordList       `json:"recordlist"`
	RulePacketsTotal []RulePacketsTotal `json:"rulepackets"`
}

type IdpsRule struct {
	Time    string        `json:"created_time"`
	Name    string        `json:"name"`
	Content []RuleContent `json:"contents"`
}
type RuleContent struct {
	Sid   uint32 `json:"sid"`
	Value string `json:"value"`
}

type RulePacketsTotal struct {
	Sid       uint64 `json:"sid"`
	Name      string `json:"name"`
	Action    string `json:"action"`
	Counts    uint64 `json:"counts"`
	Timestamp string `json:"-"`
}

type EventMessage struct {
	Id          uint32 `json:"id"`
	Timestamp   string `json:"timestamp"`
	Type        string `json:"type"`
	InInterface string `json:"inInterface"`
	Srcip       string `json:"srcip"`
	SrcPort     uint16 `json:"srcPort"`
	Destip      string `json:"destip"`
	DestPort    uint16 `json:"destPort"`
	Protocol    string `json:"protocol"`
	Description string `json:"description"`
	Rulename    string `json:"rulename"`
}

type RecordList struct {
	Date  string     `json:"date,omitempty"`
	Files []FileInfo `json:"files,omitempty"`
}

type FileInfo struct {
	Name string `json:"name,omitempty"`
	Size int64  `json:"size,omitempty"`
}

func init() {
	QC.IdpsReport = make(map[string]IdpsRePortInfo)
}

// HandleIdpsReport accepts the idps report and return report
//
// POST /api/v1/idps/report?client=client1
//
// post specific client info
/*
{
    "rules": [
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_icmp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tcp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tls"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dhcp-events"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dns-events"
        }
    ],
    "event": [
        {
            "id": 789,
            "timestamp": "2024-02-17T17:20:15+08:00",
            "type": "drop",
            "inInterface": "乙太網路",
            "srcip": "************",
            "srcPort": 0,
            "destip": "*******",
            "destPort": 0,
            "protocol": "icmp",
            "description": "icmpv4 selftest drop"
        }
    ],
    "protocol": [
        "modbus",
        "tls",
        "dns",
        "ntp",
        "dhcp",
        "sip",
        "icmp",
        "http",
        "ftp",
        "http2",
        "imap",
        "smb",
        "smtp",
        "snmp",
        "ssh",
        "tftp",
        "sip",
        "enip",
        "nfs",
        "ntp",
        "alert",
        "drop"
    ]
}
*/
//
// GET /api/v1/idps/report?client=client5
//
//	retrieve report of specific client
//
//	exapmle:
/*
{
    "rules": [
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_icmp"
        },
        {

            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tcp"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "selftest_tls"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dhcp-events"
        },
        {
            "created_time": "2024-02-19T11:22:35+08:00",
            "name": "dns-events"
        }
    ],
    "event": [
        {
            "id": 789,
            "timestamp": "2024-02-17T17:20:15+08:00",
            "type": "drop",
            "inInterface": "乙太網路",
            "srcip": "************",
            "srcPort": 0,
            "destip": "*******",
            "destPort": 0,
            "protocol": "icmp",
            "description": "icmpv4 selftest drop"
        }
    ],
    "protocol": [
        "modbus",
        "tls",
        "dns",
        "ntp",
        "dhcp",
        "sip",
        "icmp",
        "http",
        "ftp",
        "http2",
        "imap",
        "smb",
        "smtp",
        "snmp",
        "ssh",
        "tftp",
        "sip",
        "enip",
        "nfs",
        "ntp",
        "alert",
        "drop"
    ]
}
*/
// GET /api/v1/idps/report
//
//	retrieve repost of all client
//
//	exapmle:
/*
{
    "client5": {
        "rules": [
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_icmp"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_tcp"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "selftest_tls"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "dhcp-events"
            },
            {
                "created_time": "2024-02-19T11:22:35+08:00",
                "name": "dns-events"
            }
        ],
        "event": [
            {
                "id": 789,
                "timestamp": "2024-02-17T17:20:15+08:00",
                "type": "drop",
                "inInterface": "乙太網路",
                "srcip": "************",
                "srcPort": 0,
                "destip": "*******",
                "destPort": 0,
                "protocol": "icmp",
                "description": "icmpv4 selftest drop"
            }
        ],
        "protocol": [
            "modbus",
            "tls",
            "dns",
            "ntp",
            "dhcp",
            "sip",
            "icmp",
            "http",
            "ftp",
            "http2",
            "imap",
            "smb",
            "smtp",
            "snmp",
            "ssh",
            "tftp",
            "sip",
            "enip",
            "nfs",
            "ntp",
            "alert",
            "drop"
        ]
    },
    "client6": {
        "rules": null,
        "event": null,
        "protocol": [
            "modbus",
            "tls",
            "dns",
            "ntp",
            "dhcp",
            "sip",
            "icmp",
            "http",
            "ftp",
            "http2",
            "imap",
            "smb",
            "smtp",
            "snmp",
            "ssh",
            "tftp",
            "sip",
            "enip",
            "nfs",
            "ntp",
            "alert",
            "drop"
        ]
    }
}
*/
func HandleIdpsReport(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		bodyText, err := io.ReadAll(r.Body)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		defer r.Body.Close()
		data := NewIdpsRePortInfo()
		err = json.Unmarshal(bodyText, &data)
		if err != nil {
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		client := r.URL.Query().Get("client")
		if len(client) == 0 {
			err = errors.New("client: is empty")
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		QC.idpsMutex.Lock()
		defer QC.idpsMutex.Unlock()
		c := NewIdpsRePortInfo()
		if v, ok := QC.IdpsReport[client]; ok {
			c = v
		}
		if len(data.StartTime) != 0 {
			c.StartTime = data.StartTime
		}
		if data.Event != nil {
			c.Event = data.Event
		}

		if data.RecordList != nil {
			c.RecordList = data.RecordList
		}
		if data.Rules != nil {
			c.Rules = data.Rules
		}
		if data.RulePacketsTotal != nil {
			c.RulePacketsTotal = data.RulePacketsTotal
		}
		QC.IdpsReport[client] = c
		return
	}
	client := r.URL.Query().Get("client")
	if len(client) == 0 {
		QC.idpsMutex.Lock()
		defer QC.idpsMutex.Unlock()
		if len(QC.IdpsReport) == 0 {
			err := errors.New("no client existed")
			q.Q(err)
			RespondWithError(w, err)
			return
		}
		jsonBytes, err := json.Marshal(QC.IdpsReport)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	QC.idpsMutex.Lock()
	v, ok := QC.IdpsReport[client]
	QC.idpsMutex.Unlock()
	if !ok {
		err := errors.New("no client existed")
		q.Q(err)
		RespondWithError(w, err)
		return
	}
	jsonBytes, err := json.Marshal(v)
	if err != nil {
		RespondWithError(w, err)
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}
