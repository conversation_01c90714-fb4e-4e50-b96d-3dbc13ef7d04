package protocol

import (
	"fmt"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type IcmpParser struct {
	proto layers.IPProtocol
	layer gopacket.Layer
}

func (p *IcmpParser) Parse(pack gopacket.Packet) bool {
	if layer := pack.Layer(layers.LayerTypeICMPv4); layer != nil {
		p.proto = layers.IPProtocolICMPv4
		p.layer = layer
		return true
	}

	if layer := pack.Layer(layers.LayerTypeICMPv6); layer != nil {
		p.proto = layers.IPProtocolICMPv6
		p.layer = layer
		return true
	}
	return false
}
func (p *IcmpParser) GetIcmp() (layers.IPProtocol, gopacket.Layer, error) {
	switch p.proto {
	case layers.IPProtocolICMPv4:
		return layers.IPProtocolICMPv4, p.layer, nil
	case layers.IPProtocolICMPv6:
		return layers.IPProtocolICMPv6, p.layer, nil
	}
	return 0, nil, fmt.E<PERSON><PERSON>("error:not parse")
}

func NewIcmpParser() *IcmpParser {
	return &IcmpParser{}
}
