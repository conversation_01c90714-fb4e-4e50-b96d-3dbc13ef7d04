## ids

api for filter packets

### feature

- record packet



### Support OS

- Windows
- Unix (only test on ubuntu)

​	



### Run example

```go
func TestIds(t *testing.T) {
	ids := NewIds(true, true)
	ids.ResgisterReceiveEvent(123, getevent())
	err := ids.Run()
	if err != nil {
		t.Fatal(err)
	}

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	<-c
}


func getevent() func(gopacket.Packet) {
	v := func(e gopacket.Packet) {
		log.Printf("reveive packet%v:", e)

	}
	return v
}
```

