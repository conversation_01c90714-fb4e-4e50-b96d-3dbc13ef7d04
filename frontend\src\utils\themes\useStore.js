import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { theme as antdThme } from "antd";
import publicApis from "../apis/publicApis";
import protectedApi from "../apis/protectedApis";

function convertToWebSocketURL() {
  // Get the current origin (e.g., "https://example.com")
  const origin = window.location.origin;

  // Replace http:// with ws:// and https:// with wss://
  const wsUrl = origin
    .replace(/^http:\/\//i, "ws://")
    .replace(/^https:\/\//i, "wss://");

  return wsUrl;
}

export const useThemeStore = create(
  persist(
    (set) => ({
      mode: "auto",
      colorPrimary: "#13c2c2",
      baseURL:
        process.env.NODE_ENV === "development"
          ? "http://localhost:27182"
          : window.location.origin,
      wsURL:
        process.env.NODE_ENV === "development"
          ? "ws://localhost:27182"
          : convertToWebSocketURL(),
      inventoryType: "device",
      changeMode: (mode) => set((state) => ({ ...state, mode })),
      changePrimaryColor: (colorPrimary) =>
        set((state) => ({ ...state, colorPrimary })),
      changeBaseURL: (baseURL) =>
        set((state) => {
          publicApis.defaults.baseURL = baseURL;
          protectedApi.defaults.baseURL = baseURL;
          return { ...state, baseURL };
        }),
      changeWsURL: (wsURL) => set((state) => ({ ...state, wsURL })),
      changeInventoryType: (type) =>
        set((state) => ({ ...state, inventoryType: type })),
    }),
    {
      name:
        process.env.NODE_ENV === "development"
          ? "nms-setting-dev"
          : "nms-setting-prod", // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
    }
  )
);

export const customDarkAlgorithm = (seedToken, mapToken) => {
  return antdThme.darkAlgorithm({
    ...seedToken,
    ...mapToken,
    colorBgBase: "#1a2035",
  });
};
export const customLightAlgorithm = (seedToken, mapToken) => {
  return antdThme.defaultAlgorithm({
    ...seedToken,
    ...mapToken,
    colorBgBase: "#fff",
  });
};
