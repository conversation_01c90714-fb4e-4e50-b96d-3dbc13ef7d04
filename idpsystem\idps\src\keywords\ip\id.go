package ip

import (
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewId() *id {
	return &id{ipLayer: protocol.NewIpLayer()}
}

type id struct {
	value   uint16
	ipLayer protocol.IPLayer
}

func (i *id) SetUp(v any) error {
	l, err := gonidutil.ConvertToLenMatch(v)
	if err != nil {
		return err
	}
	i.value = uint16(l.Num)
	return nil
}

func (i *id) Match(packet gopacket.Packet) bool {
	ver, net, err := i.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		return i.value == v4.Id
	}
	return false
}
