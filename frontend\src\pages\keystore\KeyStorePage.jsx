import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Descriptions,
  Flex,
  Form,
  Input,
  Row,
} from "antd";
import React from "react";
import {
  useGetAllKeyStoreQuery,
  useImportKeyStoreMutation,
} from "../../app/services/commandApi";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";

const KeyStorePage = () => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const { data = {} } = useGetAllKeyStoreQuery();
  const [importKeyStore, {}] = useImportKeyStoreMutation();
  const items = Object.entries(data).map(([key, value]) => ({
    key: key,
    label: key,
    children: value,
  }));

  const onFinish = async (values) => {
    console.log("Received values of form:", values);
    const keyStore = values.keyStore;
    if (keyStore.length <= 0) {
      notification.error({
        message: "keyname and keyvalue should not be empty!",
      });
      return;
    }
    try {
      const keyStoreObj = keyStore.reduce(
        (obj, item) => Object.assign(obj, { [item.keyName]: item.keyValue }),
        {}
      );
      await importKeyStore(keyStoreObj).unwrap();
      notification.success({
        message: "successfully added key value !",
      });
    } catch (error) {
      notification.error({
        message:
          error.data.error || error.data || error || "somthing went wrong!",
      });
    } finally {
      form.resetFields();
    }
  };

  return (
    <Row gutter={[8, 8]}>
      <Col xs={24} md={8}>
        <Card title="Add Key">
          <Form
            form={form}
            layout="vertical"
            id="key-store-form"
            autoComplete="off"
            onFinish={onFinish}
          >
            <Form.List
              name="keyStore"
              rules={[
                {
                  validator: async (_, names) => {
                    if (!names || names.length <= 0) {
                      return Promise.reject(
                        new Error("add at least 1 key value!")
                      );
                    }
                  },
                },
              ]}
            >
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Flex justify="space-between">
                      <Form.Item
                        required
                        label="Key Name"
                        {...restField}
                        name={[name, "keyName"]}
                        rules={[
                          {
                            required: true,
                            message: "Please input your key name!",
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                      <Form.Item
                        required
                        label="Key Value"
                        {...restField}
                        name={[name, "keyValue"]}
                        rules={[
                          {
                            required: true,
                            message: "Please input your key value!",
                          },
                        ]}
                      >
                        <Input />
                      </Form.Item>
                      <MinusCircleOutlined onClick={() => remove(name)} />
                    </Flex>
                  ))}
                  <Form.Item>
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusOutlined />}
                    >
                      Add fields
                    </Button>
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              )}
            </Form.List>
            <Form.Item>
              <Button type="primary" block htmlType="submit">
                save
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </Col>
      <Col xs={24} md={16}>
        <Card title="List of Keys">
          <Descriptions items={items} />
        </Card>
      </Col>
    </Row>
  );
};

export default KeyStorePage;
