-- *****************************************************************
-- THERMAL-PROTECTION-MIB:  
-- ****************************************************************

MGMT-THERMAL-PROTECTION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInteger16 FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtThermalProtectionMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of thermal protection. The PHY thermal
         protections consists of four groups. Each PHY is associated to a group,
         and each group has a configured max temperature. If the average
         temperature of all sensors exceeds the configured max temperature of a
         group, then the PHYs in that group is shoutdown."
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 78 }


mgmtThermalProtectionMibObjects OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMib 1 }

mgmtThermalProtectionCapabilities OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMibObjects 1 }

mgmtThermalProtectionCapabilitiesMaxSupportedGroup OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of supported thermal protection groups."
    ::= { mgmtThermalProtectionCapabilities 1 }

mgmtThermalProtectionConfig OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMibObjects 2 }

mgmtThermalProtectionConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionConfig 1 }

mgmtThermalProtectionConfigGlobalsParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTThermalProtectionConfigGlobalsParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to assign a temperature to each of the groups"
    ::= { mgmtThermalProtectionConfigGlobals 1 }

mgmtThermalProtectionConfigGlobalsParamEntry OBJECT-TYPE
    SYNTAX      MGMTThermalProtectionConfigGlobalsParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each group associates with a temperature"
    INDEX       { mgmtThermalProtectionConfigGlobalsParamGroupIndex }
    ::= { mgmtThermalProtectionConfigGlobalsParamTable 1 }

MGMTThermalProtectionConfigGlobalsParamEntry ::= SEQUENCE {
    mgmtThermalProtectionConfigGlobalsParamGroupIndex        Integer32,
    mgmtThermalProtectionConfigGlobalsParamGroupTemperature  MGMTInteger16
}

mgmtThermalProtectionConfigGlobalsParamGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Group number."
    ::= { mgmtThermalProtectionConfigGlobalsParamEntry 1 }

mgmtThermalProtectionConfigGlobalsParamGroupTemperature OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Temperature(in C) where the interfaces mapped to the group will be shut
         down."
    ::= { mgmtThermalProtectionConfigGlobalsParamEntry 2 }

mgmtThermalProtectionConfigInterface OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionConfig 2 }

mgmtThermalProtectionConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTThermalProtectionConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to interface group configuration"
    ::= { mgmtThermalProtectionConfigInterface 1 }

mgmtThermalProtectionConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTThermalProtectionConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical port associates with a group temperature"
    INDEX       { mgmtThermalProtectionConfigInterfaceParamIfIndex }
    ::= { mgmtThermalProtectionConfigInterfaceParamTable 1 }

MGMTThermalProtectionConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtThermalProtectionConfigInterfaceParamIfIndex  MGMTInterfaceIndex,
    mgmtThermalProtectionConfigInterfaceParamGroup    MGMTUnsigned8
}

mgmtThermalProtectionConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtThermalProtectionConfigInterfaceParamEntry 1 }

mgmtThermalProtectionConfigInterfaceParamGroup OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..4)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Thermal protection groups. Object value (4) mean disable thermal
         protect for the interface. Object values from 0 to 3 are for the
         temperature group. "
    ::= { mgmtThermalProtectionConfigInterfaceParamEntry 2 }

mgmtThermalProtectionStatus OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMibObjects 3 }

mgmtThermalProtectionStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTThermalProtectionStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to thermal protection interface status"
    ::= { mgmtThermalProtectionStatus 1 }

mgmtThermalProtectionStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTThermalProtectionStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of status parameters"
    INDEX       { mgmtThermalProtectionStatusInterfaceIfIndex }
    ::= { mgmtThermalProtectionStatusInterfaceTable 1 }

MGMTThermalProtectionStatusInterfaceEntry ::= SEQUENCE {
    mgmtThermalProtectionStatusInterfaceIfIndex      MGMTInterfaceIndex,
    mgmtThermalProtectionStatusInterfaceTemperature  MGMTInteger16,
    mgmtThermalProtectionStatusInterfacePower        TruthValue
}

mgmtThermalProtectionStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtThermalProtectionStatusInterfaceEntry 1 }

mgmtThermalProtectionStatusInterfaceTemperature OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current port temperature(in C)."
    ::= { mgmtThermalProtectionStatusInterfaceEntry 2 }

mgmtThermalProtectionStatusInterfacePower OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port thermal protection status. false means port link is up and port is
         operating normally. true means port link is down and port is thermal
         protected."
    ::= { mgmtThermalProtectionStatusInterfaceEntry 3 }

mgmtThermalProtectionMibConformance OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMib 2 }

mgmtThermalProtectionMibCompliances OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMibConformance 1 }

mgmtThermalProtectionMibGroups OBJECT IDENTIFIER
    ::= { mgmtThermalProtectionMibConformance 2 }

mgmtThermalProtectionCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtThermalProtectionCapabilitiesMaxSupportedGroup }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtThermalProtectionMibGroups 1 }

mgmtThermalProtectionConfigGlobalsParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtThermalProtectionConfigGlobalsParamGroupIndex,
                  mgmtThermalProtectionConfigGlobalsParamGroupTemperature }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtThermalProtectionMibGroups 2 }

mgmtThermalProtectionConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtThermalProtectionConfigInterfaceParamIfIndex,
                  mgmtThermalProtectionConfigInterfaceParamGroup }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtThermalProtectionMibGroups 3 }

mgmtThermalProtectionStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtThermalProtectionStatusInterfaceIfIndex,
                  mgmtThermalProtectionStatusInterfaceTemperature,
                  mgmtThermalProtectionStatusInterfacePower }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtThermalProtectionMibGroups 4 }

mgmtThermalProtectionMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtThermalProtectionCapabilitiesInfoGroup,
                       mgmtThermalProtectionConfigGlobalsParamTableInfoGroup,
                       mgmtThermalProtectionConfigInterfaceParamTableInfoGroup,
                       mgmtThermalProtectionStatusInterfaceTableInfoGroup }

    ::= { mgmtThermalProtectionMibCompliances 1 }

END
