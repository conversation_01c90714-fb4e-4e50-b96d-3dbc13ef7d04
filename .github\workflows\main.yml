name: Go CICD
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: install libpcap-dev
        run:  sudo apt-get install -y libpcap-dev
      - name: install libnetfilter-queue-dev
        run:  sudo apt-get install -y libnetfilter-queue-dev
      - name: install libhyperscan-dev
        run:  sudo apt-get install -y libhyperscan-dev
      - name: install libpcre2
        run:  sudo apt-get install -y libpcre2-8-0 libpcre2-dev
      - name: setup go environment
        uses: actions/setup-go@v3.5.0
        with:
          go-version: '^1.20'
          go-version-file: '^1.20'
          check-latest: true
      - name: checkout
        uses: actions/checkout@v3.3.0
      - name: go mod tidy
        run:  go mod tidy
      - name: go build
        run: make
