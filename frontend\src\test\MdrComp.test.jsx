import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, expect, vi, beforeEach } from "vitest";
import MdrConfigModel from "../components/mdr/MdrConfigModel";
import MdrProfinetModel from "../components/mdr/MdrProfinetModel";
import MdrSetLedModel from "../components/mdr/MdrSetLedModel";
import NetworkSettingModel from "../components/mdr/NetworkSettingModel";

describe("MdrConfigModel Component", () => {
  const mockOnOk = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the modal with the correct title", () => {
    const record = { mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" };
    render(<MdrConfigModel open={true} onCancel={mockOnCancel} onOk={mockOnOk} record={record} loading={false} />);

    expect(screen.getByText("MDR Config (08-00-27-F4-AF-EF tcn-fakedevice)")).toBeInTheDocument();
  });

  it("submits the form with valid data", async () => {
    render(
      <MdrConfigModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
      />
    );  
    fireEvent.click(screen.getByText("ZONE 1"));
    fireEvent.click(screen.getByText("NORMAL"));
    fireEvent.click(screen.getByText("ENABLE"));
    fireEvent.change(screen.getByLabelText("MDR Speed"), { target: { value: 1000 } });
    fireEvent.click(screen.getByText("CW"));
    fireEvent.change(screen.getByLabelText("MDR Level"), { target: { value: 5 } });
    fireEvent.click(screen.getByText("NPN"));
  
    const okButton = screen.getByText("OK");
  
    fireEvent.click(okButton);  
    await waitFor(() => {
      expect(mockOnOk).toHaveBeenCalledWith({
        zone: 1,
        mode: "normal",
        holding: "enable",
        speed: 1000,
        direction: "cw",
        level: 5,
        sensor: "npm",
        mac: "08-00-27-F4-AF-EF",
      });
    });
  });
  

  it("resets the form and calls onCancel when Cancel button is clicked", () => {
    render(<MdrConfigModel open={true} onCancel={mockOnCancel} onOk={mockOnOk} loading={false} record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }} />);
    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);
    expect(mockOnCancel).toHaveBeenCalled();
  });
});
describe("MdrProfinetModel Component", () => {
  const mockOnOk = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the modal with the correct title", () => {
    const record = { mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" };
    render(
      <MdrProfinetModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={record}
        loading={false}
      />
    );

    expect(screen.getByText("MDR Profinet Setting (08-00-27-F4-AF-EF tcn-fakedevice)")).toBeInTheDocument();
  });

  it("displays default title if no MAC or model name is provided", () => {
    render(
      <MdrProfinetModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{}}
        loading={false}
      />
    );

    expect(screen.getByText("MDR Profinet Setting")).toBeInTheDocument();
  });

  it("shows validation error if Profinet name is empty", async () => {
    render(
      <MdrProfinetModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    const okButton = screen.getByText("OK");

    fireEvent.click(okButton);

    await waitFor(() => {
      expect(screen.getByText("Please input the profinet name!")).toBeInTheDocument();
    });
  });

  it("calls onOk with correct values when form is submitted", async () => {
    render(
      <MdrProfinetModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    fireEvent.change(screen.getByLabelText("Profinet name"), { target: { value: "ProfinetTest" } });

    const okButton = screen.getByText("OK");

    fireEvent.click(okButton);

    await waitFor(() => {
      expect(mockOnOk).toHaveBeenCalledWith({
        profinet: "ProfinetTest",
        mac: "08-00-27-F4-AF-EF",
      });
    });
  });

  it("resets the form and calls onCancel when cancel button is clicked", () => {
    render(
      <MdrProfinetModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    const cancelButton = screen.getByText("Cancel");

    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });
});
describe("MdrSetLedModel Component", () => {
  const mockOnOk = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the modal with the correct title", () => {
    const record = { mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" };
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={record}
        loading={false}
      />
    );

    expect(screen.getByText("MDR LED Setting (08-00-27-F4-AF-EF tcn-fakedevice)")).toBeInTheDocument();
  });

  it("displays the default title if no MAC or model name is provided", () => {
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{}}
        loading={false}
      />
    );

    expect(screen.getByText("MDR LED Setting")).toBeInTheDocument();
  });

  it("submits the default LED type when no selection is made", async () => {
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    const okButton = screen.getByText("OK");
    fireEvent.click(okButton);

    await waitFor(() => {
      expect(mockOnOk).toHaveBeenCalledWith({
        led: "on", 
        mac: "08-00-27-F4-AF-EF",
      });
    });
  });

  it("submits the correct LED type when a selection is made", async () => {
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    fireEvent.click(screen.getByLabelText("BLINK"));

    const okButton = screen.getByText("OK");
    fireEvent.click(okButton);

    await waitFor(() => {
      expect(mockOnOk).toHaveBeenCalledWith({
        led: "blink",
        mac: "08-00-27-F4-AF-EF",
      });
    });
  });

  it("resets the form and calls onCancel when cancel button is clicked", () => {
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it("renders the radio buttons with correct options", () => {
    render(
      <MdrSetLedModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        record={{ mac: "08-00-27-F4-AF-EF", modelname: "tcn-fakedevice" }}
        loading={false}
      />
    );

    expect(screen.getByLabelText("ON")).toBeInTheDocument();
    expect(screen.getByLabelText("OFF")).toBeInTheDocument();
    expect(screen.getByLabelText("BLINK")).toBeInTheDocument();
  });
});
describe("NetworkSettingModel Component", () => {
  const mockOnOk = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const defaultRecord = {
    ipaddress: "***********",
    mac: "08-00-27-F4-AF-EF",
    netmask: "*************",
    gateway: "*************",
    hostname: "Device-1",
    isdhcp: false,
    modelname: "tcn-fakedevice",
  };

  it("renders the modal with the correct title", () => {
    render(
      <NetworkSettingModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={defaultRecord}
      />
    );

    expect(screen.getByText("MDR Network Setting (08-00-27-F4-AF-EF tcn-fakedevice)")).toBeInTheDocument();
  });

  it("displays the default title if no MAC or model name is provided", () => {
    render(
      <NetworkSettingModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={{}}
      />
    );

    expect(screen.getByText("MDR Network Setting")).toBeInTheDocument();
  });

  it("populates form fields with initial values from the record", () => {
    render(
      <NetworkSettingModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={defaultRecord}
      />
    );
  
    expect(screen.getByLabelText("IP Address").value).toBe("***********");
    expect(screen.getByLabelText("Subnet Mask").value).toBe("*************");
    expect(screen.getByLabelText("Gateway").value).toBe("*************");
    expect(screen.getByLabelText("Host Name").value).toBe("Device-1");  
    expect(screen.getByRole("checkbox", { name: "DHCP" })).not.toBeChecked();
  });
  
   it("submits the form with valid data", async () => {
    render(
      <NetworkSettingModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={defaultRecord}
      />
    );

    const hostnameInput = screen.getByLabelText("Host Name");
    fireEvent.change(hostnameInput, { target: { value: "Updated-Host" } });

    const okButton = screen.getByText("OK");
    fireEvent.click(okButton);

    await waitFor(() => {
      expect(mockOnOk).toHaveBeenCalledWith({
        newipaddress: "***********",
        netmask: "*************",
        gateway: "*************",
        hostname: "Updated-Host",
        isdhcp: false,
        ipaddress: "***********",
        mac: "08-00-27-F4-AF-EF",
      });
    });
  });

  it("resets the form and calls onCancel when the cancel button is clicked", () => {
    render(
      <NetworkSettingModel
        open={true}
        onCancel={mockOnCancel}
        onOk={mockOnOk}
        loading={false}
        record={defaultRecord}
      />
    );

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
    expect(screen.getByLabelText("Host Name").value).toBe("");
  });
});
