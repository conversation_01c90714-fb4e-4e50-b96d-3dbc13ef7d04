import React, { useEffect, useState } from "react";
import dayjs from "dayjs";

import {
  App,
  <PERSON><PERSON>,
  ConfigProvider,
  Form,
  Input,
  List,
  Modal,
  theme,
} from "antd";
import { ProTable } from "@ant-design/pro-components";
import {
  useAddRulesMutation,
  useDeleteRulesMutation,
  useImportRulesMutation,
} from "../../app/services/idpsApi";
import { DeleteOutlined } from "@ant-design/icons";

const CategoriesTable = ({ data = [], id, onOkClick }) => {
  const token = theme.useToken().token;
  const { notification } = App.useApp();
  const [openRuleModels, setOpenRuleModels] = useState(false);
  const [openRuleAddModels, setOpenRuleAddModels] = useState(false);

  const [requestDeleteRule, { isLoading }] = useDeleteRulesMutation();

  const handleSendDeleteRequest = async (ruleName) => {
    try {
      await requestDeleteRule({
        ruleName,
        svc: id,
      }).unwrap();
      notification.success({
        message: "successfully delete command sent",
      });
    } catch (error) {
      console.log(error);
      notification.error({ message: error?.data?.error || error?.data });
    }
  };
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Created at",
      dataIndex: "created_time",
      key: "created_time",
      render: (data) => {
        return dayjs(data).format("YYYY/MM/DD HH:mm:ss");
      },
    },
    {
      title: "Action",
      key: "action",
      align: "center",
      render: (_, { name }) => {
        return (
          <DeleteOutlined
            style={{ color: token.colorError }}
            onClick={() => handleSendDeleteRequest(name)}
          />
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        cardProps={{ bodyStyle: { paddingInline: 5, paddingBlock: 0 } }}
        cardBordered={false}
        loading={isLoading}
        columns={columns}
        bordered
        rowKey="name"
        headerTitle="Rules client wise"
        size="small"
        dataSource={data}
        expandable={{
          expandedRowRender: (record) => (
            <List
              size="small"
              header={<div>Rules contents</div>}
              bordered
              dataSource={record?.contents || []}
              renderItem={(item) => <List.Item>{item.value}</List.Item>}
            />
          ),
          rowExpandable: (record) => record?.contents.length > 0,
        }}
        pagination={{
          position: ["bottomCenter"],
          showQuickJumper: true,
          size: "default",
          total: data?.length,
          defaultPageSize: 10,
          pageSizeOptions: [5, 10, 15, 20, 25],
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        options={{
          search: false,
          fullScreen: false,
          setting: false,
          reload: false,
          density: false,
        }}
        toolbar={{
          actions: [
            <Button type="primary" onClick={() => setOpenRuleModels(true)}>
              import rules
            </Button>,
            <Button type="primary" onClick={() => setOpenRuleAddModels(true)}>
              add rules
            </Button>,
          ],
        }}
        search={false}
        dateFormatter="string"
        onRow={(record, rowIndex) => {
          return {
            onClick: async (event) => {
              event.preventDefault();
              if (record) {
                onOkClick(record.name);
              }
            },
          };
        }}
      />
      <IdpsRulesImportModel
        open={openRuleModels}
        onClose={() => setOpenRuleModels(false)}
        svcId={id}
      />
      <IdpsRulesAddModel
        open={openRuleAddModels}
        onClose={() => setOpenRuleAddModels(false)}
        svcId={id}
      />
    </>
  );
};

export default CategoriesTable;

export const IdpsRulesImportModel = ({ open, onClose, svcId }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const [handleImportRules, { isLoading }] = useImportRulesMutation();

  const HandleImport = async (data) => {
    try {
      await handleImportRules({ url: data, svc: svcId }).unwrap();
      notification.success({
        message: "successfully import rules command sent",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    } finally {
      form.setFieldsValue({ importUrl: "" });
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      width={700}
      forceRender
      maskClosable={false}
      title={`Import Rules for ${svcId}`}
      okText="import"
      cancelText="CANCEL"
      confirmLoading={isLoading}
      onCancel={() => {
        form.setFieldsValue({ importUrl: "" });
        onClose();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            HandleImport(values?.importUrl);
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_import_idp_rule">
        <Form.Item
          name="importUrl"
          label="Rules URL"
          rules={[
            {
              required: true,
              message: "Please input the rules URL!",
            },
            {
              type: "url",
              message: "please input url type!",
            },
          ]}
        >
          <Input.TextArea autoSize={{ minRows: 3, maxRows: 10 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export const IdpsRulesAddModel = ({ open, onClose, svcId }) => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const [handleAddRules, { isLoading }] = useAddRulesMutation();

  const HandleAdd = async (ruleName, rules) => {
    try {
      await handleAddRules({ ruleName, rules, svc: svcId }).unwrap();
      notification.success({
        message: "successfully add rules command sent",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    } finally {
      form.setFieldsValue({ ruleName: "", rules: "" });
      onClose();
    }
  };

  return (
    <Modal
      open={open}
      width={700}
      forceRender
      maskClosable={false}
      title={`Add Rules for ${svcId}`}
      okText="Add"
      cancelText="CANCEL"
      confirmLoading={isLoading}
      onCancel={() => {
        form.setFieldsValue({ ruleName: "", rules: "" });
        onClose();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            HandleAdd(values?.ruleName, values?.rules);
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_add_idp_rule">
        <Form.Item
          name="ruleName"
          label="Rule Name"
          rules={[
            {
              required: true,
              message: "Please input the rules name!",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="rules"
          label="Rules"
          rules={[
            {
              required: true,
              message: "Please input the rules!",
            },
          ]}
        >
          <Input.TextArea autoSize={{ minRows: 3, maxRows: 10 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
