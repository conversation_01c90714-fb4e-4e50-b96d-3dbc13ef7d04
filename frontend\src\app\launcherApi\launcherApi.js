import { launcherApi as api } from "./api";

export const launcherApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getInstallerFileList: builder.query({
      query: () => "api/v1/installerList",
      providesTags: ["launcher"],
    }),
    downloadInstaller: builder.mutation({
      query: (data) => ({
        url: "api/v1/downloadInstaller",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    uninstallNimbl: builder.mutation({
      query: (data) => ({
        url: "api/v1/uninstallNimbl",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    deleteLicense: builder.mutation({
      query: (data) => ({
        url: "api/v1/upload/license",
        method: "DELETE",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    deletePrivateKey: builder.mutation({
      query: (data) => ({
        url: "api/v1/upload/privkey",
        method: "DELETE",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    getLauncherStatus: builder.query({
      query: () => "api/v1/status",
      providesTags: ["launcher"],
    }),
    getRunOutput: builder.query({
      query: () => "api/v1/services/run/output",
      providesTags: ["launcher"],
    }),
    addServicesFromLicense: builder.mutation({
      query: (data) => ({
        url: "api/v1/services/from_license",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    getAllServices: builder.query({
      query: () => "api/v1/services",
      providesTags: ["launcher"],
    }),
    startAllServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services/run/all",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    editServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    runServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services/run",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    deleteServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services",
        method: "DELETE",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    getShellServices: builder.query({
      query: () => "api/v1/services/shell",
      providesTags: ["launcher"],
    }),
    updateServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services/shell",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
    kilAllServices: builder.mutation({
      query: (data) => ({
        url: "api/v1/services/run/all",
        method: "DELETE",
        body: data,
      }),
      invalidatesTags: ["launcher"],
    }),
  }),
});

export const {
  useGetInstallerFileListQuery,
  useDownloadInstallerMutation,
  useUninstallNimblMutation,
  useDeleteLicenseMutation,
  useGetLauncherStatusQuery,
  useGetRunOutputQuery,
  useAddServicesFromLicenseMutation,
  useGetAllServicesQuery,
  useStartAllServicesMutation,
  useEditServicesMutation,
  useRunServicesMutation,
  useDeleteServicesMutation,
  useGetShellServicesQuery,
  useUpdateServicesMutation,
  useKilAllServicesMutation,
  useDeletePrivateKeyMutation,
} = launcherApi;
