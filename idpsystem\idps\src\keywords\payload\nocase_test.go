package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestNoCase(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{

		{
			content:  `alert icmp any any -> any any (content:"abc"; nocase; content:"d"; distance:0; within:1; sid:1;)`,
			expected: true,
			packet:   []byte("abcaBcd"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
