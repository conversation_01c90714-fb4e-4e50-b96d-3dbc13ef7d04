package keywords

import (
	"fmt"
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/keywords/icmp"
	"mnms/idpsystem/idps/src/keywords/ip"
	"mnms/idpsystem/idps/src/keywords/tcp"
	"mnms/idpsystem/idps/src/keywords/udp"
	"strings"

	"github.com/google/gopacket"
)

type StickyBuffer interface {
	RetrieveBffer(packet gopacket.Packet) []byte
}

func keywordPktMap() map[string]StickyBuffer {
	keywordmap := map[string]StickyBuffer{
		"ipv4.hdr":   ip.NewV4hdr(),
		"ipv6.hdr":   ip.NewV6hdr(),
		"tcp.hdr":    tcp.Newhdr(),
		"udp.hdr":    udp.Newhdr(),
		"icmpv4.hdr": icmp.NewV4hdr(),
		"icmpv6.hdr": icmp.NewV6hdr(),
	}
	return keywordmap
}
func NewStickyBuffer(v string) (StickyBuffer, error) {
	v = strings.ToLower(v)
	if v, ok := keywordPktMap()[v]; ok {

		return v, nil
	}
	return nil, fmt.Errorf("not supported KeyWord:%v", v)
}

type MpmPkter interface {
	Build() error
	MatchIds(packet gopacket.Packet) []int
	AddContent(contents mpm.Content) error
}

func NewMpmPkt(v string) (MpmPkter, error) {
	v = strings.ToLower(v)
	if v, ok := keywordPktMap()[v]; ok {
		if mpm, ok := v.(MpmPkter); ok {
			return mpm, nil
		}
	}
	return nil, fmt.Errorf("not supported KeyWord:%v", v)
}
