package tcp

import (
	"fmt"
	utilies "mnms/idpsystem/idps/src"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
)

const (
	fin  uint8 = 0x01
	syn  uint8 = 0x02
	rst  uint8 = 0x04
	push uint8 = 0x08
	ack  uint8 = 0x10
	urg  uint8 = 0x20
	enc  uint8 = 0x40
	cwr  uint8 = 0x80
)

const (
	modifiernot uint8 = iota + 1
	modifieplus
	modifieany
)

func NewFlags() *flags {
	return &flags{}
}

type flags struct {
	modifier   uint8
	flag       uint8
	ignoreflag uint8
	ck         func(flags, dflags uint8) bool
}

func (f *flags) SetUp(s string) error {
	v := &utilies.Lenmatch{}
	err := v.Parse(s)
	if err != nil {
		return err
	}
	switch v.Modifier {
	case '!':
		f.modifier = modifiernot
	case '+':
		f.modifier = modifieplus
	case '*':
		f.modifier = modifieany
	}
	found, ignore := 0, 0
	for _, flag := range v.Str {
		switch flag {
		case 'f':
			f.flag |= fin
			found++
		case 's':
			f.flag |= syn
			found++
		case 'r':
			f.flag |= rst
			found++
		case 'p':
			f.flag |= push
			found++
		case 'a':
			f.flag |= ack
			found++
		case 'u':
			f.flag |= urg
			found++
		case 'c':
			f.flag |= cwr
			found++
		case 'e':
			f.flag |= enc
			found++
		case '0':
			f.flag = 0
			found++
		case '!', '+', '*':
			return fmt.Errorf("flags:%v supports only one modifier at a time", s)
		}
	}
	if found == 0 {
		return fmt.Errorf("error no value was set: %v", s)
	}

	f.ignoreflag = 0xff
	for _, flag := range v.Str2 {
		switch flag {
		case 'f':
			f.ignoreflag &= ^fin
			ignore++
		case 's':
			f.ignoreflag &= ^syn
			ignore++
		case 'r':
			f.ignoreflag &= ^rst
			ignore++
		case 'p':
			f.ignoreflag &= ^push
			ignore++
		case 'a':
			f.ignoreflag &= ^ack
			ignore++
		case 'u':
			f.ignoreflag &= ^urg
			ignore++
		case 'c':
			f.ignoreflag &= ^cwr
			ignore++
		case 'e':
			f.ignoreflag &= ^enc
			ignore++
		}
	}
	if ignore == 0 {
		return fmt.Errorf("error no ignore value was set: %v", s)
	}
	f.ck = f.creatVerify(f.modifier)
	return nil
}

func (f *flags) Match(packet gopacket.Packet) bool {
	tp := protocol.NewTcpParser()
	b := tp.Parse(packet)
	if !b {
		return false
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return false
	}
	data := tcp.Contents
	pflag := data[13]
	dflag := f.flag
	if !(dflag&pflag > 0) {
		return f.modifier == modifiernot
	}
	flags := pflag & f.ignoreflag
	if f.ck != nil && f.ck(flags, dflag) {
		return true
	}
	return false
}

func (f *flags) creatVerify(m uint8) func(flags, dflags uint8) bool {
	switch m {
	case modifieany:
		return func(flags, dflags uint8) bool { return flags&dflags > 0 }
	case modifieplus:
		return func(flags, dflags uint8) bool { return flags&dflags == dflags }
	case modifiernot:
		return func(flags, dflags uint8) bool { return flags&dflags != dflags }
	default:
		return func(flags, dflags uint8) bool { return flags == dflags }
	}
}
