package detect

import "errors"

var ErrorIdExistent = errors.New("id has existent")
var ErrorIdNotExistent = errors.New("id hasn't existent")
var ErrorNotSignature = errors.New("not found signature")

var ErrorFormat = errors.New("error format")
var ErrorExpression = errors.New("expression format error")
var Errorportforamt = errors.New("error port foramt")
var ErrorportRangeforamt = errors.New("error port range,second port should than first")
var ErrorLayer = errors.New("not support")
