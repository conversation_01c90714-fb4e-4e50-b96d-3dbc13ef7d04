import json
import requests
import sys
from typing import Dict, List, Optional
import argparse
from colorama import init, Fore, Style

init(autoreset=True)

class ChatCLI:
    def __init__(self, base_url: str = "http://localhost:27182"):
        self.base_url = base_url
        self.current_session_id: Optional[str] = None
        self.default_model = "gpt-3.5-turbo"
        self.default_provider = "openai"
        self.token: Optional[str] = None
        self.messages: List[Dict] = []  # Store conversation history

    def set_token(self, token: str):
        self.token = token

    def login(self, user: Optional[str] = None, password: Optional[str] = None) -> bool:
        """Authenticate and store token."""
        import getpass
        if user is None:
            user = input("Username: ").strip()
        if password is None:
            password = getpass.getpass("Password: ")
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/login",
                json={"user": user, "password": password}
            )
            response.raise_for_status()
            data = response.json()
            if "token" in data:
                self.set_token(data["token"])
                print(f"Login successful. User: {data.get('user', user)} Role: {data.get('role', '')}")
                return True
            else:
                print("Login failed: No token in response.")
                return False
        except requests.exceptions.RequestException as e:
            print(f"Login error: {e}")
            return False

    def _auth_headers(self):
        headers = {}
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers

    def create_new_session(self) -> bool:
        """Create a new chat session."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False
        
        # Get API credentials from kvstore
        api_key = self.get_kvstore_value(f"{self.default_provider}_api_key")
        base_url = self.get_kvstore_value(f"{self.default_provider}_base_url")
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/llm/session/new",
                json={
                    "provider": self.default_provider,
                    "model": self.default_model,
                    "api_key": api_key or "",
                    "base_url": base_url or ""
                },
                headers=self._auth_headers()
            )
            response.raise_for_status()
            data = response.json()
            self.current_session_id = data["session_id"]
            self.messages = []  # Reset messages for new session
            print(f"New session created: {self.current_session_id}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error creating new session: {e}")
            return False

    def get_chat_history(self) -> bool:
        """Get and display chat history for current session."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False
        if not self.current_session_id:
            print(Fore.RED + "No active session. Create one with /new")
            return False

        try:
            response = requests.get(
                f"{self.base_url}/api/v1/llm/session?session_id={self.current_session_id}",
                headers=self._auth_headers()
            )
            response.raise_for_status()
            data = response.json()
            
            # Update local messages with session history
            if "messages" in data and data["messages"]:
                self.messages = data["messages"]
            
            print(Fore.CYAN + "\nChat History:")
            if self.messages:
                for idx, msg in enumerate(self.messages, 1):
                    role = msg["role"]
                    content = msg.get("content", "")
                    if role == "user":
                        color = Fore.GREEN
                    elif role == "assistant":
                        color = Fore.YELLOW
                    elif role == "system":
                        color = Fore.BLUE
                    else:
                        color = Fore.WHITE
                    print(f"{color}{idx:02d}. [{role.capitalize()}] {content}")
            else:
                print(Fore.YELLOW + "No chat history available for this session.")
            print()
            return True
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error getting chat history: {e}")
            return False

    def list_sessions(self) -> bool:
        """List all chat sessions."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False

        try:
            response = requests.get(
                f"{self.base_url}/api/v1/llm/session/list",
                headers=self._auth_headers()
            )
            response.raise_for_status()
            sessions = response.json()
            
            print(Fore.CYAN + "\nActive Sessions:")
            if sessions:
                for session in sessions:
                    session_id = session.get("session_id", "Unknown")
                    provider = session.get("provider", "Unknown")
                    model = session.get("model", "Unknown")
                    created_at = session.get("created_at", "Unknown")
                    current_indicator = " (CURRENT)" if session_id == self.current_session_id else ""
                    print(f"  {session_id[:8]}... - {provider}/{model} - {created_at}{current_indicator}")
            else:
                print(Fore.YELLOW + "No active sessions found.")
            print()
            return True
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error listing sessions: {e}")
            return False

    def delete_session(self, session_id: Optional[str] = None) -> bool:
        """Delete a chat session."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False
        
        target_session = session_id or self.current_session_id
        if not target_session:
            print(Fore.RED + "No session specified and no active session.")
            return False

        try:
            response = requests.post(
                f"{self.base_url}/api/v1/llm/session/delete",
                json={"session_id": target_session},
                headers=self._auth_headers()
            )
            response.raise_for_status()
            print(f"Session {target_session} deleted successfully.")
            
            # If we deleted the current session, clear it
            if target_session == self.current_session_id:
                self.current_session_id = None
                self.messages = []
                print("Current session cleared. Use /new to create a new session.")
            return True
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error deleting session: {e}")
            return False

    def switch_session(self, session_id: str) -> bool:
        """Switch to a different session."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False

        try:
            # Verify the session exists and get its history
            response = requests.get(
                f"{self.base_url}/api/v1/llm/session?session_id={session_id}",
                headers=self._auth_headers()
            )
            response.raise_for_status()
            data = response.json()
            
            self.current_session_id = session_id
            # Load the session's message history
            self.messages = data.get("messages", [])
            print(f"Switched to session: {session_id}")
            print(f"Loaded {len(self.messages)} messages from session history.")
            return True
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error switching to session: {e}")
            return False

    def send_message(self, message: str) -> bool:
        """Send a message to the AI assistant."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False
        if not self.current_session_id:
            print(Fore.RED + "No active session. Create one with /new")
            return False

        try:
            # Add user message to conversation history
            user_message = {
                "role": "user",
                "content": message
            }
            
            # Append to existing messages (maintain conversation history)
            all_messages = self.messages + [user_message]

            response = requests.post(
                f"{self.base_url}/api/v1/llm/session/chat",
                json={
                    "session_id": self.current_session_id,
                    "messages": all_messages  # Send all messages including history
                },
                headers=self._auth_headers()
            )
            
            # Check for HTTP errors first
            if not response.ok:
                print(Fore.RED + f"Error sending message: {response.status_code} {response.reason}")
                try:
                    # Try to parse error response as JSON
                    error_data = response.json()
                    if "error" in error_data:
                        print(Fore.RED + f"Server error: {error_data['error']}")
                    else:
                        print(Fore.RED + f"Response: {response.text}")
                except (json.JSONDecodeError, ValueError):
                    # If not JSON, print raw text
                    print(Fore.RED + f"Response: {response.text}")
                return False

            # Print raw JSON response
            try:
                data = response.json()
                print(Fore.CYAN + "Chat Response JSON:")
                print(json.dumps(data, indent=2))
                
                # Update local messages with the response
                if "new_messages" in data:
                    # The server returns new messages that were added
                    # We need to update our local conversation history
                    self.messages = all_messages + data["new_messages"]
                
                return True
                
            except json.JSONDecodeError as e:
                print(Fore.RED + f"Error parsing response JSON: {e}")
                print(Fore.RED + f"Response: {response.text}")
                return False
            
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error sending message: {e}")
            return False

    def get_available_models(self) -> Optional[List[str]]:
        """Fetch available models from the backend."""
        # Since there's no available models endpoint in the Go code,
        # return a list of common models that users can choose from
        return [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "claude-3-sonnet",
            "claude-3-haiku",
            "gemini-pro",
            "llama2",
            "mistral"
        ]

    def handle_model_command(self, user_input: str):
        """Handle the /model command: list or set model."""
        parts = user_input.strip().split()
        if len(parts) == 1:
            # List available models
            models = self.get_available_models()
            if models is not None:
                print("\nAvailable models:")
                for m in models:
                    print(f"- {m}")
                print(f"\nCurrent model: {self.default_model}\n")
            else:
                print("Could not retrieve available models.")
        else:
            # Set model
            new_model = parts[1]
            self.default_model = new_model
            print(f"Model set to: {self.default_model}")

    def handle_provider_command(self, user_input: str):
        """Handle the /provider command: list or set provider."""
        valid_providers = ["openai", "gemini", "openrouter", "ollama"]
        parts = user_input.strip().split()
        if len(parts) == 1:
            # Show available providers and current provider
            print("\nAvailable providers:")
            for p in valid_providers:
                print(f"- {p}")
            print(f"\nCurrent provider: {self.default_provider}\n")
        else:
            # Set provider
            new_provider = parts[1].lower()
            if new_provider in valid_providers:
                self.default_provider = new_provider
                print(f"Provider set to: {self.default_provider}")
            else:
                print(Fore.RED + f"Invalid provider. Valid providers: {', '.join(valid_providers)}")

    def handle_session_command(self, user_input: str):
        """Handle the /session command: list, switch, or delete sessions."""
        parts = user_input.strip().split()
        if len(parts) == 1:
            # List sessions
            self.list_sessions()
        elif len(parts) == 2:
            action = parts[1].lower()
            if action == "list":
                self.list_sessions()
            else:
                # Assume it's a session ID to switch to
                self.switch_session(parts[1])
        elif len(parts) == 3:
            action = parts[1].lower()
            session_id = parts[2]
            if action == "delete":
                self.delete_session(session_id)
            elif action == "switch":
                self.switch_session(session_id)
            else:
                print("Usage: /session [list|switch|delete] [session_id]")
        else:
            print("Usage: /session [list|switch|delete] [session_id]")

    def get_kvstore_value(self, key: str) -> Optional[str]:
        """Get a value from the kvstore API."""
        if not self.token:
            return None
        try:
            response = requests.get(
                f"{self.base_url}/api/v1/kvstore/{key}",
                headers=self._auth_headers()
            )
            if response.ok:
                data = response.json()
                if "value" in data and not data.get("error"):
                    return data["value"]
            return None
        except requests.exceptions.RequestException:
            return None

    def set_kvstore_values(self, values: dict) -> bool:
        """Set values in the kvstore API."""
        if not self.token:
            print(Fore.RED + "You must /login first.")
            return False
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/kvstore",
                json=values,
                headers=self._auth_headers()
            )
            return response.ok
        except requests.exceptions.RequestException as e:
            print(Fore.RED + f"Error setting kvstore values: {e}")
            return False

    def handle_config_command(self, user_input: str):
        """Handle the /config command: set LLM configuration using kvstore API."""
        def mask_api_key(api_key: str) -> str:
            """Mask API key but keep first 4 and last 4 characters visible."""
            if not api_key:
                return "Not set"
            if len(api_key) <= 8:
                return "*" * len(api_key)
            return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]
        
        if not self.token:
            print(Fore.RED + "You must /login first to access configuration.")
            return
        
        parts = user_input.strip().split()
        if len(parts) == 1:
            # Show current configuration from kvstore
            print(f"\nCurrent LLM Configuration:")
            print(f"Model: {self.default_model}")
            print(f"Provider: {self.default_provider}")
            
            # Get configuration from kvstore
            providers = ["openai", "gemini", "openrouter", "ollama"]
            for provider in providers:
                api_key = self.get_kvstore_value(f"{provider}_api_key")
                if api_key:
                    print(f"{provider.capitalize()} API Key: {mask_api_key(api_key)}")
                if provider == "ollama":
                    base_url = self.get_kvstore_value(f"{provider}_base_url")
                    if base_url:
                        print(f"{provider.capitalize()} Base URL: {base_url}")
            print()
        elif len(parts) >= 3:
            # Set configuration: /config provider api_key [base_url]
            provider = parts[1].lower()
            api_key = parts[2]
            base_url = parts[3] if len(parts) > 3 else ""
            
            # Validate provider
            valid_providers = ["openai", "gemini", "openrouter", "ollama"]
            if provider not in valid_providers:
                print(Fore.RED + f"Invalid provider. Valid providers: {', '.join(valid_providers)}")
                return
            
            # Prepare kvstore values
            kvstore_values = {}
            
            if provider == "ollama":
                if api_key and api_key != "''":
                    kvstore_values[f"{provider}_api_key"] = api_key
                if base_url:
                    kvstore_values[f"{provider}_base_url"] = base_url
                    print(f"Ollama configuration updated:")
                    print(f"Provider: {provider}")
                    if api_key and api_key != "''":
                        print(f"API Key: {mask_api_key(api_key)}")
                    print(f"Base URL: {base_url}")
                else:
                    print(Fore.RED + "Ollama requires a base URL. Example: /config ollama api_key http://localhost:11434")
                    return
            else:
                if api_key and api_key != "''":
                    kvstore_values[f"{provider}_api_key"] = api_key
                    print(f"LLM configuration updated:")
                    print(f"Provider: {provider}")
                    print(f"API Key: {mask_api_key(api_key)}")
                    if base_url:
                        kvstore_values[f"{provider}_base_url"] = base_url
                        print(f"Base URL: {base_url}")
                else:
                    print(Fore.RED + f"{provider.capitalize()} requires an API key.")
                    return
            
            # Save to kvstore
            if kvstore_values:
                success = self.set_kvstore_values(kvstore_values)
                if not success:
                    print(Fore.RED + "Failed to save configuration to server.")
            
        else:
            print("Usage: /config [provider api_key [base_url]]")
            print("Valid providers: openai, gemini, openrouter, ollama")
            print("Examples:")
            print("  /config openai sk-your-api-key")
            print("  /config gemini your-gemini-key")
            print("  /config ollama your-api-key http://localhost:11434")
            print("  /config - Show current configuration")

    def show_help(self):
        """Display the help menu with available commands."""
        print(Fore.CYAN + "Available commands:")
        print(Fore.CYAN + "/login - Authenticate and get a token")
        print(Fore.CYAN + "/new - Create a new chat session")
        print(Fore.CYAN + "/history - Show chat history for current session")
        print(Fore.CYAN + "/session [list|switch|delete] [session_id] - Manage chat sessions")
        print(Fore.CYAN + "/model [modelname] - Show or change the current model")
        print(Fore.CYAN + "/provider [provider] - Show or change the current provider")
        print(Fore.CYAN + "/config [provider api_key [base_url]] - Configure LLM settings")
        print(Fore.CYAN + "/help - Display this help menu")
        print(Fore.CYAN + "/exit - Exit the program")
        print(Fore.CYAN + "Type your message to chat with the AI assistant")

    def run(self):
        """Run the interactive chat CLI."""
        print(Fore.CYAN + "Welcome to MNMS Chat CLI!")
        self.show_help()

        while True:
            try:
                user_input = input(Fore.GREEN + "> " + Style.RESET_ALL).strip()
                
                if not user_input:
                    continue

                cmd = user_input.lower()
                if cmd == "/exit":
                    print(Fore.CYAN + "Goodbye!")
                    break
                elif cmd == "/help":
                    print(Fore.CYAN + "[COMMAND] /help")
                    self.show_help()
                elif cmd == "/login":
                    print(Fore.CYAN + "[COMMAND] /login")
                    self.login()
                elif cmd == "/new":
                    print(Fore.CYAN + "[COMMAND] /new")
                    self.create_new_session()
                elif cmd == "/history":
                    print(Fore.CYAN + "[COMMAND] /history")
                    self.get_chat_history()
                elif cmd.startswith("/session"):
                    print(Fore.CYAN + f"[COMMAND] {user_input}")
                    self.handle_session_command(user_input)
                elif cmd.startswith("/model"):
                    print(Fore.CYAN + f"[COMMAND] {user_input}")
                    self.handle_model_command(user_input)
                elif cmd.startswith("/provider"):
                    print(Fore.CYAN + f"[COMMAND] {user_input}")
                    self.handle_provider_command(user_input)
                elif cmd.startswith("/config"):
                    print(Fore.CYAN + f"[COMMAND] {user_input}")
                    self.handle_config_command(user_input)
                else:
                    print(Fore.GREEN + f"[You]: {user_input}")
                    self.send_message(user_input)

            except KeyboardInterrupt:
                print(Fore.CYAN + "\nGoodbye!")
                break
            except Exception as e:
                print(Fore.RED + f"An error occurred: {e}")

def main():
    # Add argparse for server URL
    parser = argparse.ArgumentParser(description="MNMS Chat CLI")
    parser.add_argument(
        "-s", "--server",
        type=str,
        default="http://localhost:27182",
        help="Base URL of the MNMS server (default: http://localhost:27182)"
    )
    args = parser.parse_args()
    cli = ChatCLI(base_url=args.server)
    cli.run()

if __name__ == "__main__":
    main()