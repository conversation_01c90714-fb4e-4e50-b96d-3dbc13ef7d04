## mnms -- Nimbl NMS

Network management system software to support basic scan, config, and multiple node cluster support with CLI and API.

Additional UI, IDPS, and Anomaly detection support added in v1.0.6 and later.

Release are available at 

https://nimbl.blackbeartechhive.com/files and 
https://github.com/bbtechhive/mnms/releases

### User guide

User guide manual is at:

https://github.com/bbtechhive/userguide

It is a markdown file that can be used to generate pdf output for delivery.
Use `mdpdf` to generate pdf.
Use VSCode to edit the table of contents at the top of the file. Please do not edit table of contents manually.

### Agent

The agent code that works with mnms is at:

https://github.com/bbtechhive/agentclient

### mnms_installation

Release related binaries and exe files and scripts are available at:

https://github.com/bbtechhive/mnms_installation/

### nms-key

There is a repo 

https://github.com/bbtechhive/nms-key 

for the license server including registration data, user data, 
file server and caddyfiles. 

But the files and images added with file server is stored on the
live site at https://nimbl.blackbeartechhive.com/files

### ollama test gpu machine assets

https://github.com/bbtechhive/assetstestgpuollamasite

### Read doc and prioritize tests

Please read [doc/tests.md](https://github.com/bbtechhive/mnms/blob/main/doc/tests.md) and other documentation under doc/

Testing is our number one most important job. It is more important than any additional features.

Release procedures must be followed as [documented there](https://github.com/bbtechhive/mnms/blob/main/doc/release_procedure.md). 

Write down everything. Document each feature. Document all bugs, discussions, features, requests for features, etc.
We need documentation so that any new members or future participants can understand the contents of this repo.
Communication should be done in writing via [github issues](https://github.com/bbtechhive/mnms/issues) and PRs. 

### Do not directly check into main branches

Must provide PR from a branch and get others to review and approve before merging to the main branch

### Run make to build

Just use Makefile

### Run go test

Before and after code changes you must run tests. 

Write as many  tests as `go test`. 

If you have added code without go tests then you have added more debt.  
Do not add problems. 
Add solutions. Solutions must be focused, simple, and have extensive tests.

Be sure to run:

```
sudo go test -v -p 1
```

and make sure all unit tests pass.  You may have to be a super user to run all tests.

On Windows, run `go test -v -p 1` in a command shell or powershell in administrator mode.


### Please do not check in binary files or large files

Please don't make git slow down because of large binary files.

If you have to drag in large binary blobs, please create a separate repo just for that and refer to that repo.
Please do not mix binaries with non-binaries in one repo.

### keep everything as simple and small as possible

Please don't drag in any dependency if not absolutely required. Remember: less is more.  
[Keep It Simple and Stupid](https://en.wikipedia.org/wiki/KISS_principle).


