-- *****************************************************************
-- ACL-MIB:  
-- ****************************************************************

MGMT-ACL-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-<PERSON><PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTASRType FROM MGMT-TC
    MGMTASType FROM MGMT-TC
    MGMTAdvDestMacType FROM MGMT-TC
    MGMTBitType FROM MGMT-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVlanTagPriority FROM MGMT-TC
    ;

mgmtAclMib MODULE-IDENTITY
    LAST-UPDATED "201904050000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of ACL"
    REVISION    "201904050000Z"
    DESCRIPTION
        "Obsoleted EvcPolicerId"
    REVISION    "201501120000Z"
    DESCRIPTION
        "Add ACE parameters for stacking device"
    REVISION    "201411130000Z"
    DESCRIPTION
        "Add ACE Ingress parameters"
    REVISION    "201408050000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 17 }


MGMTAclAceArpOp ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "ARP opcode."
    SYNTAX      INTEGER { any(0), arp(1), rarp(2), other(3) }

MGMTAclAceFrameType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The frame type of the ACE."
    SYNTAX      INTEGER { any(0), etype(1), arp(4), ipv4(5), ipv6(6) }

MGMTAclAceIngressPortListMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The ingress port list mode of the ACE."
    SYNTAX      INTEGER { any(0), specific(1), switch(2),
                          switchport(3) }

MGMTAclAceVlanTagged ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "VLAN tagged/untagged."
    SYNTAX      INTEGER { any(0), untagged(1), tagged(2) }

MGMTAclHitAction ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The hit action."
    SYNTAX      INTEGER { permit(0), deny(1), redirect(2), egress(3) }

mgmtAclMibObjects OBJECT IDENTIFIER
    ::= { mgmtAclMib 1 }

mgmtAclCapabilities OBJECT IDENTIFIER
    ::= { mgmtAclMibObjects 1 }

mgmtAclCapabilitiesAceIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum ID of ACE."
    ::= { mgmtAclCapabilities 1 }

mgmtAclCapabilitiesPolicyIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum ID of policy."
    ::= { mgmtAclCapabilities 2 }

mgmtAclCapabilitiesRateLimiterIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum ID of rate limiter."
    ::= { mgmtAclCapabilities 3 }

mgmtAclCapabilitiesEvcPolicerIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum ID of EVC policer (obsolete. Always 0)."
    ::= { mgmtAclCapabilities 4 }

mgmtAclCapabilitiesRateLimiterBitRateSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the rate limiter can be configured by bit rate."
    ::= { mgmtAclCapabilities 5 }

mgmtAclCapabilitiesEvcPolicerSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, EVC policer can be configured (obsolete. Always false)."
    ::= { mgmtAclCapabilities 6 }

mgmtAclCapabilitiesMirrorSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, mirror action is supported."
    ::= { mgmtAclCapabilities 7 }

mgmtAclCapabilitiesMultipleRedirectPortsSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, redirect port list can be configured with multiple ports. If
         false, redirect port list can be configured with only one single port."
    ::= { mgmtAclCapabilities 8 }

mgmtAclCapabilitiesSecondLookupSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, second lookup can be configured."
    ::= { mgmtAclCapabilities 9 }

mgmtAclCapabilitiesMultipleIngressPortsSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, ingress port list can be configured with multiple ports. If
         false, ingress port list can be configured with only one single port."
    ::= { mgmtAclCapabilities 10 }

mgmtAclCapabilitiesEgressPortSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, egress port list can be configured."
    ::= { mgmtAclCapabilities 11 }

mgmtAclCapabilitiesVlanTaggedSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, VLAN tagged can be configured."
    ::= { mgmtAclCapabilities 12 }

mgmtAclCapabilitiesStackableAceSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, stackable ACE is supported. The 'switch' and 'switchport' ACE
         ingress type can be configured. Otherwize, only 'any' and 'specific'
         ACE ingress type can be configured."
    ::= { mgmtAclCapabilities 13 }

mgmtAclConfig OBJECT IDENTIFIER
    ::= { mgmtAclMibObjects 2 }

mgmtAclConfigInterface OBJECT IDENTIFIER
    ::= { mgmtAclConfig 2 }

mgmtAclConfigInterfaceConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclConfigInterfaceConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of interface configuration"
    ::= { mgmtAclConfigInterface 1 }

mgmtAclConfigInterfaceConfigEntry OBJECT-TYPE
    SYNTAX      MGMTAclConfigInterfaceConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters"
    INDEX       { mgmtAclConfigInterfaceConfigIfIndex }
    ::= { mgmtAclConfigInterfaceConfigTable 1 }

MGMTAclConfigInterfaceConfigEntry ::= SEQUENCE {
    mgmtAclConfigInterfaceConfigIfIndex           MGMTInterfaceIndex,
    mgmtAclConfigInterfaceConfigPolicyId          Unsigned32,
    mgmtAclConfigInterfaceConfigHitAction         MGMTAclHitAction,
    mgmtAclConfigInterfaceConfigRedirectPortList  MGMTPortList,
    mgmtAclConfigInterfaceConfigRateLimiterId     Unsigned32,
    mgmtAclConfigInterfaceConfigEvcPolicerId      MGMTUnsigned16,
    mgmtAclConfigInterfaceConfigMirror            TruthValue,
    mgmtAclConfigInterfaceConfigLogging           TruthValue,
    mgmtAclConfigInterfaceConfigShutdown          TruthValue
}

mgmtAclConfigInterfaceConfigIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The index of logical interface."
    ::= { mgmtAclConfigInterfaceConfigEntry 1 }

mgmtAclConfigInterfaceConfigPolicyId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy ID."
    ::= { mgmtAclConfigInterfaceConfigEntry 2 }

mgmtAclConfigInterfaceConfigHitAction OBJECT-TYPE
    SYNTAX      MGMTAclHitAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The hit action operation for this interface. egress(2) is not
         supported."
    ::= { mgmtAclConfigInterfaceConfigEntry 101 }

mgmtAclConfigInterfaceConfigRedirectPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The redirect port list for this interface."
    ::= { mgmtAclConfigInterfaceConfigEntry 102 }

mgmtAclConfigInterfaceConfigRateLimiterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The rate limiter ID. 0 means to be disabled."
    ::= { mgmtAclConfigInterfaceConfigEntry 103 }

mgmtAclConfigInterfaceConfigEvcPolicerId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The EVC policer ID. 0 means to be disabled. Obsolete.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesEvcPolicerSupported' is True."
    ::= { mgmtAclConfigInterfaceConfigEntry 104 }

mgmtAclConfigInterfaceConfigMirror OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mirror operation. Frames matching this interface rule are mirrored
         to the destination mirror port that is configured in the mirror
         modules.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesMirrorSupported' is True."
    ::= { mgmtAclConfigInterfaceConfigEntry 105 }

mgmtAclConfigInterfaceConfigLogging OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the logging operation of the interface. Notice that the logging
         message doesn't include the 4 bytes CRC information. The allowed values
         are: True - Frames matching the ACE are stored in the System Log. False
         - Frames matching the ACE are not logged. Note: The logging feature
         only works when the packet length is less than 1518(without VLAN tags)
         and the System Log memory size and logging rate is limited."
    ::= { mgmtAclConfigInterfaceConfigEntry 106 }

mgmtAclConfigInterfaceConfigShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the port shut down operation of the interface. The allowed
         values are: True - If a frame matches the interface rule, the ingress
         port will be disabled. False - Port shut down is disabled for the
         interface. Note: The shutdown feature only works when the packet length
         is less than 1518(without VLAN tags)."
    ::= { mgmtAclConfigInterfaceConfigEntry 107 }

mgmtAclConfigRateLimiterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclConfigRateLimiterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of rate limiter configuration"
    ::= { mgmtAclConfig 3 }

mgmtAclConfigRateLimiterEntry OBJECT-TYPE
    SYNTAX      MGMTAclConfigRateLimiterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each rate limiter has a set of parameters"
    INDEX       { mgmtAclConfigRateLimiterRateLimiterId }
    ::= { mgmtAclConfigRateLimiterTable 1 }

MGMTAclConfigRateLimiterEntry ::= SEQUENCE {
    mgmtAclConfigRateLimiterRateLimiterId  Integer32,
    mgmtAclConfigRateLimiterBitRateEnable  TruthValue,
    mgmtAclConfigRateLimiterBitRate        Unsigned32,
    mgmtAclConfigRateLimiterPacketRate     Unsigned32
}

mgmtAclConfigRateLimiterRateLimiterId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The rate limter ID."
    ::= { mgmtAclConfigRateLimiterEntry 1 }

mgmtAclConfigRateLimiterBitRateEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Use bit rate policing instead of packet rate. True means bit rate is
         used and false means packet rate is used.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesRateLimiterBitRateSupported' is True."
    ::= { mgmtAclConfigRateLimiterEntry 2 }

mgmtAclConfigRateLimiterBitRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Bit rate in kbps.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesRateLimiterBitRateSupported' is True."
    ::= { mgmtAclConfigRateLimiterEntry 3 }

mgmtAclConfigRateLimiterPacketRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Packet rate in pps."
    ::= { mgmtAclConfigRateLimiterEntry 4 }

mgmtAclConfigAce OBJECT IDENTIFIER
    ::= { mgmtAclConfig 4 }

mgmtAclConfigAceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclConfigAceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The ACL ACE configuration table."
    ::= { mgmtAclConfigAce 1 }

mgmtAclConfigAceEntry OBJECT-TYPE
    SYNTAX      MGMTAclConfigAceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains a set of parameters."
    INDEX       { mgmtAclConfigAceAceId }
    ::= { mgmtAclConfigAceTable 1 }

MGMTAclConfigAceEntry ::= SEQUENCE {
    mgmtAclConfigAceAceId                       Integer32,
    mgmtAclConfigAceNextAceId                   Unsigned32,
    mgmtAclConfigAceHitAction                   MGMTAclHitAction,
    mgmtAclConfigAceRedirectPortList            MGMTPortList,
    mgmtAclConfigAceRedirectPortListSwitchPort  Unsigned32,
    mgmtAclConfigAceEgressPortList              MGMTPortList,
    mgmtAclConfigAceRateLimiterId               Unsigned32,
    mgmtAclConfigAceEvcPolicerId                MGMTUnsigned16,
    mgmtAclConfigAceMirror                      TruthValue,
    mgmtAclConfigAceLogging                     TruthValue,
    mgmtAclConfigAceShutdown                    TruthValue,
    mgmtAclConfigAceIngressPortListMode         MGMTAclAceIngressPortListMode,
    mgmtAclConfigAceIngressPortList             MGMTPortList,
    mgmtAclConfigAceIngressPortListSwitch       Unsigned32,
    mgmtAclConfigAceIngressPortListSwitchPort   Unsigned32,
    mgmtAclConfigAcePolicyValue                 MGMTUnsigned8,
    mgmtAclConfigAcePolicyMask                  MGMTUnsigned8,
    mgmtAclConfigAceSecondLookup                TruthValue,
    mgmtAclConfigAceVlanId                      MGMTUnsigned16,
    mgmtAclConfigAceVlanTagPriority             MGMTVlanTagPriority,
    mgmtAclConfigAceVlanTagged                  MGMTAclAceVlanTagged,
    mgmtAclConfigAceFrameType                   MGMTAclAceFrameType,
    mgmtAclConfigAceDestMacOp                   MGMTAdvDestMacType,
    mgmtAclConfigAceEtherSrcMacOp               MGMTASType,
    mgmtAclConfigAceEtherSrcMac                 MacAddress,
    mgmtAclConfigAceEtherDestMac                MacAddress,
    mgmtAclConfigAceEtherType                   MGMTUnsigned16,
    mgmtAclConfigAceArpSrcMacOp                 MGMTASType,
    mgmtAclConfigAceArpSrcMac                   MacAddress,
    mgmtAclConfigAceArpSenderIp                 IpAddress,
    mgmtAclConfigAceArpSenderIpMask             IpAddress,
    mgmtAclConfigAceArpTargetIp                 IpAddress,
    mgmtAclConfigAceArpTargetIpMask             IpAddress,
    mgmtAclConfigAceArpOpcode                   MGMTAclAceArpOp,
    mgmtAclConfigAceArpFlagReq                  MGMTBitType,
    mgmtAclConfigAceArpFlagSha                  MGMTBitType,
    mgmtAclConfigAceArpFlagTha                  MGMTBitType,
    mgmtAclConfigAceArpFlagHln                  MGMTBitType,
    mgmtAclConfigAceArpFlagHrd                  MGMTBitType,
    mgmtAclConfigAceArpFlagPro                  MGMTBitType,
    mgmtAclConfigAceIpv4ProtocolOp              MGMTASType,
    mgmtAclConfigAceIpv4Protocol                MGMTUnsigned8,
    mgmtAclConfigAceIpv4SrcIp                   IpAddress,
    mgmtAclConfigAceIpv4SrcIpMask               IpAddress,
    mgmtAclConfigAceIpv4DestIp                  IpAddress,
    mgmtAclConfigAceIpv4DestIpMask              IpAddress,
    mgmtAclConfigAceIpv4IcmpTypeOp              MGMTASType,
    mgmtAclConfigAceIpv4IcmpType                MGMTUnsigned8,
    mgmtAclConfigAceIpv4IcmpCodeOp              MGMTASType,
    mgmtAclConfigAceIpv4IcmpCode                MGMTUnsigned8,
    mgmtAclConfigAceIpv4SrcPortOp               MGMTASRType,
    mgmtAclConfigAceIpv4SrcPort                 MGMTUnsigned16,
    mgmtAclConfigAceIpv4SrcPortRange            MGMTUnsigned16,
    mgmtAclConfigAceIpv4DestPortOp              MGMTASRType,
    mgmtAclConfigAceIpv4DestPort                MGMTUnsigned16,
    mgmtAclConfigAceIpv4DestPortRange           MGMTUnsigned16,
    mgmtAclConfigAceIpv4FlagTtl                 MGMTBitType,
    mgmtAclConfigAceIpv4FlagFragment            MGMTBitType,
    mgmtAclConfigAceIpv4FlagIpOption            MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagFin              MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagSyn              MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagRst              MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagPsh              MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagAck              MGMTBitType,
    mgmtAclConfigAceIpv4TcpFlagUrg              MGMTBitType,
    mgmtAclConfigAceIpv6NextHeaderOp            MGMTASType,
    mgmtAclConfigAceIpv6NextHeader              MGMTUnsigned8,
    mgmtAclConfigAceIpv6Icmpv6TypeOp            MGMTASType,
    mgmtAclConfigAceIpv6Icmpv6Type              MGMTUnsigned8,
    mgmtAclConfigAceIpv6Icmpv6CodeOp            MGMTASType,
    mgmtAclConfigAceIpv6Icmpv6Code              MGMTUnsigned8,
    mgmtAclConfigAceIpv6SrcIp                   InetAddressIPv6,
    mgmtAclConfigAceIpv6SrcIpMask               InetAddressIPv6,
    mgmtAclConfigAceIpv6SrcPortOp               MGMTASRType,
    mgmtAclConfigAceIpv6SrcPort                 MGMTUnsigned16,
    mgmtAclConfigAceIpv6SrcPortRange            MGMTUnsigned16,
    mgmtAclConfigAceIpv6DestPortOp              MGMTASRType,
    mgmtAclConfigAceIpv6DestPort                MGMTUnsigned16,
    mgmtAclConfigAceIpv6DestPortRange           MGMTUnsigned16,
    mgmtAclConfigAceIpv6FlagTtl                 MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagFin              MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagSyn              MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagRst              MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagPsh              MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagAck              MGMTBitType,
    mgmtAclConfigAceIpv6TcpFlagUrg              MGMTBitType,
    mgmtAclConfigAceAction                      MGMTRowEditorState
}

mgmtAclConfigAceAceId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ACE ID."
    ::= { mgmtAclConfigAceEntry 1 }

mgmtAclConfigAceNextAceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of ACE next to this ACE."
    ::= { mgmtAclConfigAceEntry 2 }

mgmtAclConfigAceHitAction OBJECT-TYPE
    SYNTAX      MGMTAclHitAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The hit action operation for this ACE."
    ::= { mgmtAclConfigAceEntry 101 }

mgmtAclConfigAceRedirectPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The redirect port list for this ACE."
    ::= { mgmtAclConfigAceEntry 102 }

mgmtAclConfigAceRedirectPortListSwitchPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The redirect switch port for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switchport. And it is used when this ACE
         is applied on specific switch port of all switches in stackable
         devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceEntry 103 }

mgmtAclConfigAceEgressPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The egress port list. The port list is to define what ports are allowed
         to be egress ports. If the egress port of an incoming frame is in the
         port list then the frame will be forwared to that port. Otherwise, if
         the egress port is not in the port list then the egress port is not
         allowed and the incoming frame will be dropped.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesEgressPortSupported' is True."
    ::= { mgmtAclConfigAceEntry 104 }

mgmtAclConfigAceRateLimiterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The rate limiter ID. 0 means to be disabled."
    ::= { mgmtAclConfigAceEntry 105 }

mgmtAclConfigAceEvcPolicerId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The EVC policer ID. 0 means to be disabled. Obsolete.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesEvcPolicerSupported' is True."
    ::= { mgmtAclConfigAceEntry 106 }

mgmtAclConfigAceMirror OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mirror operation. Frames matching this ACE are mirrored to the
         destination mirror port that is configured in the mirror module.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesMirrorSupported' is True."
    ::= { mgmtAclConfigAceEntry 107 }

mgmtAclConfigAceLogging OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the logging operation of the ACE. Notice that the logging
         message doesn't include the 4 bytes CRC information. The allowed values
         are: True - Frames matching the ACE are stored in the System Log. False
         - Frames matching the ACE are not logged. Note: The logging feature
         only works when the packet length is less than 1518(without VLAN tags)
         and the system log memory size and logging rate is limited."
    ::= { mgmtAclConfigAceEntry 108 }

mgmtAclConfigAceShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the port shut down operation of the ACE. The allowed values
         are: True: If a frame matches the ACE, the ingress port will be shuted
         down. False: Port shut down is disabled for the ACE. Note: The shutdown
         feature only works when the packet length is less than 1518(without
         VLAN tags)."
    ::= { mgmtAclConfigAceEntry 109 }

mgmtAclConfigAceIngressPortListMode OBJECT-TYPE
    SYNTAX      MGMTAclAceIngressPortListMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress port list mode for this ACE. The possible mode are: 'any':
         Indicates this ACE is applied on all switches and all ports.
         'specific': Indicates this ACE is applied on specific switch and
         specific ports. 'switch': Indicates this ACE is applied on all ports of
         specific switch in stackable devices. 'switchport': Indicates this ACE
         is applied on specific switch port of all switches in stackable
         devices."
    ::= { mgmtAclConfigAceEntry 201 }

mgmtAclConfigAceIngressPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress port list for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is specific. And it is used when this ACE is
         applied on specific switch and specific ports."
    ::= { mgmtAclConfigAceEntry 202 }

mgmtAclConfigAceIngressPortListSwitch OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress switch ID for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switch. And it is used when this ACE is
         applied on specific switch port of all switches in stackable devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceEntry 203 }

mgmtAclConfigAceIngressPortListSwitchPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress switch port for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switchport. And it is used when this ACE
         is applied on specific switch port of all switches in stackable
         devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceEntry 204 }

mgmtAclConfigAcePolicyValue OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy number for this ACE."
    ::= { mgmtAclConfigAceEntry 205 }

mgmtAclConfigAcePolicyMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy mask for this ACE. The allowed range is 0x0 to 0xff. If the
         binary bit value is '0', it means this bit is don't-care. The real
         matched pattern is [policy_value & policy_bitmask]. For example, if the
         policy value is 3 and the policy bitmask is 0x10(bit 0 is don't-care
         bit), then policy 2 and 3 are applied to this rule."
    ::= { mgmtAclConfigAceEntry 206 }

mgmtAclConfigAceSecondLookup OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The second lookup operation for this ACE.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesSecondLookupSupported' is True."
    ::= { mgmtAclConfigAceEntry 207 }

mgmtAclConfigAceVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID for this ACE. Possible values are: 0(disabled), 1-4095."
    ::= { mgmtAclConfigAceEntry 301 }

mgmtAclConfigAceVlanTagPriority OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN tag priority for this ACE."
    ::= { mgmtAclConfigAceEntry 302 }

mgmtAclConfigAceVlanTagged OBJECT-TYPE
    SYNTAX      MGMTAclAceVlanTagged
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The 802.1Q VLAN tagged for this ACE.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesVlanTaggedSupported' is True."
    ::= { mgmtAclConfigAceEntry 303 }

mgmtAclConfigAceFrameType OBJECT-TYPE
    SYNTAX      MGMTAclAceFrameType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The frame type for this ACE."
    ::= { mgmtAclConfigAceEntry 401 }

mgmtAclConfigAceDestMacOp OBJECT-TYPE
    SYNTAX      MGMTAdvDestMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination MAC address to be matched."
    ::= { mgmtAclConfigAceEntry 402 }

mgmtAclConfigAceEtherSrcMacOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a Ethernet type packet's source MAC address to be
         matched. This object is only available if FrameType is ether(1)."
    ::= { mgmtAclConfigAceEntry 501 }

mgmtAclConfigAceEtherSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address. This object is only available
         if FrameType is ether(1) and EtherSrcMacOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 502 }

mgmtAclConfigAceEtherDestMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address. This object is only
         available if FrameType is ether(1) and EtherDestMacOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 503 }

mgmtAclConfigAceEtherType OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit Ethernet type field. Possible values are:
         0(disabled), 0x600-0xFFFF but excluding 0x800(IPv4), 0x806(ARP) and
         0x86DD(IPv6). This object is only available if FrameType is ether(1)
         and EtherDestMacOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 504 }

mgmtAclConfigAceArpSrcMacOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's source MAC address to be matched. This
         object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 601 }

mgmtAclConfigAceArpSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address. This object is only available
         if FrameType is arp(4) and ArpSrcMacOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 602 }

mgmtAclConfigAceArpSenderIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP sender IP address. The packet's sender address is
         AND-ed with the value of ArpSenderIpMask and then compared with the
         value of this object. If ArpSenderIp and ArpSrcIpMask are 0.0.0.0, this
         entry matches any sender IP address. This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 603 }

mgmtAclConfigAceArpSenderIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP sender IP address mask. This object is only available
         if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 604 }

mgmtAclConfigAceArpTargetIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP target IP address. The packet's target address is
         AND-ed with the value of ArpTragetIpMask and then compared with the
         value of this object. If ArpTragetIp and ArpSrcIpMask are 0.0.0.0, this
         entry matches any target IP address. This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 605 }

mgmtAclConfigAceArpTargetIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP target IP address mask. This object is only available
         if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 606 }

mgmtAclConfigAceArpOpcode OBJECT-TYPE
    SYNTAX      MGMTAclAceArpOp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's opcode to be matched. This object is only
         available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 607 }

mgmtAclConfigAceArpFlagReq OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's request/reply opcode is to be compared.
         This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 608 }

mgmtAclConfigAceArpFlagSha OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's sender hardware address field (SHA) to be
         compared. This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 609 }

mgmtAclConfigAceArpFlagTha OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's target hardware address field (THA) is to
         be compared. This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 610 }

mgmtAclConfigAceArpFlagHln OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's s hardware address length field (HLN) is
         to be compared. The value 0 means any HLN value is allowed (don't-care
         field), value 1 means HLN is not equal to Ethernet (0x06) or the (PLN)
         is not equal to IPv4 (0x04) and value 2 means HLN is equal to Ethernet
         (0x06) or the (PLN) is not equal to IPv4 (0x04). This object is only
         available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 611 }

mgmtAclConfigAceArpFlagHrd OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's hardware address space field (HRD) is to
         be compared. The value 0 means any HRD value is allowed (don't-care
         field), value 1 means HRD is not equal to Ethernet (1) and value 2
         means HRD is equal to Ethernet (1). This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceEntry 612 }

mgmtAclConfigAceArpFlagPro OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's protocol address space field (PRO) is to
         be compared. The value 0 means any PRO value is allowed (don't-care
         field), value 1 means PRO is not equal to IP (0x800) and value 2 means
         PRO is equal to IP (0x800). This object is only available if FrameType
         is arp(4)."
    ::= { mgmtAclConfigAceEntry 613 }

mgmtAclConfigAceIpv4ProtocolOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's protocol field is to be compared. This
         object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 701 }

mgmtAclConfigAceIpv4Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv4 header used to indicate a higher
         layer protocol. Possible values are 0-255. This object is only
         available if FrameType is ipv4(5) and Ipv4ProtocolOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 702 }

mgmtAclConfigAceIpv4SrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv4 address. The packet's sender address is
         AND-ed with the value of Ipv4SrcIpMask and then compared with the value
         of this object. If Ipv4SrcIp and Ipv4SrcIpMask are 0.0.0.0, this entry
         matches any source IP address. This object is only available if
         FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 703 }

mgmtAclConfigAceIpv4SrcIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv4 address mask. This object is only available
         if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 704 }

mgmtAclConfigAceIpv4DestIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified destination IPv4 address. The packet's sender address is
         AND-ed with the value of Ipv4DestIpMask and then compared with the
         value of this object. If Ipv4DestIp and Ipv4DestIpMask are 0.0.0.0,
         this entry matches any destination IP address. This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 705 }

mgmtAclConfigAceIpv4DestIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified destination IPv4 address mask. This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 706 }

mgmtAclConfigAceIpv4IcmpTypeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's ICMP type field is to be compared. This
         object is only available if FrameType is ipv4(5) and Ipv4Protocol is
         icmp(1)."
    ::= { mgmtAclConfigAceEntry 707 }

mgmtAclConfigAceIpv4IcmpType OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP type field in the IPv4 header. Possible values are 0-255. This
         object is only available if FrameType is ipv4(5), Ipv4Protocol is
         icmp(1) and Ipv4IcmpTypeOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 708 }

mgmtAclConfigAceIpv4IcmpCodeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's ICMP code field is to be compared. This
         object is only available if FrameType is ipv4(5) and Ipv4Protocol is
         icmp(1)."
    ::= { mgmtAclConfigAceEntry 709 }

mgmtAclConfigAceIpv4IcmpCode OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP code field in the IPv4 header. Possible values are 0-255. This
         object is only available if FrameType is ipv4(5), Ipv4Protocol is
         icmp(1) and Ipv4IcmpCodeOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 710 }

mgmtAclConfigAceIpv4SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 711 }

mgmtAclConfigAceIpv4SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv4SrcPortOp
         object is range(2), this object will be the starting port number of the
         port range. Valid range is 0-65535. This object is only available if
         FrameType is ipv4(5) and Ipv4SrcPortOp is not any(0)."
    ::= { mgmtAclConfigAceEntry 712 }

mgmtAclConfigAceIpv4SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv4SrcPortOp
         object is range(2), this object will be the ending port number of the
         port range. Valid range is 0-65535. This object is only available if
         FrameType is ipv4(5) and Ipv4SrcPortOp is range(2)."
    ::= { mgmtAclConfigAceEntry 713 }

mgmtAclConfigAceIpv4DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared. This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 714 }

mgmtAclConfigAceIpv4DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv4DestPortOp object in the same row is range(2), this object will be
         the starting port number of the port range. Valid range is 0-65535.
         This object is only available if FrameType is ipv4(5) and
         Ipv4DestPortOp is not any(0)."
    ::= { mgmtAclConfigAceEntry 715 }

mgmtAclConfigAceIpv4DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv4DestPortOp object in the same row is range(2), this object will be
         the ending port number of the port range. Valid range is 0-65535. This
         object is only available if FrameType is ipv4(5) and Ipv4DestPortOp is
         range(2)."
    ::= { mgmtAclConfigAceEntry 716 }

mgmtAclConfigAceIpv4FlagTtl OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's Time-to-Live field (TTL) is to be
         compared. The value 0 means any TTL value is allowed (don't-care
         field), value 1 means TTL is not equal zero and value 2 means TTL is
         equal to zero. This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 717 }

mgmtAclConfigAceIpv4FlagFragment OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's More-Fragments field (MF) is to be
         compared. The value 0 means any MF value is allowed (don't-care field),
         value 1 means MF is not equal one (MF field isn't set) and value 2
         means MF is equal to one (MF field is set). This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 718 }

mgmtAclConfigAceIpv4FlagIpOption OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's options field is to be compared. This
         object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceEntry 719 }

mgmtAclConfigAceIpv4TcpFlagFin OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP FIN flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 720 }

mgmtAclConfigAceIpv4TcpFlagSyn OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP SYN flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 721 }

mgmtAclConfigAceIpv4TcpFlagRst OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP RST flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 722 }

mgmtAclConfigAceIpv4TcpFlagPsh OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP PSH flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 723 }

mgmtAclConfigAceIpv4TcpFlagAck OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP ACK flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 724 }

mgmtAclConfigAceIpv4TcpFlagUrg OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP URG flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceEntry 725 }

mgmtAclConfigAceIpv6NextHeaderOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the IPv6 next header filter for this ACE. This object is only
         available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 801 }

mgmtAclConfigAceIpv6NextHeader OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When 'Specific' is selected for the IPv6 next header value, you can
         enter a specific value. The allowed range is 0 to 255. A frame that
         hits this ACE matches this IPv6 protocol value. This object is only
         available if FrameType is ipv6(6) and Ipv6NextHeaderOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 802 }

mgmtAclConfigAceIpv6Icmpv6TypeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's ICMPv6 type field is to be compared. This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         icmpv6(58)."
    ::= { mgmtAclConfigAceEntry 803 }

mgmtAclConfigAceIpv6Icmpv6Type OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMPv6 type field in the IPv6 header. Possible values are 0-255.
         This object is only available if FrameType is ipv6(6), Ipv6NextHeader
         is icmpv6(58) and Ipv6Icmpv6TypeOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 804 }

mgmtAclConfigAceIpv6Icmpv6CodeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's ICMPv6 code field is to be compared. This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         icmpv6(58)."
    ::= { mgmtAclConfigAceEntry 805 }

mgmtAclConfigAceIpv6Icmpv6Code OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP code field in the IPv6 header. Possible values are 0-255. This
         object is only available if FrameType is ipv6(6), Ipv6NextHeader is
         icmpv6(58) and Ipv6Icmpv6CodeOp is specific(1)."
    ::= { mgmtAclConfigAceEntry 806 }

mgmtAclConfigAceIpv6SrcIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv6 address. The packet's sender address is
         AND-ed with the value of Ipv6SrcIpMask and then compared with the value
         of this object. If Ipv6SrcIp and Ipv4SrcIpMask are 0::, this entry
         matches any source IP address. This object is only available if
         FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 807 }

mgmtAclConfigAceIpv6SrcIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv6 address mask. This object is only available
         if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 808 }

mgmtAclConfigAceIpv6SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 809 }

mgmtAclConfigAceIpv6SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv6SrcPortOp
         object in the same row is range(2), this object will be the starting
         port number of the port range. Valid range is 0-65535. This object is
         only available if FrameType is ipv6(6) and Ipv6SrcPortOp is not any(0)."
    ::= { mgmtAclConfigAceEntry 810 }

mgmtAclConfigAceIpv6SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv6SrcPortOp
         object in the same row is range(2), this object will be the ending port
         number of the port range. Valid range is 0-65535. This object is only
         available if FrameType is ipv6(6) and Ipv6SrcPortOp is range(2)."
    ::= { mgmtAclConfigAceEntry 811 }

mgmtAclConfigAceIpv6DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared. This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 812 }

mgmtAclConfigAceIpv6DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv6DestPortOp object in the same row is range(2), this object will be
         the starting port number of the port range. Valid range is 0-65535.
         This object is only available if FrameType is ipv6(6) and
         Ipv6DestPortOp is not any(0)."
    ::= { mgmtAclConfigAceEntry 813 }

mgmtAclConfigAceIpv6DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv6DestPortOp object in the same row is range(2), this object will be
         the ending port number of the port range. Valid range is 0-65535. This
         object is only available if FrameType is ipv6(6) and Ipv6DestPortOp is
         range(2)."
    ::= { mgmtAclConfigAceEntry 814 }

mgmtAclConfigAceIpv6FlagTtl OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's Time-to-Live field (TTL) is to be
         compared. The value 0 means any TTL value is allowed (don't-care
         field), value 1 means TTL is not equal zero and value 2 means TTL is
         equal to zero. This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceEntry 815 }

mgmtAclConfigAceIpv6TcpFlagFin OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'No more data from sender' field
         (FIN) is to be compared. The value 0 means any FIN value is allowed
         (don't-care field), value 1 means FIN is not equal one (FIN field isn't
         set) and value 2 means FIN is equal to one (FIN field is set). This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         tcp(6)."
    ::= { mgmtAclConfigAceEntry 816 }

mgmtAclConfigAceIpv6TcpFlagSyn OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Synchronize sequence numbers' field
         (SYN) is to be compared. The value 0 means any SYN value is allowed
         (don't-care field), value 1 means SYN is not equal one (FIN field isn't
         set) and value 2 means SYN is equal to one (FIN field is set). This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         tcp(6)."
    ::= { mgmtAclConfigAceEntry 817 }

mgmtAclConfigAceIpv6TcpFlagRst OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Reset the connection' field (RST) is
         to be compared. The value 0 means any RST value is allowed (don't-care
         field), value 1 means RST is not equal one (FIN field isn't set) and
         value 2 means RST is equal to one (FIN field is set). This object is
         only available if FrameType is ipv6(6) and Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceEntry 818 }

mgmtAclConfigAceIpv6TcpFlagPsh OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Push Function' field (PSH) is to be
         compared. The value 0 means any PSH value is allowed (don't-care
         field), value 1 means PSH is not equal one (FIN field isn't set) and
         value 2 means PSH is equal to one (FIN field is set). This object is
         only available if FrameType is ipv6(6) and Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceEntry 819 }

mgmtAclConfigAceIpv6TcpFlagAck OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Acknowledgment field significant'
         field (ACK) is to be compared. The value 0 means any FIN value is
         allowed (don't-care field), value 1 means ACK is not equal one (FIN
         field isn't set) and value 2 means ACK is equal to one (FIN field is
         set). This object is only available if FrameType is ipv6(6) and
         Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceEntry 820 }

mgmtAclConfigAceIpv6TcpFlagUrg OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Urgent Pointer field significant'
         field (URG) is to be compared. The value 0 means any URG value is
         allowed (don't-care field), value 1 means URG is not equal one (FIN
         field isn't set) and value 2 means URG is equal to one (FIN field is
         set). This object is only available if FrameType is ipv6(6) and
         Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceEntry 821 }

mgmtAclConfigAceAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAclConfigAceEntry 10000 }

mgmtAclConfigAceRowEditor OBJECT IDENTIFIER
    ::= { mgmtAclConfigAce 2 }

mgmtAclConfigAceRowEditorAceId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ACE ID."
    ::= { mgmtAclConfigAceRowEditor 1 }

mgmtAclConfigAceRowEditorNextAceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of ACE next to this ACE."
    ::= { mgmtAclConfigAceRowEditor 2 }

mgmtAclConfigAceRowEditorHitAction OBJECT-TYPE
    SYNTAX      MGMTAclHitAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The hit action operation for this ACE."
    ::= { mgmtAclConfigAceRowEditor 101 }

mgmtAclConfigAceRowEditorRedirectPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The redirect port list for this ACE."
    ::= { mgmtAclConfigAceRowEditor 102 }

mgmtAclConfigAceRowEditorRedirectPortListSwitchPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The redirect switch port for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switchport. And it is used when this ACE
         is applied on specific switch port of all switches in stackable
         devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 103 }

mgmtAclConfigAceRowEditorEgressPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The egress port list. The port list is to define what ports are allowed
         to be egress ports. If the egress port of an incoming frame is in the
         port list then the frame will be forwared to that port. Otherwise, if
         the egress port is not in the port list then the egress port is not
         allowed and the incoming frame will be dropped.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesEgressPortSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 104 }

mgmtAclConfigAceRowEditorRateLimiterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The rate limiter ID. 0 means to be disabled."
    ::= { mgmtAclConfigAceRowEditor 105 }

mgmtAclConfigAceRowEditorEvcPolicerId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The EVC policer ID. 0 means to be disabled. Obsolete.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesEvcPolicerSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 106 }

mgmtAclConfigAceRowEditorMirror OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The mirror operation. Frames matching this ACE are mirrored to the
         destination mirror port that is configured in the mirror module.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesMirrorSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 107 }

mgmtAclConfigAceRowEditorLogging OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the logging operation of the ACE. Notice that the logging
         message doesn't include the 4 bytes CRC information. The allowed values
         are: True - Frames matching the ACE are stored in the System Log. False
         - Frames matching the ACE are not logged. Note: The logging feature
         only works when the packet length is less than 1518(without VLAN tags)
         and the system log memory size and logging rate is limited."
    ::= { mgmtAclConfigAceRowEditor 108 }

mgmtAclConfigAceRowEditorShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the port shut down operation of the ACE. The allowed values
         are: True: If a frame matches the ACE, the ingress port will be shuted
         down. False: Port shut down is disabled for the ACE. Note: The shutdown
         feature only works when the packet length is less than 1518(without
         VLAN tags)."
    ::= { mgmtAclConfigAceRowEditor 109 }

mgmtAclConfigAceRowEditorIngressPortListMode OBJECT-TYPE
    SYNTAX      MGMTAclAceIngressPortListMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress port list mode for this ACE. The possible mode are: 'any':
         Indicates this ACE is applied on all switches and all ports.
         'specific': Indicates this ACE is applied on specific switch and
         specific ports. 'switch': Indicates this ACE is applied on all ports of
         specific switch in stackable devices. 'switchport': Indicates this ACE
         is applied on specific switch port of all switches in stackable
         devices."
    ::= { mgmtAclConfigAceRowEditor 201 }

mgmtAclConfigAceRowEditorIngressPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress port list for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is specific. And it is used when this ACE is
         applied on specific switch and specific ports."
    ::= { mgmtAclConfigAceRowEditor 202 }

mgmtAclConfigAceRowEditorIngressPortListSwitch OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress switch ID for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switch. And it is used when this ACE is
         applied on specific switch port of all switches in stackable devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 203 }

mgmtAclConfigAceRowEditorIngressPortListSwitchPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ingress switch port for this ACE. The object is only abaliable if
         AclAceIngressPortListMode is switchport. And it is used when this ACE
         is applied on specific switch port of all switches in stackable
         devices.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesStackableAceSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 204 }

mgmtAclConfigAceRowEditorPolicyValue OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy number for this ACE."
    ::= { mgmtAclConfigAceRowEditor 205 }

mgmtAclConfigAceRowEditorPolicyMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy mask for this ACE. The allowed range is 0x0 to 0xff. If the
         binary bit value is '0', it means this bit is don't-care. The real
         matched pattern is [policy_value & policy_bitmask]. For example, if the
         policy value is 3 and the policy bitmask is 0x10(bit 0 is don't-care
         bit), then policy 2 and 3 are applied to this rule."
    ::= { mgmtAclConfigAceRowEditor 206 }

mgmtAclConfigAceRowEditorSecondLookup OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The second lookup operation for this ACE.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesSecondLookupSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 207 }

mgmtAclConfigAceRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID for this ACE. Possible values are: 0(disabled), 1-4095."
    ::= { mgmtAclConfigAceRowEditor 301 }

mgmtAclConfigAceRowEditorVlanTagPriority OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN tag priority for this ACE."
    ::= { mgmtAclConfigAceRowEditor 302 }

mgmtAclConfigAceRowEditorVlanTagged OBJECT-TYPE
    SYNTAX      MGMTAclAceVlanTagged
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The 802.1Q VLAN tagged for this ACE.
         
         This object is only available if the capability object
         'mgmtAclCapabilitiesVlanTaggedSupported' is True."
    ::= { mgmtAclConfigAceRowEditor 303 }

mgmtAclConfigAceRowEditorFrameType OBJECT-TYPE
    SYNTAX      MGMTAclAceFrameType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The frame type for this ACE."
    ::= { mgmtAclConfigAceRowEditor 401 }

mgmtAclConfigAceRowEditorDestMacOp OBJECT-TYPE
    SYNTAX      MGMTAdvDestMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination MAC address to be matched."
    ::= { mgmtAclConfigAceRowEditor 402 }

mgmtAclConfigAceRowEditorEtherSrcMacOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a Ethernet type packet's source MAC address to be
         matched. This object is only available if FrameType is ether(1)."
    ::= { mgmtAclConfigAceRowEditor 501 }

mgmtAclConfigAceRowEditorEtherSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address. This object is only available
         if FrameType is ether(1) and EtherSrcMacOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 502 }

mgmtAclConfigAceRowEditorEtherDestMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address. This object is only
         available if FrameType is ether(1) and EtherDestMacOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 503 }

mgmtAclConfigAceRowEditorEtherType OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit Ethernet type field. Possible values are:
         0(disabled), 0x600-0xFFFF but excluding 0x800(IPv4), 0x806(ARP) and
         0x86DD(IPv6). This object is only available if FrameType is ether(1)
         and EtherDestMacOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 504 }

mgmtAclConfigAceRowEditorArpSrcMacOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's source MAC address to be matched. This
         object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 601 }

mgmtAclConfigAceRowEditorArpSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address. This object is only available
         if FrameType is arp(4) and ArpSrcMacOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 602 }

mgmtAclConfigAceRowEditorArpSenderIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP sender IP address. The packet's sender address is
         AND-ed with the value of ArpSenderIpMask and then compared with the
         value of this object. If ArpSenderIp and ArpSrcIpMask are 0.0.0.0, this
         entry matches any sender IP address. This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 603 }

mgmtAclConfigAceRowEditorArpSenderIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP sender IP address mask. This object is only available
         if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 604 }

mgmtAclConfigAceRowEditorArpTargetIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP target IP address. The packet's target address is
         AND-ed with the value of ArpTragetIpMask and then compared with the
         value of this object. If ArpTragetIp and ArpSrcIpMask are 0.0.0.0, this
         entry matches any target IP address. This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 605 }

mgmtAclConfigAceRowEditorArpTargetIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified ARP target IP address mask. This object is only available
         if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 606 }

mgmtAclConfigAceRowEditorArpOpcode OBJECT-TYPE
    SYNTAX      MGMTAclAceArpOp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's opcode to be matched. This object is only
         available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 607 }

mgmtAclConfigAceRowEditorArpFlagReq OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's request/reply opcode is to be compared.
         This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 608 }

mgmtAclConfigAceRowEditorArpFlagSha OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's sender hardware address field (SHA) to be
         compared. This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 609 }

mgmtAclConfigAceRowEditorArpFlagTha OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's target hardware address field (THA) is to
         be compared. This object is only available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 610 }

mgmtAclConfigAceRowEditorArpFlagHln OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's s hardware address length field (HLN) is
         to be compared. The value 0 means any HLN value is allowed (don't-care
         field), value 1 means HLN is not equal to Ethernet (0x06) or the (PLN)
         is not equal to IPv4 (0x04) and value 2 means HLN is equal to Ethernet
         (0x06) or the (PLN) is not equal to IPv4 (0x04). This object is only
         available if FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 611 }

mgmtAclConfigAceRowEditorArpFlagHrd OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's hardware address space field (HRD) is to
         be compared. The value 0 means any HRD value is allowed (don't-care
         field), value 1 means HRD is not equal to Ethernet (1) and value 2
         means HRD is equal to Ethernet (1). This object is only available if
         FrameType is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 612 }

mgmtAclConfigAceRowEditorArpFlagPro OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a ARP packet's protocol address space field (PRO) is to
         be compared. The value 0 means any PRO value is allowed (don't-care
         field), value 1 means PRO is not equal to IP (0x800) and value 2 means
         PRO is equal to IP (0x800). This object is only available if FrameType
         is arp(4)."
    ::= { mgmtAclConfigAceRowEditor 613 }

mgmtAclConfigAceRowEditorIpv4ProtocolOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's protocol field is to be compared. This
         object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 701 }

mgmtAclConfigAceRowEditorIpv4Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv4 header used to indicate a higher
         layer protocol. Possible values are 0-255. This object is only
         available if FrameType is ipv4(5) and Ipv4ProtocolOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 702 }

mgmtAclConfigAceRowEditorIpv4SrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv4 address. The packet's sender address is
         AND-ed with the value of Ipv4SrcIpMask and then compared with the value
         of this object. If Ipv4SrcIp and Ipv4SrcIpMask are 0.0.0.0, this entry
         matches any source IP address. This object is only available if
         FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 703 }

mgmtAclConfigAceRowEditorIpv4SrcIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv4 address mask. This object is only available
         if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 704 }

mgmtAclConfigAceRowEditorIpv4DestIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified destination IPv4 address. The packet's sender address is
         AND-ed with the value of Ipv4DestIpMask and then compared with the
         value of this object. If Ipv4DestIp and Ipv4DestIpMask are 0.0.0.0,
         this entry matches any destination IP address. This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 705 }

mgmtAclConfigAceRowEditorIpv4DestIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified destination IPv4 address mask. This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 706 }

mgmtAclConfigAceRowEditorIpv4IcmpTypeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's ICMP type field is to be compared. This
         object is only available if FrameType is ipv4(5) and Ipv4Protocol is
         icmp(1)."
    ::= { mgmtAclConfigAceRowEditor 707 }

mgmtAclConfigAceRowEditorIpv4IcmpType OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP type field in the IPv4 header. Possible values are 0-255. This
         object is only available if FrameType is ipv4(5), Ipv4Protocol is
         icmp(1) and Ipv4IcmpTypeOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 708 }

mgmtAclConfigAceRowEditorIpv4IcmpCodeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's ICMP code field is to be compared. This
         object is only available if FrameType is ipv4(5) and Ipv4Protocol is
         icmp(1)."
    ::= { mgmtAclConfigAceRowEditor 709 }

mgmtAclConfigAceRowEditorIpv4IcmpCode OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP code field in the IPv4 header. Possible values are 0-255. This
         object is only available if FrameType is ipv4(5), Ipv4Protocol is
         icmp(1) and Ipv4IcmpCodeOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 710 }

mgmtAclConfigAceRowEditorIpv4SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 711 }

mgmtAclConfigAceRowEditorIpv4SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv4SrcPortOp
         object is range(2), this object will be the starting port number of the
         port range. Valid range is 0-65535. This object is only available if
         FrameType is ipv4(5) and Ipv4SrcPortOp is not any(0)."
    ::= { mgmtAclConfigAceRowEditor 712 }

mgmtAclConfigAceRowEditorIpv4SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv4SrcPortOp
         object is range(2), this object will be the ending port number of the
         port range. Valid range is 0-65535. This object is only available if
         FrameType is ipv4(5) and Ipv4SrcPortOp is range(2)."
    ::= { mgmtAclConfigAceRowEditor 713 }

mgmtAclConfigAceRowEditorIpv4DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared. This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 714 }

mgmtAclConfigAceRowEditorIpv4DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv4DestPortOp object in the same row is range(2), this object will be
         the starting port number of the port range. Valid range is 0-65535.
         This object is only available if FrameType is ipv4(5) and
         Ipv4DestPortOp is not any(0)."
    ::= { mgmtAclConfigAceRowEditor 715 }

mgmtAclConfigAceRowEditorIpv4DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv4DestPortOp object in the same row is range(2), this object will be
         the ending port number of the port range. Valid range is 0-65535. This
         object is only available if FrameType is ipv4(5) and Ipv4DestPortOp is
         range(2)."
    ::= { mgmtAclConfigAceRowEditor 716 }

mgmtAclConfigAceRowEditorIpv4FlagTtl OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's Time-to-Live field (TTL) is to be
         compared. The value 0 means any TTL value is allowed (don't-care
         field), value 1 means TTL is not equal zero and value 2 means TTL is
         equal to zero. This object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 717 }

mgmtAclConfigAceRowEditorIpv4FlagFragment OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's More-Fragments field (MF) is to be
         compared. The value 0 means any MF value is allowed (don't-care field),
         value 1 means MF is not equal one (MF field isn't set) and value 2
         means MF is equal to one (MF field is set). This object is only
         available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 718 }

mgmtAclConfigAceRowEditorIpv4FlagIpOption OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv4 packet's options field is to be compared. This
         object is only available if FrameType is ipv4(5)."
    ::= { mgmtAclConfigAceRowEditor 719 }

mgmtAclConfigAceRowEditorIpv4TcpFlagFin OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP FIN flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 720 }

mgmtAclConfigAceRowEditorIpv4TcpFlagSyn OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP SYN flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 721 }

mgmtAclConfigAceRowEditorIpv4TcpFlagRst OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP RST flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 722 }

mgmtAclConfigAceRowEditorIpv4TcpFlagPsh OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP PSH flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 723 }

mgmtAclConfigAceRowEditorIpv4TcpFlagAck OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP ACK flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 724 }

mgmtAclConfigAceRowEditorIpv4TcpFlagUrg OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how the IPv4 TCP URG flag is matched. This object is only
         available if FrameType is ipv4(5) and Ipv4Protocol is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 725 }

mgmtAclConfigAceRowEditorIpv6NextHeaderOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the IPv6 next header filter for this ACE. This object is only
         available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 801 }

mgmtAclConfigAceRowEditorIpv6NextHeader OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When 'Specific' is selected for the IPv6 next header value, you can
         enter a specific value. The allowed range is 0 to 255. A frame that
         hits this ACE matches this IPv6 protocol value. This object is only
         available if FrameType is ipv6(6) and Ipv6NextHeaderOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 802 }

mgmtAclConfigAceRowEditorIpv6Icmpv6TypeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's ICMPv6 type field is to be compared. This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         icmpv6(58)."
    ::= { mgmtAclConfigAceRowEditor 803 }

mgmtAclConfigAceRowEditorIpv6Icmpv6Type OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMPv6 type field in the IPv6 header. Possible values are 0-255.
         This object is only available if FrameType is ipv6(6), Ipv6NextHeader
         is icmpv6(58) and Ipv6Icmpv6TypeOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 804 }

mgmtAclConfigAceRowEditorIpv6Icmpv6CodeOp OBJECT-TYPE
    SYNTAX      MGMTASType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's ICMPv6 code field is to be compared. This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         icmpv6(58)."
    ::= { mgmtAclConfigAceRowEditor 805 }

mgmtAclConfigAceRowEditorIpv6Icmpv6Code OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ICMP code field in the IPv6 header. Possible values are 0-255. This
         object is only available if FrameType is ipv6(6), Ipv6NextHeader is
         icmpv6(58) and Ipv6Icmpv6CodeOp is specific(1)."
    ::= { mgmtAclConfigAceRowEditor 806 }

mgmtAclConfigAceRowEditorIpv6SrcIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv6 address. The packet's sender address is
         AND-ed with the value of Ipv6SrcIpMask and then compared with the value
         of this object. If Ipv6SrcIp and Ipv4SrcIpMask are 0::, this entry
         matches any source IP address. This object is only available if
         FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 807 }

mgmtAclConfigAceRowEditorIpv6SrcIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified source IPv6 address mask. This object is only available
         if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 808 }

mgmtAclConfigAceRowEditorIpv6SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 809 }

mgmtAclConfigAceRowEditorIpv6SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv6SrcPortOp
         object in the same row is range(2), this object will be the starting
         port number of the port range. Valid range is 0-65535. This object is
         only available if FrameType is ipv6(6) and Ipv6SrcPortOp is not any(0)."
    ::= { mgmtAclConfigAceRowEditor 810 }

mgmtAclConfigAceRowEditorIpv6SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol. If the Ipv6SrcPortOp
         object in the same row is range(2), this object will be the ending port
         number of the port range. Valid range is 0-65535. This object is only
         available if FrameType is ipv6(6) and Ipv6SrcPortOp is range(2)."
    ::= { mgmtAclConfigAceRowEditor 811 }

mgmtAclConfigAceRowEditorIpv6DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared. This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 812 }

mgmtAclConfigAceRowEditorIpv6DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv6DestPortOp object in the same row is range(2), this object will be
         the starting port number of the port range. Valid range is 0-65535.
         This object is only available if FrameType is ipv6(6) and
         Ipv6DestPortOp is not any(0)."
    ::= { mgmtAclConfigAceRowEditor 813 }

mgmtAclConfigAceRowEditorIpv6DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol. If the
         Ipv6DestPortOp object in the same row is range(2), this object will be
         the ending port number of the port range. Valid range is 0-65535. This
         object is only available if FrameType is ipv6(6) and Ipv6DestPortOp is
         range(2)."
    ::= { mgmtAclConfigAceRowEditor 814 }

mgmtAclConfigAceRowEditorIpv6FlagTtl OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 packet's Time-to-Live field (TTL) is to be
         compared. The value 0 means any TTL value is allowed (don't-care
         field), value 1 means TTL is not equal zero and value 2 means TTL is
         equal to zero. This object is only available if FrameType is ipv6(6)."
    ::= { mgmtAclConfigAceRowEditor 815 }

mgmtAclConfigAceRowEditorIpv6TcpFlagFin OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'No more data from sender' field
         (FIN) is to be compared. The value 0 means any FIN value is allowed
         (don't-care field), value 1 means FIN is not equal one (FIN field isn't
         set) and value 2 means FIN is equal to one (FIN field is set). This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 816 }

mgmtAclConfigAceRowEditorIpv6TcpFlagSyn OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Synchronize sequence numbers' field
         (SYN) is to be compared. The value 0 means any SYN value is allowed
         (don't-care field), value 1 means SYN is not equal one (FIN field isn't
         set) and value 2 means SYN is equal to one (FIN field is set). This
         object is only available if FrameType is ipv6(6) and Ipv6NextHeader is
         tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 817 }

mgmtAclConfigAceRowEditorIpv6TcpFlagRst OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Reset the connection' field (RST) is
         to be compared. The value 0 means any RST value is allowed (don't-care
         field), value 1 means RST is not equal one (FIN field isn't set) and
         value 2 means RST is equal to one (FIN field is set). This object is
         only available if FrameType is ipv6(6) and Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 818 }

mgmtAclConfigAceRowEditorIpv6TcpFlagPsh OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Push Function' field (PSH) is to be
         compared. The value 0 means any PSH value is allowed (don't-care
         field), value 1 means PSH is not equal one (FIN field isn't set) and
         value 2 means PSH is equal to one (FIN field is set). This object is
         only available if FrameType is ipv6(6) and Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 819 }

mgmtAclConfigAceRowEditorIpv6TcpFlagAck OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Acknowledgment field significant'
         field (ACK) is to be compared. The value 0 means any FIN value is
         allowed (don't-care field), value 1 means ACK is not equal one (FIN
         field isn't set) and value 2 means ACK is equal to one (FIN field is
         set). This object is only available if FrameType is ipv6(6) and
         Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 820 }

mgmtAclConfigAceRowEditorIpv6TcpFlagUrg OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a IPv6 TCP packet's 'Urgent Pointer field significant'
         field (URG) is to be compared. The value 0 means any URG value is
         allowed (don't-care field), value 1 means URG is not equal one (FIN
         field isn't set) and value 2 means URG is equal to one (FIN field is
         set). This object is only available if FrameType is ipv6(6) and
         Ipv6NextHeader is tcp(6)."
    ::= { mgmtAclConfigAceRowEditor 821 }

mgmtAclConfigAceRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAclConfigAceRowEditor 10000 }

mgmtAclConfigAcePrecedenceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclConfigAcePrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "It displays the sequence of ACEs to be hit."
    ::= { mgmtAclConfigAce 3 }

mgmtAclConfigAcePrecedenceEntry OBJECT-TYPE
    SYNTAX      MGMTAclConfigAcePrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding ACE ID."
    INDEX       { mgmtAclConfigAcePrecedencePrecedence }
    ::= { mgmtAclConfigAcePrecedenceTable 1 }

MGMTAclConfigAcePrecedenceEntry ::= SEQUENCE {
    mgmtAclConfigAcePrecedencePrecedence  Integer32,
    mgmtAclConfigAcePrecedenceAceId       Unsigned32
}

mgmtAclConfigAcePrecedencePrecedence OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ACL ACE hit precedence."
    ::= { mgmtAclConfigAcePrecedenceEntry 1 }

mgmtAclConfigAcePrecedenceAceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identifier of ACE"
    ::= { mgmtAclConfigAcePrecedenceEntry 2 }

mgmtAclStatus OBJECT IDENTIFIER
    ::= { mgmtAclMibObjects 3 }

mgmtAclStatusInterface OBJECT IDENTIFIER
    ::= { mgmtAclStatus 2 }

mgmtAclStatusInterfaceHitCountTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclStatusInterfaceHitCountEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of interface hit count status."
    ::= { mgmtAclStatusInterface 1 }

mgmtAclStatusInterfaceHitCountEntry OBJECT-TYPE
    SYNTAX      MGMTAclStatusInterfaceHitCountEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the hit count status per interface."
    INDEX       { mgmtAclStatusInterfaceHitCountIfIndex }
    ::= { mgmtAclStatusInterfaceHitCountTable 1 }

MGMTAclStatusInterfaceHitCountEntry ::= SEQUENCE {
    mgmtAclStatusInterfaceHitCountIfIndex  MGMTInterfaceIndex,
    mgmtAclStatusInterfaceHitCountCounter  Unsigned32
}

mgmtAclStatusInterfaceHitCountIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The index of logical interface."
    ::= { mgmtAclStatusInterfaceHitCountEntry 1 }

mgmtAclStatusInterfaceHitCountCounter OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The hit count of this interface."
    ::= { mgmtAclStatusInterfaceHitCountEntry 2 }

mgmtAclStatusAce OBJECT IDENTIFIER
    ::= { mgmtAclStatus 3 }

mgmtAclStatusAceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclStatusAceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of ACE status."
    ::= { mgmtAclStatusAce 1 }

mgmtAclStatusAceEntry OBJECT-TYPE
    SYNTAX      MGMTAclStatusAceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the status per switch per precedence."
    INDEX       { mgmtAclStatusAceSwitchId,
                  mgmtAclStatusAcePrecedence }
    ::= { mgmtAclStatusAceTable 1 }

MGMTAclStatusAceEntry ::= SEQUENCE {
    mgmtAclStatusAceSwitchId    Integer32,
    mgmtAclStatusAcePrecedence  Integer32,
    mgmtAclStatusAceAclUser     MGMTDisplayString,
    mgmtAclStatusAceAceId       Unsigned32,
    mgmtAclStatusAceConflict    TruthValue
}

mgmtAclStatusAceSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The switch ID."
    ::= { mgmtAclStatusAceEntry 1 }

mgmtAclStatusAcePrecedence OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ACL ACE hit precedence."
    ::= { mgmtAclStatusAceEntry 2 }

mgmtAclStatusAceAclUser OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User of ACE"
    ::= { mgmtAclStatusAceEntry 3 }

mgmtAclStatusAceAceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identifier of ACE"
    ::= { mgmtAclStatusAceEntry 4 }

mgmtAclStatusAceConflict OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The conflict status of this ACE. When the status value is true, it
         means the specific ACE is not applied to the hardware due to hardware
         limitations."
    ::= { mgmtAclStatusAceEntry 5 }

mgmtAclStatusAceHitCountTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclStatusAceHitCountEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of ACE hit count status."
    ::= { mgmtAclStatusAce 2 }

mgmtAclStatusAceHitCountEntry OBJECT-TYPE
    SYNTAX      MGMTAclStatusAceHitCountEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the hit count status per ACE."
    INDEX       { mgmtAclStatusAceHitCountAceId }
    ::= { mgmtAclStatusAceHitCountTable 1 }

MGMTAclStatusAceHitCountEntry ::= SEQUENCE {
    mgmtAclStatusAceHitCountAceId    Integer32,
    mgmtAclStatusAceHitCountCounter  Unsigned32
}

mgmtAclStatusAceHitCountAceId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ACE ID."
    ::= { mgmtAclStatusAceHitCountEntry 1 }

mgmtAclStatusAceHitCountCounter OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The hit count of this ACE."
    ::= { mgmtAclStatusAceHitCountEntry 2 }

mgmtAclControl OBJECT IDENTIFIER
    ::= { mgmtAclMibObjects 4 }

mgmtAclControlGlobals OBJECT IDENTIFIER
    ::= { mgmtAclControl 1 }

mgmtAclControlGlobalsClearAllHitCount OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set true to clear hit counts of all ACEs and interfaces."
    ::= { mgmtAclControlGlobals 1 }

mgmtAclControlInterface OBJECT IDENTIFIER
    ::= { mgmtAclControl 2 }

mgmtAclControlInterfaceControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAclControlInterfaceControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of interface control"
    ::= { mgmtAclControlInterface 1 }

mgmtAclControlInterfaceControlEntry OBJECT-TYPE
    SYNTAX      MGMTAclControlInterfaceControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters"
    INDEX       { mgmtAclControlInterfaceControlIfIndex }
    ::= { mgmtAclControlInterfaceControlTable 1 }

MGMTAclControlInterfaceControlEntry ::= SEQUENCE {
    mgmtAclControlInterfaceControlIfIndex  MGMTInterfaceIndex,
    mgmtAclControlInterfaceControlState    TruthValue
}

mgmtAclControlInterfaceControlIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The index of logical interface."
    ::= { mgmtAclControlInterfaceControlEntry 1 }

mgmtAclControlInterfaceControlState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the port status. True is to enable the port and false is to
         shutdown the port"
    ::= { mgmtAclControlInterfaceControlEntry 2 }

mgmtAclMibConformance OBJECT IDENTIFIER
    ::= { mgmtAclMib 2 }

mgmtAclMibCompliances OBJECT IDENTIFIER
    ::= { mgmtAclMibConformance 1 }

mgmtAclMibGroups OBJECT IDENTIFIER
    ::= { mgmtAclMibConformance 2 }

mgmtAclCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclCapabilitiesAceIdMax,
                  mgmtAclCapabilitiesPolicyIdMax,
                  mgmtAclCapabilitiesRateLimiterIdMax,
                  mgmtAclCapabilitiesEvcPolicerIdMax,
                  mgmtAclCapabilitiesRateLimiterBitRateSupported,
                  mgmtAclCapabilitiesEvcPolicerSupported,
                  mgmtAclCapabilitiesMirrorSupported,
                  mgmtAclCapabilitiesMultipleRedirectPortsSupported,
                  mgmtAclCapabilitiesSecondLookupSupported,
                  mgmtAclCapabilitiesMultipleIngressPortsSupported,
                  mgmtAclCapabilitiesEgressPortSupported,
                  mgmtAclCapabilitiesVlanTaggedSupported,
                  mgmtAclCapabilitiesStackableAceSupported }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 1 }

mgmtAclConfigInterfaceConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclConfigInterfaceConfigIfIndex,
                  mgmtAclConfigInterfaceConfigPolicyId,
                  mgmtAclConfigInterfaceConfigHitAction,
                  mgmtAclConfigInterfaceConfigRedirectPortList,
                  mgmtAclConfigInterfaceConfigRateLimiterId,
                  mgmtAclConfigInterfaceConfigEvcPolicerId,
                  mgmtAclConfigInterfaceConfigMirror,
                  mgmtAclConfigInterfaceConfigLogging,
                  mgmtAclConfigInterfaceConfigShutdown }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 2 }

mgmtAclConfigRateLimiterInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclConfigRateLimiterRateLimiterId,
                  mgmtAclConfigRateLimiterBitRateEnable,
                  mgmtAclConfigRateLimiterBitRate,
                  mgmtAclConfigRateLimiterPacketRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 3 }

mgmtAclConfigAceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclConfigAceAceId, mgmtAclConfigAceNextAceId,
                  mgmtAclConfigAceHitAction,
                  mgmtAclConfigAceRedirectPortList,
                  mgmtAclConfigAceRedirectPortListSwitchPort,
                  mgmtAclConfigAceEgressPortList,
                  mgmtAclConfigAceRateLimiterId,
                  mgmtAclConfigAceEvcPolicerId,
                  mgmtAclConfigAceMirror, mgmtAclConfigAceLogging,
                  mgmtAclConfigAceShutdown,
                  mgmtAclConfigAceIngressPortListMode,
                  mgmtAclConfigAceIngressPortList,
                  mgmtAclConfigAceIngressPortListSwitch,
                  mgmtAclConfigAceIngressPortListSwitchPort,
                  mgmtAclConfigAcePolicyValue,
                  mgmtAclConfigAcePolicyMask,
                  mgmtAclConfigAceSecondLookup,
                  mgmtAclConfigAceVlanId,
                  mgmtAclConfigAceVlanTagPriority,
                  mgmtAclConfigAceVlanTagged,
                  mgmtAclConfigAceFrameType,
                  mgmtAclConfigAceDestMacOp,
                  mgmtAclConfigAceEtherSrcMacOp,
                  mgmtAclConfigAceEtherSrcMac,
                  mgmtAclConfigAceEtherDestMac,
                  mgmtAclConfigAceEtherType,
                  mgmtAclConfigAceArpSrcMacOp,
                  mgmtAclConfigAceArpSrcMac,
                  mgmtAclConfigAceArpSenderIp,
                  mgmtAclConfigAceArpSenderIpMask,
                  mgmtAclConfigAceArpTargetIp,
                  mgmtAclConfigAceArpTargetIpMask,
                  mgmtAclConfigAceArpOpcode,
                  mgmtAclConfigAceArpFlagReq,
                  mgmtAclConfigAceArpFlagSha,
                  mgmtAclConfigAceArpFlagTha,
                  mgmtAclConfigAceArpFlagHln,
                  mgmtAclConfigAceArpFlagHrd,
                  mgmtAclConfigAceArpFlagPro,
                  mgmtAclConfigAceIpv4ProtocolOp,
                  mgmtAclConfigAceIpv4Protocol,
                  mgmtAclConfigAceIpv4SrcIp,
                  mgmtAclConfigAceIpv4SrcIpMask,
                  mgmtAclConfigAceIpv4DestIp,
                  mgmtAclConfigAceIpv4DestIpMask,
                  mgmtAclConfigAceIpv4IcmpTypeOp,
                  mgmtAclConfigAceIpv4IcmpType,
                  mgmtAclConfigAceIpv4IcmpCodeOp,
                  mgmtAclConfigAceIpv4IcmpCode,
                  mgmtAclConfigAceIpv4SrcPortOp,
                  mgmtAclConfigAceIpv4SrcPort,
                  mgmtAclConfigAceIpv4SrcPortRange,
                  mgmtAclConfigAceIpv4DestPortOp,
                  mgmtAclConfigAceIpv4DestPort,
                  mgmtAclConfigAceIpv4DestPortRange,
                  mgmtAclConfigAceIpv4FlagTtl,
                  mgmtAclConfigAceIpv4FlagFragment,
                  mgmtAclConfigAceIpv4FlagIpOption,
                  mgmtAclConfigAceIpv4TcpFlagFin,
                  mgmtAclConfigAceIpv4TcpFlagSyn,
                  mgmtAclConfigAceIpv4TcpFlagRst,
                  mgmtAclConfigAceIpv4TcpFlagPsh,
                  mgmtAclConfigAceIpv4TcpFlagAck,
                  mgmtAclConfigAceIpv4TcpFlagUrg,
                  mgmtAclConfigAceIpv6NextHeaderOp,
                  mgmtAclConfigAceIpv6NextHeader,
                  mgmtAclConfigAceIpv6Icmpv6TypeOp,
                  mgmtAclConfigAceIpv6Icmpv6Type,
                  mgmtAclConfigAceIpv6Icmpv6CodeOp,
                  mgmtAclConfigAceIpv6Icmpv6Code,
                  mgmtAclConfigAceIpv6SrcIp,
                  mgmtAclConfigAceIpv6SrcIpMask,
                  mgmtAclConfigAceIpv6SrcPortOp,
                  mgmtAclConfigAceIpv6SrcPort,
                  mgmtAclConfigAceIpv6SrcPortRange,
                  mgmtAclConfigAceIpv6DestPortOp,
                  mgmtAclConfigAceIpv6DestPort,
                  mgmtAclConfigAceIpv6DestPortRange,
                  mgmtAclConfigAceIpv6FlagTtl,
                  mgmtAclConfigAceIpv6TcpFlagFin,
                  mgmtAclConfigAceIpv6TcpFlagSyn,
                  mgmtAclConfigAceIpv6TcpFlagRst,
                  mgmtAclConfigAceIpv6TcpFlagPsh,
                  mgmtAclConfigAceIpv6TcpFlagAck,
                  mgmtAclConfigAceIpv6TcpFlagUrg,
                  mgmtAclConfigAceAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 4 }

mgmtAclConfigAceRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclConfigAceRowEditorAceId,
                  mgmtAclConfigAceRowEditorNextAceId,
                  mgmtAclConfigAceRowEditorHitAction,
                  mgmtAclConfigAceRowEditorRedirectPortList,
                  mgmtAclConfigAceRowEditorRedirectPortListSwitchPort,
                  mgmtAclConfigAceRowEditorEgressPortList,
                  mgmtAclConfigAceRowEditorRateLimiterId,
                  mgmtAclConfigAceRowEditorEvcPolicerId,
                  mgmtAclConfigAceRowEditorMirror,
                  mgmtAclConfigAceRowEditorLogging,
                  mgmtAclConfigAceRowEditorShutdown,
                  mgmtAclConfigAceRowEditorIngressPortListMode,
                  mgmtAclConfigAceRowEditorIngressPortList,
                  mgmtAclConfigAceRowEditorIngressPortListSwitch,
                  mgmtAclConfigAceRowEditorIngressPortListSwitchPort,
                  mgmtAclConfigAceRowEditorPolicyValue,
                  mgmtAclConfigAceRowEditorPolicyMask,
                  mgmtAclConfigAceRowEditorSecondLookup,
                  mgmtAclConfigAceRowEditorVlanId,
                  mgmtAclConfigAceRowEditorVlanTagPriority,
                  mgmtAclConfigAceRowEditorVlanTagged,
                  mgmtAclConfigAceRowEditorFrameType,
                  mgmtAclConfigAceRowEditorDestMacOp,
                  mgmtAclConfigAceRowEditorEtherSrcMacOp,
                  mgmtAclConfigAceRowEditorEtherSrcMac,
                  mgmtAclConfigAceRowEditorEtherDestMac,
                  mgmtAclConfigAceRowEditorEtherType,
                  mgmtAclConfigAceRowEditorArpSrcMacOp,
                  mgmtAclConfigAceRowEditorArpSrcMac,
                  mgmtAclConfigAceRowEditorArpSenderIp,
                  mgmtAclConfigAceRowEditorArpSenderIpMask,
                  mgmtAclConfigAceRowEditorArpTargetIp,
                  mgmtAclConfigAceRowEditorArpTargetIpMask,
                  mgmtAclConfigAceRowEditorArpOpcode,
                  mgmtAclConfigAceRowEditorArpFlagReq,
                  mgmtAclConfigAceRowEditorArpFlagSha,
                  mgmtAclConfigAceRowEditorArpFlagTha,
                  mgmtAclConfigAceRowEditorArpFlagHln,
                  mgmtAclConfigAceRowEditorArpFlagHrd,
                  mgmtAclConfigAceRowEditorArpFlagPro,
                  mgmtAclConfigAceRowEditorIpv4ProtocolOp,
                  mgmtAclConfigAceRowEditorIpv4Protocol,
                  mgmtAclConfigAceRowEditorIpv4SrcIp,
                  mgmtAclConfigAceRowEditorIpv4SrcIpMask,
                  mgmtAclConfigAceRowEditorIpv4DestIp,
                  mgmtAclConfigAceRowEditorIpv4DestIpMask,
                  mgmtAclConfigAceRowEditorIpv4IcmpTypeOp,
                  mgmtAclConfigAceRowEditorIpv4IcmpType,
                  mgmtAclConfigAceRowEditorIpv4IcmpCodeOp,
                  mgmtAclConfigAceRowEditorIpv4IcmpCode,
                  mgmtAclConfigAceRowEditorIpv4SrcPortOp,
                  mgmtAclConfigAceRowEditorIpv4SrcPort,
                  mgmtAclConfigAceRowEditorIpv4SrcPortRange,
                  mgmtAclConfigAceRowEditorIpv4DestPortOp,
                  mgmtAclConfigAceRowEditorIpv4DestPort,
                  mgmtAclConfigAceRowEditorIpv4DestPortRange,
                  mgmtAclConfigAceRowEditorIpv4FlagTtl,
                  mgmtAclConfigAceRowEditorIpv4FlagFragment,
                  mgmtAclConfigAceRowEditorIpv4FlagIpOption,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagFin,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagSyn,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagRst,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagPsh,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagAck,
                  mgmtAclConfigAceRowEditorIpv4TcpFlagUrg,
                  mgmtAclConfigAceRowEditorIpv6NextHeaderOp,
                  mgmtAclConfigAceRowEditorIpv6NextHeader,
                  mgmtAclConfigAceRowEditorIpv6Icmpv6TypeOp,
                  mgmtAclConfigAceRowEditorIpv6Icmpv6Type,
                  mgmtAclConfigAceRowEditorIpv6Icmpv6CodeOp,
                  mgmtAclConfigAceRowEditorIpv6Icmpv6Code,
                  mgmtAclConfigAceRowEditorIpv6SrcIp,
                  mgmtAclConfigAceRowEditorIpv6SrcIpMask,
                  mgmtAclConfigAceRowEditorIpv6SrcPortOp,
                  mgmtAclConfigAceRowEditorIpv6SrcPort,
                  mgmtAclConfigAceRowEditorIpv6SrcPortRange,
                  mgmtAclConfigAceRowEditorIpv6DestPortOp,
                  mgmtAclConfigAceRowEditorIpv6DestPort,
                  mgmtAclConfigAceRowEditorIpv6DestPortRange,
                  mgmtAclConfigAceRowEditorIpv6FlagTtl,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagFin,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagSyn,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagRst,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagPsh,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagAck,
                  mgmtAclConfigAceRowEditorIpv6TcpFlagUrg,
                  mgmtAclConfigAceRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 5 }

mgmtAclConfigAcePrecedenceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclConfigAcePrecedencePrecedence,
                  mgmtAclConfigAcePrecedenceAceId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 6 }

mgmtAclStatusInterfaceHitCountInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclStatusInterfaceHitCountIfIndex,
                  mgmtAclStatusInterfaceHitCountCounter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 7 }

mgmtAclStatusAceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclStatusAceSwitchId,
                  mgmtAclStatusAcePrecedence, mgmtAclStatusAceAclUser,
                  mgmtAclStatusAceAceId, mgmtAclStatusAceConflict }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 8 }

mgmtAclStatusAceHitCountInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclStatusAceHitCountAceId,
                  mgmtAclStatusAceHitCountCounter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 9 }

mgmtAclControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclControlGlobalsClearAllHitCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 10 }

mgmtAclControlInterfaceControlInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAclControlInterfaceControlIfIndex,
                  mgmtAclControlInterfaceControlState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAclMibGroups 11 }

mgmtAclMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtAclCapabilitiesInfoGroup,
                       mgmtAclConfigInterfaceConfigInfoGroup,
                       mgmtAclConfigRateLimiterInfoGroup,
                       mgmtAclConfigAceInfoGroup,
                       mgmtAclConfigAceRowEditorInfoGroup,
                       mgmtAclConfigAcePrecedenceInfoGroup,
                       mgmtAclStatusInterfaceHitCountInfoGroup,
                       mgmtAclStatusAceInfoGroup,
                       mgmtAclStatusAceHitCountInfoGroup,
                       mgmtAclControlGlobalsInfoGroup,
                       mgmtAclControlInterfaceControlInfoGroup }

    ::= { mgmtAclMibCompliances 1 }

END
