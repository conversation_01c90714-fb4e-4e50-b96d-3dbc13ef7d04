package mnms

import (
	"testing"
)

func init() {
	// Initialize test data
	testData := map[string]string{
		"key1": "value1",
		"key2": "value2",
		"key3": "value3",
		"key4": "value4",
		"key5": "value5",
	}

	// Put test data into KVStore
	for k, v := range testData {
		KVStore[k] = v
	}
}

// TestKVStoreGetValues test GetValues
func TestKVStoreGetValues(t *testing.T) {
	testCases := []struct {
		name     string
		keys     []string
		error    bool
		expected map[string]string
	}{
		{
			name:     "Get existing keys",
			keys:     []string{"key1", "key3", "key5"},
			expected: map[string]string{"key1": "value1", "key3": "value3", "key5": "value5"},
			error:    false,
		},
		{
			name:     "Get non-existing keys",
			keys:     []string{"key6", "key7"},
			expected: map[string]string{},
			error:    true,
		},
		{
			name:     "Get mix of existing and non-existing keys",
			keys:     []string{"key2", "key4", "key8"},
			expected: map[string]string{"key2": "value2", "key4": "value4"},
			error:    true,
		},
		{
			name:     "Get with empty input",
			keys:     []string{},
			expected: map[string]string{},
			error:    false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := GetKVValues(tc.keys)
			if err != nil && !tc.error {
				t.Errorf("Error getting KV values: %v", err)
			}
			if tc.error && err != nil {
				t.Logf("Expected error: %v", err)
				return
			}

			if len(result) != len(tc.expected) {
				t.Errorf("Expected %d results, but got %d", len(tc.expected), len(result))
			}

			for k, v := range tc.expected {
				if result[k] != v {
					t.Errorf("Expected %s for key %s, but got %s", v, k, result[k])
				}
			}

			for k := range result {
				if _, ok := tc.expected[k]; !ok {
					t.Errorf("Unexpected key %s in result", k)
				}
			}
		})
	}

}

// TestReplaceKeysInCommand tests the ReplaceKeysInCommand function
func TestReplaceKeysInCommand(t *testing.T) {
	// Add key to KVStore
	SetKVValues(map[string]string{
		"dev1-mac": "00-01-02-AA-BB-EF",
		"password": "123456",
		"account":  "admin",
	})

	//q.P = ".*"
	QC.IsRoot = true
	cmd := "agent config gps enable :dev1-mac 1"
	replacedCmd, err := ExpandCommandKVValue(cmd)
	if err != nil {
		t.Errorf("Error replacing keys in command: %v", err)
	}
	expectedCmd := "agent config gps enable 00-01-02-AA-BB-EF 1"
	if replacedCmd != expectedCmd {
		t.Errorf("Expected command: %s, but got: %s", expectedCmd, replacedCmd)
	}

	cmd = "set time 10:11:12"
	expectedCmd = "set time 10:11:12"
	replacedCmd, err = ExpandCommandKVValue(cmd)
	if err != nil {
		t.Errorf("Error replacing keys in command: %v", err)
	}
	if replacedCmd != expectedCmd {
		t.Errorf("Expected command: %s, but got: %s", expectedCmd, replacedCmd)
	}
	cmd = "agent config password :account :password"
	expectedCmd = "agent config password admin 123456"
	replacedCmd, err = ExpandCommandKVValue(cmd)
	if err != nil {
		t.Errorf("Error replacing keys in command: %v", err)
	}
	if replacedCmd != expectedCmd {
		t.Errorf("Expected command: %s, but got: %s", expectedCmd, replacedCmd)
	}

	cmd = ":password"
	expectedCmd = "123456"
	replacedCmd, err = ExpandCommandKVValue(cmd)
	if err != nil {
		t.Errorf("Error replacing keys in command: %v", err)
	}
	if replacedCmd != expectedCmd {
		t.Errorf("Expected command: %s, but got: %s", expectedCmd, replacedCmd)
	}
	cmd = "123456"
	expectedCmd = "123456"
	replacedCmd, err = ExpandCommandKVValue(cmd)
	if err != nil {
		t.Errorf("Error replacing keys in command: %v", err)
	}
	if replacedCmd != expectedCmd {
		t.Errorf("Expected command: %s, but got: %s", expectedCmd, replacedCmd)
	}
}
