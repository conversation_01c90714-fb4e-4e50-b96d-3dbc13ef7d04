import Mib<PERSON>rowserSlice, {
    GetMibBrowserData,
    GetMibCommandResult,
    setMibIp,
    setMibMaxRepeators,
    setMibOID,
    setMibOperation,
    setMibPort,
    setMibReadCommunity,
    setMibSnmpVersion,
    setMibValue,
    setMibValueType,
    setMibWriteCommunity,
  } from "../features/mibbrowser/MibBrowserSlice";
  import { describe, expect, it, vi } from "vitest";
  import { store } from "../app/store";
  import { loginUser } from "../features/auth/userAuthSlice";
  
  const initialState = {
    mibBrowserStatus: "in_progress",
    message: "",
    cmdResponse: [],
    ip_address: "*************",
    oid: ".1.3",
    operation: "get",
    value: "",
    valueType: "OctetString",
    port: "161",
    readCommunity: "public",
    writeCommunity: "private",
    version: "v2c",
    maxRepetors: "20",
  };
  
  describe("MibBrowserSlice", () => {
    it("Set an authentication token in a request header", async () => {
      await store.dispatch(loginUser({ user: "admin", password: "default" }));
    });
  
    it("Should handle GetMibBrowserData correctly when successful", async () => {
      await store.dispatch(GetMibBrowserData([
        { command: `snmp ${initialState.operation} ${initialState.ip_address} ${initialState.oid}`},
      ]));
      
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
  
    it("Should handle GetMibCommandResult correctly when successful", async () => {
      const paramObj = {
        cValue: "snmp walk *************",
        pageSize: 10,
        pageNum: 1,
        totalPage: 5,
      };
      await store.dispatch(GetMibCommandResult(paramObj));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
  
    it("Should handle setMibIp correctly when successful", async () => {
      const newIpAddress = "*************";
      await store.dispatch(setMibIp(newIpAddress));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibOID correctly when successful", async () => {
      const oid = ".1.3";
      await store.dispatch(setMibOID(oid));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibOperation correctly when successful", async () => {
      const operation = "get";
      await store.dispatch(setMibOperation(operation));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibValue correctly when successful", async () => {
      const value = "";
      await store.dispatch(setMibValue(value));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibValueType correctly when successful", async () => {
      const valueType = "OctetString";
      await store.dispatch(setMibValueType(valueType));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibPort correctly when successful", async () => {
      const port = "161";
      await store.dispatch(setMibPort(port));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibReadCommunity correctly when successful", async () => {
      const readCommunity = "public";
      await store.dispatch(setMibReadCommunity(readCommunity));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibWriteCommunity correctly when successful", async () => {
      const writeCommunity = "private";
      await store.dispatch(setMibWriteCommunity(writeCommunity));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibSnmpVersion correctly when successful", async () => {
      const version = "v2c";
      await store.dispatch(setMibSnmpVersion(version));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
    it("Should handle setMibMaxRepeators correctly when successful", async () => {
      const maxRepetors = "20";
      await store.dispatch(setMibMaxRepeators(maxRepetors));
      const updatedState = store.getState().mibmgmt;
      expect(updatedState.mibBrowserStatus).toEqual("success");
    });
  });
  