package mnms

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/qeof/q"
)

type FileInfos struct {
	Name string `json:"name"`
	Size int64  `json:"size"`
	Type string `json:"type"`
	Path string `json:"path"`
}
type FileList struct {
	Files  []FileInfos `json:"files"`
	Status string      `json:"status"`
}

func init() {
	QC.UpdateSvcInterval = 60
}

func CloseService(delayTime int) {
	msgs := fmt.Sprintf("After %d seconds, service %s will stop.", delayTime, QC.Name)
	SendSyslog(LOG_NOTICE, "main", msgs)
	q.Q(msgs)
	time.Sleep(time.Duration(delayTime) * time.Second)
	ClientExit()
	SyslogExit()
	DoExit(1)
}

func ServiceCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "service stop") {
		ws := strings.Fields(cmd)
		if len(ws) != 2 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
			return cmdinfo
		}
		if cmdinfo.Kind != "root" {
			if QC.Name != cmdinfo.Client {
				cmdinfo.Status = fmt.Sprintf("error: client name %s is not match service %s", cmdinfo.Client, QC.Name)
				return cmdinfo
			}
		}
		go func() {
			// After 10 seconds, service will stop
			CloseService(10)
		}()
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "service update") {
		ws := strings.Fields(cmd)
		if len(ws) != 2 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
			return cmdinfo
		}
		if cmdinfo.Kind != "root" {
			if QC.Name != cmdinfo.Client {
				cmdinfo.Status = fmt.Sprintf("error: client name %s is not match service %s", cmdinfo.Client, QC.Name)
				return cmdinfo
			}
		}

		// update service version
		if QC.IsRoot {
			fileName, err := CheckCloudVersion()
			if err != nil {
				cmdinfo.Status = fmt.Sprintf("error: %v", err)
				return cmdinfo
			} else {
				err = UpdateVersionFromCloud(fileName)
				if err != nil {
					cmdinfo.Status = fmt.Sprintf("error: %v", err)
					return cmdinfo
				}
			}
		} else {
			rootVersion, err := CheckRootSvcVersion()
			if err != nil {
				cmdinfo.Status = fmt.Sprintf("error: %v", err)
				return cmdinfo
			} else {
				if rootVersion != "" {
					err = UpdateVersionFromRootSvc(rootVersion)
					if err != nil {
						cmdinfo.Status = fmt.Sprintf("error: %v", err)
						return cmdinfo
					}
				}
			}
		}
		go func() {
			// After 10 seconds, service will stop
			CloseService(10)
		}()
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	cmdinfo.Status = "error: unknown command"
	return cmdinfo
}

func RunAutomaticallyCheckServiceVersion() error {
	q.Q("Automatically check service version")
	for {
		if QC.IsRoot {
			latestVersionFwFileName, err := CheckCloudVersion()
			if err != nil {
				q.Q(err)
			} else {
				latestVersion, err := FindVersion(latestVersionFwFileName)
				if err != nil {
					q.Q(err)
					latestVersion = latestVersionFwFileName
				}
				msgs := fmt.Sprintf("The root service %s has the latest version %s, while the current version is %s. If you wish to update to %s, please refer to the user manual, Chapter 6.20.1.",
					QC.Name, latestVersion, QC.Version, latestVersion)
				SendSyslog(LOG_ALERT, "AutoUpdateService", msgs)
				return nil
			}
		} else {
			rootVersionFwFileName, err := CheckRootSvcVersion()
			if err != nil {
				q.Q(err)
			} else {
				if rootVersionFwFileName != "" {
					service := ""
					if QC.Kind == "nms" {
						service = "network service"
					}
					if QC.Kind == "syslog" {
						service = "log service"
					}
					if QC.Kind == "idps" {
						service = "idps service"
					}
					if service != "" {
						rootVersion, err := FindVersion(rootVersionFwFileName)
						if err != nil {
							q.Q(err)
							rootVersion = rootVersionFwFileName
						}
						msgs := fmt.Sprintf("The %s %s has the latest version %s, while the current version is %s. If you wish to update to %s, please refer to the user manual, Chapter 6.20.1.",
							service, QC.Name, rootVersion, QC.Version, rootVersion)
						SendSyslog(LOG_ALERT, "AutoUpdateService", msgs)
					}
					return nil
				}
			}
		}
		time.Sleep(time.Duration(QC.UpdateSvcInterval) * time.Second)
	}
}

func FindVersion(fwFileName string) (string, error) {
	var bbnimNamePrefix string
	if runtime.GOOS == "windows" {
		bbnimNamePrefix = "bbnim_windows_amd64_"
	} else {
		bbnimNamePrefix = "bbnim_linux_amd64_"
	}
	modifyFwFileName := strings.Replace(fwFileName, bbnimNamePrefix, "", -1)
	fwVersion := strings.Replace(modifyFwFileName, ".zip", "", -1)
	return fwVersion, nil
}

func copyFile(src, dest string) error {
	source, err := os.Open(src)
	if err != nil {
		return err
	}
	defer source.Close()

	bytes, err := io.ReadAll(source)
	if err != nil {
		return err
	}
	err = os.WriteFile(dest, bytes, 0o755)
	if err != nil {
		return err
	}

	return nil
}

func unZipFilesToDestPath(zipFileName string, fileList []*zip.File, destPath string) ([]FileInfos, error) {
	zipFileFolder := strings.Replace(zipFileName, ".zip", "", -1)

	newFileFolder := path.Join(destPath, zipFileFolder)

	// no exist : create a directory
	_, err := os.Stat(newFileFolder)
	if err != nil {
		err := os.MkdirAll(newFileFolder, 0o777)
		if err != nil {
			return nil, err
		}
	}

	var filesPath []FileInfos
	for _, f := range fileList {
		rc, err := f.Open()
		if err != nil {
			return nil, err
		}
		defer rc.Close()

		// skip directory
		if f.FileInfo().IsDir() {
			continue
		}
		// skip other files
		fileName := path.Base(f.Name)
		if !strings.HasPrefix(fileName, "bb") {
			continue
		}

		// define the new file path
		newFilePath := path.Join(newFileFolder, fileName)

		_, err = os.Stat(newFilePath)
		if err != nil {
			// create a file
			bytes, err := io.ReadAll(rc)
			if err != nil {
				return nil, err
			}
			err = os.WriteFile(newFilePath, bytes, 0o755)
			if err != nil {
				return nil, err
			}
		}
		tmp := FileInfos{
			Name: fileName,
			Path: newFilePath,
			Type: path.Ext(f.Name),
		}
		filesPath = append(filesPath, tmp)
	}
	return filesPath, nil
}

func replaceOldFiles(filesPath []FileInfos, destPath string, executableName string) error {
	for _, v := range filesPath {
		programName := path.Join(destPath, v.Name)

		q.Q(v.Name, executableName)
		if strings.HasPrefix(v.Name, executableName) {
			_, err := os.Stat(programName)
			if err != nil {
				q.Q(programName, "can move file directly")
				err = copyFile(v.Path, programName)
				if err != nil {
					return err
				}
			} else {
				q.Q(programName, "need some mthod")

				fileNameWithoutExt := strings.TrimSuffix(v.Name, v.Type)
				q.Q(fileNameWithoutExt, v.Name, v.Type)

				modifiedFileName := path.Join(destPath, fileNameWithoutExt+".old"+v.Type)

				err := os.Rename(programName, modifiedFileName)
				if err != nil {
					return err
				}
				err = copyFile(v.Path, programName)
				if err != nil {
					return err
				}
			}
			break
		}
	}
	return nil
}

func CheckCloudVersion() (string, error) {
	var bbnimFwName string
	if runtime.GOOS == "windows" {
		bbnimFwName = "bbnim_windows_amd64_"
	} else {
		bbnimFwName = "bbnim_linux_amd64_"
	}

	var fileList FileList

	// get https://nms.blackbeartechhive.com/files list
	req, err := http.NewRequest("GET", "https://nms.blackbeartechhive.com/api/v1/list", nil)
	if err != nil {
		return "", err
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		q.Q(err)
		return "", err
	}
	if resp != nil {
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", err
		}
		err = json.Unmarshal(body, &fileList)
		if err != nil {
			return "", err
		}
	}

	// compare QC.Version
	higherVersion := QC.Version
	higherVersionName := ""

	for _, v := range fileList.Files {
		if strings.HasPrefix(v.Name, bbnimFwName) {
			fwVersion, err := FindVersion(v.Name)
			if err != nil {
				q.Q("error: cannot find version", err)
				return "", err
			}
			q.Q(v.Name)
			result, err := compareVersion(fwVersion, higherVersion)
			if err != nil {
				return "", err
			}
			if result > 0 {
				higherVersion = fwVersion
				higherVersionName = v.Name
			}
		}
	}
	if strings.Contains(higherVersion, QC.Version) {
		return "", fmt.Errorf("bbnim is already the latest version")
	}

	return higherVersionName, nil
}

func UpdateVersionFromCloud(zipFileName string) error {
	// download file from url
	url := "https://nms.blackbeartechhive.com/api/v1/files/" + zipFileName
	q.Q(zipFileName, url)

	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	if resp == nil {
		return fmt.Errorf("resp is nil")
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	// unzip file
	num := int64(len(data))
	zipReader, err := zip.NewReader(bytes.NewReader(data), num)
	if err != nil {
		return fmt.Errorf("invalid zip file")
	}
	// copy file
	exfilePath, err := os.Executable()
	if err != nil {
		return err
	}
	exPath := filepath.Dir(exfilePath)
	unzipFilesPath, err := unZipFilesToDestPath(zipFileName, zipReader.File, exPath)
	if err != nil {
		return err
	}

	executableName := filepath.Base(exfilePath)

	// replace old files to new files
	err = replaceOldFiles(unzipFilesPath, exPath, executableName)
	if err != nil {
		return err
	}

	return nil
}

func compareVersion(version1 string, version2 string) (int, error) {
	version1Int, err := TransferVersionToNumber(version1)
	if err != nil {
		return 0, err
	}
	version2Int, err := TransferVersionToNumber(version2)
	if err != nil {
		return 0, err
	}
	if version1Int > version2Int {
		return 1, nil
	}
	return -1, nil
}

func CheckServiceVersion(serviceVersion string) (string, string) {
	if !QC.IsRoot {
		return "", "error: only root can run this function"
	}

	serviceVersionInt, err := TransferVersionToNumber(serviceVersion)
	if err != nil {
		return "", fmt.Sprintf("error: %v", err)
	}
	// compare QC.Version
	bbnimVersionInt, err := TransferVersionToNumber(QC.Version)
	if err != nil {
		return "", fmt.Sprintf("error: %v", err)
	}
	q.Q(serviceVersionInt, bbnimVersionInt)
	if serviceVersionInt >= bbnimVersionInt {
		return "", "service don't need to update"
	}
	return QC.Version, ""
}

func CheckRootSvcVersion() (string, error) {
	type ServiceVersion struct {
		Version string `json:"version"`
		Status  string `json:"status"`
	}
	req := ServiceVersion{
		Version: QC.Version,
	}

	jsonBytes, err := json.Marshal(req)
	if err != nil {
		q.Q(err)
		return "", err
	}
	// update results back to root
	resp, err := PostWithToken(QC.RootURL+"/api/v1/service/update", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err)
		return "", err
	}
	if resp != nil {
		defer resp.Body.Close()
		bodyText, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q(err)
			return "", err
		}
		var serviceversion ServiceVersion
		err = json.Unmarshal(bodyText, &serviceversion)
		if err != nil {
			q.Q(err)
			return "", err
		}
		q.Q(serviceversion)
		if serviceversion.Status != "" {
			q.Q(serviceversion.Status)
			return "", fmt.Errorf("%v", serviceversion.Status)
		}
		return serviceversion.Version, nil
	}
	return "", fmt.Errorf("error: cannot get response")
}

func UpdateVersionFromRootSvc(rootVersion string) error {
	q.Q("update", rootVersion)
	var bbnimFwZipName string
	if runtime.GOOS == "windows" {
		bbnimFwZipName = "bbnim_windows_amd64_" + rootVersion + ".zip"
	} else {
		bbnimFwZipName = "bbnim_linux_amd64_" + rootVersion + ".zip"
	}

	// download file from url
	resp, err := GetWithToken(QC.RootURL+"/api/v1/service/update", QC.AdminToken)
	if err != nil {
		q.Q(err)
		return err
	}
	if resp != nil {
		defer resp.Body.Close()

		data, err := io.ReadAll(resp.Body)
		if err != nil {
			q.Q(err)
			return err
		}
		// unzip file
		num := int64(len(data))
		zipReader, err := zip.NewReader(bytes.NewReader(data), num)
		if err != nil {
			return fmt.Errorf("error: invalid zip file")
		}
		// copy file
		exfilePath, err := os.Executable()
		if err != nil {
			q.Q(err)
			return err
		}
		exPath := filepath.Dir(exfilePath)
		unzipFilesPath, err := unZipFilesToDestPath(bbnimFwZipName, zipReader.File, exPath)
		if err != nil {
			q.Q(err)
			return err
		}

		executableName := filepath.Base(exfilePath)

		// replace old files to new files
		err = replaceOldFiles(unzipFilesPath, exPath, executableName)
		if err != nil {
			q.Q(err)
			return err
		}
		return nil
	}
	return fmt.Errorf("error: cannot get response")
}

func DownloadFwFromCloud() ([]byte, error) {
	var bbnimFwName string
	if runtime.GOOS == "windows" {
		bbnimFwName = "bbnim_windows_amd64_" + QC.Version + ".zip"
	} else {
		bbnimFwName = "bbnim_linux_amd64_" + QC.Version + ".zip"
	}

	// download file from url
	url := "https://nms.blackbeartechhive.com/api/v1/files/" + bbnimFwName
	q.Q(url)

	resp, err := http.Get(url)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	if resp == nil {
		return nil, fmt.Errorf("resp is nil")
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return data, nil
}
