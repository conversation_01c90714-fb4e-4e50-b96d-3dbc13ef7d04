import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  licenseAlertSelector,
  setMessage,
} from "../../features/socketControl/licenseAlertSlice";
import { App, Typography, theme as antdTheme } from "antd";
import { WarningOutlined } from "@ant-design/icons";
import { Flexbox } from "react-layout-kit";

const LicenseAlert = () => {
  const dispatch = useDispatch();
  const { modal } = App.useApp();
  const { token } = antdTheme.useToken();
  const { licenseErrorMsg, showLicenseError } =
    useSelector(licenseAlertSelector);

  const handleOkClick = () => {
    dispatch(setMessage({ kind: "clear" }));
  };

  useEffect(() => {
    if (showLicenseError) {
      modal.info({
        icon: null,
        width: 360,
        className: "confirm-class",
        onOk: handleOkClick,
        content: (
          <Flexbox align="center" direction="vertical">
            <WarningOutlined
              style={{
                color: token.colorWarning,
                fontSize: 64,
              }}
            />
            <Typography.Title level={4}>License Alert</Typography.Title>
            <Typography.Text strong>{licenseErrorMsg}</Typography.Text>
          </Flexbox>
        ),
      });
    }
  }, [showLicenseError]);

  return <div></div>;
};

export default LicenseAlert;
