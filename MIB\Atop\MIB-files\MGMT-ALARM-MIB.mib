-- *****************************************************************
-- ALARM-MIB:  
-- ****************************************************************

MGMT-ALARM-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtAlarmMib MODULE-IDENTITY
    LAST-UPDATED "201602080000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private mib for alarms"
    REVISION    "201602080000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 136 }


mgmtAlarmMibObjects OBJECT IDENTIFIER
    ::= { mgmtAlarmMib 1 }

mgmtAlarmConfig OBJECT IDENTIFIER
    ::= { mgmtAlarmMibObjects 2 }

mgmtAlarmConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAlarmConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is the list of configured alarms. The index is the name of
         the alarm"
    ::= { mgmtAlarmConfig 1 }

mgmtAlarmConfigEntry OBJECT-TYPE
    SYNTAX      MGMTAlarmConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index is a dotted name e.g. alarm.port.status"
    INDEX       { mgmtAlarmConfigAlarmName }
    ::= { mgmtAlarmConfigTable 1 }

MGMTAlarmConfigEntry ::= SEQUENCE {
    mgmtAlarmConfigAlarmName   MGMTDisplayString,
    mgmtAlarmConfigExpression  MGMTDisplayString,
    mgmtAlarmConfigAction      MGMTRowEditorState
}

mgmtAlarmConfigAlarmName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..99))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the alarm"
    ::= { mgmtAlarmConfigEntry 1 }

mgmtAlarmConfigExpression OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..1023))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The expression defining the alarm."
    ::= { mgmtAlarmConfigEntry 2 }

mgmtAlarmConfigAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAlarmConfigEntry 100 }

mgmtAlarmConfigTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtAlarmConfig 2 }

mgmtAlarmConfigTableRowEditorAlarmName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..99))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the alarm"
    ::= { mgmtAlarmConfigTableRowEditor 1 }

mgmtAlarmConfigTableRowEditorExpression OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..1023))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The expression defining the alarm."
    ::= { mgmtAlarmConfigTableRowEditor 2 }

mgmtAlarmConfigTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAlarmConfigTableRowEditor 100 }

mgmtAlarmStatus OBJECT IDENTIFIER
    ::= { mgmtAlarmMibObjects 3 }

mgmtAlarmStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAlarmStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is the list of alarm nodes. The index is the name of the
         alarm node"
    ::= { mgmtAlarmStatus 1 }

mgmtAlarmStatusEntry OBJECT-TYPE
    SYNTAX      MGMTAlarmStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index is a dotted name e.g. alarm.port.status"
    INDEX       { mgmtAlarmStatusAlarmName }
    ::= { mgmtAlarmStatusTable 1 }

MGMTAlarmStatusEntry ::= SEQUENCE {
    mgmtAlarmStatusAlarmName      MGMTDisplayString,
    mgmtAlarmStatusSuppressed     TruthValue,
    mgmtAlarmStatusActive         TruthValue,
    mgmtAlarmStatusExposedActive  TruthValue
}

mgmtAlarmStatusAlarmName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..99))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the alarm"
    ::= { mgmtAlarmStatusEntry 1 }

mgmtAlarmStatusSuppressed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the alarm subtree is suppressed. When a subtree is
         suppressed, the status does not contribute to the state of the superior
         alarm tree."
    ::= { mgmtAlarmStatusEntry 2 }

mgmtAlarmStatusActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the alarm is active"
    ::= { mgmtAlarmStatusEntry 3 }

mgmtAlarmStatusExposedActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The exposed alarm status."
    ::= { mgmtAlarmStatusEntry 4 }

mgmtAlarmControl OBJECT IDENTIFIER
    ::= { mgmtAlarmMibObjects 4 }

mgmtAlarmControlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAlarmControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is the list of alarm nodes. The index is the name of the
         alarm node"
    ::= { mgmtAlarmControl 1 }

mgmtAlarmControlEntry OBJECT-TYPE
    SYNTAX      MGMTAlarmControlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "An index is a dotted name e.g. alarm.port.status"
    INDEX       { mgmtAlarmControlAlarmName }
    ::= { mgmtAlarmControlTable 1 }

MGMTAlarmControlEntry ::= SEQUENCE {
    mgmtAlarmControlAlarmName  MGMTDisplayString,
    mgmtAlarmControlSuppress   TruthValue
}

mgmtAlarmControlAlarmName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..99))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the alarm"
    ::= { mgmtAlarmControlEntry 1 }

mgmtAlarmControlSuppress OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether to suppress the alarm subtree. When a subtree is
         suppressed, the status does not contribute to the state of the superior
         alarm tree."
    ::= { mgmtAlarmControlEntry 2 }

mgmtAlarmTrap OBJECT IDENTIFIER
    ::= { mgmtAlarmMibObjects 6 }

mgmtAlarmTrapStatusAdd NOTIFICATION-TYPE
    OBJECTS     { mgmtAlarmStatusAlarmName, mgmtAlarmStatusSuppressed,
                  mgmtAlarmStatusActive, mgmtAlarmStatusExposedActive }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been added. The index(es) and value(s)
         of the row is included in the trap."

    ::= { mgmtAlarmTrap 1 }

mgmtAlarmTrapStatusMod NOTIFICATION-TYPE
    OBJECTS     { mgmtAlarmStatusAlarmName, mgmtAlarmStatusSuppressed,
                  mgmtAlarmStatusActive, mgmtAlarmStatusExposedActive }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
          has been updated."

    ::= { mgmtAlarmTrap 2 }

mgmtAlarmTrapStatusDel NOTIFICATION-TYPE
    OBJECTS     { mgmtAlarmStatusAlarmName }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been deleted. The index(es) of the
         row is included in the trap."

    ::= { mgmtAlarmTrap 3 }

mgmtAlarmMibConformance OBJECT IDENTIFIER
    ::= { mgmtAlarmMib 2 }

mgmtAlarmMibCompliances OBJECT IDENTIFIER
    ::= { mgmtAlarmMibConformance 1 }

mgmtAlarmMibGroups OBJECT IDENTIFIER
    ::= { mgmtAlarmMibConformance 2 }

mgmtAlarmConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAlarmConfigAlarmName, mgmtAlarmConfigExpression,
                  mgmtAlarmConfigAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAlarmMibGroups 1 }

mgmtAlarmConfigTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAlarmConfigTableRowEditorAlarmName,
                  mgmtAlarmConfigTableRowEditorExpression,
                  mgmtAlarmConfigTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAlarmMibGroups 2 }

mgmtAlarmStatusInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAlarmStatusAlarmName, mgmtAlarmStatusSuppressed,
                  mgmtAlarmStatusActive, mgmtAlarmStatusExposedActive }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAlarmMibGroups 3 }

mgmtAlarmControlTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAlarmControlAlarmName, mgmtAlarmControlSuppress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAlarmMibGroups 4 }

mgmtAlarmTrapStatusAddInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtAlarmTrapStatusAdd }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtAlarmMibGroups 5 }

mgmtAlarmTrapStatusModInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtAlarmTrapStatusMod }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtAlarmMibGroups 6 }

mgmtAlarmTrapStatusDelInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtAlarmTrapStatusDel }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtAlarmMibGroups 7 }

mgmtAlarmMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtAlarmConfigTableInfoGroup,
                       mgmtAlarmConfigTableRowEditorInfoGroup,
                       mgmtAlarmStatusInfoGroup,
                       mgmtAlarmControlTableInfoGroup,
                       mgmtAlarmTrapStatusAddInfoGroup,
                       mgmtAlarmTrapStatusModInfoGroup,
                       mgmtAlarmTrapStatusDelInfoGroup }

    ::= { mgmtAlarmMibCompliances 1 }

END
