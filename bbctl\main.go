package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"mnms"
	"os"
	"regexp"
	"runtime/debug"
	"strings"
	"time"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.Stderr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	flagversion := flag.Bool("version", false, "print version")
	cmdflagnoow := flag.Bool("cno", false, "command overwrite flag")
	cmdflagall := flag.Bool("ca", false, "command all flag")
	cmdflagnosys := flag.Bool("cns", false, "command no syslog flag")
	cmdClient := flag.String("cc", "", "command network service name specification")
	cmdTag := flag.String("ct", "", "command tag")
	localRootURL := flag.String("r", fmt.Sprintf("http://localhost:%d", mnms.QC.Port), "address of the root service")
	checkCmd := flag.Bool("ci", false, "check the whole result of the command")
	checkCmdResult := flag.Bool("cr", false, "check the result of the command")
	ck := flag.String("ck", "usercommand", "kinds of command")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	dp := flag.String("P", "", "debug log pattern string")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	flag.Parse()
	if *flagversion {
		printVersion()
		mnms.DoExit(0)
	}

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	if *debuglog {
		*dp = ".*"
	}

	if *dp == "." {
		fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
		mnms.DoExit(1)
	}
	_, err := regexp.Compile(*dp)
	if err != nil {
		fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
		mnms.DoExit(1)
	}
	q.P = *dp
	q.Q(q.O, q.P)

	args := flag.Args()
	CheckArgs(args)
	// implement cli by posting commands via http api
	acmd := args[0]
	found := false
	for _, c := range mnms.ValidCommands {
		if c == acmd {
			found = true
		}
	}

	if !found {
		fmt.Fprintf(os.Stderr, "error: invalid cmd %s\n\n", acmd)
		helpMsg := mnms.HelpCmd("help")
		fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
		mnms.DoExit(1)
	}
	if len(args) < 2 {
		fmt.Fprintf(os.Stderr, "error: insufficient args\n")
		helpMsg := mnms.HelpCmd("help " + acmd)
		fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
		mnms.DoExit(1)
	}
	if args[1] == "help" {
		for _, c := range mnms.ValidCommands {
			if c == acmd {
				fmt.Println(acmd)
				helpMsg := mnms.HelpCmd("help " + acmd)
				fmt.Fprintf(os.Stderr, "%s\n", helpMsg)
				mnms.DoExit(0)
			}
		}
	}

	// check args length > 1 and args[1] is a 'util'
	if len(os.Args) > 2 && os.Args[1] == "util" && os.Args[2] != "help" {
		err := mnms.ProcessDirectCommands()
		if err != nil {
			fmt.Fprintln(os.Stderr, err)
		}
		mnms.DoExit(0)
		return
	}

	mnms.QC.AdminToken, err = mnms.GetToken("admin")
	if err != nil {
		q.Q(err)
		fmt.Fprintln(os.Stderr, "error: can't get admin token")
		mnms.DoExit(1)
	}
	cmd := strings.Join(args, " ")
	kcmd := cmd
	// if *cmdClient != "" {
	// 	// For -cc option, the command is sent to the specified service
	// 	// command map's key is not just command string but also include client name "@ client-name command"
	// 	kcmd = "@" + *cmdClient + " " + cmd
	// 	// kcmd = fmt.Sprintf("%v -cc %v", kcmd, *cmdClient)
	// }
	ci := mnms.CmdInfo{
		Timestamp:   time.Now().Format(time.RFC3339),
		Command:     cmd,
		NoOverwrite: *cmdflagnoow,
		All:         *cmdflagall,
		NoSyslog:    *cmdflagnosys,
		Kind:        *ck,
		Client:      *cmdClient,
		Tag:         *cmdTag,
	}
	cmdList := []mnms.CmdInfo{ci}
	jsonBytes, err := json.Marshal(cmdList)
	if err != nil {
		q.Q(err)
		mnms.DoExit(1)
	}
	q.Q("posting", cmdList)
	url := *localRootURL + "/api/v1/commands"
	resp, err := mnms.PostWithToken(url, mnms.QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err.Error())
		fmt.Fprintf(os.Stderr, "error: cannot connect to root server at %v\n", *localRootURL)
		mnms.DoExit(1)
	}
	if resp == nil {
		fmt.Fprintf(os.Stderr, "error: no response from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}

	// save close, resp should be nil here
	defer resp.Body.Close()
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		q.Q(err.Error())
		fmt.Fprintf(os.Stderr, "error: reading response from root server at %v\n", localRootURL)
		mnms.DoExit(1)
	}
	q.Q(string(result))

	if *checkCmd || *checkCmdResult {
		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()
		// check the result of the command
		go func() {
			// print . every second
			for {
				select {

				case <-ctx.Done():
					return
				default:
					fmt.Print(".")
					time.Sleep(time.Second)
				}
			}
		}()

		q.Q("waiting for command result ", kcmd)
		cmdResult, err := mnms.QueryCmdTilFinished(ctx, *localRootURL, time.Second, kcmd)
		q.Q("command result", cmdResult)
		if err != nil {
			fmt.Fprintf(os.Stderr, "error: %v\n", err)
			mnms.DoExit(1)
		}
		if *checkCmd {
			fmt.Fprintf(os.Stderr, "\n%s\n", PrettyPrint(cmdResult))
		}
		if *checkCmdResult {
			fmt.Fprintf(os.Stderr, "\n%s\n", PrettyPrint(cmdResult.Result))
		}

		mnms.DoExit(0)
	}

	// automatically update service
	mnms.QC.RootURL = *localRootURL
	rootVersionFwFileName, err := mnms.CheckRootSvcVersion()
	if err != nil {
		q.Q(err)
	} else {
		rootVersion, err := mnms.FindVersion(rootVersionFwFileName)
		if err != nil {
			q.Q(err)
			rootVersion = rootVersionFwFileName
		}
		fmt.Fprintf(os.Stderr, "The bbctl %s has the latest version %s, while the current version is %s. If you wish to update to %s, please refer to the user manual, Chapter 6.20.1.",
			mnms.QC.Name, rootVersion, mnms.QC.Version, rootVersion)
	}

	mnms.DoExit(0)

}

// PrettyPrint
func PrettyPrint(i interface{}) string {
	switch i.(type) {
	case string:
		if strings.HasPrefix(i.(string), "{") || strings.HasPrefix(i.(string), "[") {
			// maybe json
			var out bytes.Buffer
			err := json.Indent(&out, []byte(i.(string)), "", "  ")
			if err != nil {
				return i.(string)
			}
			return out.String()
		}
		return i.(string)
	default:
		s, _ := json.MarshalIndent(i, "", "\t")
		return string(s)
	}
}
