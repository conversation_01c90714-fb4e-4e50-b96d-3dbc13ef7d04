package ethernetip_test

import (
	"fmt"
	"log"
	"mnms/idpsystem/idps/ids"
	"os"
	"testing"
	"time"

	"github.com/danomagnum/gologix"
	"github.com/google/gonids"
)

const testproto = "enip"

var count = 0

func TestMain(m *testing.M) {
	r := gologix.PathRouter{}

	p1 := gologix.MapTagProvider{}
	path1, err := gologix.ParsePath("1,0")
	if err != nil {
		log.Printf("problem parsing path. %v", err)
		os.Exit(1)
	}
	r.<PERSON>le(path1.Bytes(), &p1)

	s := gologix.NewServer(&r)
	go s.Serve()

	t := time.NewTicker(time.Second * 5)
	go func() {
		for {
			<-t.C
			p1.Mutex.Lock()
			log.Printf("Data 1: %v", p1.Data)
			p1.Mutex.Unlock()

		}
	}()
	time.Sleep(time.Second * 3)
	m.Run()
}
func TestDetect(t *testing.T) {
	i, err := ids.NewIds(false, false)
	if err != nil {
		t.Fatal(err)
	}
	i.<PERSON>lo(true)
	s := `alert enip $HOME_NET any <> $HOME_NET any (msg:"test modbus";sid:1;)`
	r, err := gonids.ParseRule(s)
	if err != nil {
		t.Fatal(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	i.RegisterMatchEvent(event())

	err = i.Run()
	if err != nil {
		t.Fatal(err)
	}
	err = i.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = i.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	defer i.Close()
	err = runEnip()
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Second * 3)
	if count == 0 {
		t.Fatalf("test fail,not detect proto:%v", testproto)
	}
}
func event() func(ids.Event) {
	v := func(event ids.Event) {
		count++
		log.Printf("%v", event)
	}
	return v
}
func runEnip() error {
	client := gologix.NewClient("127.0.0.1")
	err := client.Connect()
	if err != nil {
		return fmt.Errorf("Error opening client. %v", err)

	}
	defer client.Disconnect()

	err = client.ListAllTags(0)

	if err != nil {
		log.Printf("Error getting tag list. %v", err)
	}

	log.Printf("Found %d tags.", len(client.KnownTags))
	// list through the tag list and read them all
	for tagname := range client.KnownTags {
		tag := client.KnownTags[tagname]
		log.Printf("%s: %v", tag.Name, tag.Info.Type)

		// TODO: in theory we should do more to read multi-dim arrays.
		qty := uint16(1)
		if tag.Info.Dimension1 != 0 {
			tagname = tagname + "[0]"
			x := tag.Info.Atomic()
			qty = uint16(tag.Info.Dimension1)
			_ = x
		}
		if tag.UDT == nil && !tag.Info.Atomic() {
			//log.Print("Not Atomic or UDT")
			continue
		}
		if tag.UDT != nil {
			log.Printf("%s size = %d", tag.Name, tag.UDT.Size())
		}

		val, err := client.Read_single(tagname, tag.Info.Type, qty)
		if err != nil {
			log.Printf("Error!  Problem reading tag %s. %v", tagname, err)
			continue
		}
		log.Printf("     = %v", val)
	}

	log.Printf("Found %d tags.", len(client.KnownTags))
	return nil
}
