package keywords

import (
	"fmt"
	"mnms/idpsystem/idps/src/keywords/ip"
	"mnms/idpsystem/idps/src/keywords/tcp"
	"strings"
)

type Tagser interface {
	SetUp(v string) error
}

func NewTags(v string) (Tagser, error) {
	v = strings.ToLower(v)
	if v, ok := tagsMap()[v]; ok {
		return v, nil
	}
	return nil, fmt.Errorf("not supported KeyWord:%v", v)
}

func tagsMap() map[string]Tagser {
	keywordmap := map[string]Tagser{
		"ipopts":     ip.NewIpopts(),
		"ip_proto":   ip.NewProto(),
		"geoip":      ip.NewGeoip(),
		"fragbits":   ip.NewFragbits(),
		"fragoffset": ip.NewFragoffset(),
		"tos":        ip.NewTos(),
		"tcp.flags":  tcp.NewFlags(),
		"window":     tcp.<PERSON>ow(),
	}
	return keywordmap
}
