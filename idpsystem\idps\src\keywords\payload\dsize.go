package payload

import (
	"fmt"

	"github.com/google/gonids"
)

type dsize struct {
	min    int
	max    int
	num    int
	verify func(int) bool
}

func newDsize(l *list, v *gonids.LenMatch) error {
	d := &Detect{
		id:         l.id,
		postition:  v.DataPosition,
		detectedID: detectDsize,
	}
	dsize := &dsize{}
	ck, err := dsize.creatFunc(v)
	if err != nil {
		return err
	}
	dsize.verify = ck
	d.data = dsize
	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}

func (d *dsize) inspect(l int) bool {
	if d.verify != nil {
		return d.verify(l)
	}
	return true
}

func (d *dsize) creatFunc(v *gonids.LenMatch) (func(int) bool, error) {
	switch v.Operator {
	case "":
		d.num = v.Num
		return func(p int) bool { return p == d.num }, nil
	case "<=":
		d.num = v.Num
		return func(p int) bool { return p <= d.num }, nil
	case "<":
		d.num = v.Num
		return func(p int) bool { return p < d.num }, nil
	case ">":
		d.num = v.Num
		return func(p int) bool { return p > d.num }, nil
	case ">=":
		d.num = v.Num
		return func(p int) bool { return p >= d.num }, nil
	case "<>":
		d.max = v.Max
		d.min = v.Min
		return func(p int) bool { return p > d.min && p < d.max }, nil
	case "!":
		d.num = v.Num
		return func(p int) bool { return p != d.num }, nil
	default:
		return nil, fmt.Errorf("invalid operator: %s", v.Operator)
	}

}
