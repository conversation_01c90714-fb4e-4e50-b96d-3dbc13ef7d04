import { MessageOutlined } from "@ant-design/icons";
import { FloatButton, Badge } from "antd";
import React, { memo } from "react";
import { useChatStore } from "../../../features/chat/chat-store";

const ChatFloatButton = memo(() => {
  const { toggleChat, isChatOpen, messages } = useChatStore();

  // Count unread messages (for future enhancement)
  const unreadCount = 0; // This could be implemented later

  return (
    <Badge count={unreadCount} size="small">
      <FloatButton
        shape="circle"
        type={isChatOpen ? "default" : "primary"}
        tooltip={isChatOpen ? "Close Chat" : "Open AI Assistant"}
        style={{
          insetInlineEnd: `calc(100vw - 105px)`,
          insetBlockEnd: 24,
        }}
        icon={<MessageOutlined />}
        onClick={toggleChat}
      />
    </Badge>
  );
});

ChatFloatButton.displayName = "ChatFloatButton";

export default ChatFloatButton;
