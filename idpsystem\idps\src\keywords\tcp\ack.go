package tcp

import (
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
)

func NewAck() *acknowledgment {
	return &acknowledgment{}
}

type acknowledgment struct {
	v uint32
}

func (s *acknowledgment) SetUp(v any) error {
	l, err := gonidutil.ConvertToLenMatch(v)
	if err != nil {
		return err
	}
	s.v = uint32(l.Num)
	return nil
}
func (s *acknowledgment) Match(packet gopacket.Packet) bool {
	tp := protocol.NewTcpParser()
	b := tp.Parse(packet)
	if !b {
		return false
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return false
	}
	return s.v == tcp.Ack
}
