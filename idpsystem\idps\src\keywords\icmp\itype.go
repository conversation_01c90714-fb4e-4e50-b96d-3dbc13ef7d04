package icmp

import (
	"fmt"
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewItype() *itype {
	return &itype{layer: protocol.NewIcmpParser()}
}

type itype struct {
	layer         *protocol.IcmpParser
	min, num, max uint8
	ck            func(p uint8) bool
}

func (i *itype) SetUp(input any) error {

	v, err := gonidutil.ConvertToLenMatch(input)
	if err != nil {
		return err
	}
	ck, err := i.creatFunc(v.Operator)
	if err != nil {
		return err
	}
	i.num = uint8(v.Num)
	i.min = uint8(v.Min)
	i.max = uint8(v.Max)
	i.ck = ck
	return nil
}

func (i *itype) Match(packet gopacket.Packet) bool {
	b := i.layer.Parse(packet)
	if !b {
		return false
	}
	proto, lay, err := i.layer.GetIcmp()
	if err != nil {
		return false
	}
	if proto == layers.IPProtocolICMPv4 {
		if icmp, ok := lay.(*layers.ICMPv4); ok {
			return i.ck(icmp.TypeCode.Type())
		}

	} else if proto == layers.IPProtocolICMPv6 {
		if icmp, ok := lay.(*layers.ICMPv6); ok {
			return i.ck(icmp.TypeCode.Type())
		}
	}

	return false
}

func (i *itype) creatFunc(op string) (func(uint8) bool, error) {
	switch op {
	case ">":
		return func(p uint8) bool { return p > i.num }, nil
	case "<":
		return func(p uint8) bool { return p < i.num }, nil
	case "<>":
		return func(p uint8) bool { return p > i.min && p < i.max }, nil
	case "":
		return func(p uint8) bool { return p == i.num }, nil
	default:
		return nil, fmt.Errorf("not supported Operator:%v", op)
	}
}
