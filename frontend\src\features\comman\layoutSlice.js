import { createSlice, createSelector } from "@reduxjs/toolkit";

const layoutSlice = createSlice({
  name: "layout",
  initialState: {
    isCollapsed: false,
  },
  reducers: {
    setIsCollapsed: (state, { payload }) => {
      state.isCollapsed = payload;
    },
  },
});

export const { setIsCollapsed } = layoutSlice.actions;

export const layoutSliceSelector = createSelector(
  (state) => state.layout,
  ({ isCollapsed }) => ({
    isCollapsed,
  })
);

export default layoutSlice;
