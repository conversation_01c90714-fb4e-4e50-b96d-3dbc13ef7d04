-- *****************************************************************
-- SYNCE-MIB:  
-- ****************************************************************

-- <PERSON><PERSON><PERSON> is aware that some terminology used in this technical document is
-- antiquated and inappropriate. As a result of the complex nature of software
-- where seemingly simple changes have unpredictable, and often far-reaching
-- negative results on the software's functionality (requiring extensive retesting
-- and revalidation) we are unable to make the desired changes in all legacy
-- systems without compromising our product or our clients' products.

MGMT-SYNCE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtSynceMib MODULE-IDENTITY
    LAST-UPDATED "201606160000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private SyncE MIB."
    REVISION    "201606160000Z"
    DESCRIPTION
        "Added synceLolAlarmState used in place of TruthValue in
         SynceStatusGlobalClockSelectionModeLol"
    REVISION    "201603170000Z"
    DESCRIPTION
        "Updated MIB to indicate capability to quality level in SyncE"
    REVISION    "201602240000Z"
    DESCRIPTION
        "Updated MIB to indicate capability to nominate PTP sources in SyncE"
    REVISION    "201406240000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 42 }


MGMTsynceAnegMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { none(0), preferedSlave(1),
                          preferedMaster(2), forcedSlave(3) }

MGMTsynceEecOption ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { eecOption1(0), eecOption2(1) }

MGMTsynceFrequency ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { disabled(0), freq1544kHz(1), freq2048kHz(2),
                          freq10MHz(3), freqMax(4) }

MGMTsynceLolAlarmState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { false(0), true(1), na(2) }

MGMTsyncePtsfState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { none(0), unusable(1), lossSync(2),
                          lossAnnounce(3) }

MGMTsynceQualityLevel ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { qlNone(0), qlPrc(1), qlSsua(2), qlSsub(3),
                          qlEec1(4), qlDnu(5), qlInv(6), qlFail(7),
                          qlLink(8), qlPrs(9), qlStu(10), qlSt2(11),
                          qlTnc(12), qlSt3e(13), qlEec2(14),
                          qlSmc(15), qlProv(16), qlDus(17) }

MGMTsynceSelectionMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { manual(0), manualToSelected(1),
                          autoNonrevertive(2), autoRevertive(3),
                          forcedHoldover(4), forcedFreeRun(5) }

MGMTsynceSelectorState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "-"
    SYNTAX      INTEGER { locked(0), holdover(1), freerun(2), ptp(3),
                          refFailed(4), acquiring(5) }

mgmtSynceMibObjects OBJECT IDENTIFIER
    ::= { mgmtSynceMib 1 }

mgmtSynceCapabilities OBJECT IDENTIFIER
    ::= { mgmtSynceMibObjects 1 }

mgmtSynceCapabilitiesGlobal OBJECT IDENTIFIER
    ::= { mgmtSynceCapabilities 1 }

mgmtSynceCapabilitiesGlobalSourceCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of SyncE sources supported by the device."
    ::= { mgmtSynceCapabilitiesGlobal 1 }

mgmtSynceCapabilitiesGlobalHasPtp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the build supports PTP clocks as sources for SyncE."
    ::= { mgmtSynceCapabilitiesGlobal 2 }

mgmtSynceConfig OBJECT IDENTIFIER
    ::= { mgmtSynceMibObjects 2 }

mgmtSynceConfigGlobal OBJECT IDENTIFIER
    ::= { mgmtSynceConfig 1 }

mgmtSynceConfigGlobalClockSelectionMode OBJECT IDENTIFIER
    ::= { mgmtSynceConfigGlobal 1 }

mgmtSynceConfigGlobalClockSelectionModeSelectionMode OBJECT-TYPE
    SYNTAX      MGMTsynceSelectionMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The selection mode."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 1 }

mgmtSynceConfigGlobalClockSelectionModeSource OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Nominated source for manuel selection mode."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 2 }

mgmtSynceConfigGlobalClockSelectionModeWtrTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "WTR timer value in minutes. Range is 0 to 12 minutes where 0 means that
         the timer is disabled."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 3 }

mgmtSynceConfigGlobalClockSelectionModeSsmHoldover OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Tx overwrite SSM used when clock controller is hold over."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 4 }

mgmtSynceConfigGlobalClockSelectionModeSsmFreerun OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Tx overwrite SSM used when clock controller is free run."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 5 }

mgmtSynceConfigGlobalClockSelectionModeEecOption OBJECT-TYPE
    SYNTAX      MGMTsynceEecOption
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Synchronous Ethernet Equipment Clock option."
    ::= { mgmtSynceConfigGlobalClockSelectionMode 6 }

mgmtSynceConfigGlobalStationClocks OBJECT IDENTIFIER
    ::= { mgmtSynceConfigGlobal 2 }

mgmtSynceConfigGlobalStationClocksStationClkOut OBJECT-TYPE
    SYNTAX      MGMTsynceFrequency
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Station clock output frequency setting."
    ::= { mgmtSynceConfigGlobalStationClocks 1 }

mgmtSynceConfigGlobalStationClocksStationClkIn OBJECT-TYPE
    SYNTAX      MGMTsynceFrequency
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Station clock input frequency setting."
    ::= { mgmtSynceConfigGlobalStationClocks 2 }

mgmtSynceConfigSources OBJECT IDENTIFIER
    ::= { mgmtSynceConfig 2 }

mgmtSynceConfigSourcesClockSourceNominationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceConfigSourcesClockSourceNominationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the SyncE source nomination configuration."
    ::= { mgmtSynceConfigSources 1 }

mgmtSynceConfigSourcesClockSourceNominationEntry OBJECT-TYPE
    SYNTAX      MGMTSynceConfigSourcesClockSourceNominationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The sourceId index must be a value from 0 up to the number of sources
         minus one."
    INDEX       {                   mgmtSynceConfigSourcesClockSourceNominationSourceId }
    ::= { mgmtSynceConfigSourcesClockSourceNominationTable 1 }

MGMTSynceConfigSourcesClockSourceNominationEntry ::= SEQUENCE {
    mgmtSynceConfigSourcesClockSourceNominationSourceId      Integer32,
    mgmtSynceConfigSourcesClockSourceNominationNominated     TruthValue,
    mgmtSynceConfigSourcesClockSourceNominationNetworkPort   MGMTInterfaceIndex,
    mgmtSynceConfigSourcesClockSourceNominationClkInPort     MGMTUnsigned8,
    mgmtSynceConfigSourcesClockSourceNominationPriority      Unsigned32,
    mgmtSynceConfigSourcesClockSourceNominationAnegMode      MGMTsynceAnegMode,
    mgmtSynceConfigSourcesClockSourceNominationSsmOverwrite  MGMTsynceQualityLevel,
    mgmtSynceConfigSourcesClockSourceNominationHoldoffTime   Unsigned32
}

mgmtSynceConfigSourcesClockSourceNominationSourceId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 1 }

mgmtSynceConfigSourcesClockSourceNominationNominated OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates if source is nominated."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 2 }

mgmtSynceConfigSourcesClockSourceNominationNetworkPort OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index of the norminated source."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 3 }

mgmtSynceConfigSourcesClockSourceNominationClkInPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clock input of the norminated source."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 4 }

mgmtSynceConfigSourcesClockSourceNominationPriority OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Priority of the nominated source."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 5 }

mgmtSynceConfigSourcesClockSourceNominationAnegMode OBJECT-TYPE
    SYNTAX      MGMTsynceAnegMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Autonogotiation mode auto-master-slave."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 6 }

mgmtSynceConfigSourcesClockSourceNominationSsmOverwrite OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SSM overwrite quality."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 7 }

mgmtSynceConfigSourcesClockSourceNominationHoldoffTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hold Off timer value in 100ms (3 - 18). Zero means no hold off."
    ::= { mgmtSynceConfigSourcesClockSourceNominationEntry 8 }

mgmtSynceConfigPorts OBJECT IDENTIFIER
    ::= { mgmtSynceConfig 3 }

mgmtSynceConfigPortsPortConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceConfigPortsPortConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the SyncE port configuration."
    ::= { mgmtSynceConfigPorts 1 }

mgmtSynceConfigPortsPortConfigEntry OBJECT-TYPE
    SYNTAX      MGMTSynceConfigPortsPortConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The portId index must be a value from 0 up to the number of ports minus
         one."
    INDEX       { mgmtSynceConfigPortsPortConfigPortId }
    ::= { mgmtSynceConfigPortsPortConfigTable 1 }

MGMTSynceConfigPortsPortConfigEntry ::= SEQUENCE {
    mgmtSynceConfigPortsPortConfigPortId      MGMTInterfaceIndex,
    mgmtSynceConfigPortsPortConfigSsmEnabled  TruthValue
}

mgmtSynceConfigPortsPortConfigPortId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceConfigPortsPortConfigEntry 1 }

mgmtSynceConfigPortsPortConfigSsmEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Quality level via SSM enabled."
    ::= { mgmtSynceConfigPortsPortConfigEntry 2 }

mgmtSynceStatus OBJECT IDENTIFIER
    ::= { mgmtSynceMibObjects 3 }

mgmtSynceStatusGlobal OBJECT IDENTIFIER
    ::= { mgmtSynceStatus 1 }

mgmtSynceStatusGlobalClockSelectionMode OBJECT IDENTIFIER
    ::= { mgmtSynceStatusGlobal 1 }

mgmtSynceStatusGlobalClockSelectionModeClockInput OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The clock source locked to when clock selector is in locked state."
    ::= { mgmtSynceStatusGlobalClockSelectionMode 1 }

mgmtSynceStatusGlobalClockSelectionModeSelectorState OBJECT-TYPE
    SYNTAX      MGMTsynceSelectorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This is indicating the state of the clock selector."
    ::= { mgmtSynceStatusGlobalClockSelectionMode 2 }

mgmtSynceStatusGlobalClockSelectionModeLosx OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LOSX"
    ::= { mgmtSynceStatusGlobalClockSelectionMode 3 }

mgmtSynceStatusGlobalClockSelectionModeLol OBJECT-TYPE
    SYNTAX      MGMTsynceLolAlarmState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Clock selector has raised the Los Of Lock alarm."
    ::= { mgmtSynceStatusGlobalClockSelectionMode 4 }

mgmtSynceStatusGlobalClockSelectionModeDhold OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Clock selector has not yet calculated the holdover frequency offset to
         local oscillator."
    ::= { mgmtSynceStatusGlobalClockSelectionMode 5 }

mgmtSynceStatusSources OBJECT IDENTIFIER
    ::= { mgmtSynceStatus 2 }

mgmtSynceStatusSourcesClockSourceNominationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceStatusSourcesClockSourceNominationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the clock source nomination status."
    ::= { mgmtSynceStatusSources 1 }

mgmtSynceStatusSourcesClockSourceNominationEntry OBJECT-TYPE
    SYNTAX      MGMTSynceStatusSourcesClockSourceNominationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The sourceId index must be a value must be a value from 0 up to the
         number of sources minus one."
    INDEX       {                   mgmtSynceStatusSourcesClockSourceNominationSourceId }
    ::= { mgmtSynceStatusSourcesClockSourceNominationTable 1 }

MGMTSynceStatusSourcesClockSourceNominationEntry ::= SEQUENCE {
    mgmtSynceStatusSourcesClockSourceNominationSourceId  Integer32,
    mgmtSynceStatusSourcesClockSourceNominationLocs      TruthValue,
    mgmtSynceStatusSourcesClockSourceNominationFos       TruthValue,
    mgmtSynceStatusSourcesClockSourceNominationSsm       TruthValue,
    mgmtSynceStatusSourcesClockSourceNominationWtr       TruthValue
}

mgmtSynceStatusSourcesClockSourceNominationSourceId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceStatusSourcesClockSourceNominationEntry 1 }

mgmtSynceStatusSourcesClockSourceNominationLocs OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LOCS"
    ::= { mgmtSynceStatusSourcesClockSourceNominationEntry 2 }

mgmtSynceStatusSourcesClockSourceNominationFos OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "FOS"
    ::= { mgmtSynceStatusSourcesClockSourceNominationEntry 3 }

mgmtSynceStatusSourcesClockSourceNominationSsm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "SSM"
    ::= { mgmtSynceStatusSourcesClockSourceNominationEntry 4 }

mgmtSynceStatusSourcesClockSourceNominationWtr OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "WTR"
    ::= { mgmtSynceStatusSourcesClockSourceNominationEntry 5 }

mgmtSynceStatusPorts OBJECT IDENTIFIER
    ::= { mgmtSynceStatus 3 }

mgmtSynceStatusPortsPortStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceStatusPortsPortStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the port status."
    ::= { mgmtSynceStatusPorts 1 }

mgmtSynceStatusPortsPortStatusEntry OBJECT-TYPE
    SYNTAX      MGMTSynceStatusPortsPortStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The portId index must be a value must be a value from 0 up to the
         number of ports minus one."
    INDEX       { mgmtSynceStatusPortsPortStatusPortId }
    ::= { mgmtSynceStatusPortsPortStatusTable 1 }

MGMTSynceStatusPortsPortStatusEntry ::= SEQUENCE {
    mgmtSynceStatusPortsPortStatusPortId  MGMTInterfaceIndex,
    mgmtSynceStatusPortsPortStatusSsmRx   MGMTsynceQualityLevel,
    mgmtSynceStatusPortsPortStatusSsmTx   MGMTsynceQualityLevel,
    mgmtSynceStatusPortsPortStatusMaster  TruthValue
}

mgmtSynceStatusPortsPortStatusPortId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceStatusPortsPortStatusEntry 1 }

mgmtSynceStatusPortsPortStatusSsmRx OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Monitoring of the received SSM QL on this port."
    ::= { mgmtSynceStatusPortsPortStatusEntry 2 }

mgmtSynceStatusPortsPortStatusSsmTx OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Monitoring of the transmitted SSM QL on this port."
    ::= { mgmtSynceStatusPortsPortStatusEntry 3 }

mgmtSynceStatusPortsPortStatusMaster OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If PHY is in 1000BaseT Mode then this is monitoring the master/slave
         mode."
    ::= { mgmtSynceStatusPortsPortStatusEntry 4 }

mgmtSynceStatusPtp OBJECT IDENTIFIER
    ::= { mgmtSynceStatus 4 }

mgmtSynceStatusPtpPortStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceStatusPtpPortStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the PTP port status."
    ::= { mgmtSynceStatusPtp 1 }

mgmtSynceStatusPtpPortStatusEntry OBJECT-TYPE
    SYNTAX      MGMTSynceStatusPtpPortStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The sourceId index must be a value must be a value from 0 up to the
         number of PTP sources minus one."
    INDEX       { mgmtSynceStatusPtpPortStatusSourceId }
    ::= { mgmtSynceStatusPtpPortStatusTable 1 }

MGMTSynceStatusPtpPortStatusEntry ::= SEQUENCE {
    mgmtSynceStatusPtpPortStatusSourceId  Integer32,
    mgmtSynceStatusPtpPortStatusSsmRx     MGMTsynceQualityLevel,
    mgmtSynceStatusPtpPortStatusPtsf      MGMTsyncePtsfState
}

mgmtSynceStatusPtpPortStatusSourceId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-
         
         This object is only available if the capability object
         'mgmtSynceCapabilitiesGlobalHasPtp' is True."
    ::= { mgmtSynceStatusPtpPortStatusEntry 1 }

mgmtSynceStatusPtpPortStatusSsmRx OBJECT-TYPE
    SYNTAX      MGMTsynceQualityLevel
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Monitoring of the received SSM QL on this port.
         
         This object is only available if the capability object
         'mgmtSynceCapabilitiesGlobalHasPtp' is True."
    ::= { mgmtSynceStatusPtpPortStatusEntry 2 }

mgmtSynceStatusPtpPortStatusPtsf OBJECT-TYPE
    SYNTAX      MGMTsyncePtsfState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "PTSF status for PTP source.
         
         This object is only available if the capability object
         'mgmtSynceCapabilitiesGlobalHasPtp' is True."
    ::= { mgmtSynceStatusPtpPortStatusEntry 3 }

mgmtSynceControl OBJECT IDENTIFIER
    ::= { mgmtSynceMibObjects 4 }

mgmtSynceControlSources OBJECT IDENTIFIER
    ::= { mgmtSynceControl 1 }

mgmtSynceControlSourcesClockSourceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSynceControlSourcesClockSourceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the SyncE sources control structure."
    ::= { mgmtSynceControlSources 1 }

mgmtSynceControlSourcesClockSourceEntry OBJECT-TYPE
    SYNTAX      MGMTSynceControlSourcesClockSourceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The sourceId index must be a value must be a value from 0 up to the
         number of sources minus one."
    INDEX       { mgmtSynceControlSourcesClockSourceSourceId }
    ::= { mgmtSynceControlSourcesClockSourceTable 1 }

MGMTSynceControlSourcesClockSourceEntry ::= SEQUENCE {
    mgmtSynceControlSourcesClockSourceSourceId  Integer32,
    mgmtSynceControlSourcesClockSourceClearWtr  MGMTUnsigned8
}

mgmtSynceControlSourcesClockSourceSourceId OBJECT-TYPE
    SYNTAX      Integer32 (0..32767)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceControlSourcesClockSourceEntry 1 }

mgmtSynceControlSourcesClockSourceClearWtr OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "-"
    ::= { mgmtSynceControlSourcesClockSourceEntry 2 }

mgmtSynceMibConformance OBJECT IDENTIFIER
    ::= { mgmtSynceMib 2 }

mgmtSynceMibCompliances OBJECT IDENTIFIER
    ::= { mgmtSynceMibConformance 1 }

mgmtSynceMibGroups OBJECT IDENTIFIER
    ::= { mgmtSynceMibConformance 2 }

mgmtSynceCapabilitiesGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceCapabilitiesGlobalSourceCount,
                  mgmtSynceCapabilitiesGlobalHasPtp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 1 }

mgmtSynceConfigGlobalClockSelectionModeInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtSynceConfigGlobalClockSelectionModeSelectionMode,
                  mgmtSynceConfigGlobalClockSelectionModeSource,
                  mgmtSynceConfigGlobalClockSelectionModeWtrTime,
                  mgmtSynceConfigGlobalClockSelectionModeSsmHoldover,
                  mgmtSynceConfigGlobalClockSelectionModeSsmFreerun,
                  mgmtSynceConfigGlobalClockSelectionModeEecOption }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 2 }

mgmtSynceConfigGlobalStationClocksInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceConfigGlobalStationClocksStationClkOut,
                  mgmtSynceConfigGlobalStationClocksStationClkIn }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 3 }

mgmtSynceConfigSourcesClockSourceNominationInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtSynceConfigSourcesClockSourceNominationSourceId,
                  mgmtSynceConfigSourcesClockSourceNominationNominated,
                  mgmtSynceConfigSourcesClockSourceNominationNetworkPort,
                  mgmtSynceConfigSourcesClockSourceNominationClkInPort,
                  mgmtSynceConfigSourcesClockSourceNominationPriority,
                  mgmtSynceConfigSourcesClockSourceNominationAnegMode,
                  mgmtSynceConfigSourcesClockSourceNominationSsmOverwrite,
                  mgmtSynceConfigSourcesClockSourceNominationHoldoffTime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 4 }

mgmtSynceConfigPortsPortConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceConfigPortsPortConfigPortId,
                  mgmtSynceConfigPortsPortConfigSsmEnabled }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 5 }

mgmtSynceStatusGlobalClockSelectionModeInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceStatusGlobalClockSelectionModeClockInput,
                  mgmtSynceStatusGlobalClockSelectionModeSelectorState,
                  mgmtSynceStatusGlobalClockSelectionModeLosx,
                  mgmtSynceStatusGlobalClockSelectionModeLol,
                  mgmtSynceStatusGlobalClockSelectionModeDhold }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 6 }

mgmtSynceStatusSourcesClockSourceNominationInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtSynceStatusSourcesClockSourceNominationSourceId,
                  mgmtSynceStatusSourcesClockSourceNominationLocs,
                  mgmtSynceStatusSourcesClockSourceNominationFos,
                  mgmtSynceStatusSourcesClockSourceNominationSsm,
                  mgmtSynceStatusSourcesClockSourceNominationWtr }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 7 }

mgmtSynceStatusPortsPortStatusInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceStatusPortsPortStatusPortId,
                  mgmtSynceStatusPortsPortStatusSsmRx,
                  mgmtSynceStatusPortsPortStatusSsmTx,
                  mgmtSynceStatusPortsPortStatusMaster }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 8 }

mgmtSynceStatusPtpPortStatusInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceStatusPtpPortStatusSourceId,
                  mgmtSynceStatusPtpPortStatusSsmRx,
                  mgmtSynceStatusPtpPortStatusPtsf }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 9 }

mgmtSynceControlSourcesClockSourceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSynceControlSourcesClockSourceSourceId,
                  mgmtSynceControlSourcesClockSourceClearWtr }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSynceMibGroups 10 }

mgmtSynceMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtSynceCapabilitiesGlobalInfoGroup,
                       mgmtSynceConfigGlobalClockSelectionModeInfoGroup,
                       mgmtSynceConfigGlobalStationClocksInfoGroup,
                       mgmtSynceConfigSourcesClockSourceNominationInfoGroup,
                       mgmtSynceConfigPortsPortConfigInfoGroup,
                       mgmtSynceStatusGlobalClockSelectionModeInfoGroup,
                       mgmtSynceStatusSourcesClockSourceNominationInfoGroup,
                       mgmtSynceStatusPortsPortStatusInfoGroup,
                       mgmtSynceStatusPtpPortStatusInfoGroup,
                       mgmtSynceControlSourcesClockSourceInfoGroup }

    ::= { mgmtSynceMibCompliances 1 }

END
