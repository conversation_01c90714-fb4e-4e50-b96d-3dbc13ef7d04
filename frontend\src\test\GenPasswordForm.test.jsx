import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import GenPasswordForm from '../components/auths/GenPasswordForm';

describe('GenPasswordForm', () => {
  const onCreateMock = vi.fn();
  const onCancelMock = vi.fn();

  it('renders GenPasswordForm component with the form', () => {
    render(
      <GenPasswordForm
        open={true}
        onCreate={onCreateMock}
        onCancel={onCancelMock}
        loadingGenPass={false}
      />
    );

    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Temprory Password')).toBeInTheDocument();
    expect(screen.getByText('Generate Password')).toBeInTheDocument();
  });

  it('calls onCancel when clicking Cancel button', () => {
    render(
      <GenPasswordForm
        open={true}
        onCreate={onCreateMock}
        onCancel={onCancelMock}
        loadingGenPass={false}
      />
    );

    fireEvent.click(screen.getByText('Cancel'));

    expect(onCancelMock).toHaveBeenCalled();
  });

  it('resets form fields when clicking Cancel button', async () => {
    render(
      <GenPasswordForm
        open={true}
        onCreate={onCreateMock}
        onCancel={onCancelMock}
        loadingGenPass={false}
      />
    );

    fireEvent.change(screen.getByLabelText('Email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Temprory Password'), { target: { value: 'password@123' } });

    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => {
      expect(screen.getByLabelText('Email')).toHaveValue('');
      expect(screen.getByLabelText('Temprory Password')).toHaveValue('');
    });
  });

});
