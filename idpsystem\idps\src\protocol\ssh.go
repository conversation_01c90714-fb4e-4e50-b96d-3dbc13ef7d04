package protocol

import (
	"encoding/binary"
	"errors"
	"fmt"
	"strings"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

//var Layerssh = gopacket.RegisterLayerType(int(sshProto), gopacket.LayerTypeMetadata{Name: sshProto.String(), Decoder: gopacket.DecodeFunc(decodeSSh)})

type sshParser struct {
}

func (s *sshParser) Parse(pack gopacket.Packet) bool {
	tp := NewTcpParser()
	b := tp.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeSSh(tp.GetTcp().Payload)
	return err == nil

}

type ssh struct {
	layers.BaseLayer
}

func decodeSSh(data []byte) (*ssh, error) {
	s := &ssh{}
	err := s.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return s, nil

}

func (s *ssh) DecodeFromBytes(data []byte) error {
	err := sshdecode(data)
	if err != nil {
		return err
	}
	return nil
}

const (
	msgDisconnect           = 1
	msgKexInit              = 20
	msgKexDHInit            = 30
	msgKexDHReply           = 31
	msgServiceRequest       = 5
	msgServiceAccept        = 6
	msgExtInfo              = 7
	msgUserAuthRequest      = 50
	msgUserAuthFailure      = 51
	msgUserAuthSuccess      = 52
	msgChannelOpen          = 90
	msgChannelData          = 94
	msgChannelOpenConfirm   = 91
	msgChannelOpenFailure   = 92
	msgChannelRequest       = 98
	msgChannelSuccess       = 99
	msgChannelFailure       = 100
	msgChannelClose         = 97
	msgChannelEOF           = 96
	msgGlobalRequest        = 80
	msgRequestSuccess       = 81
	msgRequestFailure       = 82
	msgChannelWindowAdjust  = 93
	msgUserAuthPubKeyOk     = 60
	msgUserAuthGSSAPIToken  = 61
	msgUserAuthGSSAPIMIC    = 66
	msgUserAuthGSSAPIErrTok = 64
	msgUserAuthGSSAPIError  = 65
)

// Decode a packet into its corresponding message.
func sshdecode(packet []byte) error {
	if len(packet) < 6 {
		return errors.New("ssh packet error")
	}
	payloadStr := string(packet)
	hasSuffix := strings.HasSuffix(payloadStr, "\n")
	hasSSHStr := strings.HasPrefix(payloadStr, "SSH") || strings.Contains(payloadStr, "OpenSSH")
	if hasSuffix && hasSSHStr {
		return nil
	}
	//calculator len
	br := false
	length := binary.BigEndian.Uint32(packet[0:4])
	paylen := len(packet[4:])
	if length == uint32(paylen) {
		br = true
	}
	pr := false
	p := packet[5]
	if _, ok := packetTypeNames[p]; ok {
		pr = true
	}
	if br && pr {
		return nil
	} else {
		return unexpectedMessageError(0, packet[5])
	}

}

func unexpectedMessageError(expected, got uint8) error {
	return fmt.Errorf("ssh: unexpected message type %d (expected %d)", got, expected)
}

var packetTypeNames = map[byte]string{
	msgDisconnect:          "disconnectMsg",
	msgServiceRequest:      "serviceRequestMsg",
	msgServiceAccept:       "serviceAcceptMsg",
	msgExtInfo:             "extInfoMsg",
	msgKexInit:             "kexInitMsg",
	msgKexDHInit:           "kexDHInitMsg",
	msgKexDHReply:          "kexDHReplyMsg",
	msgUserAuthRequest:     "userAuthRequestMsg",
	msgUserAuthSuccess:     "userAuthSuccessMsg",
	msgUserAuthFailure:     "userAuthFailureMsg",
	msgUserAuthPubKeyOk:    "userAuthPubKeyOkMsg",
	msgGlobalRequest:       "globalRequestMsg",
	msgRequestSuccess:      "globalRequestSuccessMsg",
	msgRequestFailure:      "globalRequestFailureMsg",
	msgChannelOpen:         "channelOpenMsg",
	msgChannelData:         "channelDataMsg",
	msgChannelOpenConfirm:  "channelOpenConfirmMsg",
	msgChannelOpenFailure:  "channelOpenFailureMsg",
	msgChannelWindowAdjust: "windowAdjustMsg",
	msgChannelEOF:          "channelEOFMsg",
	msgChannelClose:        "channelCloseMsg",
	msgChannelRequest:      "channelRequestMsg",
	msgChannelSuccess:      "channelRequestSuccessMsg",
	msgChannelFailure:      "channelRequestFailureMsg",
}
