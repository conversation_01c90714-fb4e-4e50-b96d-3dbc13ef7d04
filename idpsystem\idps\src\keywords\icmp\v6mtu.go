package icmp

import (
	"fmt"
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"golang.org/x/net/icmp"
)

func NewV6Mtu() *mtu {
	return &mtu{layer: protocol.NewIcmpParser()}
}

type mtu struct {
	layer         *protocol.IcmpParser
	min, num, max uint32
	ck            func(p uint32) bool
}

func (i *mtu) SetUp(input any) error {
	v, err := gonidutil.ConvertToLenMatch(input)
	if err != nil {
		return err
	}
	ck, err := i.creatFunc(v.Operator)
	if err != nil {
		return err
	}
	i.num = uint32(v.Num)
	i.min = uint32(v.Min)
	i.max = uint32(v.Max)
	i.ck = ck
	return nil
}

func (i *mtu) Match(packet gopacket.Packet) bool {
	b := i.layer.Parse(packet)
	if !b {
		return false
	}
	proto, lay, err := i.layer.GetIcmp()
	if err != nil {
		return false
	}
	if proto == layers.IPProtocolICMPv6 {
		b := make([]byte, 0, len(lay.LayerContents())+len(lay.LayerPayload()))
		b = append(b, lay.LayerContents()...)
		b = append(b, lay.LayerPayload()...)
		msg, err := icmp.ParseMessage(int(layers.IPProtocolICMPv6), b)
		if err != nil {
			return false
		}

		if pkt, ok := msg.Body.(*icmp.PacketTooBig); ok {
			if i.ck != nil {
				return i.ck(uint32(pkt.MTU))
			}
		}
	}

	return false
}

func (i *mtu) creatFunc(op string) (func(uint32) bool, error) {
	switch op {
	case ">":
		return func(p uint32) bool { return p > i.num }, nil
	case "<":
		return func(p uint32) bool { return p < i.num }, nil
	case "<>":
		return func(p uint32) bool { return p > i.min && p < i.max }, nil
	case "":
		return func(p uint32) bool { return p == i.num }, nil
	default:
		return nil, fmt.Errorf("not supported Operator:%v", op)
	}
}
