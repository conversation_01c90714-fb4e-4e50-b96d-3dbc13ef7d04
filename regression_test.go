package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/influxdata/go-syslog/v3"
)

var rootsvcPath = "bbrootsvc"
var nmssvcPath = "bbnmssvc"
var logsvcPath = "bblogsvc"
var anomsvcPath = "bbanomsvc"
var ctlPath = "bbctl"

var passedItems = []string{}
var failedItems = []string{}

func killProcess() {
	// kill bbrootsvc, bbnmssvc
	var regressionTestServices = []string{"bbrootsvc", "bbnmssvc", "bblogsvc", "bbanomsvc"}
	for _, svc := range regressionTestServices {
		if runtime.GOOS == "windows" {
			exec.Command("taskkill", "/IM", svc+".exe", "/F").Run()
		} else {
			exec.Command("pkill", svc).Run()
		}
	}
}

func build() {
	fmt.Println("Building bbnim")
	cmd := "make"
	exec.Command("bash", "-c", cmd).Run()

	var exe string
	if runtime.GOOS == "windows" {
		exe = ".exe"
	}

	rootsvcPath = filepath.Join("bbrootsvc", "bbrootsvc"+exe)
	nmssvcPath = filepath.Join("bbnmssvc", "bbnmssvc"+exe)
	logsvcPath = filepath.Join("bblogsvc", "bblogsvc"+exe)
	anomsvcPath = filepath.Join("bbanomsvc", "bbanomsvc"+exe)
	ctlPath = filepath.Join("bbctl", "bbctl"+exe)

	files := []string{rootsvcPath, nmssvcPath, logsvcPath, anomsvcPath, ctlPath}
	for _, file := range files {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Printf("%s not found\n", file)
			os.Exit(1)
		}
	}
}

var regressionRootUrl = "http://localhost:27182"
var regressionNMS1Url = "http://localhost:27183"
var regressionNMS2Url = "http://localhost:27184"

func runProcess() {
	// run
	fmt.Println("Starting bbnim: bbrootsvc, bbnmssvc, bblogsvc, bbanomsvc")
	p, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	fmt.Println("Starting bbrootsvc on port: ", p)
	cmd := exec.Command(rootsvcPath, "-n", "root", "-p", fmt.Sprintf("%d", p))
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting bbrootsvc: %s\n", err)
		os.Exit(1)
	}
	time.Sleep(1 * time.Second)

	type SvcInfo struct {
		Name string   `json:"name"`
		Path string   `json:"path"`
		Ars  []string `json:"args"`
	}

	regressionRootUrl = "http://localhost:" + fmt.Sprintf("%d", p)
	fmt.Println("Root url: ", regressionRootUrl)

	nmsPort, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	regressionNMS1Url = "http://localhost:" + fmt.Sprintf("%d", nmsPort)
	fmt.Println("NMS1 url: ", regressionNMS1Url)
	svcs := []SvcInfo{
		{"bbnmssvc", nmssvcPath, []string{"-n", "nms", "-r", regressionRootUrl, "-mb", ":11883", "-rs", ":5514", "-p", fmt.Sprintf("%d", nmsPort), "-ir", "5"}},
		{"bblogsvc", logsvcPath, []string{"-n", "log", "-r", regressionRootUrl}},
		{"bbanomsvc", anomsvcPath, []string{"-n", "anom", "-r", regressionRootUrl}},
	}

	for _, svc := range svcs {
		cmd := exec.Command(svc.Path, svc.Ars...)
		err := cmd.Start()
		if err != nil {
			fmt.Printf("Failed starting %s: %s\n", svc.Name, err)
			os.Exit(1)
		}
	}

	time.Sleep(10 * time.Second)
}

func login() (string, error) {
	body := `{"user":"admin","password":"default"}`
	resp, err := http.Post(regressionRootUrl+"/api/v1/login", "application/json", strings.NewReader(body))
	if err != nil {
		fmt.Printf("Failed making POST request: %s\n", err)
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("expected status OK but got: %v", resp.StatusCode)
	}
	fmt.Println("Login success")
	var loginResp struct {
		Token string `json:"token"`
		Role  string `json:"role"`
		User  string `json:"user"`
	}
	err = json.NewDecoder(resp.Body).Decode(&loginResp)
	if err != nil {
		fmt.Printf("Failed unmarshaling response: %s\n", err)
		return "", err
	}
	return loginResp.Token, nil
}

func getDeviceList(token string) (map[string]DevInfo, error) {
	// GET /api/v1/devices
	url := regressionRootUrl + "/api/v1/devices"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var devices map[string]DevInfo
	err = json.NewDecoder(resp.Body).Decode(&devices)
	if err != nil {
		fmt.Printf("Failed unmarshaling response: %s\n", err)
		return nil, err
	}
	_, ok := devices["11-22-33-44-55-66"]
	if ok {
		delete(devices, "11-22-33-44-55-66")
	}
	return devices, nil
}

func ctlCommand(commands [][]string) error {
	for _, cmd := range commands {
		// fmt.Println(ctl, cmd)
		if cmd[0] != "util" {
			cmd = append([]string{"-r", regressionRootUrl}, cmd...)
		}
		exec.Command(ctlPath, cmd...).Run()
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

func queryCmdData(token string) (map[string]CmdInfo, error) {
	url := regressionRootUrl + "/api/v1/commands?cmd=all"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	cmddata := make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmddata)
	if err != nil {
		return nil, err
	}
	return cmddata, nil
}

func validateCommandsStatus(commands [][]string, token string) error {
	cmddata, err := queryCmdData(token)
	if err != nil {
		return err
	}

	for _, cmd := range commands {

		// if cmd has "-", remove them and the next item
		for cmd[0][0] == '-' {
			if cmd[0] == "-ck" || cmd[0] == "-cc" {
				cmd = cmd[2:]
			}
		}

		item := strings.Join(cmd, " ")
		cmdinfo, ok := cmddata[item]
		if !ok {
			agentCmd := "agent " + item
			cmdinfo, ok = cmddata[agentCmd]
			if !ok {
				snmpCmd := "snmp " + item
				cmdinfo, ok = cmddata[snmpCmd]
				if !ok {
					gwdCmd := item
					if strings.HasPrefix(item, "config network set") {
						// remove the last one (dhcp)
						gwdCmd = strings.Join(cmd[:len(cmd)-1], " ")
					}
					gwdCmd = "gwd " + gwdCmd
					cmdinfo, ok = cmddata[gwdCmd]
					if !ok {
						fmt.Println("Command not found: ", item)
						failedItems = append(failedItems, item)
						continue
					} else {
						item = gwdCmd
					}
				} else {
					item = snmpCmd
				}
			} else {
				item = agentCmd
			}
		}

		// retry if status is empty, wait 10 seconds and get new cmddata, 3 times
		for i := 0; i < 3; i++ {
			if len(cmdinfo.Status) >= 0 {
				break
			}
			time.Sleep(10 * time.Second)
			cmddata, _ = queryCmdData(token)
			cmdinfo, _ = cmddata[item]
		}

		if cmdinfo.Status != "ok" {
			item += ": " + cmdinfo.Status
			failedItems = append(failedItems, item)
		} else {
			passedItems = append(passedItems, item)
		}
	}
	return nil
}

var fakeDevices = map[string]DevInfo{}

func devTest(token string) error {
	fmt.Println("Running dev test")
	devices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	fmt.Println("Devices number: ", len(devices))
	if len(devices) == 0 {
		return fmt.Errorf("no devices found")
	}

	// dev syslog test
	// get old syslog first
	commands := make([][]string, 0)
	type devSyslog struct {
		Status bool   `json:"status"`
		Result string `json:"result"`
	}
	devSyslogStatus := make(map[string]devSyslog)
	for _, dev := range devices {
		// looks like snmp must be enabled first
		commands = append(commands, []string{"snmp", "enable", dev.Mac})
		commands = append(commands, []string{"config", "syslog", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(180 * time.Second)
	cmdData, err := queryCmdData(token)
	if err != nil {
		return err
	}
	for _, cmd := range commands {
		if cmd[0] != "config" {
			continue
		}
		mac := cmd[3]

		item := strings.Join(cmd, " ")
		cmdinfo, ok := cmdData[item]
		if !ok {
			agentCmd := "agent " + item
			cmdinfo, ok = cmdData[agentCmd]
			if !ok {
				snmpCmd := "snmp " + item
				cmdinfo, ok = cmdData[snmpCmd]
				if !ok {
					fmt.Println("Command not found: ", item)
					failedItems = append(failedItems, item)
					continue
				}
				item = snmpCmd
			} else {
				item = agentCmd
			}
		}

		if cmdinfo.Status != "ok" {
			devSyslogStatus[mac] = devSyslog{false, ""}
			failedItems = append(failedItems, strings.Join(cmd, " ")+": "+cmdData[strings.Join(cmd, " ")].Status)
		} else {
			devSyslogStatus[mac] = devSyslog{true, cmdData[strings.Join(cmd, " ")].Result}
		}
	}

	// random number (for hostname and syslog port)
	// 1000-9999
	randNum := 1000 + rand.Intn(9000)
	rand4char := fmt.Sprintf("%04d", randNum)
	radomhost := "host" + rand4char
	fmt.Println("Random hostname: ", radomhost)
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"beep", dev.Mac})
		if devSyslogStatus[dev.Mac].Status {
			commands = append(commands, []string{"config", "syslog", "set", dev.Mac, "1", dev.IPAddress, rand4char, "1", "1"})
		}
	}
	ctlCommand(commands)
	time.Sleep(2 * time.Minute)

	// validate
	err = validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// validate device syslog if set successfully
	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}
	commands = make([][]string, 0)
	for _, dev := range devices {
		cmdinfo, ok := cmdData["config syslog set "+dev.Mac+" 1 "+dev.IPAddress+" "+rand4char+" 1 1"]
		if !ok {
			continue
		}
		cmdStatus := cmdinfo.Status
		if cmdStatus != "ok" {
			continue
		}
		// get again
		commands = append(commands, []string{"config", "syslog", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(30 * time.Second)
	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}

	reverseCommands := make([][]string, 0)
	for _, cmd := range commands {
		cmdStatus := cmdData[strings.Join(cmd, " ")].Status
		if cmdStatus != "ok" {
			failedItems = append(failedItems, strings.Join(cmd, " ")+": "+cmdStatus)
			continue
		}
		cmdResult := cmdData[strings.Join(cmd, " ")].Result
		actual := SyslogStatus{}
		err = json.Unmarshal([]byte(cmdResult), &actual)
		if err != nil {
			failedItems = append(failedItems, strings.Join(cmd, " ")+": "+err.Error())
			continue
		}
		ipaddr := devices[cmd[3]].IPAddress
		if actual.Status != "1" || actual.Serverip != ipaddr || actual.ServerPort != rand4char || actual.Level != "1" || actual.LogToFlash != "1" {
			failedItems = append(failedItems, strings.Join(cmd, " ")+": expected: 1 "+ipaddr+" "+rand4char+" 1 1, got: "+actual.Status+" "+actual.Serverip+" "+actual.ServerPort+" "+actual.Level+" "+actual.LogToFlash)
		} else {
			passedItems = append(passedItems, strings.Join(cmd, " "))

			//set old syslog back
			if devSyslogStatus[cmd[3]].Status {
				var oldSyslog SyslogStatus
				err = json.Unmarshal([]byte(devSyslogStatus[cmd[3]].Result), &oldSyslog)
				if err != nil {
					return err
				}
				reverseCommands = append(reverseCommands, []string{"config", "syslog", "set", cmd[3], oldSyslog.Status, oldSyslog.Serverip, oldSyslog.ServerPort, oldSyslog.Level, oldSyslog.LogToFlash})
			}
		}
	}
	ctlCommand(reverseCommands)
	time.Sleep(2 * time.Minute)

	commands = make([][]string, 0)
	// test config network set
	for _, dev := range devices {
		commands = append(commands, []string{"config", "network", "set", dev.Mac, dev.IPAddress, dev.IPAddress, dev.Netmask, dev.Gateway, radomhost, "0"})
	}
	ctlCommand(commands)
	time.Sleep(2 * time.Minute)
	err = validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}
	// validate device network by device list
	newDevices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}
	for _, dev := range devices {
		mac := dev.Mac
		// model := dev.ModelName
		item := "config network set " + mac + " " + dev.IPAddress + " " + dev.IPAddress + " " + dev.Netmask + " " + dev.Gateway + " " + radomhost + " 0"
		cmdinfo, ok := cmdData[item]
		if !ok {
			continue
		}
		if cmdinfo.Status != "ok" {
			continue
		}
		if newDevices[mac].Hostname != radomhost {
			extra := fmt.Sprintf("expected hostname: %s", radomhost)
			failedItems = append(failedItems, item, extra)
		} else {
			passedItems = append(passedItems, item)
			// set old hostname back
			// commands = append(commands, []string{"config", "network", "set", mac, dev.IPAddress, dev.IPAddress, dev.Netmask, dev.Gateway, dev.Hostname, "0"})
		}
	}

	return nil
}

func mqttTest(token string) error {
	fmt.Println("Running mqtt test")

	commands := make([][]string, 0)
	commands = append(commands, []string{"mqtt", "sub", "localhost:11883", "topictest"})
	commands = append(commands, []string{"mqtt", "pub", "localhost:11883", "topictest", "hello this is a test message"})
	commands = append(commands, []string{"mqtt", "unsub", "localhost:11883", "topictest"})
	commands = append(commands, []string{"mqtt", "list"})

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate commands
	err := validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// validate mqtt sub
	url := regressionRootUrl + "/api/v1/syslogs?number=10"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	logs := []syslog.Base{}
	err = json.NewDecoder(resp.Body).Decode(&logs)
	if err != nil {
		return err
	}
	found := false
	for _, log := range logs {
		if strings.Contains(*log.Message, "hello this is a test message") {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("expected message not found")
	}

	return nil
}

func wgTest(token string) error {
	fmt.Println("Running wg test")
	commands := make([][]string, 0)
	commands = append(commands, []string{"-ck", "root", "wg", "config", "interface", "set", "**********/24", "55820"})
	commands = append(commands, []string{"-ck", "root", "wg", "config", "peer", "add", "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=", "10.253.0.2/32"})
	commands = append(commands, []string{"wg", "config", "interface", "set", "10.253.0.2/32"})
	commands = append(commands, []string{"wg", "config", "peer", "add", "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=", "10.253.0.0/24", "122.147.151.234:55820", "30"})

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate
	err := validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}
	// wg api
	url := regressionRootUrl + "/api/v1/wg"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	wgData := make(map[string]WgInfo)
	err = json.NewDecoder(resp.Body).Decode(&wgData)
	if err != nil {
		return err
	}
	if len(wgData) != 2 {
		return fmt.Errorf("expected 2 wg interfaces but got: %v", len(wgData))
	}
	return nil
}

func tcpproxyTest(token string) error {
	fmt.Println("Running tcpproxy test")
	commands := make([][]string, 0)
	commands = append(commands, []string{"tcpproxy", "start", ":1234", ":5678"})

	// run commands
	ctlCommand(commands)
	time.Sleep(15 * time.Second)

	// validate
	err := validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// tcpproxy api
	proxyData := make(map[string]map[string]TcpProxyInfo)
	url := regressionRootUrl + "/api/v1/tcpproxy"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(&proxyData)
	if err != nil {
		return err
	}
	if len(proxyData) != 1 {
		return fmt.Errorf("expected 1 tcpproxy but got: %v", len(proxyData))
	}

	// stop tcpproxy
	defer func() {
		commands = make([][]string, 0)
		commands = append(commands, []string{"tcpproxy", "stop", ":1234"})
		ctlCommand(commands)
	}()
	return nil
}

func logsvcTest(token string) error {
	fmt.Println("Running logsvc test")
	commands := make([][]string, 0)
	commands = append(commands, []string{"-cc", "log", "syslog", "config", "maxsize", "500"})
	commands = append(commands, []string{"-cc", "log", "syslog", "config", "compress", "true"})
	commands = append(commands, []string{"-cc", "log", "syslog", "list"})
	commands = append(commands, []string{"-cc", "log", "syslog", "geturl", "all", "5"})

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate
	err := validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// check geturl result
	cmddata, err := queryCmdData(token)
	if err != nil {
		return err
	}
	if cmddata["syslog geturl syslog_log 5"].Status == "ok" {
		result := cmddata["syslog geturl syslog_log 5"].Result
		// should be a url can get content
		resp, err := GetWithToken(result, token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
	}

	return nil
}

func syslogTest(token string) error {
	fmt.Println("Running syslog test")
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/syslog", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}

/*
***Will restart nimbl, make sure this is the last test or understand what's going on***
 */
func authTest(token string) error {
	fmt.Println("Running auth test")
	commands := make([][]string, 0)
	commands = append(commands, []string{"util", "genpair", "-name", "newkey"})
	commands = append(commands, []string{"util", "export", "config", "-pubkey", "newkey.pub", "-out", "123"})
	commands = append(commands, []string{"util", "decrypt", "config", "-privkey", "newkey", "-in", "123", "-out", "456"})

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate 123, 456 exist
	if _, err := os.Stat("123"); os.IsNotExist(err) {
		return err
	}
	if _, err := os.Stat("456"); os.IsNotExist(err) {
		return err
	}
	defer os.Remove("newkey")
	defer os.Remove("newkey.pub")
	defer os.Remove("123")
	defer os.Remove("456")

	// modify 456, which is a json file in UserConfig format
	// load 456, ex: {"users":[{"name":"admin","email":"","role":"admin","password":"default","enable2FA":false,"secret":""}]}
	var users MNMSConfig
	jsonBytes, err := os.ReadFile("456")
	if err != nil {
		return err
	}
	err = json.Unmarshal(jsonBytes, &users)
	if err != nil {
		return err
	}
	users.Users = append(users.Users, UserConfig{Name: "test", Email: "", Role: "user", Password: "default", Enable2FA: false, Secret: ""})
	jsonBytes, err = json.Marshal(users)
	if err != nil {
		return err
	}
	// overwrite 456
	err = os.WriteFile("456", jsonBytes, 0644)
	if err != nil {
		return err
	}

	// encrypt 456
	commands = make([][]string, 0)
	commands = append(commands, []string{"util", "encrypt", "config", "-pubkey", "newkey.pub", "-in", "456", "-out", "789"})
	ctlCommand(commands)

	if _, err := os.Stat("789"); os.IsNotExist(err) {
		return err
	}
	defer os.Remove("789")

	// run nimbl with newkey
	killProcess()
	time.Sleep(2 * time.Second)

	p, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	cmd := exec.Command(rootsvcPath, "-n", "root", "-privkey", "newkey", "-p", fmt.Sprintf("%d", p))
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting bbrootsvc: %s\n", err)
		os.Exit(1)
	}
	regressionRootUrl = "http://localhost:" + fmt.Sprintf("%d", p)
	time.Sleep(5 * time.Second)

	// update config
	commands = make([][]string, 0)
	commands = append(commands, []string{"util", "import", "config", "-in", "789", "-root", regressionRootUrl})
	ctlCommand(commands)

	// get users to see if test user is added
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/users", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	var users2 []UserConfig
	err = json.NewDecoder(resp.Body).Decode(&users2)
	if err != nil {
		return err
	}
	found := false
	for _, user := range users2 {
		if user.Name == "test" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("config not updated")
	}
	return nil
}

func agentTest(token string) error {
	fmt.Println("Running agent test")
	devices, err := getDeviceList(token)
	if err != nil {
		return err
	}

	// get old syslog first
	commands := make([][]string, 0)
	type devSyslog struct {
		Status bool   `json:"status"`
		Result string `json:"result"`
	}
	devSyslogStatus := make(map[string]devSyslog)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "config", "syslog", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(10 * time.Second)
	cmdData, err := queryCmdData(token)
	if err != nil {
		return err
	}
	for _, cmd := range commands {
		mac := cmd[3]
		if cmdData[strings.Join(cmd, " ")].Status != "ok" {
			devSyslogStatus[mac] = devSyslog{false, ""}
			failedItems = append(failedItems, strings.Join(cmd, " "))
		} else {
			devSyslogStatus[mac] = devSyslog{true, cmdData[strings.Join(cmd, " ")].Result}
		}
	}

	rand4char := fmt.Sprintf("%04d", rand.Intn(10000))
	tmp := "reg" + rand4char
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "beep", dev.Mac})
		commands = append(commands, []string{"agent", "snmp", "enable", dev.Mac, "1"})
		if devSyslogStatus[dev.Mac].Status {
			commands = append(commands, []string{"agent", "config", "syslog", "set", dev.Mac, "1", dev.IPAddress, "6624", "1", "1"})
		}
		commands = append(commands, []string{"agent", "snmp", "trap", "add", dev.Mac, dev.IPAddress, "6622", "public"})
		commands = append(commands, []string{"agent", "devinfo", "send", dev.Mac})
		commands = append(commands, []string{"agent", "topologyinfo", "send", dev.Mac})
		commands = append(commands, []string{"agent", "config", "network", "set", dev.Mac, dev.IPAddress, dev.Netmask, dev.Gateway, tmp, "0"})
	}

	// run commands ( network set may take a while )
	ctlCommand(commands)
	time.Sleep(1 * time.Minute)

	// validate
	err = validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// check syslog
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "config", "syslog", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(10 * time.Second)
	// result should be SyslogStatus

	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}
	for _, dev := range devices {
		expect := SyslogStatus{
			Status:     "1",
			Serverip:   dev.IPAddress,
			ServerPort: "6624",
			Level:      "1",
			LogToFlash: "1",
		}
		jsonBytes, err := json.Marshal(expect)
		if err != nil {
			return err
		}
		if cmdData["agent config syslog get "+dev.Mac].Result != string(jsonBytes) {
			failedItems = append(failedItems, "agent config syslog get "+dev.Mac)
		} else {
			passedItems = append(passedItems, "agent config syslog get "+dev.Mac)
		}
	}

	// check trap
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "snmp", "trap", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(10 * time.Second)
	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}
	for _, dev := range devices {
		// type trapInfo struct {
		// 	ServerIp string `json:"server_ip"`
		// 	ServerPort string `json:"server_port"`
		// 	Community string `json:"community"`
		// }
		// expect := trapInfo{
		// 	ServerIp: dev.IPAddress,
		// 	ServerPort: "6622",
		// 	Community: "public",
		// }
		// result is an array of trapInfo, check if ip port community is correct
		result := cmdData["agent snmp trap get "+dev.Mac].Result
		if strings.Contains(result, dev.IPAddress) && strings.Contains(result, "6622") && strings.Contains(result, "public") {
			passedItems = append(passedItems, "agent snmp trap add "+dev.Mac)
		} else {
			failedItems = append(failedItems, "agent snmp trap add "+dev.Mac)
		}

	}
	// delete trap
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "snmp", "trap", "delete", dev.Mac, dev.IPAddress, "6622", "public"})
	}
	ctlCommand(commands)
	time.Sleep(10 * time.Second)
	// check if trap is deleted
	commands = make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"agent", "snmp", "trap", "get", dev.Mac})
	}
	ctlCommand(commands)
	time.Sleep(10 * time.Second)
	cmdData, err = queryCmdData(token)
	if err != nil {
		return err
	}
	for _, dev := range devices {
		result := cmdData["agent snmp trap get "+dev.Mac].Result
		if strings.Contains(result, dev.IPAddress) && strings.Contains(result, "6622") && strings.Contains(result, "public") {
			failedItems = append(failedItems, "agent snmp trap delete "+dev.Mac)
		} else {
			passedItems = append(passedItems, "agent snmp trap delete "+dev.Mac)
		}
	}

	// check network
	newDevices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	commands = make([][]string, 0)
	for _, dev := range devices {
		mac := dev.Mac
		model := dev.ModelName
		item := "agent network config set " + mac + " " + model
		if newDevices[mac].Hostname != rand4char {
			failedItems = append(failedItems, item)
		} else {
			passedItems = append(passedItems, item)

			// set old hostname back
			commands = append(commands, []string{"agent", "config", "network", "set", mac, dev.IPAddress, dev.Netmask, dev.Gateway, dev.Hostname, "0"})
		}
	}
	// run commands
	ctlCommand(commands)
	time.Sleep(1 * time.Minute)

	return nil
}

func putWithToken(url, token string, body io.Reader) (*http.Response, error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("PUT", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func deleteWithToken(url, token string, body io.Reader) (*http.Response, error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("DELETE", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func userTest(token string) error {
	fmt.Println("Running user test")
	// add user : resp=$(curl -s -X POST -H "Authorization: Bearer $received_token" -d '{ "name": "abc1", "email": "<EMAIL>", "password": "Pas$Word1" , "role": "user"}' $URL/api/v1/users)
	url := regressionRootUrl + "/api/v1/users"
	body := `{"name": "abc1", "email": "<EMAIL>", "password": "Pas$Word1", "role": "user"}`
	resp, err := PostWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get user list
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// check if user is in the list
	var users []UserConfig
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found := false
	for _, user := range users {
		if user.Name == "abc1" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("user not found")
	}

	// update user, PUT /api/v1/users
	body = `{"name": "abc1", "email": "<EMAIL>", "password": "Pas$Word2", "role": "user"}`
	resp, err = putWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	// check if user is updated
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found = false
	for _, user := range users {
		if user.Name == "abc1" && user.Email == "<EMAIL>" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("user not found")
	}

	// delete user DELETE /api/v1/users
	body = `{"name": "abc1"}`
	resp, err = deleteWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	// check if user is deleted
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found = false
	for _, user := range users {
		if user.Name == "abc1" {
			found = true
			break
		}
	}
	if found {
		return fmt.Errorf("user not deleted")
	}
	return nil
}

func registerTest(token string) error {
	fmt.Println("Running register test")
	fakeClient := ClientInfo{
		Name:       "fake",
		NumDevices: 100,
		NumCmds:    100,
		Start:      int(time.Now().Unix()),
		Now:        int(time.Now().Unix()),
	}
	jsonBytes, err := json.Marshal(fakeClient)
	if err != nil {
		return err
	}
	resp, err := PostWithToken(regressionRootUrl+"/api/v1/register", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("post register failed")
	}

	// check if the client is registered
	resp, err = GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients)
	if err != nil {
		return err
	}
	found := false
	for name, _ := range clients {
		if name == "fake" {
			found = true
		}
	}
	if !found {
		return fmt.Errorf("client not found")
	}

	return nil
}

func clientsTest(token string) error {
	fmt.Println("Running clients test")
	// get clients
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients)
	if err != nil {
		return err
	}
	if len(clients) == 0 {
		return fmt.Errorf("no client found")
	}
	for name := range clients {
		fmt.Println("client: ", name)
	}

	// add client
	newClient := ClientInfo{
		Name:       "newclient",
		NumDevices: 100,
		NumCmds:    100,
	}
	clients["newclient"] = newClient
	jsonBytes, err := json.Marshal(clients)
	if err != nil {
		return err
	}
	resp, err = PostWithToken(regressionRootUrl+"/api/v1/clients", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get clients
	resp, err = GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients2 := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients2)
	if err != nil {
		return err
	}
	if len(clients2) != len(clients) {
		return fmt.Errorf("expected %d clients but got: %v", len(clients), len(clients2))
	}

	return nil
}

func snmpTest(token string) error {
	fmt.Println("Running snmp test")

	// system oids
	sysDescr := ".*******.*******.0"
	sysContact := ".*******.*******.0"
	sysName := ".*******.*******.0"
	sysObject := ".*******.*******.0"

	// test basic snmp oids
	// if want to test full oids, use Parse_MIBs to get all oids in MIB directory

	devices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	commands := make([][]string, 0)
	for _, dev := range devices {
		commands = append(commands, []string{"snmp", "get", dev.IPAddress, sysDescr})
		commands = append(commands, []string{"snmp", "get", dev.IPAddress, sysContact})
		commands = append(commands, []string{"snmp", "get", dev.IPAddress, sysName})
		commands = append(commands, []string{"snmp", "get", dev.IPAddress, sysObject})
	}

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate
	err = validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// check sysObject result
	// result is private_mib_oid
	// private_mib_oid + base_oid = full_oid
	commands = make([][]string, 0)
	basic_oids := strings.Split(Basic_Oids, "\n")
	cmdData, err := queryCmdData(token)
	if err != nil {
		return err
	}
	for _, dev := range devices {
		if cmdData["snmp get "+dev.IPAddress+" "+sysObject].Status != "ok" {
			continue
		}

		private_mib_oid := cmdData["snmp get "+dev.IPAddress+" "+sysObject].Result
		if private_mib_oid == "" {
			failedItems = append(failedItems, "snmp get "+dev.IPAddress+" "+sysObject)
			continue
		}
		for _, line := range basic_oids {
			if line == "nil" {
				continue
			}
			//split by "="
			ws := strings.Split(line, "=")
			if len(ws) != 4 {
				continue
			}

			node := private_mib_oid + ws[1]
			commands = append(commands, []string{"snmp", "get", dev.IPAddress, node})
		}
	}
	ctlCommand(commands)
	time.Sleep(20 * time.Second)

	// validate
	err = validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	return nil
}

func anomTest(token string) error {
	fmt.Println("Running anomaly test")

	// config openai key or use ollama
	// if default key is used, only 50 requests per execution

	// detect anomaly
	commands := make([][]string, 0)
	// bbctl -cc an1 anomaly detect "https://drive.google.com/uc?export=download&id=1i6y-Gxev2E4-KakQUk2zY4npm8kau9Gq"
	commands = append(commands, []string{"-cc", "anom", "anomaly", "detect", "https://drive.google.com/uc?export=download&id=1i6y-Gxev2E4-KakQUk2zY4npm8kau9Gq"})
	ctlCommand(commands)
	time.Sleep(60 * time.Second)

	err := validateCommandsStatus(commands, token)
	if err != nil {
		return err
	}

	// check if anomaly is detected
	url := regressionRootUrl + "/api/v1/anomaly/reports/list"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	res := make(map[string]interface{})
	err = json.NewDecoder(resp.Body).Decode(&res)
	if err != nil {
		return err
	}
	// fmt.Println(res)
	// total logs: 32
	num, ok := res["total"]
	if !ok {
		return fmt.Errorf("total not found")
	}
	totalStr := fmt.Sprintf("%v", num)
	if totalStr == "0" {
		return fmt.Errorf("anomaly not detected")
	}

	// add normal data

	// detect anomaly again

	// check if anomaly is detected

	// add anomaly data

	// detect anomaly again

	// check if anomaly is detected

	return nil
}

func nestedTest(token string) error {
	// register nested NMS
	// {"bbnmssvc", nmssvcPath, []string{"-n", "nms/client1", "-r", fmt.Sprintf("http://localhost:%d", nmsPort), "-rs", ":5534", "-ss", ":5544", "-fake", "-p", fmt.Sprintf("%d", nms2Port), "-ir", "5"}},
	nms2Port, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	regressionNMS2Url = "http://localhost:" + fmt.Sprintf("%d", nms2Port)
	cmd := exec.Command(nmssvcPath, "-n", "nms/client1", "-r", regressionNMS1Url, "-rs", ":5534", "-ss", ":5544", "-fake", "-p", fmt.Sprintf("%d", nms2Port), "-ir", "5")
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting nms2: %s\n", err)
		os.Exit(1)
	}

	for i := 0; i < 3; i++ {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/clients", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		var clients map[string]ClientInfo
		err = json.NewDecoder(resp.Body).Decode(&clients)
		if err != nil {
			return err
		}
		if _, ok := clients["nms/client1"]; ok {
			break
		}

		if i == 2 {
			// dump all clients
			for name, client := range clients {
				fmt.Println(name, client)
			}
			return fmt.Errorf("nested NMS not found")
		}
		time.Sleep(10 * time.Second)
	}

	// get device list from root to see fake data (00-60-E9-2D-91-3E) is added
	for i := 0; i < 3; i++ {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/devices", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		devices := make(map[string]DevInfo)
		err = json.NewDecoder(resp.Body).Decode(&devices)
		if err != nil {
			return err
		}

		if _, ok := devices["00-60-E9-2D-91-3E"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("nested device not found")
		}
		time.Sleep(10 * time.Second)
	}

	// post fake port info and topology info to FakeNMS1
	url := regressionNMS2Url + "/api/v1/agent/ports"
	fakePortAndPowerInfo := `{
		"00-60-E9-2D-91-3E": {
            "portStatus": [
                {
                    "portName": "Port 1",
                    "portStatus": true,
                    "speed": "1000",
                    "portMode": "copper",
                    "inOctets": "12345",
                    "inErrors": "0",
                    "inUcastPkts": "67890",
                    "inMulticastPkts": "1234",
                    "inBroadcastPkts": "5678",
                    "outOctets": "98765",
                    "outErrors": "0",
                    "outUcastPkts": "54321",
                    "outMulticastPkts": "4321",
                    "outBroadcastPkts": "8765",
                    "enableStatus": true
                },
                {
                    "portName": "Port 2",
                    "portStatus": false,
                    "speed": "100",
                    "portMode": "fiber",
                    "inOctets": "23456",
                    "inErrors": "1",
                    "inUcastPkts": "78901",
                    "inMulticastPkts": "2345",
                    "inBroadcastPkts": "6789",
                    "outOctets": "87654",
                    "outErrors": "1",
                    "outUcastPkts": "65432",
                    "outMulticastPkts": "5432",
                    "outBroadcastPkts": "9876",
                    "enableStatus": false
                }
            ],
            "powerStatus": [
                {
                    "powerId": "1",
                    "status": true
                },
                {
                    "powerId": "2",
                    "status": false
                }
            ]
        }
    }`
	resp, err := PostWithToken(url, token, strings.NewReader(fakePortAndPowerInfo))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	url = regressionNMS2Url + "/api/v1/topology"
	fakeTopologyInfo := `{
		"id": "00-60-E9-2D-91-3E",
		"ipAddress": "***********",
		"modelname": "Model-X",
		"services": "Service-A",
		"lastUpdated": 1627847261,
		"linkData": []
	}`
	resp, err = PostWithToken(url, token, strings.NewReader(fakeTopologyInfo))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get port info and topology info from root to see fake data is added
	for i := 0; i < 3; i++ {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/agent/ports", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		ports := make(map[string]PortAndPowerInfo)
		err = json.NewDecoder(resp.Body).Decode(&ports)
		if err != nil {
			return err
		}
		if _, ok := ports["00-60-E9-2D-91-3E"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("port and power info not found")
		}
		time.Sleep(10 * time.Second)
	}

	for i := 0; i < 3; i++ {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/topology", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		topology := make(map[string]Topology)
		err = json.NewDecoder(resp.Body).Decode(&topology)
		if err != nil {
			return err
		}
		if _, ok := topology["00-60-E9-2D-91-3E"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("topology info not found")
		}
		time.Sleep(10 * time.Second)
	}

	// test commands from root to FakeNMS1
	commands := make([][]string, 0)
	commands = append(commands, []string{"-cc", "nms/client1", "msg", "syslog", "send", "0", "1", "Test", "test syslog"})
	ctlCommand(commands)
	// check command status
	for i := 0; i < 3; i++ {
		cmdData, err := queryCmdData(token)
		if err != nil {
			return err
		}
		if cmdData["msg syslog send 0 1 Test test syslog"].Status == "ok" {
			break
		}

		if i == 2 {
			// dump all cmd data
			for k, v := range cmdData {
				fmt.Println(k, v)
			}
			return fmt.Errorf("command not found")
		}
		time.Sleep(10 * time.Second)
	}

	return nil
}

func TestNimBL(t *testing.T) {
	fmt.Println("Regression test ...")
	fmt.Println("This will restart nimbl, make sure this is the last test or understand what's going on and not running with time limit.")

	killProcess()
	defer killProcess()
	build()
	runProcess()

	//
	token, err := login()
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name string
		fn   func(string) error
	}{
		{"clientsTest", clientsTest}, // client test should be the first test to make sure all services are running
		{"mqttTest", mqttTest},
		{"wgTest", wgTest},
		{"tcpproxyTest", tcpproxyTest},
		{"userTest", userTest},
		{"registerTest", registerTest},
		{"syslogTest", syslogTest},
		{"logsvcTest", logsvcTest},
		{"devTest", devTest},
		{"anomTest", anomTest},

		// {"agentTest", agentTest},
		// {"snmpTest", snmpTest},

		{"nestedTest", nestedTest},

		// authTest should be the last test due to nimbl restart with new key
		{"authTest", authTest},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.fn(token)
			if err == nil {
				passedItems = append(passedItems, test.name)
			} else {
				fmt.Println(err)
				failedItems = append(failedItems, test.name)
			}
		})
	}

	// write result to file with datetime
	filename := fmt.Sprintf("regression_%s.txt", time.Now().Format("20060102_150405"))
	file, err := os.Create(filename)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()
	file.WriteString(fmt.Sprintf("Passed %d items\n", len(passedItems)))
	file.WriteString(fmt.Sprintf("Failed %d items --------\n%s\n", len(failedItems), strings.Join(failedItems, "\n")))
	fmt.Println("Regression test done, please check the result in ", filename)

	if len(failedItems) > 0 {
		// t.Logf("Failed %d items --------\n%s\n", len(failedItems), strings.Join(failedItems, "\n"))
		t.Logf("Failed %d items --------\n", len(failedItems))
	} else {
		t.Log("All tests passed")
	}
}
