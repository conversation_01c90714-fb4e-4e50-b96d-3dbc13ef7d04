import React from "react";
import { render, fireEvent, waitFor } from "@testing-library/react";
import DetectAnomaly from "../components/dashboard/DetectAnomaly";
import { describe, expect, it, vi } from "vitest";
import { store } from "../app/store";
import { Provider } from "react-redux";

describe("DetectAnomaly", () => {
  it("renders the DetectAnomaly component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <DetectAnomaly open={true} />
      </Provider>
    );
    expect(container).toBeTruthy();
  });

  it("handles creation of anomaly detection", async () => {
    const onCreateMock = vi.fn();
    const onCancelMock = vi.fn();

    const { getByLabelText, getByText } = render(
      <Provider store={store}>
        {" "}
        <DetectAnomaly
          open={true}
          onCreate={onCreateMock}
          onCancel={onCancelMock}
        />
      </Provider>
    );

    fireEvent.change(getByLabelText("Enter log"), {
      target: { value: "Sample log entry" },
    });
    fireEvent.change(getByLabelText("Enter score"), {
      target: { value: 0.8 },
    });

    fireEvent.click(getByText("detect"));

    await waitFor(() => {
      expect(onCreateMock).toHaveBeenCalledWith({
        log: "Sample log entry",
        score: 0.8,
      });
    });
  });

  it("handles cancellation of anomaly detection", async () => {
    const onCreateMock = vi.fn();
    const onCancelMock = vi.fn();

    const { getByText } = render(
      <Provider store={store}>
        {" "}
        <DetectAnomaly
          open={true}
          onCreate={onCreateMock}
          onCancel={onCancelMock}
        />
      </Provider>
    );
    fireEvent.click(getByText("CANCEL"));
    await waitFor(() => {
      expect(onCancelMock).toHaveBeenCalled();
    });
  });
});
