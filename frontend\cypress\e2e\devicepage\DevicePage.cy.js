describe("Device Page", () => {
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");
    cy.visit("/devices");
    cy.intercept("GET", "/api/v1/devices").as("getDevices");
    cy.wait("@getDevices"); // Wait for devices data load
  });

  it('should load device table with data', () => {
    cy.get('.ant-table-row').should('have.length.at.least', 1);
    cy.get('.ant-table-row').first().contains('td', '************');
  });

  it('should search devices by IP address', () => {
    cy.get('.ant-input-search input').type('************');
    cy.get('.ant-table-row').should('have.length', 1);
    cy.get('.ant-table-row').first().contains('************');
  });

  it('should open context menu on row right-click', () => {
      cy.get('.ant-table-row').first()
        .find('td').first() 
        .rightclick();
        cy.get('body')
        .find('*[class*=dropdown]')
        .should('be.visible');
    });

    it('should beep a single device via context menu', () => {
      cy.intercept('POST', '/api/v1/commands').as('beepCommand');
      cy.get('.ant-table-row').first().find('td').first().rightclick();
      cy.contains('Beep').click();
      cy.get('.ant-modal')
      .filter(':visible')
      .should('have.length', 1)
      .within(() => {
        cy.contains('Confirm Beep Device').should('be.visible');
        cy.contains('button', 'OK').should('be.visible').click();
      });
      cy.wait('@beepCommand').its('request.body').should((body) => {
        expect(body[0].command).to.match(/^beep\s/);
      });
    });

  it('should reboot a single device via context menu', () => {
    cy.intercept('POST', '/api/v1/commands').as('rebootCommand');
    cy.get('.ant-table-row').first().find('td').first().rightclick();
    cy.contains('Reboot').click();
    cy.get('.ant-modal')
    .filter(':visible')
    .should('have.length', 1)
    .within(() => {
      cy.contains('Confirm Reboot Device').should('be.visible');
      cy.contains('button', 'OK').should('be.visible').click();
    });
    cy.wait('@rebootCommand').its('request.body').should((body) => {
      expect(body[0].command).to.match(/^reset\s/);
    });
  });

  it("should select multiple devices and perform mass delete", () => {
    cy.intercept("POST", "/api/v1/commands").as("deleteCommand");
    cy.get(".ant-checkbox-input").eq(1).click();
    cy.get(".ant-table-row").first().find("td").first().rightclick();
    cy.contains("Delete Device").click();
    cy.get(".ant-modal")
      .filter(":visible")
      .should("have.length", 1)
      .within(() => {
        cy.contains("Delete Device").should("be.visible");
        cy.contains("button", "OK").should("be.visible").click();
      });

    cy.wait("@deleteCommand")
      .its("request.body")
      .should((body) => {
        expect(body[0].command).to.match(/^device delete\s/);
      });
  });

  it('should open firmware update modal and submit form', () => {
    cy.intercept("POST", "/api/v1/commands").as("fwUpdate");
    cy.get('.ant-checkbox-input').eq(1).click();
    cy.get(".ant-table-row").first().find("td").first().rightclick();
    cy.contains('Upload Firmware').click();
    cy.get('textarea').first().type('https://www.atoponline.com/wp-content/uploads/2017/11/EHG7504_EHG7508_EHG7512_EHG7516_EHG7520_EHG9508_EHG9512_EMG8508_EMG8510_RHG7528_RHG9528-K800A800.zip');
    cy.contains('button', 'OK').click();
      cy.wait("@fwUpdate")
      .its("request.body")
      .should((body) => {
        expect(body[0].command).to.match(/^firmware update\s/);
      });
  });

  it('should switch to MDR inventory type', () => {
    cy.get('.ant-segmented-item-label').contains('mdr').click();
  });

  it("should scan new devices by IP range", () => {
    cy.intercept("POST", "/api/v1/commands").as("scanCommand");
    cy.get('[data-testid="scan-ip-btn"]').click();

    // Wait for modal to be visible
    cy.get(".ant-modal")
      .filter(":visible")
      .should("exist")
      .within(() => {
        cy.get('input[placeholder="eg. xxx.xxx.xxx.xxx/xx"]').type(
          "************/24"
        );
        cy.contains("button", /^ok$/i).click();
      });

    cy.wait("@scanCommand")
      .its("request.body")
      .should((body) => {
        expect(body[0].command).to.match(/^scan\s/);
      });
  });
});
