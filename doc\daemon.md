# Daemon

 run a program as a service (daemon)

### required

run as root or administrator

## Command

run | start | stop | restart | install |  uninstall | status

-n: as service name

### run

just run as usual

```sh
$ ./bbrootsvc -n root -daemon run
```

### install

install will run automatically as service

```sh
$ ./bbrootsvc -n root -daemon install
```

### uninstall

uninstall and stop service

```sh
$ ./bbrootsvc -n root -daemon uninstall 
```

### start

if installed, start service

```sh
$ ./bbrootsvc -n root -daemon start 
```

### stop

if service is running, stop it

```sh
$ ./bbrootsvc -n root -daemon stop 
```

### restart

restart service

```sh
$ ./bbrootsvc -n root -daemon restart 
```

### status

show service status

```sh
$ ./bbrootsvc -n root -daemon status 
```

