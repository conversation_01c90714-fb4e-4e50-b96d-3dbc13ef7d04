import { useThemeStore } from "../utils/themes/useStore";
import { beforeEach, describe, expect, it } from "vitest";
import { renderHook } from "@testing-library/react";
describe("useThemeStore", () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it("should initialize with default values", () => {
    const { result } = renderHook(() => useThemeStore());
    const store = result.current;

    expect(store.mode).toBe("auto");
    expect(store.colorPrimary).toBe("#13c2c2");
    expect(store.baseURL).toBe("http://localhost:27182");
    expect(store.wsURL).toBe("ws://localhost:27182");
  });

});
