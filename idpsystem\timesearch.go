package idpsystem

import (
	"fmt"
	"time"
)

const timeformat = "2006-01-02-15:04"

func NewTimeSearch(st string, end string, layout string) (*timeSearch, error) {
	s, err := time.ParseInLocation(layout, st, time.Local)
	if err != nil {
		return nil, err
	}
	e, err := time.ParseInLocation(layout, end, time.Local)
	if err != nil {
		return nil, err
	}
	if e.Before(s) {
		return nil, fmt.Errorf("end time:%v should than start time:%v ", end, s)
	}

	return &timeSearch{starttime: s, endTime: e}, nil
}

type timeSearch struct {
	starttime time.Time
	endTime   time.Time
}

func (t *timeSearch) search(layout, target string) (bool, error) {
	date, err := time.Parse(layout, target)
	if err != nil {
		return false, err
	}
	if (t.starttime.Before(date) || t.starttime.Equal(date)) &&
		(t.endTime.After(date) || t.endTime.Equal(date)) {
		return true, nil
	}

	return false, nil
}
