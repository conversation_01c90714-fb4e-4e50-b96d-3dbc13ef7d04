package ids

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/go-ping/ping"
	"github.com/google/gonids"
)

var count = 0

func TestPing(t *testing.T) {
	count = 0
	i, err := NewIds(false, false)
	if err != nil {
		panic(err)
	}
	ip := "*******"
	v := fmt.Sprintf(`alert icmp %v any <> $HOME_NET any (msg:"test";sid:1;)`, ip)
	r, err := gonids.ParseRule(v)
	if err != nil {
		panic(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		panic(err)
	}
	i.RegisterMatchEvent(getevent())
	err = i.Run()
	if err != nil {
		panic(err)
	}
	err = i.Build()
	if err != nil {
		panic(err)
	}

	err = i.ApplyRules()
	if err != nil {
		panic(err)
	}
	defer i.Close()
	_, err = pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if count == 0 {
		t.Fatal("should drop arp")
	}
}
func getevent() func(Event) {
	v := func(event Event) {
		count++
	}
	return v
}

func pingTest(ip string, count int, timeout time.Duration) (int, error) {
	var err error
	c := 0
	pinger, err := ping.NewPinger(ip)
	if err != nil {
		return c, err
	}
	pinger.SetPrivileged(true)
	pinger.Count = count
	pinger.OnRecv = func(pkt *ping.Packet) {
		fmt.Printf("%d bytes from %s: icmp_seq=%d time=%v\n",
			pkt.Nbytes, pkt.IPAddr, pkt.Seq, pkt.Rtt)
		c++
	}
	go func() {
		err = pinger.Run()
	}()
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	for range ctx.Done() {
		return c, err
	}
	return c, nil
}
