package idpsystem

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"mnms"
	"net/url"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/qeof/q"
)

//var IdpsInfo mnms.IdpsRePortInfo

func init() {
	f := func(cmdinfo *mnms.CmdInfo) *mnms.CmdInfo {
		return IdpsCmd(cmdinfo)
	}
	mnms.IdpsCmd = f
	//	time := time.Now().Format(time.DateTime)
	//	IdpsInfo = mnms.IdpsRePortInfo{StartTime: time}
}

// contronl idps
//
// commnad 1:
//
// import rules to client
//
// Usage :idps rules import url
//
// Example :
//
// idps rules import http://xxx
//
// commnad 2:
//
// delete rules for client
//
// Usage :idps rules delete name
//
// Example :
//
// idps rules delete selftest_icmp
//
// commnad 3:
//
// add rule into category files
//
// Usage :idps rules add [category] [rule]
//
// [category] : category (file name)
// [rule] : content of rule
//
// Example :
//
// idps rules add icmp_category drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:"icmpv4 selftest drop";sid:789;)
// commnad 4:
//
// delete records
//
// Usage :idps records delete
//
//	-f: file name of records(option)
//	-d: date(format:2024-08-09)(option)
//
//	Example :
//
//	delete file name: alert.log data:2024-08-12
//	idps records delete -f alert.log -d 2024-08-12
//
//	delete all of file on 2024-08-12
//	idps records delete -d 2024-08-12
//
//	delete all of file
//	idps records delete
//
// commnad 5:
//
// search records
//
// Usage :idps records search
//
//	-f: file name of record
//	-st: start time (format:2006-01-02-15:04)
//	-et end time (format:2006-01-02-15:04)
//
// Example :
//
// idps records search -f alert.log -st 2024-08-14-15:04 -et 2024-09-14-15:04
func IdpsCmd(cmdinfo *mnms.CmdInfo) *mnms.CmdInfo {

	importcmd := "idps rules import"
	deleteRulecmd := "idps rules delete"
	addRulecmd := "idps rules add"
	deleteRecordcmd := "idps records delete"
	searchRecordcmd := "idps records search"
	cmd, err := mnms.ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	switch {
	case strings.HasPrefix(cmd, importcmd):
		ws := strings.Fields(cmd)
		if len(ws) < 4 {
			q.Q("error", len(ws))
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		paths := ws[3:]
		err := importFiles(paths)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		_, err = updateRulesToRoot()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
	case strings.HasPrefix(cmd, deleteRulecmd):
		ws := strings.Fields(cmd)
		if len(ws) < 4 {
			q.Q("error", len(ws))
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}

		err := deleteRule(ws[3])
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		_, err = updateRulesToRoot()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
	case strings.HasPrefix(cmd, searchRecordcmd):
		ws := strings.Fields(cmd)
		if len(ws) < 3 {
			q.Q("error", len(ws))
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		fl := mnms.NewFlag(searchRecordcmd, flag.ContinueOnError)

		var f string
		var st string
		var et string
		fl.StringVar(&f, "f", "", "log name")
		fl.StringVar(&st, "st", "", "start time")
		fl.StringVar(&et, "et", "", "end time")
		err := fl.Parse(ws[3:])
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		if len(f) == 0 {
			q.Q(err)
			cmdinfo.Status = "error: " + "lost -f file"
			return cmdinfo
		}
		err = updateRecordSearchedToRoot(f, st, et, timeformat)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
	case strings.HasPrefix(cmd, addRulecmd):
		ws := strings.Fields(cmd)
		if len(ws) < 5 {
			q.Q("error", len(ws))
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		rule := strings.Join(ws[4:], " ")
		err := addRule(Category(ws[3]), rule)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		_, err = updateRulesToRoot()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"

	case strings.HasPrefix(cmd, deleteRecordcmd):
		ws := strings.Fields(cmd)
		var d string
		var n string
		fl := mnms.NewFlag(deleteRecordcmd, flag.ContinueOnError)
		fl.StringVar(&d, "d", "", "date")
		fl.StringVar(&n, "f", "", "fiel name")
		err := fl.Parse(ws[3:])
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		err = deleteRecord(d, n)
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		err = updateRecordListToRoot()
		if err != nil {
			q.Q(err)
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
	default:
		cmdinfo.Status = "error: invalid command"
	}

	return cmdinfo
}

func importFiles(value []string) error {
	idps, err := getIdpsystem()
	if err != nil {
		return err
	}

	for _, u := range value {
		isurl := false
		ur, _ := url.ParseRequestURI(u)
		if ur != nil && ur.Scheme != "" && ur.Host != "" {
			isurl = true
		}
		switch isurl {
		//parse ulr file
		case true:
			err := idps.ImportFilesFromUrl([]string{u})
			if err != nil {
				return err
			}
			//parse  common file
		default:
			err := idps.ImportFiles([]string{u})
			if err != nil {
				return err
			}
		}
	}
	err = idps.ApplyRules()
	if err != nil {
		return err
	}
	return nil
}

// updateRulesToRoot post latest rule to root
func updateRulesToRoot() ([]byte, error) {
	idpsys, err := getIdpsystem()
	if err != nil {
		return nil, err
	}
	cs, _ := idpsys.GetAllCategory()
	rules := covertCategoryToRule(cs)

	err = postReport(rules)
	if err != nil {
		return nil, err
	}
	jsonBytes, err := json.Marshal(rules)
	if err != nil {
		return nil, err
	}

	return jsonBytes, nil
}

func postReport(info interface{}) error {
	report := mnms.NewIdpsRePortInfo()
	switch v := info.(type) {
	case string:
		report.StartTime = v
	case []mnms.IdpsRule:
		report.Rules = v
	case []mnms.EventMessage:
		report.Event = v
	case []mnms.RecordList:
		report.RecordList = v
	case []mnms.RulePacketsTotal:
		report.RulePacketsTotal = v
	case mnms.IdpsRePortInfo:
		report = v
	default:
		return ErrorType
	}

	jsonBytes, err := json.Marshal(report)
	if err != nil {
		return err
	}
	res, err := mnms.PostWithToken(mnms.QC.RootURL+"/api/v1/idps/report?client="+mnms.QC.Name, mnms.QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	if res != nil {
		defer res.Body.Close()
	}
	return nil
}

func deleteRule(name string) error {
	idps, err := getIdpsystem()
	if err != nil {
		return err
	}
	err = idps.DeleteCategory(Category(name))
	if err != nil {
		return err
	}

	return nil
}

func addRule(name Category, rule string) error {
	idps, err := getIdpsystem()
	if err != nil {
		return err
	}
	err = idps.AddRule(name, rule)
	if err != nil {
		return err
	}
	err = idps.ApplyRules()
	if err != nil {
		return err
	}
	return nil
}

// updateRecordSearchedToRoot events   to root
func updateRecordSearchedToRoot(file string, st string, end string, layout string) error {

	m, err := searchRecord(file, st, end, layout)
	if err != nil {
		return err
	}
	err = postReport(m)
	if err != nil {
		return err
	}
	return nil

}

// updateListToRoot post  records to root
func updateRecordListToRoot() error {
	idps, err := getIdpsystem()
	if err != nil {
		return err
	}
	l, _ := idps.RecordList()
	err = postReport(l)
	if err != nil {
		return err
	}
	return nil
}

func deleteRecord(date, name string) error {
	idps, err := getIdpsystem()
	if err != nil {
		return err
	}
	if len(name) == 0 {
		err = idps.DeleteAllRecord(date)
		if err != nil {
			return err
		}
	} else {
		err = idps.DeleteSpecificRecord(date, name)
		if err != nil {
			return err
		}
	}

	return nil
}

func searchRecord(file string, st string, et string, layout string) ([]mnms.EventMessage, error) {
	idps, err := getIdpsystem()
	if err != nil {
		return []mnms.EventMessage{}, err
	}
	return idps.managedRecord.TimeSearch(file, st, et, layout)
}

func covertCategoryToRule(cs []categoryInfo) []mnms.IdpsRule {
	r := []mnms.IdpsRule{}
	for _, c := range cs {
		if c.Enable {
			rule := mnms.IdpsRule{}
			rule.Name = string(c.Name)
			rule.Time = c.Time
			cont := c.getInfos()
			copier.Copy(&rule.Content, &cont)
			r = append(r, rule)

		}
	}
	return r
}
