package idpsystem

import (
	"mnms"
	"net/http"
)

var client *idpsClientInfo

type idpsClientInfo struct {
	idps             *IdpSystem
	recordlist       []mnms.RecordList
	RulePacketsTotal []mnms.RulePacketsTotal
}

func newIdpsClient(idps *IdpSystem) *idpsClientInfo {
	return &idpsClientInfo{idps: idps}
}

func (i *idpsClientInfo) postRecordlist() error {
	old := Hash(i.recordlist)
	newlist, _ := i.idps.RecordList()
	new := Hash(newlist)
	if !Compare(old, new) {
		if err := postReport(newlist); err != nil {
			return err
		} else {
			i.recordlist = newlist
		}
	}
	return nil
}

func (i *idpsClientInfo) postRulePacketsTotal() error {
	old := Hash(i.RulePacketsTotal)
	newtotal := i.idps.ReadAllRulePacketTotal()
	new := Hash(newtotal)
	if !Compare(old, new) {
		if err := postReport(newtotal); err != nil {
			return err
		} else {
			i.RulePacketsTotal = newtotal
		}
	}
	return nil
}

func (i *idpsClientInfo) postAll() error {
	report := mnms.NewIdpsRePortInfo()
	report.StartTime = i.idps.startTime
	list, _ := i.idps.RecordList()
	report.RecordList = list
	total := i.idps.ReadAllRulePacketTotal()
	report.RulePacketsTotal = total
	cs, _ := i.idps.GetAllCategory()
	rules := covertCategoryToRule(cs)
	report.Rules = rules
	err := postReport(report)
	if err != nil {
		return err
	}
	i.recordlist = report.RecordList
	i.RulePacketsTotal = report.RulePacketsTotal
	return nil
}
func InitIdpsClient() error {
	err := InitIdpSystem()
	if err != nil {
		return err
	}
	i, err := getIdpsystem()
	if err != nil {
		return err
	}
	client = newIdpsClient(i)
	return nil
}

func UpdateIdpsClient() error {
	b := clientIsExisted()
	if !b {
		return client.postAll()
	}

	err := client.postRecordlist()
	if err != nil {
		return err
	}
	err = client.postRulePacketsTotal()
	if err != nil {
		return err
	}

	return nil
}

func Run() error {
	idpsys := client.idps
	err := idpsys.Start()
	if err != nil {
		return err
	}
	return nil
}

func clientIsExisted() bool {
	res, err := mnms.GetWithToken(mnms.QC.RootURL+"/api/v1/idps/report?client="+mnms.QC.Name, mnms.QC.AdminToken)
	if err != nil {
		return false
	}
	if res != nil {
		defer res.Body.Close()
	}
	if res.StatusCode == http.StatusOK {
		return true
	}
	return false
}
