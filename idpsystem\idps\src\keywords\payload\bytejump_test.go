package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestBasicByteJump(t *testing.T) {
	var tests = []struct {
		content  string
		expected byteJump
	}{

		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_jump:4,0 , relative , little, string,dec, align, from_beginning;sid:1;)`,
			expected: byteJump{
				nbytes:      4,
				offset:      0,
				multiplier:  1,
				post_offset: 0,
				flags:       byteJumpRelative | byteJumpLittle | byteJumpAlign | byteJumpString | byteJumpBegin,
				base:        byteJumpBaseDec,
			},
		},

		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_jump: 4,0 , relative , little, string,dec, align, from_beginning ,multiplier 2 , post_offset -16;sid:1;)`,
			expected: byteJump{
				nbytes:      4,
				offset:      0,
				multiplier:  2,
				post_offset: -16,
				flags:       byteJumpRelative | byteJumpLittle | byteJumpAlign | byteJumpString | byteJumpBegin,
				base:        byteJumpBaseDec,
			},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}

			if v, ok := d.RetrieveData().(*byteJump); ok {
				if v.nbytes != test.expected.nbytes {
					t.Error("nbytes error: unexpected")
				}
				if v.offset != test.expected.offset {
					t.Error("offset error: unexpected")
				}
				if v.multiplier != test.expected.multiplier {
					t.Error("multiplier error: unexpected")
				}
				if v.post_offset != test.expected.post_offset {
					t.Error("post_offset error: unexpected")
				}
				if v.flags != test.expected.flags {
					t.Error("flags error: unexpected")
				}
				if v.base != test.expected.base {
					t.Error("base error: unexpected")
				}

			}
		})
	}
}

func TestByteJump(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";byte_jump:1,13; byte_jump:4,0,relative;content:"|48 00 00|"; within:3; sid:144; rev:1;)`,
			expected: captured,
			packet: []byte{0x00, 0x00, 0x00, 0x77, 0xff, 0x53,
				0x4d, 0x42, 0x2f, 0x00, 0x00, 0x00, 0x00, 0x18,
				0x01, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
				0x92, 0xa4, 0x01, 0x08, 0x17, 0x5c, 0x0e, 0xff,
				0x00, 0x00, 0x00, 0x01, 0x40, 0x48, 0x00, 0x00,
				0x00, 0xff},
		},
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";content:"XYZ"; byte_jump:2,0,relative,string,dec; content:"ABCD"; distance:0; within:4; sid:1; rev:1;)`,
			expected: captured,
			packet:   []byte("XYZ04abcdABCD"),
		},
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";content:"XYZ"; byte_jump:2,0,relative,string,dec; content:"cdABCD"; within:6; sid:1; rev:1;)`,
			expected: uncaptured,
			packet:   []byte("XYZ04abcdABCD"),
		},
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";content:"XX"; byte_jump:2,0,relative,string,dec,from_beginning; content:"ABCD"; distance:4; within:4; sid:1; rev:1;)`,
			expected: captured,
			packet:   []byte("XX04abcdABCD"),
		},
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";content:"XX"; byte_jump:2,0,relative,string,dec,from_beginning; content:"abcdABCD"; distance:0; within:8; sid:1; rev:1;)`,
			expected: captured,
			packet:   []byte("XX04abcdABCD"),
		},
		{
			content:  `alert ip any any -> any any (msg:"TestByteTest";content:"XX"; byte_jump:2,0,relative,string,dec,from_end, post_offset -8; content:"ABCD";  sid:1; rev:1;)`,
			expected: captured,
			packet:   []byte("XX04abcdABCD"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}
