import React, { useState } from "react";
import { App, Config<PERSON>rovider, theme as antdTheme } from "antd";
import { ProTable } from "@ant-design/pro-components";
import dayjs from "dayjs";
import {
  useDeleteAnomalyReportMutation,
  useGetAnomalyReportswithPagingQuery,
} from "../../app/services/anomalyApi";
import { DeleteColumnOutlined, DeleteOutlined } from "@ant-design/icons";

const { useToken } = antdTheme;

const LogHistory = ({ onRowclick, client }) => {
  const token = useToken().token;
  const { notification } = App.useApp();
  const [selectedRow, setSelectedRow] = useState("");
  const [page, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const onChange = (page, pageSize) => {
    setCurrent(page);
    setPageSize(pageSize);
  };
  const { data, isLoading, refetch } = useGetAnomalyReportswithPagingQuery(
    { client, page, pageSize },
    { refetchOnMountOrArgChange: true }
  );

  const [deleteAnomalyReport, {}] = useDeleteAnomalyReportMutation({});
  const handleDeleteClick = async (id) => {
    try {
      await deleteAnomalyReport({ action: "delete", report: { id } }).unwrap();
      notification.success({
        message: "deleted report successfully!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  };

  const columns = [
    {
      title: "Time",
      dataIndex: "since",
      key: "since",
      width: 120,
      render: (data) => dayjs(data).format("YYYY/MM/DD HH:mm:ss"),
    },
    {
      title: "Total count",
      dataIndex: "total_count",
      key: "total_count",
      width: 60,
    },
    {
      title: "anomaly",
      dataIndex: "total_anomaly",
      key: "total_anomaly",
      width: 60,
    },
    {
      title: "error",
      dataIndex: "total_error",
      key: "total_error",
      width: 60,
    },
    {
      title: "Source",
      width: 150,
      dataIndex: "source",
      key: "source",
    },
    {
      title: "Action",
      width: 50,
      key: "action",
      fixed: "right",
      render: (_, record) => (
        <DeleteOutlined
          style={{ color: token.colorError }}
          onClick={() => handleDeleteClick(record.id)}
        />
      ),
    },
  ];

  return (
    <ProTable
      cardProps={{
        style: { padding: 0 },
        bodyStyle: { padding: 0 },
      }}
      cardBordered={false}
      loading={isLoading}
      rowClassName={(record) => (record.id === selectedRow ? "row_select" : "")}
      columns={columns}
      rowKey="id"
      headerTitle="Anomaly detect history"
      defaultSize="small"
      dataSource={data?.reports || []}
      pagination={{
        current: page,
        onChange: onChange,
        position: ["bottomCenter"],
        showQuickJumper: false,
        size: "default",
        total: data?.total,
        defaultPageSize: 5,
        pageSize,
        pageSizeOptions: [5, 10, 15, 20, 25],
        showTotal: (total, range) =>
          `${range[0]}-${range[1]} of ${total} items`,
      }}
      scroll={{
        x: 850,
      }}
      options={{
        search: false,
        reload: () => {
          refetch();
        },
        fullScreen: false,
        setting: false,
      }}
      search={false}
      dateFormatter="string"
      columnsState={{
        persistenceKey: "ad-history-table",
        persistenceType: "localStorage",
      }}
      onRow={(record) => {
        return {
          onClick: () => {
            if (record) {
              setSelectedRow(record.id);
              onRowclick({ ...record });
            }
          },
        };
      }}
    />
  );
};

export default LogHistory;
