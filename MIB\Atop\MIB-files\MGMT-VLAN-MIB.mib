-- *****************************************************************
-- VLAN-MIB:  
-- ****************************************************************

MGMT-VLAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTEtherType FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    MGMTVlanListQuarter FROM MGMT-TC
    ;

mgmtVlanMib MODULE-IDENTITY
    LAST-UPDATED "202008240000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MIB for VLAN."
    REVISION    "202008240000Z"
    DESCRIPTION
        "Add support for managing flooding"
    REVISION    "201904050000Z"
    DESCRIPTION
        "Obsoleted a couple of VLAN users"
    REVISION    "201501160000Z"
    DESCRIPTION
        "Added Shared VLAN Learning table"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 13 }


MGMTVlanEgressTagging ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates how egress tagging occurs."
    SYNTAX      INTEGER { untagThis(0), tagThis(1), tagAll(2),
                          untagAll(3) }

MGMTVlanIngressAcceptance ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the type of frames that are not discarded on
         ingress w.r.t. VLAN tagging."
    SYNTAX      INTEGER { all(0), tagged(1), untagged(2) }

MGMTVlanPortMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Determines the underlying port mode.
         
         Access ports are only members of one VLAN, the AccessVlan.
         
         Trunk ports are by default members of all VLANs, which can be limited
         with TrunkVlans.
         
         Hybrid ports allow for changing all port VLAN parameters. As trunk
         ports, hybrid ports are by default members of all VLANs, which can be
         limited with HybridVlans."
    SYNTAX      INTEGER { access(0), trunk(1), hybrid(2) }

MGMTVlanPortType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates if a port is VLAN aware, and if so, to which
         EtherType it is sensitive."
    SYNTAX      INTEGER { unaware(0), c(1), s(2), sCustom(3) }

MGMTVlanUserType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the VLAN user type. A value of 'combined'
         indicates the VLAN settings as programmed to hardware. A value of
         'admin' indicates the VLAN settings as programmed by the administrative
         user, and any other value indicates a software module that changes VLAN
         settings 'behind the scenes'."
    SYNTAX      INTEGER { combined(0), admin(1), dot1x(3), mvrp(4),
                          gvrp(5), mvr(6), voiceVlan(7), mstp(8),
                          erps(9), mepObsolete(10), evcObsolete(11),
                          vcl(12), rmirror(13) }

mgmtVlanMibObjects OBJECT IDENTIFIER
    ::= { mgmtVlanMib 1 }

mgmtVlanCapabilities OBJECT IDENTIFIER
    ::= { mgmtVlanMibObjects 1 }

mgmtVlanCapabilitiesVlanIdMin OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum VLAN ID that can be configured on the device."
    ::= { mgmtVlanCapabilities 1 }

mgmtVlanCapabilitiesVlanIdMax OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum VLAN ID that can be configured on the device."
    ::= { mgmtVlanCapabilities 2 }

mgmtVlanCapabilitiesFidCnt OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of Shared VLAN Learning (SVL) Filter IDs (FIDs) supported by
         this device. 0 if SVL is not supported."
    ::= { mgmtVlanCapabilities 3 }

mgmtVlanCapabilitiesHasFlooding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, flooding can be managed."
    ::= { mgmtVlanCapabilities 4 }

mgmtVlanConfig OBJECT IDENTIFIER
    ::= { mgmtVlanMibObjects 2 }

mgmtVlanConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtVlanConfig 1 }

mgmtVlanConfigGlobalsMain OBJECT IDENTIFIER
    ::= { mgmtVlanConfigGlobals 1 }

mgmtVlanConfigGlobalsMainCustomSPortEtherType OBJECT-TYPE
    SYNTAX      MGMTEtherType (1536..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TPID (EtherType) for ports marked as Custom-S tag aware."
    ::= { mgmtVlanConfigGlobalsMain 1 }

mgmtVlanConfigGlobalsMainAccessVlans0To1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtVlanConfigGlobalsMain 2 }

mgmtVlanConfigGlobalsMainAccessVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtVlanConfigGlobalsMain 3 }

mgmtVlanConfigGlobalsMainAccessVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtVlanConfigGlobalsMain 4 }

mgmtVlanConfigGlobalsMainAccessVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtVlanConfigGlobalsMain 5 }

mgmtVlanConfigGlobalsNameTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanConfigGlobalsNameEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of VLAN names."
    ::= { mgmtVlanConfigGlobals 2 }

mgmtVlanConfigGlobalsNameEntry OBJECT-TYPE
    SYNTAX      MGMTVlanConfigGlobalsNameEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the name of a given VLAN."
    INDEX       { mgmtVlanConfigGlobalsNameVlanId }
    ::= { mgmtVlanConfigGlobalsNameTable 1 }

MGMTVlanConfigGlobalsNameEntry ::= SEQUENCE {
    mgmtVlanConfigGlobalsNameVlanId  MGMTVlan,
    mgmtVlanConfigGlobalsNameName    MGMTDisplayString
}

mgmtVlanConfigGlobalsNameVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID. Valid range is 1 - 4095."
    ::= { mgmtVlanConfigGlobalsNameEntry 1 }

mgmtVlanConfigGlobalsNameName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN Name. Default for VLAN 1 is 'default'. Default for any other VLAN
         is 'VLANxxxx', where 'xxxx' is a decimal representation of the VLAN ID
         with leading zeroes."
    ::= { mgmtVlanConfigGlobalsNameEntry 2 }

mgmtVlanConfigGlobalsFloodingTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanConfigGlobalsFloodingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "(Table of VLAN flooding configuration.)This is an optional table and is
         only present if vlanCapabilities.hasFlooding is true."
    ::= { mgmtVlanConfigGlobals 3 }

mgmtVlanConfigGlobalsFloodingEntry OBJECT-TYPE
    SYNTAX      MGMTVlanConfigGlobalsFloodingEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the flooding configuration of a given VLAN."
    INDEX       { mgmtVlanConfigGlobalsFloodingVlanId }
    ::= { mgmtVlanConfigGlobalsFloodingTable 1 }

MGMTVlanConfigGlobalsFloodingEntry ::= SEQUENCE {
    mgmtVlanConfigGlobalsFloodingVlanId    MGMTVlan,
    mgmtVlanConfigGlobalsFloodingFlooding  TruthValue
}

mgmtVlanConfigGlobalsFloodingVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID. Valid range is 1 - 4095."
    ::= { mgmtVlanConfigGlobalsFloodingEntry 1 }

mgmtVlanConfigGlobalsFloodingFlooding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flooding."
    ::= { mgmtVlanConfigGlobalsFloodingEntry 2 }

mgmtVlanConfigInterfaces OBJECT IDENTIFIER
    ::= { mgmtVlanConfig 2 }

mgmtVlanConfigInterfacesTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-port configuration."
    ::= { mgmtVlanConfigInterfaces 1 }

mgmtVlanConfigInterfacesEntry OBJECT-TYPE
    SYNTAX      MGMTVlanConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the VLAN configuration for a port interface."
    INDEX       { mgmtVlanConfigInterfacesIfIndex }
    ::= { mgmtVlanConfigInterfacesTable 1 }

MGMTVlanConfigInterfacesEntry ::= SEQUENCE {
    mgmtVlanConfigInterfacesIfIndex                  MGMTInterfaceIndex,
    mgmtVlanConfigInterfacesMode                     MGMTVlanPortMode,
    mgmtVlanConfigInterfacesAccessVlan               MGMTVlan,
    mgmtVlanConfigInterfacesTrunkNativeVlan          MGMTVlan,
    mgmtVlanConfigInterfacesTrunkTagNativeVlan       TruthValue,
    mgmtVlanConfigInterfacesTrunkVlans0KTo1K         MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesTrunkVlans1KTo2K         MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesTrunkVlans2KTo3K         MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesTrunkVlans3KTo4K         MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesHybridNativeVlan         MGMTVlan,
    mgmtVlanConfigInterfacesHybridPortType           MGMTVlanPortType,
    mgmtVlanConfigInterfacesHybridIngressFiltering   TruthValue,
    mgmtVlanConfigInterfacesHybridIngressAcceptance  MGMTVlanIngressAcceptance,
    mgmtVlanConfigInterfacesHybridEgressTagging      MGMTVlanEgressTagging,
    mgmtVlanConfigInterfacesHybridVlans0KTo1K        MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesHybridVlans1KTo2K        MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesHybridVlans2KTo3K        MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesHybridVlans3KTo4K        MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesForbiddenVlans0KTo1K     MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesForbiddenVlans1KTo2K     MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesForbiddenVlans2KTo3K     MGMTVlanListQuarter,
    mgmtVlanConfigInterfacesForbiddenVlans3KTo4K     MGMTVlanListQuarter
}

mgmtVlanConfigInterfacesIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtVlanConfigInterfacesEntry 1 }

mgmtVlanConfigInterfacesMode OBJECT-TYPE
    SYNTAX      MGMTVlanPortMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN mode of the port."
    ::= { mgmtVlanConfigInterfacesEntry 2 }

mgmtVlanConfigInterfacesAccessVlan OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The port VLAN ID the port will be assigned when Mode is Access."
    ::= { mgmtVlanConfigInterfacesEntry 3 }

mgmtVlanConfigInterfacesTrunkNativeVlan OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The port VLAN ID the port will be assigned when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 4 }

mgmtVlanConfigInterfacesTrunkTagNativeVlan OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether frames classified to TrunkNativeVlan get tagged on
         egress. Used when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 5 }

mgmtVlanConfigInterfacesTrunkVlans0KTo1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 6 }

mgmtVlanConfigInterfacesTrunkVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 7 }

mgmtVlanConfigInterfacesTrunkVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 8 }

mgmtVlanConfigInterfacesTrunkVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is trunk."
    ::= { mgmtVlanConfigInterfacesEntry 9 }

mgmtVlanConfigInterfacesHybridNativeVlan OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The port VLAN ID the port will be assigned when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 10 }

mgmtVlanConfigInterfacesHybridPortType OBJECT-TYPE
    SYNTAX      MGMTVlanPortType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls awareness and whether it reacts to C-tags, S-tags,
         Custom-S-tags. Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 11 }

mgmtVlanConfigInterfacesHybridIngressFiltering OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether frames classified to a certain VLAN ID get discarded
         (true) or not (false) if the port is not member of the VLAN ID. Used
         when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 12 }

mgmtVlanConfigInterfacesHybridIngressAcceptance OBJECT-TYPE
    SYNTAX      MGMTVlanIngressAcceptance
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether frames are accepted on ingress depending on VLAN tag
         in frame. Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 13 }

mgmtVlanConfigInterfacesHybridEgressTagging OBJECT-TYPE
    SYNTAX      MGMTVlanEgressTagging
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls tagging of frames on egress. tagThis(1) is not allowed. Used
         when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 14 }

mgmtVlanConfigInterfacesHybridVlans0KTo1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 15 }

mgmtVlanConfigInterfacesHybridVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 16 }

mgmtVlanConfigInterfacesHybridVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 17 }

mgmtVlanConfigInterfacesHybridVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating whether the port is member of a
         VLAN ('1') or not ('0'). Used when Mode is hybrid."
    ::= { mgmtVlanConfigInterfacesEntry 18 }

mgmtVlanConfigInterfacesForbiddenVlans0KTo1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating whether the port can ever become
         a member of a VLAN ('0') or not ('1'). Used in all modes."
    ::= { mgmtVlanConfigInterfacesEntry 19 }

mgmtVlanConfigInterfacesForbiddenVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating whether the port can ever become
         a member of a VLAN ('0') or not ('1'). Used in all modes."
    ::= { mgmtVlanConfigInterfacesEntry 20 }

mgmtVlanConfigInterfacesForbiddenVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating whether the port can ever become
         a member of a VLAN ('0') or not ('1'). Used in all modes."
    ::= { mgmtVlanConfigInterfacesEntry 21 }

mgmtVlanConfigInterfacesForbiddenVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating whether the port can ever become a
         member of a VLAN ('0') or not ('1'). Used in all modes."
    ::= { mgmtVlanConfigInterfacesEntry 22 }

mgmtVlanConfigInterfacesSvlTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanConfigInterfacesSvlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Shared VLAN Learning (SVL) allows for having one or more VLAN IDs map
         to the same Filter ID (FID). For a given set of VLANs, if an individual
         MAC address is learned in one VLAN, that learned information is used in
         forwarding decisions taken for that address relative to all other VLANs
         in the given set.
         
         fidCnt, which can be found in the capabilities section, indicates the
         number of FIDs available on this platform. The feature is not available
         if this number is 0."
    ::= { mgmtVlanConfigInterfaces 2 }

mgmtVlanConfigInterfacesSvlEntry OBJECT-TYPE
    SYNTAX      MGMTVlanConfigInterfacesSvlEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is indexed by VLAN ID"
    INDEX       { mgmtVlanConfigInterfacesSvlVlanId }
    ::= { mgmtVlanConfigInterfacesSvlTable 1 }

MGMTVlanConfigInterfacesSvlEntry ::= SEQUENCE {
    mgmtVlanConfigInterfacesSvlVlanId    MGMTVlan,
    mgmtVlanConfigInterfacesSvlFilterId  MGMTUnsigned16,
    mgmtVlanConfigInterfacesSvlAction    MGMTRowEditorState
}

mgmtVlanConfigInterfacesSvlVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID. Valid range is 1 - 4095."
    ::= { mgmtVlanConfigInterfacesSvlEntry 1 }

mgmtVlanConfigInterfacesSvlFilterId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Filter ID (FID) used in Shared VLAN Learning. Zero or more VLANs may
         map into the same FID."
    ::= { mgmtVlanConfigInterfacesSvlEntry 2 }

mgmtVlanConfigInterfacesSvlAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVlanConfigInterfacesSvlEntry 100 }

mgmtVlanConfigInterfacesSvlTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtVlanConfigInterfaces 3 }

mgmtVlanConfigInterfacesSvlTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN ID. Valid range is 1 - 4095."
    ::= { mgmtVlanConfigInterfacesSvlTableRowEditor 1 }

mgmtVlanConfigInterfacesSvlTableRowEditorFilterId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Filter ID (FID) used in Shared VLAN Learning. Zero or more VLANs may
         map into the same FID."
    ::= { mgmtVlanConfigInterfacesSvlTableRowEditor 2 }

mgmtVlanConfigInterfacesSvlTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVlanConfigInterfacesSvlTableRowEditor 100 }

mgmtVlanStatus OBJECT IDENTIFIER
    ::= { mgmtVlanMibObjects 3 }

mgmtVlanStatusInterfaces OBJECT IDENTIFIER
    ::= { mgmtVlanStatus 1 }

mgmtVlanStatusInterfacesTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanStatusInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-interface (port) status."
    ::= { mgmtVlanStatusInterfaces 1 }

mgmtVlanStatusInterfacesEntry OBJECT-TYPE
    SYNTAX      MGMTVlanStatusInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the VLAN configuration for a port interface for a
         given VLAN user."
    INDEX       { mgmtVlanStatusInterfacesIfIndex,
                  mgmtVlanStatusInterfacesVlanUser }
    ::= { mgmtVlanStatusInterfacesTable 1 }

MGMTVlanStatusInterfacesEntry ::= SEQUENCE {
    mgmtVlanStatusInterfacesIfIndex            MGMTInterfaceIndex,
    mgmtVlanStatusInterfacesVlanUser           MGMTVlanUserType,
    mgmtVlanStatusInterfacesPvid               MGMTVlan,
    mgmtVlanStatusInterfacesUvid               MGMTVlan,
    mgmtVlanStatusInterfacesPortType           MGMTVlanPortType,
    mgmtVlanStatusInterfacesIngressFiltering   TruthValue,
    mgmtVlanStatusInterfacesIngressAcceptance  MGMTVlanIngressAcceptance,
    mgmtVlanStatusInterfacesEgressTagging      MGMTVlanEgressTagging
}

mgmtVlanStatusInterfacesIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtVlanStatusInterfacesEntry 1 }

mgmtVlanStatusInterfacesVlanUser OBJECT-TYPE
    SYNTAX      MGMTVlanUserType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN user."
    ::= { mgmtVlanStatusInterfacesEntry 2 }

mgmtVlanStatusInterfacesPvid OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port VLAN ID set by this user."
    ::= { mgmtVlanStatusInterfacesEntry 3 }

mgmtVlanStatusInterfacesUvid OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Untagged VLAN ID set by a user. This may only be populated by non-admin
         users."
    ::= { mgmtVlanStatusInterfacesEntry 4 }

mgmtVlanStatusInterfacesPortType OBJECT-TYPE
    SYNTAX      MGMTVlanPortType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VLAN Awareness and tag reaction set by this user."
    ::= { mgmtVlanStatusInterfacesEntry 5 }

mgmtVlanStatusInterfacesIngressFiltering OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Ingress filtering enabled or disabled by this user."
    ::= { mgmtVlanStatusInterfacesEntry 6 }

mgmtVlanStatusInterfacesIngressAcceptance OBJECT-TYPE
    SYNTAX      MGMTVlanIngressAcceptance
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VLAN tagging accepted upon ingress configured by this user."
    ::= { mgmtVlanStatusInterfacesEntry 7 }

mgmtVlanStatusInterfacesEgressTagging OBJECT-TYPE
    SYNTAX      MGMTVlanEgressTagging
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Egress tagging configured by this user."
    ::= { mgmtVlanStatusInterfacesEntry 8 }

mgmtVlanStatusMemberships OBJECT IDENTIFIER
    ::= { mgmtVlanStatus 2 }

mgmtVlanStatusMembershipsVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanStatusMembershipsVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of per-VLAN, per-VLAN user port memberships."
    ::= { mgmtVlanStatusMemberships 1 }

mgmtVlanStatusMembershipsVlanEntry OBJECT-TYPE
    SYNTAX      MGMTVlanStatusMembershipsVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains a port list of VLAN memberships for a given VLAN and
         VLAN user.The table is sparsely populated, so if a VLAN user doesn't
         contribute, the row is non-existent."
    INDEX       { mgmtVlanStatusMembershipsVlanVlanId,
                  mgmtVlanStatusMembershipsVlanVlanUser }
    ::= { mgmtVlanStatusMembershipsVlanTable 1 }

MGMTVlanStatusMembershipsVlanEntry ::= SEQUENCE {
    mgmtVlanStatusMembershipsVlanVlanId    MGMTVlan,
    mgmtVlanStatusMembershipsVlanVlanUser  MGMTVlanUserType,
    mgmtVlanStatusMembershipsVlanPortList  MGMTPortList
}

mgmtVlanStatusMembershipsVlanVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN ID. Valid range is 1 - 4095."
    ::= { mgmtVlanStatusMembershipsVlanEntry 1 }

mgmtVlanStatusMembershipsVlanVlanUser OBJECT-TYPE
    SYNTAX      MGMTVlanUserType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "VLAN user."
    ::= { mgmtVlanStatusMembershipsVlanEntry 2 }

mgmtVlanStatusMembershipsVlanPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port list."
    ::= { mgmtVlanStatusMembershipsVlanEntry 3 }

mgmtVlanMibConformance OBJECT IDENTIFIER
    ::= { mgmtVlanMib 2 }

mgmtVlanMibCompliances OBJECT IDENTIFIER
    ::= { mgmtVlanMibConformance 1 }

mgmtVlanMibGroups OBJECT IDENTIFIER
    ::= { mgmtVlanMibConformance 2 }

mgmtVlanCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanCapabilitiesVlanIdMin,
                  mgmtVlanCapabilitiesVlanIdMax,
                  mgmtVlanCapabilitiesFidCnt,
                  mgmtVlanCapabilitiesHasFlooding }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 1 }

mgmtVlanConfigGlobalsMainInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigGlobalsMainCustomSPortEtherType,
                  mgmtVlanConfigGlobalsMainAccessVlans0To1K,
                  mgmtVlanConfigGlobalsMainAccessVlans1KTo2K,
                  mgmtVlanConfigGlobalsMainAccessVlans2KTo3K,
                  mgmtVlanConfigGlobalsMainAccessVlans3KTo4K }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 2 }

mgmtVlanConfigGlobalsNameTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigGlobalsNameVlanId,
                  mgmtVlanConfigGlobalsNameName }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 3 }

mgmtVlanConfigGlobalsFloodingTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigGlobalsFloodingVlanId,
                  mgmtVlanConfigGlobalsFloodingFlooding }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 4 }

mgmtVlanConfigInterfacesTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigInterfacesIfIndex,
                  mgmtVlanConfigInterfacesMode,
                  mgmtVlanConfigInterfacesAccessVlan,
                  mgmtVlanConfigInterfacesTrunkNativeVlan,
                  mgmtVlanConfigInterfacesTrunkTagNativeVlan,
                  mgmtVlanConfigInterfacesTrunkVlans0KTo1K,
                  mgmtVlanConfigInterfacesTrunkVlans1KTo2K,
                  mgmtVlanConfigInterfacesTrunkVlans2KTo3K,
                  mgmtVlanConfigInterfacesTrunkVlans3KTo4K,
                  mgmtVlanConfigInterfacesHybridNativeVlan,
                  mgmtVlanConfigInterfacesHybridPortType,
                  mgmtVlanConfigInterfacesHybridIngressFiltering,
                  mgmtVlanConfigInterfacesHybridIngressAcceptance,
                  mgmtVlanConfigInterfacesHybridEgressTagging,
                  mgmtVlanConfigInterfacesHybridVlans0KTo1K,
                  mgmtVlanConfigInterfacesHybridVlans1KTo2K,
                  mgmtVlanConfigInterfacesHybridVlans2KTo3K,
                  mgmtVlanConfigInterfacesHybridVlans3KTo4K,
                  mgmtVlanConfigInterfacesForbiddenVlans0KTo1K,
                  mgmtVlanConfigInterfacesForbiddenVlans1KTo2K,
                  mgmtVlanConfigInterfacesForbiddenVlans2KTo3K,
                  mgmtVlanConfigInterfacesForbiddenVlans3KTo4K }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 5 }

mgmtVlanConfigInterfacesSvlTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigInterfacesSvlVlanId,
                  mgmtVlanConfigInterfacesSvlFilterId,
                  mgmtVlanConfigInterfacesSvlAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 6 }

mgmtVlanConfigInterfacesSvlTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanConfigInterfacesSvlTableRowEditorVlanId,
                  mgmtVlanConfigInterfacesSvlTableRowEditorFilterId,
                  mgmtVlanConfigInterfacesSvlTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 7 }

mgmtVlanStatusInterfacesTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanStatusInterfacesIfIndex,
                  mgmtVlanStatusInterfacesVlanUser,
                  mgmtVlanStatusInterfacesPvid,
                  mgmtVlanStatusInterfacesUvid,
                  mgmtVlanStatusInterfacesPortType,
                  mgmtVlanStatusInterfacesIngressFiltering,
                  mgmtVlanStatusInterfacesIngressAcceptance,
                  mgmtVlanStatusInterfacesEgressTagging }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 8 }

mgmtVlanStatusMembershipsVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanStatusMembershipsVlanVlanId,
                  mgmtVlanStatusMembershipsVlanVlanUser,
                  mgmtVlanStatusMembershipsVlanPortList }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanMibGroups 9 }

mgmtVlanMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtVlanCapabilitiesInfoGroup,
                       mgmtVlanConfigGlobalsMainInfoGroup,
                       mgmtVlanConfigGlobalsNameTableInfoGroup,
                       mgmtVlanConfigGlobalsFloodingTableInfoGroup,
                       mgmtVlanConfigInterfacesTableInfoGroup,
                       mgmtVlanConfigInterfacesSvlTableInfoGroup,
                       mgmtVlanConfigInterfacesSvlTableRowEditorInfoGroup,
                       mgmtVlanStatusInterfacesTableInfoGroup,
                       mgmtVlanStatusMembershipsVlanTableInfoGroup }

    ::= { mgmtVlanMibCompliances 1 }

END
