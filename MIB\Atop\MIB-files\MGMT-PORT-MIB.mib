-- *****************************************************************
-- PORT-MIB:  
-- ****************************************************************

MGMT-PORT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortStatusSpeed FROM MGMT-TC
    MGMTSfpTransceiver FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtPortMib MODULE-IDENTITY
    LAST-UPDATED "202101060000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the PORT MIB"
    REVISION    "202101060000Z"
    DESCRIPTION
        "Added more capabilities, ForceClause73, and FECMode."
    REVISION    "202011160000Z"
    DESCRIPTION
        "Added SFPDateCode to SFP info."
    REVISION    "201911290000Z"
    DESCRIPTION
        "Added more media types in MGMTPortMedia enumeration."
    REVISION    "201808240000Z"
    DESCRIPTION
        "Add capabilities."
    REVISION    "201711270000Z"
    DESCRIPTION
        "Changed PortConfigAdvertiseDisabled to Unsigned32"
    REVISION    "201701270000Z"
    DESCRIPTION
        "Added 802.3br statistics"
    REVISION    "201611280000Z"
    DESCRIPTION
        "Add 2.5G auto-negotiate capability"
    REVISION    "201507070000Z"
    DESCRIPTION
        "Port speed is moved into the TC MIB"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 11 }


MGMTPortFc ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration controls the interface flow control."
    SYNTAX      INTEGER { off(0), on(1) }

MGMTPortFecMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration controls/shows the Forward Error Correction mode."
    SYNTAX      INTEGER { fecModeNone(0), fecModeRFec(1),
                          fecModeRSFec(2), fecModeAuto(3) }

MGMTPortMedia ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration controls the interface media type."
    SYNTAX      INTEGER { rj45(0), sfp(1), dual(2) }

MGMTPortPhyVeriPhyStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations show the VeriPhy status."
    SYNTAX      INTEGER { correctlyTerminatedPair(0), openPair(1),
                          shortPair(2), abnormalTermination(4),
                          crossPairShortToPairA(8),
                          crossPairShortToPairB(9),
                          crossPairShortToPairC(10),
                          crossPairShortToPairD(11),
                          abnormalCrossPairCouplingToPairA(12),
                          abnormalCrossPairCouplingToPairB(13),
                          abnormalCrossPairCouplingToPairC(14),
                          abnormalCrossPairCouplingToPairD(15),
                          unknownResult(16), veriPhyRunning(17) }

MGMTPortSpeed ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration controls the interface speed. E.g force10ModeFdx means
         force 10Mbs full duplex."
    SYNTAX      INTEGER { force10ModeFdx(0), force10ModeHdx(1),
                          force100ModeFdx(2), force100ModeHdx(3),
                          force1GModeFdx(4), autoNegMode(5),
                          force2G5ModeFdx(6), force5GModeFdx(7),
                          force10GModeFdx(8), force12GModeFdx(9),
                          force25GModeFdx(10) }

mgmtPortMibObjects OBJECT IDENTIFIER
    ::= { mgmtPortMib 1 }

mgmtPortCapabilities OBJECT IDENTIFIER
    ::= { mgmtPortMibObjects 1 }

mgmtPortCapabilitiesLastFrameLenThreshold OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The max length of the frames counted in the last range of the frame
         counter group."
    ::= { mgmtPortCapabilities 1 }

mgmtPortCapabilitiesHasKR OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If HasKRv2 or HasKRv3 is true, so is this"
    ::= { mgmtPortCapabilities 2 }

mgmtPortCapabilitiesFrameLengthMaxMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The smallest maximum acceptable ingress frame length that can be
         configured."
    ::= { mgmtPortCapabilities 3 }

mgmtPortCapabilitiesFrameLengthMaxMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The largest maximum acceptable ingress frame length that can be
         configured."
    ::= { mgmtPortCapabilities 4 }

mgmtPortCapabilitiesHasKRv2 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether at least one of the ports supports 10G BASE-KR."
    ::= { mgmtPortCapabilities 5 }

mgmtPortCapabilitiesHasKRv3 OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether at least one of the ports supports 10G or 25G
         BASE-KR."
    ::= { mgmtPortCapabilities 6 }

mgmtPortCapabilitiesAggrCaps OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The aggregated capability flags for all ports on this platform."
    ::= { mgmtPortCapabilities 7 }

mgmtPortCapabilitiesPortCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of ports on this device"
    ::= { mgmtPortCapabilities 8 }

mgmtPortCapabilitiesHasPFC OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "True if this platform supports priority-based flow control"
    ::= { mgmtPortCapabilities 9 }

mgmtPortConfig OBJECT IDENTIFIER
    ::= { mgmtPortMibObjects 2 }

mgmtPortConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the port interface parameters"
    ::= { mgmtPortConfig 1 }

mgmtPortConfigEntry OBJECT-TYPE
    SYNTAX      MGMTPortConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of parameters"
    INDEX       { mgmtPortConfigIfIndex }
    ::= { mgmtPortConfigTable 1 }

MGMTPortConfigEntry ::= SEQUENCE {
    mgmtPortConfigIfIndex            MGMTInterfaceIndex,
    mgmtPortConfigShutdown           TruthValue,
    mgmtPortConfigSpeed              MGMTPortSpeed,
    mgmtPortConfigAdvertiseDisabled  Unsigned32,
    mgmtPortConfigMediaType          MGMTPortMedia,
    mgmtPortConfigFC                 MGMTPortFc,
    mgmtPortConfigMTU                Unsigned32,
    mgmtPortConfigExcessiveRestart   TruthValue,
    mgmtPortConfigPFC                MGMTUnsigned8,
    mgmtPortConfigFrameLengthCheck   TruthValue,
    mgmtPortConfigForceClause73      TruthValue,
    mgmtPortConfigFECMode            MGMTPortFecMode
}

mgmtPortConfigIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortConfigEntry 1 }

mgmtPortConfigShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether interface is shutdown or powered up. Set to TRUE in
         order to power down the interface."
    ::= { mgmtPortConfigEntry 2 }

mgmtPortConfigSpeed OBJECT-TYPE
    SYNTAX      MGMTPortSpeed
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the port speed and duplex."
    ::= { mgmtPortConfigEntry 3 }

mgmtPortConfigAdvertiseDisabled OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "In auto mode, bitmask that allows features not to be advertised.
         
         Bit 0: Disable half duplex advertising.
         
         Bit 1: Disable full duplex advertising.
         
         Bit 3: Disable 2.5G advertising.
         
         Bit 4: Disable 1G advertising.
         
         Bit 6: Disable 100M advertising.
         
         Bit 7: Disable 10M advertising.
         
         Bit 8: Disable 5G advertising.
         
         Bit 9: Disable 10G advertising.
         
         When not in auto mode, the value shall be zero."
    ::= { mgmtPortConfigEntry 4 }

mgmtPortConfigMediaType OBJECT-TYPE
    SYNTAX      MGMTPortMedia
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the port media type."
    ::= { mgmtPortConfigEntry 5 }

mgmtPortConfigFC OBJECT-TYPE
    SYNTAX      MGMTPortFc
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the port flow control mode."
    ::= { mgmtPortConfigEntry 6 }

mgmtPortConfigMTU OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the port's Maximum Transmission Unit."
    ::= { mgmtPortConfigEntry 7 }

mgmtPortConfigExcessiveRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TRUE to restart half-duplex back-off algorithm after 16 collisions.
         FALSE to discard frame after 16 collisions"
    ::= { mgmtPortConfigEntry 8 }

mgmtPortConfigPFC OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "802.1Qbb Priority Flow Control bitmask, one bit for each priority.E.g.
         0x01 = prio 0, 0x80 = prio 7, 0xFF = prio 0-7"
    ::= { mgmtPortConfigEntry 9 }

mgmtPortConfigFrameLengthCheck OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TRUE to enforce 802.3 frame length check (from Ethertype field). If
         enabled frames with length that doesn't match the frame length field
         will be dropped."
    ::= { mgmtPortConfigEntry 10 }

mgmtPortConfigForceClause73 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TRUE to enforce 802.3 clause 73 aneg. Speed must be set to 'AUTO' in
         that case."
    ::= { mgmtPortConfigEntry 11 }

mgmtPortConfigFECMode OBJECT-TYPE
    SYNTAX      MGMTPortFecMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether to force a given Forward Error Correction mode on the
         port."
    ::= { mgmtPortConfigEntry 12 }

mgmtPortStatus OBJECT IDENTIFIER
    ::= { mgmtPortMibObjects 3 }

mgmtPortStatusInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatusInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the status of the ports"
    ::= { mgmtPortStatus 1 }

mgmtPortStatusInformationEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatusInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of status parameters"
    INDEX       { mgmtPortStatusInformationIfIndex }
    ::= { mgmtPortStatusInformationTable 1 }

MGMTPortStatusInformationEntry ::= SEQUENCE {
    mgmtPortStatusInformationIfIndex        MGMTInterfaceIndex,
    mgmtPortStatusInformationLink           TruthValue,
    mgmtPortStatusInformationFdx            TruthValue,
    mgmtPortStatusInformationFiber          TruthValue,
    mgmtPortStatusInformationSpeed          MGMTPortStatusSpeed,
    mgmtPortStatusInformationSFPType        MGMTSfpTransceiver,
    mgmtPortStatusInformationSFPVendorName  MGMTDisplayString,
    mgmtPortStatusInformationSFPVendorPN    MGMTDisplayString,
    mgmtPortStatusInformationSFPVendorRev   MGMTDisplayString,
    mgmtPortStatusInformationSFPVendorSN    MGMTDisplayString,
    mgmtPortStatusInformationSFPDateCode    MGMTDisplayString,
    mgmtPortStatusInformationFecMode        MGMTPortFecMode
}

mgmtPortStatusInformationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatusInformationEntry 1 }

mgmtPortStatusInformationLink OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows whether interface has link."
    ::= { mgmtPortStatusInformationEntry 2 }

mgmtPortStatusInformationFdx OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows whether interface is running in full duplex."
    ::= { mgmtPortStatusInformationEntry 3 }

mgmtPortStatusInformationFiber OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows whether interface is an SFP interface or RJ45."
    ::= { mgmtPortStatusInformationEntry 4 }

mgmtPortStatusInformationSpeed OBJECT-TYPE
    SYNTAX      MGMTPortStatusSpeed
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the current interface speed."
    ::= { mgmtPortStatusInformationEntry 5 }

mgmtPortStatusInformationSFPType OBJECT-TYPE
    SYNTAX      MGMTSfpTransceiver
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the current interface SFP type."
    ::= { mgmtPortStatusInformationEntry 6 }

mgmtPortStatusInformationSFPVendorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..19))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the SFP vendor name."
    ::= { mgmtPortStatusInformationEntry 7 }

mgmtPortStatusInformationSFPVendorPN OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..19))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the SFP vendor Product Number."
    ::= { mgmtPortStatusInformationEntry 8 }

mgmtPortStatusInformationSFPVendorRev OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..5))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the SFP vendor Revision."
    ::= { mgmtPortStatusInformationEntry 9 }

mgmtPortStatusInformationSFPVendorSN OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..19))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the SFP vendor Serial Number."
    ::= { mgmtPortStatusInformationEntry 10 }

mgmtPortStatusInformationSFPDateCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the SFP Date Code."
    ::= { mgmtPortStatusInformationEntry 11 }

mgmtPortStatusInformationFecMode OBJECT-TYPE
    SYNTAX      MGMTPortFecMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Tells the current FEC mode used on the port"
    ::= { mgmtPortStatusInformationEntry 12 }

mgmtPortStatusVeriPhyResult OBJECT IDENTIFIER
    ::= { mgmtPortStatus 3 }

mgmtPortStatusVeriPhyResultTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatusVeriPhyResultEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the VeriPhy result from the last VeriPhy run for
         the interface"
    ::= { mgmtPortStatusVeriPhyResult 1 }

mgmtPortStatusVeriPhyResultEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatusVeriPhyResultEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of VeriPhy results"
    INDEX       { mgmtPortStatusVeriPhyResultIfIndex }
    ::= { mgmtPortStatusVeriPhyResultTable 1 }

MGMTPortStatusVeriPhyResultEntry ::= SEQUENCE {
    mgmtPortStatusVeriPhyResultIfIndex                   MGMTInterfaceIndex,
    mgmtPortStatusVeriPhyResultVeriPhyStatusPairA        MGMTPortPhyVeriPhyStatus,
    mgmtPortStatusVeriPhyResultVeriPhyStatusPairB        MGMTPortPhyVeriPhyStatus,
    mgmtPortStatusVeriPhyResultVeriPhyStatusPairC        MGMTPortPhyVeriPhyStatus,
    mgmtPortStatusVeriPhyResultVeriPhyStatusPairD        MGMTPortPhyVeriPhyStatus,
    mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairA  MGMTUnsigned8,
    mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairB  MGMTUnsigned8,
    mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairC  MGMTUnsigned8,
    mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairD  MGMTUnsigned8
}

mgmtPortStatusVeriPhyResultIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatusVeriPhyResultEntry 1 }

mgmtPortStatusVeriPhyResultVeriPhyStatusPairA OBJECT-TYPE
    SYNTAX      MGMTPortPhyVeriPhyStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status for the cable pair
         
         0 - Cable is Correctly terminated pair
         
         1 - Open pair
         
         2 - Shorted pair
         
         4 - Abnormal termination
         
         8 - Cross-pair short to pair A
         
         9 - Cross-pair short to pair B
         
         10 - Cross-pair short to pair C
         
         11 - Cross-pair short to pair D
         
         12 - Abnormal cross-pair coupling - pair A
         
         13 - Abnormal cross-pair coupling - pair B
         
         14 - Abnormal cross-pair coupling - pair C
         
         15 - Abnormal cross-pair coupling - pair D"
    ::= { mgmtPortStatusVeriPhyResultEntry 2 }

mgmtPortStatusVeriPhyResultVeriPhyStatusPairB OBJECT-TYPE
    SYNTAX      MGMTPortPhyVeriPhyStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status for the cable pair
         
         0 - Cable is Correctly terminated pair
         
         1 - Open pair
         
         2 - Shorted pair
         
         4 - Abnormal termination
         
         8 - Cross-pair short to pair A
         
         9 - Cross-pair short to pair B
         
         10 - Cross-pair short to pair C
         
         11 - Cross-pair short to pair D
         
         12 - Abnormal cross-pair coupling - pair A
         
         13 - Abnormal cross-pair coupling - pair B
         
         14 - Abnormal cross-pair coupling - pair C
         
         15 - Abnormal cross-pair coupling - pair D"
    ::= { mgmtPortStatusVeriPhyResultEntry 3 }

mgmtPortStatusVeriPhyResultVeriPhyStatusPairC OBJECT-TYPE
    SYNTAX      MGMTPortPhyVeriPhyStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status for the cable pair
         
         0 - Cable is Correctly terminated pair
         
         1 - Open pair
         
         2 - Shorted pair
         
         4 - Abnormal termination
         
         8 - Cross-pair short to pair A
         
         9 - Cross-pair short to pair B
         
         10 - Cross-pair short to pair C
         
         11 - Cross-pair short to pair D
         
         12 - Abnormal cross-pair coupling - pair A
         
         13 - Abnormal cross-pair coupling - pair B
         
         14 - Abnormal cross-pair coupling - pair C
         
         15 - Abnormal cross-pair coupling - pair D"
    ::= { mgmtPortStatusVeriPhyResultEntry 4 }

mgmtPortStatusVeriPhyResultVeriPhyStatusPairD OBJECT-TYPE
    SYNTAX      MGMTPortPhyVeriPhyStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status for the cable pair
         
         0 - Cable is Correctly terminated pair
         
         1 - Open pair
         
         2 - Shorted pair
         
         4 - Abnormal termination
         
         8 - Cross-pair short to pair A
         
         9 - Cross-pair short to pair B
         
         10 - Cross-pair short to pair C
         
         11 - Cross-pair short to pair D
         
         12 - Abnormal cross-pair coupling - pair A
         
         13 - Abnormal cross-pair coupling - pair B
         
         14 - Abnormal cross-pair coupling - pair C
         
         15 - Abnormal cross-pair coupling - pair D"
    ::= { mgmtPortStatusVeriPhyResultEntry 5 }

mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairA OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status cable length i meters for the cable pair. When VeriPhy
         is completed, the cable diagnostics results is shown in the VeriPhy
         status table. Note that VeriPHY is only accurate for cables of length 7
         - 140 meters.
         
         The resolution is 3 meters"
    ::= { mgmtPortStatusVeriPhyResultEntry 6 }

mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairB OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status cable length i meters for the cable pair. When VeriPhy
         is completed, the cable diagnostics results is shown in the VeriPhy
         status table. Note that VeriPHY is only accurate for cables of length 7
         - 140 meters.
         
         The resolution is 3 meters"
    ::= { mgmtPortStatusVeriPhyResultEntry 7 }

mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairC OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status cable length i meters for the cable pair. When VeriPhy
         is completed, the cable diagnostics results is shown in the VeriPhy
         status table. Note that VeriPHY is only accurate for cables of length 7
         - 140 meters.
         
         The resolution is 3 meters"
    ::= { mgmtPortStatusVeriPhyResultEntry 8 }

mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairD OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VeriPhy status cable length i meters for the cable pair. When VeriPhy
         is completed, the cable diagnostics results is shown in the VeriPhy
         status table. Note that VeriPHY is only accurate for cables of length 7
         - 140 meters.
         
         The resolution is 3 meters"
    ::= { mgmtPortStatusVeriPhyResultEntry 9 }

mgmtPortControl OBJECT IDENTIFIER
    ::= { mgmtPortMibObjects 4 }

mgmtPortControlStatisticsClear OBJECT IDENTIFIER
    ::= { mgmtPortControl 1 }

mgmtPortControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear port Interface statistics"
    ::= { mgmtPortControlStatisticsClear 1 }

mgmtPortControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTPortControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of control parameters"
    INDEX       { mgmtPortControlStatisticsClearIfIndex }
    ::= { mgmtPortControlStatisticsClearTable 1 }

MGMTPortControlStatisticsClearEntry ::= SEQUENCE {
    mgmtPortControlStatisticsClearIfIndex          MGMTInterfaceIndex,
    mgmtPortControlStatisticsClearStatisticsClear  TruthValue
}

mgmtPortControlStatisticsClearIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortControlStatisticsClearEntry 1 }

mgmtPortControlStatisticsClearStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the statistics of an interface."
    ::= { mgmtPortControlStatisticsClearEntry 2 }

mgmtPortControlVeriPhyStart OBJECT IDENTIFIER
    ::= { mgmtPortControl 2 }

mgmtPortControlVeriPhyStartTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortControlVeriPhyStartEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to start VeriPhy for the interface"
    ::= { mgmtPortControlVeriPhyStart 1 }

mgmtPortControlVeriPhyStartEntry OBJECT-TYPE
    SYNTAX      MGMTPortControlVeriPhyStartEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of control parameters"
    INDEX       { mgmtPortControlVeriPhyStartIfIndex }
    ::= { mgmtPortControlVeriPhyStartTable 1 }

MGMTPortControlVeriPhyStartEntry ::= SEQUENCE {
    mgmtPortControlVeriPhyStartIfIndex  MGMTInterfaceIndex,
    mgmtPortControlVeriPhyStartStart    TruthValue
}

mgmtPortControlVeriPhyStartIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortControlVeriPhyStartEntry 1 }

mgmtPortControlVeriPhyStartStart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to start VeriPHy for the interface.
         
         When running 10 and 100 Mbps, ports will be linked down while running
         VeriPHY. Therefore, running VeriPHY on a 10 or 100 Mbps management port
         will cause the switch to stop responding until VeriPHY is complete."
    ::= { mgmtPortControlVeriPhyStartEntry 2 }

mgmtPortStatistics OBJECT IDENTIFIER
    ::= { mgmtPortMibObjects 5 }

mgmtPortStatisticsRmonStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsRmonStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port interface RMON statistics counters"
    ::= { mgmtPortStatistics 1 }

mgmtPortStatisticsRmonStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsRmonStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsRmonStatisticsIfIndex }
    ::= { mgmtPortStatisticsRmonStatisticsTable 1 }

MGMTPortStatisticsRmonStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsRmonStatisticsIfIndex            MGMTInterfaceIndex,
    mgmtPortStatisticsRmonStatisticsRxDropEvents       Counter64,
    mgmtPortStatisticsRmonStatisticsRxOctets           Counter64,
    mgmtPortStatisticsRmonStatisticsRxPkts             Counter64,
    mgmtPortStatisticsRmonStatisticsRxBroadcastPkts    Counter64,
    mgmtPortStatisticsRmonStatisticsRxMulticastPkts    Counter64,
    mgmtPortStatisticsRmonStatisticsRxCrcAlignErrPkts  Counter64,
    mgmtPortStatisticsRmonStatisticsRxUndersizePkts    Counter64,
    mgmtPortStatisticsRmonStatisticsRxOversizePkts     Counter64,
    mgmtPortStatisticsRmonStatisticsRxFragmentsPkts    Counter64,
    mgmtPortStatisticsRmonStatisticsRxJabbersPkts      Counter64,
    mgmtPortStatisticsRmonStatisticsRx64Pkts           Counter64,
    mgmtPortStatisticsRmonStatisticsRx65to127Pkts      Counter64,
    mgmtPortStatisticsRmonStatisticsRx128to255Pkts     Counter64,
    mgmtPortStatisticsRmonStatisticsRx256to511Pkts     Counter64,
    mgmtPortStatisticsRmonStatisticsRx512to1023Pkts    Counter64,
    mgmtPortStatisticsRmonStatisticsRx1024to1518Pkts   Counter64,
    mgmtPortStatisticsRmonStatisticsRx1519PktsToMax    Counter64,
    mgmtPortStatisticsRmonStatisticsTxDropEvents       Counter64,
    mgmtPortStatisticsRmonStatisticsTxOctets           Counter64,
    mgmtPortStatisticsRmonStatisticsTxPkts             Counter64,
    mgmtPortStatisticsRmonStatisticsTxBroadcastPkts    Counter64,
    mgmtPortStatisticsRmonStatisticsTxMulticastPkts    Counter64,
    mgmtPortStatisticsRmonStatisticsTx64Pkts           Counter64,
    mgmtPortStatisticsRmonStatisticsTx65to127Pkts      Counter64,
    mgmtPortStatisticsRmonStatisticsTx128to255Pkts     Counter64,
    mgmtPortStatisticsRmonStatisticsTx256to511Pkts     Counter64,
    mgmtPortStatisticsRmonStatisticsTx512to1023Pkts    Counter64,
    mgmtPortStatisticsRmonStatisticsTx1024to1518Pkts   Counter64,
    mgmtPortStatisticsRmonStatisticsTx1519PktsToMax    Counter64
}

mgmtPortStatisticsRmonStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 1 }

mgmtPortStatisticsRmonStatisticsRxDropEvents OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of frames discarded due to ingress congestion."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 2 }

mgmtPortStatisticsRmonStatisticsRxOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received (good and bad) bytes. Includes FCS, but
         excludes framing bits."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 3 }

mgmtPortStatisticsRmonStatisticsRxPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received (good and bad) packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 4 }

mgmtPortStatisticsRmonStatisticsRxBroadcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received (good and bad) broadcast packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 5 }

mgmtPortStatisticsRmonStatisticsRxMulticastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received (good and bad) multicast packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 6 }

mgmtPortStatisticsRmonStatisticsRxCrcAlignErrPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of frames received with CRC or alignment errors."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 7 }

mgmtPortStatisticsRmonStatisticsRxUndersizePkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of short frames (frames that are smaller than 64
         bytes) received with valid CRC."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 8 }

mgmtPortStatisticsRmonStatisticsRxOversizePkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of long frames received with valid CRC."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 9 }

mgmtPortStatisticsRmonStatisticsRxFragmentsPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of short frames (frames that are smaller than 64
         bytes) received with invalid CRC."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 10 }

mgmtPortStatisticsRmonStatisticsRxJabbersPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of long frames (frames that are longer than the configured
         maximum frame length for this interface) received with invalid CRC."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 11 }

mgmtPortStatisticsRmonStatisticsRx64Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of 64 bytes frames received."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 12 }

mgmtPortStatisticsRmonStatisticsRx65to127Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size within 65 to 127 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 13 }

mgmtPortStatisticsRmonStatisticsRx128to255Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size within 128 to 255 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 14 }

mgmtPortStatisticsRmonStatisticsRx256to511Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size within 256 to 511 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 15 }

mgmtPortStatisticsRmonStatisticsRx512to1023Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size within 512 to 1023 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 16 }

mgmtPortStatisticsRmonStatisticsRx1024to1518Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size within 1024 to nn bytes where
         nn is varying between platforms. The actual value of nn is shown in the
         capabilities:LastFrameLenThreshold parameter."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 17 }

mgmtPortStatisticsRmonStatisticsRx1519PktsToMax OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of received frames with size larger than nn bytes where nn
         is varying between platforms. The actual value of nn is shown in the
         capabilities:LastFrameLenThreshold parameter."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 18 }

mgmtPortStatisticsRmonStatisticsTxDropEvents OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of frames discarded due to egress congestion."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 19 }

mgmtPortStatisticsRmonStatisticsTxOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of transmitted (good and bad) bytes. Includes FCS, but
         excludes framing bits."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 20 }

mgmtPortStatisticsRmonStatisticsTxPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of transmitted (good and bad) packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 21 }

mgmtPortStatisticsRmonStatisticsTxBroadcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of transmitted (good and bad) broadcast packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 22 }

mgmtPortStatisticsRmonStatisticsTxMulticastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of transmitted (good and bad) multicast packets."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 23 }

mgmtPortStatisticsRmonStatisticsTx64Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of 64 bytes frames transmitted."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 24 }

mgmtPortStatisticsRmonStatisticsTx65to127Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size within 65 to 127 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 25 }

mgmtPortStatisticsRmonStatisticsTx128to255Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size within 128 to 255 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 26 }

mgmtPortStatisticsRmonStatisticsTx256to511Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size within 256 to 511 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 27 }

mgmtPortStatisticsRmonStatisticsTx512to1023Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size within 512 to 1023 bytes."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 28 }

mgmtPortStatisticsRmonStatisticsTx1024to1518Pkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size within 1024 to nn bytes
         where nn is varying between platforms. The actual value of nn is shown
         in the capabilities:LastFrameLenThreshold parameter."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 29 }

mgmtPortStatisticsRmonStatisticsTx1519PktsToMax OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of transmitted frames with size larger than nn bytes where
         nn is varying between platforms. The actual value of nn is shown in the
         capabilities:LastFrameLenThreshold parameter."
    ::= { mgmtPortStatisticsRmonStatisticsEntry 30 }

mgmtPortStatisticsIfGroupStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsIfGroupStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port interfaces group (RFC 2863) counters"
    ::= { mgmtPortStatistics 2 }

mgmtPortStatisticsIfGroupStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsIfGroupStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsIfGroupStatisticsIfIndex }
    ::= { mgmtPortStatisticsIfGroupStatisticsTable 1 }

MGMTPortStatisticsIfGroupStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsIfGroupStatisticsIfIndex           MGMTInterfaceIndex,
    mgmtPortStatisticsIfGroupStatisticsRxOctets          Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxUnicastPkts     Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxMulticastPkts   Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxBroadcastPkts   Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxNonUnicastPkts  Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxDiscards        Counter64,
    mgmtPortStatisticsIfGroupStatisticsRxErrors          Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxOctets          Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxUnicastPkts     Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxMulticastPkts   Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxBroadcastPkts   Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxNonUnicastPkts  Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxDiscardPkts     Counter64,
    mgmtPortStatisticsIfGroupStatisticsTxErrorPkts       Counter64
}

mgmtPortStatisticsIfGroupStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 1 }

mgmtPortStatisticsIfGroupStatisticsRxOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of bytes received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 2 }

mgmtPortStatisticsIfGroupStatisticsRxUnicastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of uni-cast frames received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 3 }

mgmtPortStatisticsIfGroupStatisticsRxMulticastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of multi-cast frames received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 4 }

mgmtPortStatisticsIfGroupStatisticsRxBroadcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of broad-cast frames received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 5 }

mgmtPortStatisticsIfGroupStatisticsRxNonUnicastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of non uni-cast frames received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 6 }

mgmtPortStatisticsIfGroupStatisticsRxDiscards OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received frames discarded."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 7 }

mgmtPortStatisticsIfGroupStatisticsRxErrors OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of frames with errors received."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 8 }

mgmtPortStatisticsIfGroupStatisticsTxOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of bytes transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 9 }

mgmtPortStatisticsIfGroupStatisticsTxUnicastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of uni-cast frames transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 10 }

mgmtPortStatisticsIfGroupStatisticsTxMulticastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of multi-cast frames transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 11 }

mgmtPortStatisticsIfGroupStatisticsTxBroadcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of broad-cast frames transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 12 }

mgmtPortStatisticsIfGroupStatisticsTxNonUnicastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of non uni-cast frames transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 13 }

mgmtPortStatisticsIfGroupStatisticsTxDiscardPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of discard frames which should been transmitted."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 14 }

mgmtPortStatisticsIfGroupStatisticsTxErrorPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of frames transmit with error."
    ::= { mgmtPortStatisticsIfGroupStatisticsEntry 15 }

mgmtPortStatisticsEthernetLikeStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsEthernetLikeStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port Ethernet-like interfaces counters"
    ::= { mgmtPortStatistics 3 }

mgmtPortStatisticsEthernetLikeStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsEthernetLikeStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsEthernetLikeStatisticsIfIndex }
    ::= { mgmtPortStatisticsEthernetLikeStatisticsTable 1 }

MGMTPortStatisticsEthernetLikeStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsEthernetLikeStatisticsIfIndex        MGMTInterfaceIndex,
    mgmtPortStatisticsEthernetLikeStatisticsRxPauseFrames  Counter64,
    mgmtPortStatisticsEthernetLikeStatisticsTxPauseFrames  Counter64
}

mgmtPortStatisticsEthernetLikeStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsEthernetLikeStatisticsEntry 1 }

mgmtPortStatisticsEthernetLikeStatisticsRxPauseFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of pause frames received."
    ::= { mgmtPortStatisticsEthernetLikeStatisticsEntry 2 }

mgmtPortStatisticsEthernetLikeStatisticsTxPauseFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of pause frames transmitted."
    ::= { mgmtPortStatisticsEthernetLikeStatisticsEntry 3 }

mgmtPortStatisticsQueuesStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsQueuesStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port interfaces queues counters"
    ::= { mgmtPortStatistics 4 }

mgmtPortStatisticsQueuesStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsQueuesStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsQueuesStatisticsIfIndex,
                  mgmtPortStatisticsQueuesStatisticsQueue }
    ::= { mgmtPortStatisticsQueuesStatisticsTable 1 }

MGMTPortStatisticsQueuesStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsQueuesStatisticsIfIndex  MGMTInterfaceIndex,
    mgmtPortStatisticsQueuesStatisticsQueue    Integer32,
    mgmtPortStatisticsQueuesStatisticsRxPrio   Counter64,
    mgmtPortStatisticsQueuesStatisticsTxPrio   Counter64
}

mgmtPortStatisticsQueuesStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsQueuesStatisticsEntry 1 }

mgmtPortStatisticsQueuesStatisticsQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index."
    ::= { mgmtPortStatisticsQueuesStatisticsEntry 2 }

mgmtPortStatisticsQueuesStatisticsRxPrio OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of frames received for the queue."
    ::= { mgmtPortStatisticsQueuesStatisticsEntry 3 }

mgmtPortStatisticsQueuesStatisticsTxPrio OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of frames transmitted for the queue."
    ::= { mgmtPortStatisticsQueuesStatisticsEntry 4 }

mgmtPortStatisticsBridgeStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsBridgeStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port interface bridge counters"
    ::= { mgmtPortStatistics 5 }

mgmtPortStatisticsBridgeStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsBridgeStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsBridgeStatisticsIfIndex }
    ::= { mgmtPortStatisticsBridgeStatisticsTable 1 }

MGMTPortStatisticsBridgeStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsBridgeStatisticsIfIndex          MGMTInterfaceIndex,
    mgmtPortStatisticsBridgeStatisticsRxBridgeDiscard  Counter64
}

mgmtPortStatisticsBridgeStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsBridgeStatisticsEntry 1 }

mgmtPortStatisticsBridgeStatisticsRxBridgeDiscard OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of bridge frames discarded."
    ::= { mgmtPortStatisticsBridgeStatisticsEntry 2 }

mgmtPortStatisticsDot3brStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortStatisticsDot3brStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the port 802.3br counters"
    ::= { mgmtPortStatistics 6 }

mgmtPortStatisticsDot3brStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTPortStatisticsDot3brStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtPortStatisticsDot3brStatisticsIfIndex }
    ::= { mgmtPortStatisticsDot3brStatisticsTable 1 }

MGMTPortStatisticsDot3brStatisticsEntry ::= SEQUENCE {
    mgmtPortStatisticsDot3brStatisticsIfIndex                      MGMTInterfaceIndex,
    mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssErrorCount  Counter64,
    mgmtPortStatisticsDot3brStatisticsAMACMergeFrameSmdErrorCount  Counter64,
    mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssOkCount     Counter64,
    mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountRx         Counter64,
    mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountTx         Counter64,
    mgmtPortStatisticsDot3brStatisticsAMACMergeHoldCount           Counter64
}

mgmtPortStatisticsDot3brStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 1 }

mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssErrorCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of MAC frames with reassembly errors."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 2 }

mgmtPortStatisticsDot3brStatisticsAMACMergeFrameSmdErrorCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of received MAC frames / MAC frame fragments rejected due to
         unknown SMD value or arriving with an SMD-C when no frame is in
         progress."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 3 }

mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssOkCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of MAC frames that were successfully reassembled and delivered
         to MAC."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 4 }

mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountRx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of the number of additional mPackets received due to
         preemption."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 5 }

mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountTx OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of the number of additional mPackets transmitted due to
         preemption."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 6 }

mgmtPortStatisticsDot3brStatisticsAMACMergeHoldCount OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A count of the number of times the variable hold (see 802.3br, section
         ********) transitions from FALSE to TRUE."
    ::= { mgmtPortStatisticsDot3brStatisticsEntry 7 }

mgmtPortMibConformance OBJECT IDENTIFIER
    ::= { mgmtPortMib 2 }

mgmtPortMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPortMibConformance 1 }

mgmtPortMibGroups OBJECT IDENTIFIER
    ::= { mgmtPortMibConformance 2 }

mgmtPortCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortCapabilitiesLastFrameLenThreshold,
                  mgmtPortCapabilitiesHasKR,
                  mgmtPortCapabilitiesFrameLengthMaxMin,
                  mgmtPortCapabilitiesFrameLengthMaxMax,
                  mgmtPortCapabilitiesHasKRv2,
                  mgmtPortCapabilitiesHasKRv3,
                  mgmtPortCapabilitiesAggrCaps,
                  mgmtPortCapabilitiesPortCnt,
                  mgmtPortCapabilitiesHasPFC }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 1 }

mgmtPortConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortConfigIfIndex, mgmtPortConfigShutdown,
                  mgmtPortConfigSpeed,
                  mgmtPortConfigAdvertiseDisabled,
                  mgmtPortConfigMediaType, mgmtPortConfigFC,
                  mgmtPortConfigMTU, mgmtPortConfigExcessiveRestart,
                  mgmtPortConfigPFC, mgmtPortConfigFrameLengthCheck,
                  mgmtPortConfigForceClause73, mgmtPortConfigFECMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 2 }

mgmtPortStatusInformationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatusInformationIfIndex,
                  mgmtPortStatusInformationLink,
                  mgmtPortStatusInformationFdx,
                  mgmtPortStatusInformationFiber,
                  mgmtPortStatusInformationSpeed,
                  mgmtPortStatusInformationSFPType,
                  mgmtPortStatusInformationSFPVendorName,
                  mgmtPortStatusInformationSFPVendorPN,
                  mgmtPortStatusInformationSFPVendorRev,
                  mgmtPortStatusInformationSFPVendorSN,
                  mgmtPortStatusInformationSFPDateCode,
                  mgmtPortStatusInformationFecMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 3 }

mgmtPortStatusVeriPhyResultTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatusVeriPhyResultIfIndex,
                  mgmtPortStatusVeriPhyResultVeriPhyStatusPairA,
                  mgmtPortStatusVeriPhyResultVeriPhyStatusPairB,
                  mgmtPortStatusVeriPhyResultVeriPhyStatusPairC,
                  mgmtPortStatusVeriPhyResultVeriPhyStatusPairD,
                  mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairA,
                  mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairB,
                  mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairC,
                  mgmtPortStatusVeriPhyResultVeriPhyLengthStatusPairD }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 4 }

mgmtPortControlStatisticsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortControlStatisticsClearIfIndex,
                  mgmtPortControlStatisticsClearStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 5 }

mgmtPortControlVeriPhyStartTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortControlVeriPhyStartIfIndex,
                  mgmtPortControlVeriPhyStartStart }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 6 }

mgmtPortStatisticsRmonStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsRmonStatisticsIfIndex,
                  mgmtPortStatisticsRmonStatisticsRxDropEvents,
                  mgmtPortStatisticsRmonStatisticsRxOctets,
                  mgmtPortStatisticsRmonStatisticsRxPkts,
                  mgmtPortStatisticsRmonStatisticsRxBroadcastPkts,
                  mgmtPortStatisticsRmonStatisticsRxMulticastPkts,
                  mgmtPortStatisticsRmonStatisticsRxCrcAlignErrPkts,
                  mgmtPortStatisticsRmonStatisticsRxUndersizePkts,
                  mgmtPortStatisticsRmonStatisticsRxOversizePkts,
                  mgmtPortStatisticsRmonStatisticsRxFragmentsPkts,
                  mgmtPortStatisticsRmonStatisticsRxJabbersPkts,
                  mgmtPortStatisticsRmonStatisticsRx64Pkts,
                  mgmtPortStatisticsRmonStatisticsRx65to127Pkts,
                  mgmtPortStatisticsRmonStatisticsRx128to255Pkts,
                  mgmtPortStatisticsRmonStatisticsRx256to511Pkts,
                  mgmtPortStatisticsRmonStatisticsRx512to1023Pkts,
                  mgmtPortStatisticsRmonStatisticsRx1024to1518Pkts,
                  mgmtPortStatisticsRmonStatisticsRx1519PktsToMax,
                  mgmtPortStatisticsRmonStatisticsTxDropEvents,
                  mgmtPortStatisticsRmonStatisticsTxOctets,
                  mgmtPortStatisticsRmonStatisticsTxPkts,
                  mgmtPortStatisticsRmonStatisticsTxBroadcastPkts,
                  mgmtPortStatisticsRmonStatisticsTxMulticastPkts,
                  mgmtPortStatisticsRmonStatisticsTx64Pkts,
                  mgmtPortStatisticsRmonStatisticsTx65to127Pkts,
                  mgmtPortStatisticsRmonStatisticsTx128to255Pkts,
                  mgmtPortStatisticsRmonStatisticsTx256to511Pkts,
                  mgmtPortStatisticsRmonStatisticsTx512to1023Pkts,
                  mgmtPortStatisticsRmonStatisticsTx1024to1518Pkts,
                  mgmtPortStatisticsRmonStatisticsTx1519PktsToMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 7 }

mgmtPortStatisticsIfGroupStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsIfGroupStatisticsIfIndex,
                  mgmtPortStatisticsIfGroupStatisticsRxOctets,
                  mgmtPortStatisticsIfGroupStatisticsRxUnicastPkts,
                  mgmtPortStatisticsIfGroupStatisticsRxMulticastPkts,
                  mgmtPortStatisticsIfGroupStatisticsRxBroadcastPkts,
                  mgmtPortStatisticsIfGroupStatisticsRxNonUnicastPkts,
                  mgmtPortStatisticsIfGroupStatisticsRxDiscards,
                  mgmtPortStatisticsIfGroupStatisticsRxErrors,
                  mgmtPortStatisticsIfGroupStatisticsTxOctets,
                  mgmtPortStatisticsIfGroupStatisticsTxUnicastPkts,
                  mgmtPortStatisticsIfGroupStatisticsTxMulticastPkts,
                  mgmtPortStatisticsIfGroupStatisticsTxBroadcastPkts,
                  mgmtPortStatisticsIfGroupStatisticsTxNonUnicastPkts,
                  mgmtPortStatisticsIfGroupStatisticsTxDiscardPkts,
                  mgmtPortStatisticsIfGroupStatisticsTxErrorPkts }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 8 }

mgmtPortStatisticsEthernetLikeStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsEthernetLikeStatisticsIfIndex,
                  mgmtPortStatisticsEthernetLikeStatisticsRxPauseFrames,
                  mgmtPortStatisticsEthernetLikeStatisticsTxPauseFrames }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 9 }

mgmtPortStatisticsQueuesStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsQueuesStatisticsIfIndex,
                  mgmtPortStatisticsQueuesStatisticsQueue,
                  mgmtPortStatisticsQueuesStatisticsRxPrio,
                  mgmtPortStatisticsQueuesStatisticsTxPrio }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 10 }

mgmtPortStatisticsBridgeStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsBridgeStatisticsIfIndex,
                  mgmtPortStatisticsBridgeStatisticsRxBridgeDiscard }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 11 }

mgmtPortStatisticsDot3brStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortStatisticsDot3brStatisticsIfIndex,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssErrorCount,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeFrameSmdErrorCount,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeFrameAssOkCount,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountRx,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeFragCountTx,
                  mgmtPortStatisticsDot3brStatisticsAMACMergeHoldCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortMibGroups 12 }

mgmtPortMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPortCapabilitiesInfoGroup,
                       mgmtPortConfigInfoGroup,
                       mgmtPortStatusInformationTableInfoGroup,
                       mgmtPortStatusVeriPhyResultTableInfoGroup,
                       mgmtPortControlStatisticsClearTableInfoGroup,
                       mgmtPortControlVeriPhyStartTableInfoGroup,
                       mgmtPortStatisticsRmonStatisticsTableInfoGroup,
                       mgmtPortStatisticsIfGroupStatisticsTableInfoGroup,
                       mgmtPortStatisticsEthernetLikeStatisticsTableInfoGroup,
                       mgmtPortStatisticsQueuesStatisticsTableInfoGroup,
                       mgmtPortStatisticsBridgeStatisticsTableInfoGroup,
                       mgmtPortStatisticsDot3brStatisticsTableInfoGroup }

    ::= { mgmtPortMibCompliances 1 }

END
