// cypress/e2e/login.cy.js
/// <reference types="cypress" />

describe('Login Page Tests', () => {
  beforeEach(() => {
    cy.visit('/login');
    // Clear session storage before each test
    cy.window().then((win) => win.sessionStorage.clear());
  });

  it('Logs in successfully', () => {
    cy.get('[data-testid="username"]').type('admin');
    cy.get('[data-testid="password"]').type('default');
    cy.get('[data-testid="submit"]').click();
    cy.url().should('include', '/dashboard');
  });

  it('should render login form correctly', () => {
    // Test basic page structure
    cy.get('.ant-card').should('be.visible');
    cy.get('img[alt="NIMBL"]').should('be.visible');
    cy.get('input[placeholder="Username"]').should('exist');
    cy.get('input[placeholder="Password"]').should('exist');
    cy.contains('button', 'Sign in').should('be.visible');
  });

  it('should show validation errors for empty fields', () => {
    cy.contains('button', 'Sign in').click();
    cy.contains('Please input your username !').should('be.visible');
    cy.contains('Please input your Password!').should('be.visible');
  });

  it('should handle failed login attempt', () => {
    cy.intercept('POST', '/api/v1/login', {
      statusCode: 401,
      body: "Invalid credentials" 
    }).as('loginRequest');
  
    cy.get('input[placeholder="Username"]')
      .type('wrong_user', { delay: 50 });
    cy.get('input[placeholder="Password"]')
      .type('wrong_pass', { delay: 50 });
    
    cy.contains('button', 'Sign in').click({ force: true });
  
    cy.wait('@loginRequest').then((interception) => {
      expect(interception.request.body).to.deep.equal({
        user: 'wrong_user',
        password: 'wrong_pass'
      });
    });
  
    // Verify notification shows string message
    cy.get('.ant-notification-notice-error')
      .should('contain', 'Invalid credentials');
    
    cy.contains('button', 'Sign in').should('be.disabled');
  });

  it('should re-enable submit button after cooldown', () => {
    cy.clock();
    cy.intercept('POST', '/api/v1/login', { statusCode: 401 }).as('loginFail');
    
    cy.get('input[placeholder="Username"]').type('user');
    cy.get('input[placeholder="Password"]').type('pass');
    cy.contains('button', 'Sign in').click();
    
    cy.wait('@loginFail');
    cy.contains('button', 'Sign in').should('be.disabled');
    cy.tick(10000);
    cy.contains('button', 'Sign in').should('not.be.disabled');
    cy.clock().invoke('restore');
  });
});