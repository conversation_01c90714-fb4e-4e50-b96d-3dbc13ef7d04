-- *****************************************************************
-- SYSUTIL-MIB:  
-- ****************************************************************

MGMT-SYSUTIL-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-<PERSON><PERSON>
    <PERSON>Address FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    ;

mgmtSysutilMib MODULE-IDENTITY
    LAST-UPDATED "201602170000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of SysUtil"
    REVISION    "201602170000Z"
    DESCRIPTION
        "Add system temperature monitor"
    REVISION    "201602150000Z"
    DESCRIPTION
        "Add board serial and type to board info"
    REVISION    "201511020000Z"
    DESCRIPTION
        "Add system time config"
    REVISION    "201510300000Z"
    DESCRIPTION
        "Add board info"
    REVISION    "201510200000Z"
    DESCRIPTION
        "Add system config info"
    REVISION    "201510150000Z"
    DESCRIPTION
        "Add system uptime status"
    REVISION    "201411110000Z"
    DESCRIPTION
        "Add system LED status"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 24 }


MGMTSysutilPowerSupplyStateType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of power supply state."
    SYNTAX      INTEGER { active(0), standby(1), notPresent(2) }

MGMTSysutilRebootType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of reboot."
    SYNTAX      INTEGER { noReboot(0), coldReboot(1), warmReboot(2) }

MGMTSysutilSystemLedClearType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of system LED status clearing."
    SYNTAX      INTEGER { all(0), fatal(1), software(2), post(3),
                          ztp(4), stackFwChk(5) }

MGMTSysutilTemperatureMonitorSensorType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of temperature sensors."
    SYNTAX      INTEGER { board(0), junction(1) }

mgmtSysutilMibObjects OBJECT IDENTIFIER
    ::= { mgmtSysutilMib 1 }

mgmtSysutilCapabilities OBJECT IDENTIFIER
    ::= { mgmtSysutilMibObjects 1 }

mgmtSysutilCapabilitiesWarmRebootSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if warm restart is supported or not. true means it is
         supported. false means it is not supported."
    ::= { mgmtSysutilCapabilities 1 }

mgmtSysutilCapabilitiesPostSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if POST(Power On Self Test) is supported or not. true(1) means
         it is supported. false(2) means it is not supported."
    ::= { mgmtSysutilCapabilities 2 }

mgmtSysutilCapabilitiesZtpSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if ZTP(Zero Touch Provisioning) is supported or not. true(1)
         means it is supported. false(2) means it is not supported."
    ::= { mgmtSysutilCapabilities 3 }

mgmtSysutilCapabilitiesStackFwChkSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if stack firmware version check is supported or not. true(1)
         means it is supported. false(2) means it is not supported."
    ::= { mgmtSysutilCapabilities 4 }

mgmtSysutilConfig OBJECT IDENTIFIER
    ::= { mgmtSysutilMibObjects 2 }

mgmtSysutilConfigSystemInfo OBJECT IDENTIFIER
    ::= { mgmtSysutilConfig 1 }

mgmtSysutilConfigSystemInfoHostname OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hostname"
    ::= { mgmtSysutilConfigSystemInfo 1 }

mgmtSysutilConfigSystemInfoContact OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Contact name."
    ::= { mgmtSysutilConfigSystemInfo 2 }

mgmtSysutilConfigSystemInfoLocation OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Location."
    ::= { mgmtSysutilConfigSystemInfo 3 }

mgmtSysutilConfigSystemTime OBJECT IDENTIFIER
    ::= { mgmtSysutilConfig 2 }

mgmtSysutilConfigSystemTimeSystemCurTime OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Current system time"
    ::= { mgmtSysutilConfigSystemTime 1 }

mgmtSysutilConfigSystemTimeSystemCurTimeFormat OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Format for setting up current system time"
    ::= { mgmtSysutilConfigSystemTime 2 }

mgmtSysutilConfigTemperatureMonitorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilConfigTemperatureMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of temperature monitor config."
    ::= { mgmtSysutilConfig 3 }

mgmtSysutilConfigTemperatureMonitorEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilConfigTemperatureMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row set the sensor config."
    INDEX       { mgmtSysutilConfigTemperatureMonitorSensorId }
    ::= { mgmtSysutilConfigTemperatureMonitorTable 1 }

MGMTSysutilConfigTemperatureMonitorEntry ::= SEQUENCE {
    mgmtSysutilConfigTemperatureMonitorSensorId           MGMTSysutilTemperatureMonitorSensorType,
    mgmtSysutilConfigTemperatureMonitorLowThreshold       Integer32,
    mgmtSysutilConfigTemperatureMonitorHighThreshold      Integer32,
    mgmtSysutilConfigTemperatureMonitorCriticalThreshold  Integer32,
    mgmtSysutilConfigTemperatureMonitorHysteresis         Integer32
}

mgmtSysutilConfigTemperatureMonitorSensorId OBJECT-TYPE
    SYNTAX      MGMTSysutilTemperatureMonitorSensorType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of sensor for tempeature monitor."
    ::= { mgmtSysutilConfigTemperatureMonitorEntry 1 }

mgmtSysutilConfigTemperatureMonitorLowThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-40..125)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The low threshold of temperature monior."
    ::= { mgmtSysutilConfigTemperatureMonitorEntry 2 }

mgmtSysutilConfigTemperatureMonitorHighThreshold OBJECT-TYPE
    SYNTAX      Integer32 (-40..125)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The high threshold of temperature monior."
    ::= { mgmtSysutilConfigTemperatureMonitorEntry 3 }

mgmtSysutilConfigTemperatureMonitorCriticalThreshold OBJECT-TYPE
    SYNTAX      Integer32 (90..150)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The critical threshold of temperature monior."
    ::= { mgmtSysutilConfigTemperatureMonitorEntry 4 }

mgmtSysutilConfigTemperatureMonitorHysteresis OBJECT-TYPE
    SYNTAX      Integer32 (1..5)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of hysteresis for temperature check."
    ::= { mgmtSysutilConfigTemperatureMonitorEntry 5 }

mgmtSysutilStatus OBJECT IDENTIFIER
    ::= { mgmtSysutilMibObjects 3 }

mgmtSysutilStatusCpuLoad OBJECT IDENTIFIER
    ::= { mgmtSysutilStatus 1 }

mgmtSysutilStatusCpuLoadAverage100msec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average CPU load (%) in 100 milli-seconds."
    ::= { mgmtSysutilStatusCpuLoad 1 }

mgmtSysutilStatusCpuLoadAverage1sec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average CPU load (%) in 1 second."
    ::= { mgmtSysutilStatusCpuLoad 2 }

mgmtSysutilStatusCpuLoadAverage10sec OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Average CPU load (%) in 10 seconds."
    ::= { mgmtSysutilStatusCpuLoad 3 }

mgmtSysutilStatusPowerSupplyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilStatusPowerSupplyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of power supply status."
    ::= { mgmtSysutilStatus 2 }

mgmtSysutilStatusPowerSupplyEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilStatusPowerSupplyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the power supply status."
    INDEX       { mgmtSysutilStatusPowerSupplySwitchId,
                  mgmtSysutilStatusPowerSupplyPsuId }
    ::= { mgmtSysutilStatusPowerSupplyTable 1 }

MGMTSysutilStatusPowerSupplyEntry ::= SEQUENCE {
    mgmtSysutilStatusPowerSupplySwitchId     Integer32,
    mgmtSysutilStatusPowerSupplyPsuId        Integer32,
    mgmtSysutilStatusPowerSupplyState        MGMTSysutilPowerSupplyStateType,
    mgmtSysutilStatusPowerSupplyDescription  MGMTDisplayString
}

mgmtSysutilStatusPowerSupplySwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch."
    ::= { mgmtSysutilStatusPowerSupplyEntry 1 }

mgmtSysutilStatusPowerSupplyPsuId OBJECT-TYPE
    SYNTAX      Integer32 (1..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of power supply."
    ::= { mgmtSysutilStatusPowerSupplyEntry 2 }

mgmtSysutilStatusPowerSupplyState OBJECT-TYPE
    SYNTAX      MGMTSysutilPowerSupplyStateType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The state of power supply."
    ::= { mgmtSysutilStatusPowerSupplyEntry 3 }

mgmtSysutilStatusPowerSupplyDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..30))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The description of power supply."
    ::= { mgmtSysutilStatusPowerSupplyEntry 4 }

mgmtSysutilStatusSystemLedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilStatusSystemLedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of system LED status."
    ::= { mgmtSysutilStatus 3 }

mgmtSysutilStatusSystemLedEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilStatusSystemLedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the system LED status."
    INDEX       { mgmtSysutilStatusSystemLedSwitchId }
    ::= { mgmtSysutilStatusSystemLedTable 1 }

MGMTSysutilStatusSystemLedEntry ::= SEQUENCE {
    mgmtSysutilStatusSystemLedSwitchId     Integer32,
    mgmtSysutilStatusSystemLedDescription  MGMTDisplayString
}

mgmtSysutilStatusSystemLedSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch."
    ::= { mgmtSysutilStatusSystemLedEntry 1 }

mgmtSysutilStatusSystemLedDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The description of system LED status."
    ::= { mgmtSysutilStatusSystemLedEntry 2 }

mgmtSysutilStatusSystemUptime OBJECT IDENTIFIER
    ::= { mgmtSysutilStatus 4 }

mgmtSysutilStatusSystemUptimeSystemUptime OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..10))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time since the DUT is Up."
    ::= { mgmtSysutilStatusSystemUptime 1 }

mgmtSysutilStatusBoardInfo OBJECT IDENTIFIER
    ::= { mgmtSysutilStatus 5 }

mgmtSysutilStatusBoardInfoBoardMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Board Mac Address"
    ::= { mgmtSysutilStatusBoardInfo 1 }

mgmtSysutilStatusBoardInfoBoardID OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Board ID"
    ::= { mgmtSysutilStatusBoardInfo 2 }

mgmtSysutilStatusBoardInfoBoardSerial OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Board ID"
    ::= { mgmtSysutilStatusBoardInfo 3 }

mgmtSysutilStatusBoardInfoBoardType OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Board ID"
    ::= { mgmtSysutilStatusBoardInfo 4 }

mgmtSysutilStatusTemperatureMonitorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilStatusTemperatureMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of temperature monitor status."
    ::= { mgmtSysutilStatus 6 }

mgmtSysutilStatusTemperatureMonitorEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilStatusTemperatureMonitorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the sensor status."
    INDEX       { mgmtSysutilStatusTemperatureMonitorSensorId }
    ::= { mgmtSysutilStatusTemperatureMonitorTable 1 }

MGMTSysutilStatusTemperatureMonitorEntry ::= SEQUENCE {
    mgmtSysutilStatusTemperatureMonitorSensorId       MGMTSysutilTemperatureMonitorSensorType,
    mgmtSysutilStatusTemperatureMonitorLowAlarm       TruthValue,
    mgmtSysutilStatusTemperatureMonitorHighAlarm      TruthValue,
    mgmtSysutilStatusTemperatureMonitorCriticalAlarm  TruthValue,
    mgmtSysutilStatusTemperatureMonitorTemperature    Integer32
}

mgmtSysutilStatusTemperatureMonitorSensorId OBJECT-TYPE
    SYNTAX      MGMTSysutilTemperatureMonitorSensorType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of sensor for tempeature monitor."
    ::= { mgmtSysutilStatusTemperatureMonitorEntry 1 }

mgmtSysutilStatusTemperatureMonitorLowAlarm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The alarm flag of temperature low status."
    ::= { mgmtSysutilStatusTemperatureMonitorEntry 2 }

mgmtSysutilStatusTemperatureMonitorHighAlarm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The alarm flag of temperature high status."
    ::= { mgmtSysutilStatusTemperatureMonitorEntry 3 }

mgmtSysutilStatusTemperatureMonitorCriticalAlarm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The alarm flag of temperature critical status."
    ::= { mgmtSysutilStatusTemperatureMonitorEntry 4 }

mgmtSysutilStatusTemperatureMonitorTemperature OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current temperature."
    ::= { mgmtSysutilStatusTemperatureMonitorEntry 5 }

mgmtSysutilControl OBJECT IDENTIFIER
    ::= { mgmtSysutilMibObjects 4 }

mgmtSysutilControlRebootTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilControlRebootEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to reboot a swicth"
    ::= { mgmtSysutilControl 1 }

mgmtSysutilControlRebootEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilControlRebootEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each switch has a set of parameters"
    INDEX       { mgmtSysutilControlRebootSwitchId }
    ::= { mgmtSysutilControlRebootTable 1 }

MGMTSysutilControlRebootEntry ::= SEQUENCE {
    mgmtSysutilControlRebootSwitchId  Integer32,
    mgmtSysutilControlRebootType      MGMTSysutilRebootType
}

mgmtSysutilControlRebootSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch."
    ::= { mgmtSysutilControlRebootEntry 1 }

mgmtSysutilControlRebootType OBJECT-TYPE
    SYNTAX      MGMTSysutilRebootType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of reboot. noReboot(0) does not reboot. coldReboot(1) is to do
         cold reboot. warmReboot(2) is to do warm reboot, but this is optional.
         The OID of mgmtSysutilCapabilitiesWarmRebootSupported tells if warm
         reboot is supported or not."
    ::= { mgmtSysutilControlRebootEntry 2 }

mgmtSysutilControlSystemLedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSysutilControlSystemLedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear the system LED error status"
    ::= { mgmtSysutilControl 2 }

mgmtSysutilControlSystemLedEntry OBJECT-TYPE
    SYNTAX      MGMTSysutilControlSystemLedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each switch has a set of parameters"
    INDEX       { mgmtSysutilControlSystemLedSwitchId }
    ::= { mgmtSysutilControlSystemLedTable 1 }

MGMTSysutilControlSystemLedEntry ::= SEQUENCE {
    mgmtSysutilControlSystemLedSwitchId     Integer32,
    mgmtSysutilControlSystemLedClearStatus  MGMTSysutilSystemLedClearType
}

mgmtSysutilControlSystemLedSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch."
    ::= { mgmtSysutilControlSystemLedEntry 1 }

mgmtSysutilControlSystemLedClearStatus OBJECT-TYPE
    SYNTAX      MGMTSysutilSystemLedClearType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of system LED status clearing.all(0) is used to clear all error
         status of the system LED and back to normal indication. fatal(1) is
         used to clear fatal error status of the system LED. software(2) is used
         to clear generic software error status of the system LED. post(3) is
         used to clear POST(Power On Self Test) error status of the system LED.
         ztp(4) is used to clear ZTP(Zero Touch Provisioning) error status of
         the system LED. stackFwChk(5) is used to clear stack firmware version
         check error status of the system LED."
    ::= { mgmtSysutilControlSystemLedEntry 2 }

mgmtSysutilMibConformance OBJECT IDENTIFIER
    ::= { mgmtSysutilMib 2 }

mgmtSysutilMibCompliances OBJECT IDENTIFIER
    ::= { mgmtSysutilMibConformance 1 }

mgmtSysutilMibGroups OBJECT IDENTIFIER
    ::= { mgmtSysutilMibConformance 2 }

mgmtSysutilCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilCapabilitiesWarmRebootSupported,
                  mgmtSysutilCapabilitiesPostSupported,
                  mgmtSysutilCapabilitiesZtpSupported,
                  mgmtSysutilCapabilitiesStackFwChkSupported }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 1 }

mgmtSysutilConfigSystemInfoInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilConfigSystemInfoHostname,
                  mgmtSysutilConfigSystemInfoContact,
                  mgmtSysutilConfigSystemInfoLocation }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 2 }

mgmtSysutilConfigSystemTimeInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilConfigSystemTimeSystemCurTime,
                  mgmtSysutilConfigSystemTimeSystemCurTimeFormat }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 3 }

mgmtSysutilConfigTemperatureMonitorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilConfigTemperatureMonitorSensorId,
                  mgmtSysutilConfigTemperatureMonitorLowThreshold,
                  mgmtSysutilConfigTemperatureMonitorHighThreshold,
                  mgmtSysutilConfigTemperatureMonitorCriticalThreshold,
                  mgmtSysutilConfigTemperatureMonitorHysteresis }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 4 }

mgmtSysutilStatusCpuLoadInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusCpuLoadAverage100msec,
                  mgmtSysutilStatusCpuLoadAverage1sec,
                  mgmtSysutilStatusCpuLoadAverage10sec }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 5 }

mgmtSysutilStatusPowerSupplyInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusPowerSupplySwitchId,
                  mgmtSysutilStatusPowerSupplyPsuId,
                  mgmtSysutilStatusPowerSupplyState,
                  mgmtSysutilStatusPowerSupplyDescription }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 6 }

mgmtSysutilStatusSystemLedInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusSystemLedSwitchId,
                  mgmtSysutilStatusSystemLedDescription }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 7 }

mgmtSysutilStatusSystemUptimeInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusSystemUptimeSystemUptime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 8 }

mgmtSysutilStatusBoardInfoInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusBoardInfoBoardMacAddress,
                  mgmtSysutilStatusBoardInfoBoardID,
                  mgmtSysutilStatusBoardInfoBoardSerial,
                  mgmtSysutilStatusBoardInfoBoardType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 9 }

mgmtSysutilStatusTemperatureMonitorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilStatusTemperatureMonitorSensorId,
                  mgmtSysutilStatusTemperatureMonitorLowAlarm,
                  mgmtSysutilStatusTemperatureMonitorHighAlarm,
                  mgmtSysutilStatusTemperatureMonitorCriticalAlarm,
                  mgmtSysutilStatusTemperatureMonitorTemperature }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 10 }

mgmtSysutilControlRebootInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilControlRebootSwitchId,
                  mgmtSysutilControlRebootType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 11 }

mgmtSysutilControlSystemLedInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSysutilControlSystemLedSwitchId,
                  mgmtSysutilControlSystemLedClearStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSysutilMibGroups 12 }

mgmtSysutilMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtSysutilCapabilitiesInfoGroup,
                       mgmtSysutilConfigSystemInfoInfoGroup,
                       mgmtSysutilConfigSystemTimeInfoGroup,
                       mgmtSysutilConfigTemperatureMonitorInfoGroup,
                       mgmtSysutilStatusCpuLoadInfoGroup,
                       mgmtSysutilStatusPowerSupplyInfoGroup,
                       mgmtSysutilStatusSystemLedInfoGroup,
                       mgmtSysutilStatusSystemUptimeInfoGroup,
                       mgmtSysutilStatusBoardInfoInfoGroup,
                       mgmtSysutilStatusTemperatureMonitorInfoGroup,
                       mgmtSysutilControlRebootInfoGroup,
                       mgmtSysutilControlSystemLedInfoGroup }

    ::= { mgmtSysutilMibCompliances 1 }

END
