import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";
import RootClusterInfos from "../components/clusterInfo/RootClusterInfos";
import { loginUser } from "../features/auth/userAuthSlice";
import { RootClusterInfo } from "../features/clusterInfo/clusterInfoSlice";
import { describe, expect, it } from "vitest";

describe("RootClusterInfos", () => {
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("Should handle RootClusterInfo correctly when successful", async () => {
    await store.dispatch(RootClusterInfo());
    const updatedState = store.getState().clusterInfoData;
    expect(updatedState.fetching).toEqual(false);
  });
  
  it("renders RootClusterInfos component", () => {
    render(
      <Provider store={store}>
        <RootClusterInfos />
      </Provider>
    );
  });
});
