import axios from "axios";

// Create Axios instance
// const instance = axios.create({
//   baseURL: baseURL,
// });

const baseURL =
  process.env.NODE_ENV === "development"
    ? localStorage.getItem("nms-setting") === null
      ? "http://localhost:27182"
      : `${JSON.parse(localStorage.getItem("nms-setting")).state.baseURL}`
    : window.location.origin;

const instance = axios.create({
  baseURL: baseURL,
});

instance.defaults.headers.common[
  "Authorization"
] = `Bearer ${sessionStorage.getItem("nmstoken")}`;

export default instance;
