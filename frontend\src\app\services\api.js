import { createApi } from "@reduxjs/toolkit/query/react";
import protectedApi from "../../utils/apis/protectedApis";

const axiosBaseQuery = async (args, api) => {
  try {
    let {
      url,
      params = undefined,
      body,
      method,
      responseType = "json",
    } = typeof args == "string" ? { url: args } : args;
    const result = await protectedApi({
      url,
      method,
      data: body,
      params,
      responseType,
    });
    return { data: result.data };
  } catch (axiosError) {
    let err = axiosError;
    return {
      error: {
        status: err.response?.status,
        data: err.response?.data || err.message,
      },
    };
  }
};

export const api = createApi({
  reducerPath: "api",
  baseQuery: axiosBaseQuery,
  tagTypes: [
    "Idps",
    "offlineCheck",
    "commands",
    "keystore",
    "ports",
    "topology",
    "aiassist",
  ],
  endpoints: (builder) => ({}),
});
