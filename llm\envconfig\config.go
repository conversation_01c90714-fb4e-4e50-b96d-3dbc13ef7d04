package envconfig

import (
	"fmt"
	"os"
	"strings"

	"github.com/qeof/q"
)

// Var returns an environment variable stripped of leading and trailing quotes or spaces
func Var(key string) (string, error) {
	s := strings.Trim(strings.TrimSpace(os.Getenv(key)), "\"'")
	if s == "" {
		return "", fmt.<PERSON>rrorf("environment variable %s is not set", key)
	}
	return s, nil
}

// LLMAPIKey returns the API key for the LLM. API key can be configured via the NIMBL_LLM_API_KEY environment variable.
func LLMAPIKey() (string, error) {
	s, err := Var("NIMBL_LLM_API_KEY")
	if err != nil {
		return "", err
	}
	return s, nil
}

// LLMToken maybe someone want to use Ollama with token?
func LLMToken() (string, error) {
	s, err := Var("NIMBL_LLM_TOKEN")
	if err != nil {
		q.Q("NIMBL_LLM_TOKEN is not set", err)
		return "", err
	}
	return s, nil
}

// LLMURL returns the host for the LLM. Host can be configured via the NIMBL_LLM_HOST environment variable.
func LLMURL() (string, error) {
	s, err := Var("NIMBL_LLM_URL")
	if err != nil {
		// for backward compatibility
		s, err = Var("NIMBL_LLM_HOST")
		if err != nil {
			q.Q("NIMBL_LLM_HOST is not set", err)
			return "", err
		}
	}
	return s, nil
}

// LLMModel returns the model for the LLM. Model can be configured via the NIMBL_LLM_MODEL environment variable.
func LLMModel(vendor string) (string, error) {
	s, err := Var("NIMBL_LLM_MODEL")
	if err != nil {
		q.Q("NIMBL_LLM_MODEL is not set", err)
		//return default model
		if vendor == "openai" || vendor == "open-ai" {

			return "gpt-4o", fmt.Errorf("NIMBL_LLM_MODEL is not set, use default model gpt-4o")
		}
		if vendor == "ollama" {
			// Ollama model must be specified as it refers to a locally available model

			return "", fmt.Errorf("NIMBL_LLM_MODEL is required for Ollama provider (e.g., llama3:8b)")
		}
		if vendor == "gemini" {

			return "gemini-1.5-flash-latest", fmt.Errorf("NIMBL_LLM_MODEL is not set, use default model gemini-1.5-flash-latest")
		}
		if vendor == "openrouter" {

			return "openai/gpt-3.5-turbo", fmt.Errorf("NIMBL_LLM_MODEL is not set, use default model openai/gpt-3.5-turbo")
		}
		return "", err
	}
	return s, nil
}
