package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"strconv"
)

// --- Reusing Structs from process_devices.go ---

// DeviceCapabilities defines the structure for the "capabilities" field.
type DeviceCapabilities struct {
	Gwd bool `json:"gwd"`
}

// Device defines the structure for each device entry.
type Device struct {
	Mac            string              `json:"mac"`
	ModelName      string              `json:"modelname"`
	Timestamp      string              `json:"timestamp"`
	ScanProto      string              `json:"scanproto"`
	IPAddress      string              `json:"ipaddress"`
	Netmask        string              `json:"netmask"`
	Gateway        string              `json:"gateway"`
	Hostname       string              `json:"hostname"`
	Kernel         string              `json:"kernel"`
	AP             string              `json:"ap"`
	ScannedBy      string              `json:"scannedby"`
	ArpMissed      int                 `json:"arpmissed"`
	Lock           bool                `json:"lock"`
	ReadCommunity  string              `json:"readcommunity"`
	WriteCommunity string              `json:"writecommunity"`
	IsDHCP         bool                `json:"isdhcp"`
	IsOnline       bool                `json:"isonline"`
	TopologyProto  string              `json:"topologyproto"`
	SvcDiscoVia    string              `json:"svcdiscovia"`
	Capabilities   *DeviceCapabilities `json:"capabilities"`
	DeviceErrors   interface{}         `json:"device_errors"`
	Username       string              `json:"username"`
	Password       string              `json:"password"`
	TunneledURL    string              `json:"tunneled_url"`
	SnmpSupported  string              `json:"snmpSupported"`
	SnmpEnabled    string              `json:"snmpEnabled"`
}

// --- End of Reused Structs ---

// --- Summary Structs ---

// ChunkSummary holds the summary for a single chunk of devices.
type ChunkSummary struct {
	ChunkID         string         `json:"chunk_id"`
	TotalDevices    int            `json:"total_devices"`
	OnlineDevices   int            `json:"online_devices"`
	OfflineDevices  int            `json:"offline_devices"`
	ModelCounts     map[string]int `json:"model_counts"`
	HostnameCounts  map[string]int `json:"hostname_counts"`
}

// FinalSummary holds the aggregated summary from all chunks.
type FinalSummary struct {
	TotalDevices          int            `json:"total_devices"`
	TotalOnlineDevices    int            `json:"total_online_devices"`
	TotalOfflineDevices   int            `json:"total_offline_devices"`
	OverallModelCounts    map[string]int `json:"overall_model_counts"`
	OverallHostnameCounts map[string]int `json:"overall_hostname_counts"`
	NumberOfChunks        int            `json:"number_of_chunks"`
}

// OutputData defines the structure for the final JSON output file.
type OutputData struct {
	Chunks          map[string][]Device   `json:"chunks"`
	ChunkSummaries  map[string]ChunkSummary `json:"chunk_summaries"`
	FinalSummary    FinalSummary          `json:"final_summary"`
}

// --- End of Summary Structs ---


// summarizeChunk processes a slice of devices and returns a summary.
func summarizeChunk(chunkID string, devices []Device) ChunkSummary {
	summary := ChunkSummary{
		ChunkID:        chunkID,
		TotalDevices:   len(devices),
		ModelCounts:    make(map[string]int),
		HostnameCounts: make(map[string]int),
	}

	for _, device := range devices {
		if device.IsOnline {
			summary.OnlineDevices++
		} else {
			summary.OfflineDevices++
		}

		if device.ModelName != "" {
			summary.ModelCounts[device.ModelName]++
		}
		if device.Hostname != "" {
			summary.HostnameCounts[device.Hostname]++
		}
	}
	return summary
}

// generateFinalSummary aggregates summaries from all chunks.
func generateFinalSummary(chunkSummaries map[string]ChunkSummary) FinalSummary {
	final := FinalSummary{
		OverallModelCounts:    make(map[string]int),
		OverallHostnameCounts: make(map[string]int),
		NumberOfChunks:        len(chunkSummaries),
	}

	for _, cs := range chunkSummaries {
		final.TotalDevices += cs.TotalDevices
		final.TotalOnlineDevices += cs.OnlineDevices
		final.TotalOfflineDevices += cs.OfflineDevices

		for model, count := range cs.ModelCounts {
			final.OverallModelCounts[model] += count
		}
		for hostname, count := range cs.HostnameCounts {
			final.OverallHostnameCounts[hostname] += count
		}
	}
	return final
}


func main() {
	inputFile := flag.String("input", "", "Path to the input JSON file containing a list of devices")
	outputFile := flag.String("output", "chunks_with_summary_output.json", "Path to the output JSON file for chunks and summaries")
	chunkSizeStr := flag.String("chunksize", "10", "Number of devices per chunk")

	flag.Parse()

	if *inputFile == "" {
		log.Fatal("Error: -input flag (input JSON file path) is required.")
	}

	chunkSize, err := strconv.Atoi(*chunkSizeStr)
	if err != nil || chunkSize <= 0 {
		log.Fatalf("Error: -chunksize must be a positive integer. Got: %s", *chunkSizeStr)
	}

	file, err := os.Open(*inputFile)
	if err != nil {
		log.Fatalf("Error opening input file %s: %v", *inputFile, err)
	}
	defer file.Close()

	byteValue, err := io.ReadAll(file)
	if err != nil {
		log.Fatalf("Error reading input file %s: %v", *inputFile, err)
	}

	var allDevices []Device
	if err := json.Unmarshal(byteValue, &allDevices); err != nil {
		if len(byteValue) == 0 || string(byteValue) == "[]" || string(byteValue) == "null" {
			log.Println("Input file is empty or contains an empty/null array. No data to process.")
			// Create an empty output structure
			emptyOutput := OutputData{
				Chunks:         make(map[string][]Device),
				ChunkSummaries: make(map[string]ChunkSummary),
				FinalSummary: FinalSummary{ // Initialize maps for empty final summary
					OverallModelCounts:    make(map[string]int),
					OverallHostnameCounts: make(map[string]int),
				},
			}
			outputJSON, _ := json.MarshalIndent(emptyOutput, "", "  ")
			err = os.WriteFile(*outputFile, outputJSON, 0644)
			if err != nil {
				log.Fatalf("Error writing empty output file %s: %v", *outputFile, err)
			}
			fmt.Printf("Empty output file with summary structure created at %s\n", *outputFile)
			return
		}
		log.Fatalf("Error unmarshaling JSON from %s: %v. Ensure it's an array of devices.", *inputFile, err)
	}

	if len(allDevices) == 0 {
		log.Println("No devices found in the input file. No data to process.")
		emptyOutput := OutputData{
			Chunks:         make(map[string][]Device),
			ChunkSummaries: make(map[string]ChunkSummary),
			FinalSummary: FinalSummary{
				OverallModelCounts:    make(map[string]int),
				OverallHostnameCounts: make(map[string]int),
			},
		}
		outputJSON, _ := json.MarshalIndent(emptyOutput, "", "  ")
		err = os.WriteFile(*outputFile, outputJSON, 0644)
		if err != nil {
			log.Fatalf("Error writing empty output file %s: %v", *outputFile, err)
		}
		fmt.Printf("Empty output file with summary structure created at %s\n", *outputFile)
		return
	}

	// --- Chunking and Summarization ---
	outputData := OutputData{
		Chunks:         make(map[string][]Device),
		ChunkSummaries: make(map[string]ChunkSummary),
	}

	numDevices := len(allDevices)
	chunkIndex := 0

	for i := 0; i < numDevices; i += chunkSize {
		end := i + chunkSize
		if end > numDevices {
			end = numDevices
		}

		currentDeviceChunk := make([]Device, len(allDevices[i:end]))
		copy(currentDeviceChunk, allDevices[i:end])

		chunkKey := fmt.Sprintf("chunk_%d", chunkIndex)
		outputData.Chunks[chunkKey] = currentDeviceChunk

		// Summarize this chunk
		chunkSum := summarizeChunk(chunkKey, currentDeviceChunk)
		outputData.ChunkSummaries[chunkKey] = chunkSum
		
		chunkIndex++
	}

	// Generate the final summary from all chunk summaries
	outputData.FinalSummary = generateFinalSummary(outputData.ChunkSummaries)

	// Marshal the complete output data
	outputJSON, err := json.MarshalIndent(outputData, "", "  ")
	if err != nil {
		log.Fatalf("Error marshaling output data to JSON: %v", err)
	}

	err = os.WriteFile(*outputFile, outputJSON, 0644)
	if err != nil {
		log.Fatalf("Error writing output file %s: %v", *outputFile, err)
	}

	fmt.Printf("Successfully processed %d devices into %d chunks with summaries in %s (chunk size: %d).\n", numDevices, chunkIndex, *outputFile, chunkSize)
}
