package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestByteTestItem(t *testing.T) {
	var tests = []struct {
		content  string
		expected byteTest
	}{

		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:23,=,0xffffffffffffffff,0,string,oct;sid:1;)`,
			expected: byteTest{
				nbytes: 23,
				offset: 0,
				op:     "=",
				value:  0xffffffffffffffff,
				base:   byteTestBaseOct,
				flags:  byteTestString,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:4,<,5,0,dce;sid:1;)`,
			expected: byteTest{
				nbytes: 4,
				offset: 0,
				op:     "<",
				value:  5,
				flags:  byteTestDCE,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:4,<,5,0;sid:1;)`,
			expected: byteTest{
				nbytes: 4,
				offset: 0,
				op:     "<",
				value:  5,
				flags:  0,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:4,<,5,0;sid:1;)`,
			expected: byteTest{
				nbytes: 4,
				offset: 0,
				op:     "<",
				value:  5,
				flags:  0,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:4, <, 5, 0, bitmask 0xf8;sid:1;)`,
			expected: byteTest{
				nbytes:              4,
				offset:              0,
				op:                  "<",
				value:               5,
				flags:               byteTestBitmask,
				bitmask:             0xf8,
				bitmask_shift_count: 3,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_test:4,!<,5,0,relative,string,hex, big, bitmask 0xf8;sid:1;)`,
			expected: byteTest{
				nbytes:              4,
				offset:              0,
				op:                  "!<",
				negop:               true,
				value:               5,
				base:                byteTestBaseHex,
				flags:               byteTestBig | byteTestRelative | byteTestString | byteTestBitmask,
				bitmask:             0xf8,
				bitmask_shift_count: 3,
			},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			if v, ok := d.RetrieveData().(*byteTest); ok {

				if v.nbytes != test.expected.nbytes {
					t.Error("nbytes error: unexpected")
				}
				if v.offset != test.expected.offset {
					t.Error("offset error: unexpected")
				}
				if v.op != test.expected.op {
					t.Error("op error: unexpected")
				}
				if v.value != test.expected.value {
					t.Error("value error: unexpected")
				}
				if v.base != test.expected.base {
					t.Error("base error: unexpected")
				}
				if v.flags != test.expected.flags {
					t.Error("flags error: unexpected")
				}
				if v.negop != test.expected.negop {
					t.Error("negop error: unexpected")
				}
				if v.flags&byteTestBitmask > 0 {
					if v.bitmask != test.expected.bitmask {
						t.Error("bitmask error: unexpected")
					}
					if v.bitmask_shift_count != test.expected.bitmask_shift_count {
						t.Error("bitmask_shift_count error: unexpected")
					}
				}

			}
		})
	}
}

func TestByteTest(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";content:"XYZ"; content:"_klm_"; distance:0; content:"abcd";
				 distance:4;byte_test:4,=,1234,-8,relative,string;sid:1;)`,
			expected: true,
			packet:   []byte("XYZ_klm_1234abcd_XYZ_klm_5678abcd"),
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";content:"XYZ"; content:"_klm_"; distance:0; content:"abcd";
				 distance:4;byte_test:4,=,5678,-8,relative,string;sid:1;)`,
			expected: true,
			packet:   []byte("XYZ_klm_1234abcd_XYZ_klm_5678abcd"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}
