-- *****************************************************************
-- MAC-MIB:  
-- ****************************************************************

MGMT-MAC-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    <PERSON>TInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    ;

mgmtMacMib MODULE-IDENTITY
    LAST-UPDATED "201905290000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the MAC MIB"
    REVISION    "201905290000Z"
    DESCRIPTION
        "Removed support for SR and PSFP."
    REVISION    "201702230000Z"
    DESCRIPTION
        "Added support for SR and PSFP."
    REVISION    "201408200000Z"
    DESCRIPTION
        "Updated types"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 12 }


MGMTMACPortLearnMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The learning mode of the port."
    SYNTAX      INTEGER { auto(0), disable(1), secure(2) }

mgmtMacMibObjects OBJECT IDENTIFIER
    ::= { mgmtMacMib 1 }

mgmtMacCapabilities OBJECT IDENTIFIER
    ::= { mgmtMacMibObjects 1 }

mgmtMacCapabilitiesNonVolatileMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Max number of static non-volatile MAC addresses that can be stored in
         the system."
    ::= { mgmtMacCapabilities 1 }

mgmtMacConfig OBJECT IDENTIFIER
    ::= { mgmtMacMibObjects 2 }

mgmtMacConfigFdbGlobal OBJECT IDENTIFIER
    ::= { mgmtMacConfig 1 }

mgmtMacConfigFdbGlobalAgeTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mac address aging time in the FDB."
    ::= { mgmtMacConfigFdbGlobal 1 }

mgmtMacConfigFdbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacConfigFdbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents static mac addresses added through the mgmt
         interface. "
    ::= { mgmtMacConfig 2 }

mgmtMacConfigFdbEntry OBJECT-TYPE
    SYNTAX      MGMTMacConfigFdbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtMacConfigFdbVlanId, mgmtMacConfigFdbMacAddress }
    ::= { mgmtMacConfigFdbTable 1 }

MGMTMacConfigFdbEntry ::= SEQUENCE {
    mgmtMacConfigFdbVlanId      MGMTVlan,
    mgmtMacConfigFdbMacAddress  MacAddress,
    mgmtMacConfigFdbPortList    MGMTPortList,
    mgmtMacConfigFdbAction      MGMTRowEditorState
}

mgmtMacConfigFdbVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan id used for indexing."
    ::= { mgmtMacConfigFdbEntry 1 }

mgmtMacConfigFdbMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The destination MAC address which this entry applies."
    ::= { mgmtMacConfigFdbEntry 2 }

mgmtMacConfigFdbPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of destination ports for which frames with this DMAC is forwarded
         to."
    ::= { mgmtMacConfigFdbEntry 3 }

mgmtMacConfigFdbAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtMacConfigFdbEntry 100 }

mgmtMacConfigFdbTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtMacConfig 3 }

mgmtMacConfigFdbTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id used for indexing."
    ::= { mgmtMacConfigFdbTableRowEditor 1 }

mgmtMacConfigFdbTableRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination MAC address which this entry applies."
    ::= { mgmtMacConfigFdbTableRowEditor 2 }

mgmtMacConfigFdbTableRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of destination ports for which frames with this DMAC is forwarded
         to."
    ::= { mgmtMacConfigFdbTableRowEditor 3 }

mgmtMacConfigFdbTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtMacConfigFdbTableRowEditor 100 }

mgmtMacConfigPortLearnTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacConfigPortLearnEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the learning mode of each port"
    ::= { mgmtMacConfig 4 }

mgmtMacConfigPortLearnEntry OBJECT-TYPE
    SYNTAX      MGMTMacConfigPortLearnEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtMacConfigPortLearnIfIndex }
    ::= { mgmtMacConfigPortLearnTable 1 }

MGMTMacConfigPortLearnEntry ::= SEQUENCE {
    mgmtMacConfigPortLearnIfIndex        MGMTInterfaceIndex,
    mgmtMacConfigPortLearnLearnMode      MGMTMACPortLearnMode,
    mgmtMacConfigPortLearnChangeAllowed  TruthValue
}

mgmtMacConfigPortLearnIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index."
    ::= { mgmtMacConfigPortLearnEntry 1 }

mgmtMacConfigPortLearnLearnMode OBJECT-TYPE
    SYNTAX      MGMTMACPortLearnMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The learn mode of the port. Auto(0) means auto learning. Disable(1)
         means that learning is disabled. Secure(2) means that learning frames
         are discarded."
    ::= { mgmtMacConfigPortLearnEntry 2 }

mgmtMacConfigPortLearnChangeAllowed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If internal modules have changed the learned mode then the user is not
         allowed to change it from this interface. This entry tells you if the
         LearnMode can be changed (true) or not (false). This is a read only
         entry - write is ignored."
    ::= { mgmtMacConfigPortLearnEntry 3 }

mgmtMacConfigVlanLearnTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacConfigVlanLearnEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the learning mode of each vlan 1-4095"
    ::= { mgmtMacConfig 5 }

mgmtMacConfigVlanLearnEntry OBJECT-TYPE
    SYNTAX      MGMTMacConfigVlanLearnEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtMacConfigVlanLearnVlanId }
    ::= { mgmtMacConfigVlanLearnTable 1 }

MGMTMacConfigVlanLearnEntry ::= SEQUENCE {
    mgmtMacConfigVlanLearnVlanId  MGMTVlan,
    mgmtMacConfigVlanLearnMode    TruthValue
}

mgmtMacConfigVlanLearnVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan id used for indexing."
    ::= { mgmtMacConfigVlanLearnEntry 1 }

mgmtMacConfigVlanLearnMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Learn mode of the VLAN, True = Enabled, False = Disabled"
    ::= { mgmtMacConfigVlanLearnEntry 2 }

mgmtMacStatus OBJECT IDENTIFIER
    ::= { mgmtMacMibObjects 3 }

mgmtMacStatusFdbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacStatusFdbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents all mac addresses in the FDB"
    ::= { mgmtMacStatus 1 }

mgmtMacStatusFdbEntry OBJECT-TYPE
    SYNTAX      MGMTMacStatusFdbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtMacStatusFdbVlanId, mgmtMacStatusFdbMacAddress }
    ::= { mgmtMacStatusFdbTable 1 }

MGMTMacStatusFdbEntry ::= SEQUENCE {
    mgmtMacStatusFdbVlanId      MGMTVlan,
    mgmtMacStatusFdbMacAddress  MacAddress,
    mgmtMacStatusFdbPortList    MGMTPortList,
    mgmtMacStatusFdbDynamic     MGMTUnsigned8,
    mgmtMacStatusFdbCopyToCpu   MGMTUnsigned8
}

mgmtMacStatusFdbVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan id used for indexing."
    ::= { mgmtMacStatusFdbEntry 1 }

mgmtMacStatusFdbMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The destination MAC address which this entry applies."
    ::= { mgmtMacStatusFdbEntry 2 }

mgmtMacStatusFdbPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "List of destination ports for which frames with this DMAC is forwarded
         to."
    ::= { mgmtMacStatusFdbEntry 3 }

mgmtMacStatusFdbDynamic OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The entry is dynamically learned (True) or statically added (False)"
    ::= { mgmtMacStatusFdbEntry 4 }

mgmtMacStatusFdbCopyToCpu OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Copy this frame to the CPU (True) or not (False)"
    ::= { mgmtMacStatusFdbEntry 5 }

mgmtMacStatusFdbStaticTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacStatusFdbStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents all static mac addresses in the FDB"
    ::= { mgmtMacStatus 2 }

mgmtMacStatusFdbStaticEntry OBJECT-TYPE
    SYNTAX      MGMTMacStatusFdbStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtMacStatusFdbStaticVlanId,
                  mgmtMacStatusFdbStaticMacAddress }
    ::= { mgmtMacStatusFdbStaticTable 1 }

MGMTMacStatusFdbStaticEntry ::= SEQUENCE {
    mgmtMacStatusFdbStaticVlanId      MGMTVlan,
    mgmtMacStatusFdbStaticMacAddress  MacAddress,
    mgmtMacStatusFdbStaticPortList    MGMTPortList,
    mgmtMacStatusFdbStaticDynamic     MGMTUnsigned8,
    mgmtMacStatusFdbStaticCopyToCpu   MGMTUnsigned8
}

mgmtMacStatusFdbStaticVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan id used for indexing."
    ::= { mgmtMacStatusFdbStaticEntry 1 }

mgmtMacStatusFdbStaticMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The destination MAC address which this entry applies."
    ::= { mgmtMacStatusFdbStaticEntry 2 }

mgmtMacStatusFdbStaticPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "List of destination ports for which frames with this DMAC is forwarded
         to."
    ::= { mgmtMacStatusFdbStaticEntry 3 }

mgmtMacStatusFdbStaticDynamic OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The entry is dynamically learned (True) or statically added (False)"
    ::= { mgmtMacStatusFdbStaticEntry 4 }

mgmtMacStatusFdbStaticCopyToCpu OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Copy this frame to the CPU (True) or not (False)"
    ::= { mgmtMacStatusFdbStaticEntry 5 }

mgmtMacStatusFdbPortStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMacStatusFdbPortStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represent the statistics of the Port interfaces"
    ::= { mgmtMacStatus 3 }

mgmtMacStatusFdbPortStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTMacStatusFdbPortStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a number of learned addresses"
    INDEX       { mgmtMacStatusFdbPortStatisticsIfIndex }
    ::= { mgmtMacStatusFdbPortStatisticsTable 1 }

MGMTMacStatusFdbPortStatisticsEntry ::= SEQUENCE {
    mgmtMacStatusFdbPortStatisticsIfIndex  MGMTInterfaceIndex,
    mgmtMacStatusFdbPortStatisticsDynamic  Unsigned32
}

mgmtMacStatusFdbPortStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index."
    ::= { mgmtMacStatusFdbPortStatisticsEntry 1 }

mgmtMacStatusFdbPortStatisticsDynamic OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total number of dynamic learned addresses on the port"
    ::= { mgmtMacStatusFdbPortStatisticsEntry 2 }

mgmtMacStatusFdbStatistics OBJECT IDENTIFIER
    ::= { mgmtMacStatus 4 }

mgmtMacStatusFdbStatisticsTotalDynamic OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total dynamic learned addresses in the FDB"
    ::= { mgmtMacStatusFdbStatistics 1 }

mgmtMacStatusFdbStatisticsTotalStatic OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Total static addresses in the FDB"
    ::= { mgmtMacStatusFdbStatistics 2 }

mgmtMacControl OBJECT IDENTIFIER
    ::= { mgmtMacMibObjects 4 }

mgmtMacControlFlushAll OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flush all dynamic learned Mac addresses. Set the value to 'true' to
         perform the action. Read will always return 'false'."
    ::= { mgmtMacControl 1 }

mgmtMacMibConformance OBJECT IDENTIFIER
    ::= { mgmtMacMib 2 }

mgmtMacMibCompliances OBJECT IDENTIFIER
    ::= { mgmtMacMibConformance 1 }

mgmtMacMibGroups OBJECT IDENTIFIER
    ::= { mgmtMacMibConformance 2 }

mgmtMacCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacCapabilitiesNonVolatileMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 1 }

mgmtMacConfigFdbGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacConfigFdbGlobalAgeTime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 2 }

mgmtMacConfigFdbTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacConfigFdbVlanId, mgmtMacConfigFdbMacAddress,
                  mgmtMacConfigFdbPortList, mgmtMacConfigFdbAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 3 }

mgmtMacConfigFdbTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacConfigFdbTableRowEditorVlanId,
                  mgmtMacConfigFdbTableRowEditorMacAddress,
                  mgmtMacConfigFdbTableRowEditorPortList,
                  mgmtMacConfigFdbTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 4 }

mgmtMacConfigPortLearnInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacConfigPortLearnIfIndex,
                  mgmtMacConfigPortLearnLearnMode,
                  mgmtMacConfigPortLearnChangeAllowed }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 5 }

mgmtMacConfigVlanLearnInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacConfigVlanLearnVlanId,
                  mgmtMacConfigVlanLearnMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 6 }

mgmtMacStatusFdbTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacStatusFdbVlanId, mgmtMacStatusFdbMacAddress,
                  mgmtMacStatusFdbPortList, mgmtMacStatusFdbDynamic,
                  mgmtMacStatusFdbCopyToCpu }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 7 }

mgmtMacStatusFdbStaticTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacStatusFdbStaticVlanId,
                  mgmtMacStatusFdbStaticMacAddress,
                  mgmtMacStatusFdbStaticPortList,
                  mgmtMacStatusFdbStaticDynamic,
                  mgmtMacStatusFdbStaticCopyToCpu }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 8 }

mgmtMacStatusFdbPortStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacStatusFdbPortStatisticsIfIndex,
                  mgmtMacStatusFdbPortStatisticsDynamic }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 9 }

mgmtMacStatusFdbStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacStatusFdbStatisticsTotalDynamic,
                  mgmtMacStatusFdbStatisticsTotalStatic }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 10 }

mgmtMacControlInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMacControlFlushAll }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMacMibGroups 11 }

mgmtMacMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtMacCapabilitiesInfoGroup,
                       mgmtMacConfigFdbGlobalInfoGroup,
                       mgmtMacConfigFdbTableInfoGroup,
                       mgmtMacConfigFdbTableRowEditorInfoGroup,
                       mgmtMacConfigPortLearnInfoGroup,
                       mgmtMacConfigVlanLearnInfoGroup,
                       mgmtMacStatusFdbTableInfoGroup,
                       mgmtMacStatusFdbStaticTableInfoGroup,
                       mgmtMacStatusFdbPortStatisticsInfoGroup,
                       mgmtMacStatusFdbStatisticsInfoGroup,
                       mgmtMacControlInfoGroup }

    ::= { mgmtMacMibCompliances 1 }

END
