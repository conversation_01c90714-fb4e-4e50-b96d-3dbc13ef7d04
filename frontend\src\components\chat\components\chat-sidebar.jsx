import React, { memo, useState } from "react";
import {
  Button,
  Typography,
  Space,
  Divider,
  Form,
  Select,
  Input,
  message,
  List,
  Card,
  Tooltip,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import { useTheme } from "antd-style";
import {
  useSetLLMsessionMutation,
  useGetLLMsessionListQuery,
  useGetLLMsessionQuery,
  useDeleteLLMsessionMutation,
} from "../../../app/services/aiassistApi";
import { useChatStore } from "../../../features/chat/chat-store";

const { Text, Title } = Typography;
const { Option } = Select;

// Provider options
const PROVIDERS = [
  { value: "openai", label: "OpenAI" },
  { value: "gemini", label: "Gemini" },
  { value: "ollama", label: "Ollama (Local)" },
  { value: "openrouter", label: "OpenRouter" },
];

// Default models for each provider
const DEFAULT_MODELS = {
  openai: "gpt-4o",
  gemini: "gemini-1.5-flash-latest",
  ollama: "llama3:8b",
  openrouter: "openai/gpt-3.5-turbo",
};

const ChatSidebar = memo(() => {
  const theme = useTheme();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showNewSessionForm, setShowNewSessionForm] = useState(false);

  const {
    sessions,
    currentSession,
    session_id,
    setSessions,
    setCurrentSession,
    clearMessages,
    addMessage,
  } = useChatStore();

  const {
    data: sessionsList,
    isLoading: isLoadingSessions,
    refetch: refetchSessions,
  } = useGetLLMsessionListQuery();

  const { data: sessionDetails, isLoading: isLoadingDetails } =
    useGetLLMsessionQuery(session_id, {
      skip: !session_id,
    });

  const [createSession] = useSetLLMsessionMutation();
  const [deleteSession] = useDeleteLLMsessionMutation();

  // Update sessions list when data changes
  React.useEffect(() => {
    if (sessionsList) {
      setSessions(sessionsList);

      // If no sessions exist, reset all store values
      if (sessionsList.length === 0) {
        setCurrentSession(null);
        clearMessages();
      }
    }
  }, [sessionsList, setSessions, setCurrentSession, clearMessages]);

  // Load session details and messages when session changes
  React.useEffect(() => {
    if (sessionDetails) {
      setCurrentSession(sessionDetails);

      // Clear current messages and load session messages
      clearMessages();

      // Add session messages to chat
      if (sessionDetails.messages && sessionDetails.messages.length > 0) {
        sessionDetails.messages.forEach((msg) => {
          addMessage({
            ...(msg.content && { content: msg.content }),
            role: msg.role,
            ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
            ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
            ...(msg.name && { name: msg.name }),
            timestamp: new Date(msg.timestamp || Date.now()),
          });
        });
      } else {
        // Add welcome message if no messages exist
        addMessage({
          ...(sessionDetails.provider &&
            sessionDetails.model && {
              content: `Session loaded: ${sessionDetails.provider}/${sessionDetails.model}. How can I help you today?`,
            }),
          role: "assistant",
          timestamp: new Date(),
        });
      }
    }
  }, [sessionDetails, setCurrentSession, clearMessages, addMessage]);

  const handleProviderChange = (provider) => {
    // Auto-select default model for the provider
    form.setFieldsValue({ model: DEFAULT_MODELS[provider] });
  };

  const handleCreateSession = async (values) => {
    setLoading(true);
    try {
      const sessionData = {
        provider: values.provider,
        model: values.model,
        ...(values.api_key && { api_key: values.api_key }),
        ...(values.base_url && { base_url: values.base_url }),
      };

      const response = await createSession(sessionData).unwrap();

      // Set the new session as current
      setCurrentSession(response);

      // Clear existing messages and add welcome message
      clearMessages();
      addMessage({
        content: `New session started with ${response.provider}/${response.model}. How can I help you today?`,
        role: "assistant",
        timestamp: new Date(),
      });

      message.success("New session created successfully!");
      setShowNewSessionForm(false);
      form.resetFields();
      refetchSessions();
    } catch (error) {
      console.error("Failed to create session:", error);
      message.error("Failed to create new session. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSelectSession = (selectedSession) => {
    if (selectedSession.session_id === session_id) return;

    // Set the current session which will trigger session details loading
    setCurrentSession(selectedSession);
  };

  const handleDeleteSession = async (sessionToDelete) => {
    try {
      await deleteSession({ session_id: sessionToDelete.session_id }).unwrap();
      message.success("Session deleted successfully");

      // If deleted session was current, clear current session
      if (sessionToDelete.session_id === session_id) {
        setCurrentSession(null);
        clearMessages();
      }

      // Refresh sessions list
      refetchSessions();
    } catch (error) {
      console.error("Failed to delete session:", error);
      message.error("Failed to delete session");
    }
  };

  const formatSessionLabel = (session) => {
    return `${session.provider}/${session.model}`;
  };

  const formatSessionDescription = (session) => {
    const createdAt = new Date(session.created_at).toLocaleDateString();
    const messageCount = session.messages?.length || 0;
    return `Created: ${createdAt} • Messages: ${messageCount}`;
  };

  return (
    <div
      style={{
        width: 300,
        height: "100%",
        backgroundColor: theme.colorBgContainer,
        borderRight: `1px solid ${theme.colorBorder}`,
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Sidebar Header */}
      <div
        style={{
          padding: "16px",
          borderBottom: `1px solid ${theme.colorBorder}`,
        }}
      >
        <Title level={5} style={{ margin: 0, marginBottom: 8 }}>
          AI Sessions
        </Title>
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<PlusOutlined />}
            onClick={() => setShowNewSessionForm(!showNewSessionForm)}
          >
            New Session
          </Button>
          <Tooltip title="Refresh sessions">
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => refetchSessions()}
              loading={isLoadingSessions}
            />
          </Tooltip>
        </Space>
      </div>

      {/* New Session Form */}
      {showNewSessionForm && (
        <div
          style={{
            padding: "16px",
            borderBottom: `1px solid ${theme.colorBorder}`,
            backgroundColor: theme.colorBgLayout,
          }}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateSession}
            size="small"
            initialValues={{
              provider: "openai",
              model: DEFAULT_MODELS.openai,
            }}
          >
            <Form.Item
              name="provider"
              label="Provider"
              rules={[{ required: true, message: "Please select a provider" }]}
            >
              <Select
                placeholder="Select AI provider"
                onChange={handleProviderChange}
              >
                {PROVIDERS.map((provider) => (
                  <Option key={provider.value} value={provider.value}>
                    {provider.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="model"
              label="Model"
              rules={[{ required: true, message: "Please enter a model name" }]}
            >
              <Input placeholder="Enter model name" />
            </Form.Item>

            <Form.Item name="api_key" label="API Key (Optional)">
              <Input.Password
                placeholder="Enter API key (optional)"
                autoComplete="off"
              />
            </Form.Item>

            <Form.Item name="base_url" label="Base URL (Optional)">
              <Input placeholder="Enter custom base URL (optional)" />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="small"
                >
                  Create
                </Button>
                <Button
                  size="small"
                  onClick={() => {
                    setShowNewSessionForm(false);
                    form.resetFields();
                  }}
                >
                  Cancel
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      )}

      {/* Sessions List */}
      <div style={{ flex: 1, overflow: "auto" }}>
        {sessions && sessions.length > 0 ? (
          <List
            dataSource={sessions}
            renderItem={(session) => (
              <List.Item
                style={{
                  padding: "8px 16px",
                  cursor: "pointer",
                  backgroundColor:
                    session.session_id === session_id
                      ? theme.colorPrimaryBg
                      : "transparent",
                  borderLeft:
                    session.session_id === session_id
                      ? `3px solid ${theme.colorPrimary}`
                      : "3px solid transparent",
                }}
                onClick={() => handleSelectSession(session)}
              >
                <List.Item.Meta
                  title={
                    <Text
                      strong={session.session_id === session_id}
                      style={{ fontSize: 14 }}
                    >
                      {formatSessionLabel(session)}
                    </Text>
                  }
                  description={
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {formatSessionDescription(session)}
                    </Text>
                  }
                />
                <Popconfirm
                  title="Delete session"
                  description="Are you sure you want to delete this session?"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    handleDeleteSession(session);
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                    onClick={(e) => e.stopPropagation()}
                    style={{ opacity: 0.6 }}
                  />
                </Popconfirm>
              </List.Item>
            )}
          />
        ) : (
          <div
            style={{
              padding: "32px 16px",
              textAlign: "center",
            }}
          >
            <Text type="secondary">No sessions yet</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Create your first session to get started
            </Text>
          </div>
        )}
      </div>
    </div>
  );
});

ChatSidebar.displayName = "ChatSidebar";

export default ChatSidebar;
