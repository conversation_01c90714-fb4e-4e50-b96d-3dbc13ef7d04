package mnms

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
)

func TestAgent(t *testing.T) {
	// init encryption
	encryption, _ := NewEncryptionContext()

	fakeHelloMsg := fmt.Sprintf("{\"kind\":\"announce\",\"ip\":\"%s\",\"url\":\"none\",\"version\":\"test\"}", "*************")
	jsonBytes := []byte(fakeHelloMsg)
	encryptionText, _, _ := EncryptCipherText(encryption.sharedSecret, encryption.nonce, jsonBytes, len(jsonBytes))
	// encode base64
	b64_text := base64.StdEncoding.EncodeToString(encryptionText)

	// decode base64
	encryption_messages, _ := base64.StdEncoding.DecodeString(b64_text)
	// decryption
	//encryption_messages_str := string(messages[:lenMessages])
	//encryption_messages := stringToByteHex(encryption_messages_str)
	plain_text, _ := DecryptCipherText(*encryption, encryption_messages)
	// fill AgentEthernetFrame
	var msg AgentMessages
	err := json.Unmarshal(plain_text[:], &msg)
	if err != nil {
		t.Fatalf("error: %v", err)
		return
	}
	if !strings.HasPrefix(msg.Kind, "announce") {
		t.Fatalf("error: %v", err)
		return
	}
	return
}
