package protocol

import (
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type IPversion uint8

const (
	IPV4 IPversion = 4
	IPV6 IPversion = 6
)

type IPLayer interface {
	ParseIP(packet gopacket.Packet) (IPversion, gopacket.NetworkLayer, error)
}

func NewIpLayer() IPLayer {
	return &ipParser{}
}

type ipParser struct {
}

func (i *ipParser) Parse(pack gopacket.Packet) bool {
	_, _, err := netWorkLayer(pack)
	return err == nil
}

func (i *ipParser) ParseIP(pack gopacket.Packet) (IPversion, gopacket.NetworkLayer, error) {

	return netWorkLayer(pack)
}

func netWorkLayer(pack gopacket.Packet) (IPversion, gopacket.NetworkLayer, error) {
	if netLayer := pack.NetworkLayer(); netLayer != nil {
		switch netLayer.(type) {
		case *layers.IPv4:
			return IPV4, netLayer, nil
		case *layers.IPv6:
			return IPV6, netLayer, nil
		}
	}
	return 0, nil, ErrorPacketsNotSupported
}
