describe("MibbrowserPage E2E Tests", () => {
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");
    cy.visit("/mibbrowser");
  });

  it("renders the MibbrowserPage correctly", () => {
    cy.contains("Mib Browser").should("exist");
  });

  it("fills in the form and triggers GET operation", () => {
    cy.get("input").eq(0).type("*************");
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Get").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

  it("fills in the form and triggers Walk operation", () => {
    cy.get("input").eq(0).type("*************"); // IP address
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Walk").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

  it("fills in the form and triggers Bulk operation", () => {
    cy.get("input").eq(0).type("*************"); 
    cy.get(".ant-select-selector").eq(0).click();
    cy.get(".ant-select-item-option-content").contains("Bulk").click();
    cy.intercept("POST", "**/GetMibBrowserData").as("mibCommand");
    cy.contains("go").click();
    cy.contains("View Result").click();
  });

 
  it('opens SNMP Set modal when operation is set', () => {
    cy.get('.ant-select-selector').click();
    cy.get(".ant-select-item-option-content").contains("Set").click();
    cy.contains('go').click();
    cy.contains('SNMP Set').should('exist'); // Modal title check
  });

});
