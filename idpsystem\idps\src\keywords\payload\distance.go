package payload

import (
	"errors"
	"fmt"
)

func distanceSetUp(l *list, v string) error {
	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("distance needs " +
			"preceding content, uricontent option, http_client_body, " +
			"http_server_body, http_header option, http_raw_header option, " +
			"http_method option, http_cookie, http_raw_uri, " +
			"http_stat_msg, http_stat_code, http_user_agent or " +
			"file_data/dce_stub_data sticky buffer option")
	}
	d, _ := dc.data.(*content)
	if (d.flags & distance) == distance {
		return errors.New("can't use multiple distances for the same content")
	}
	if d.flags&depth > 0 || d.flags&offset > 0 {
		return errors.New("can't use a relative " +
			"keyword like within/distance with a absolute " +
			"relative keyword like depth/offset for the same " +
			"content")
	}
	if d.flags&contentNegated > 0 && d.flags&fastpPattern > 0 {
		return errors.New("can't have a relative " +
			"negated keyword set along with a fast_pattern")

	}
	if d.flags&fastpPatternOnly > 0 {
		return errors.New("can't have a relative " +
			"keyword set along with a fast_pattern:only;")

	}
	if v[0] != '-' && isalpha(v[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(v, l, &index) {
			return fmt.Errorf("unknown byte_ keyword var seen in distance - %s", v)
		}
		d.distance = int32(index)
		d.flags |= distanceVar
	} else {
		if stringParseI32RangeCheck(&d.distance, 0, v, -contentValueMax, contentValueMax) < 0 {
			return fmt.Errorf("invalid value for distance:%v", v)
		}
	}
	d.flags |= distance
	prev := detectGetLastSMFromLists(l, []detectedId{detectContent, detectPCRE})
	if prev == nil {
		return nil
	}

	if prev.detectedID == detectContent {
		prevct, _ := prev.data.(*content)
		if (prevct.flags & fastpPatternOnly) > 0 {
			return errors.New("previous keyword " +
				"has a fast_pattern:only; set. Can't " +
				"have relative keywords around a fast_pattern " +
				"only content")

		}
		if d.flags&contentNegated == 0 {
			prevct.flags |= distanceNext
		} else {
			prevct.flags |= relativeNext
		}
	} else if prev.detectedID == detectPCRE {
		//todo
	}

	return nil
}
