package protocol

import (
	"fmt"
	"strings"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type ProcotolAnalyzer interface {
	Parse(gopacket.Packet) bool
}

// porotocol supported
func protocolMap() map[string]ProcotolAnalyzer {
	ProtocolMap := make(map[string]ProcotolAnalyzer)
	ProtocolMap["enip"] = &enipParser{}
	ProtocolMap["tcp"] = NewTcpParser()
	ProtocolMap["http"] = &httpParser{}
	ProtocolMap["tls"] = &tlsParser{}
	ProtocolMap["tftp"] = &tftpParser{}
	ProtocolMap["imap"] = &imapParser{}
	ProtocolMap["dhcp"] = &dhcpParser{}
	ProtocolMap["nfs"] = &nfsParser{}
	ProtocolMap["udp"] = &udpParser{}
	ProtocolMap["http2"] = &http2Parser{}
	ProtocolMap["smb"] = &smbParser{}
	ProtocolMap["ssh"] = &sshParser{}
	ProtocolMap["modbus"] = &modbusParser{}
	ProtocolMap["snmp"] = &snmpParser{}
	ProtocolMap["ntp"] = &ntpParser{}
	ProtocolMap["icmp"] = NewIcmpParser()
	ProtocolMap["ftp"] = &ftpParser{}
	ProtocolMap["dns"] = &dnsParser{}
	ProtocolMap["smtp"] = &smtpParser{}
	ProtocolMap["sip"] = &sipParser{}
	ProtocolMap["ip"] = &ipParser{}
	return ProtocolMap
}

func FindPacketAnalyzer(protocol string) (ProcotolAnalyzer, error) {
	pmap := protocolMap()
	if alyer, ok := pmap[strings.ToLower(protocol)]; ok {
		return alyer, nil
	}
	return nil, fmt.Errorf("not supported protocol:%v", protocol)
}

type ipversion int

const (
	ipv4version ipversion = 0x40
	ipv6version ipversion = 0x60
)

func ConvertGoNetWorkPacket(payload []byte) (gopacket.Packet, error) {
	var packet gopacket.Packet
	switch ipversion(payload[0] & 0xf0) {
	case ipv4version:
		packet = gopacket.NewPacket(payload, layers.LayerTypeIPv4, gopacket.DecodeOptions{Lazy: false, NoCopy: true})
	case ipv6version:
		packet = gopacket.NewPacket(payload, layers.LayerTypeIPv6, gopacket.DecodeOptions{Lazy: false, NoCopy: true})
	}

	if packet != nil {
		return packet, nil
	}
	return nil, ErrorPacketsNotSupported
}
