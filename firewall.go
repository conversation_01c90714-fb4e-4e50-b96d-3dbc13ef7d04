package mnms

import (
	"fmt"
	"os/exec"
	"runtime"
	"strings"
)

func checkUfw() error {
	if runtime.GOOS == "linux" {
		// check if ufw is installed
		_, err := exec.LookPath("ufw")
		if err != nil {
			return fmt.Errorf("error: ufw is not installed")
		}
		return nil
	}
	return fmt.Errorf("error: ufw is not supported on this platform")
}

func checkNetsh() error {
	if runtime.GOOS == "windows" {
		// check if netsh is installed
		_, err := exec.LookPath("netsh")
		if err != nil {
			return fmt.Errorf("error: netsh is not installed")
		}
		return nil
	}
	return fmt.Errorf("error: netsh is not supported on this platform")
}

func ManagePort(action, port, protocol string) error {
	var ufwCmd, netshCmd string

	switch action {
	case "allow":
		if len(protocol) == 0 {
			ufwCmd = fmt.Sprintf("ufw allow %s", port)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall add rule name=\"Open Port %s\" dir=in action=allow protocol=TCP localport=%s", port, port)
		} else {
			ufwCmd = fmt.Sprintf("ufw allow %s/%s", port, protocol)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall add rule name=\"Open Port %s\" dir=in action=allow protocol=%s localport=%s", port, protocol, port)
		}
	case "deny":
		if len(protocol) == 0 {
			ufwCmd = fmt.Sprintf("ufw deny %s", port)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall add rule name=\"Close Port %s\" dir=in action=block protocol=TCP localport=%s", port, port)
		} else {
			ufwCmd = fmt.Sprintf("ufw deny %s/%s", port, protocol)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall add rule name=\"Close Port %s\" dir=in action=block protocol=%s localport=%s", port, protocol, port)
		}
	case "remove":
		if len(protocol) == 0 {
			ufwCmd = fmt.Sprintf("ufw delete allow %s && ufw delete deny %s", port, port)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall delete rule name=\"Open Port %s\" && netsh advfirewall firewall delete rule name=\"Close Port %s\"", port, port)
		} else {
			ufwCmd = fmt.Sprintf("ufw delete allow %s/%s && ufw delete deny %s/%s", port, protocol, port, protocol)
			netshCmd = fmt.Sprintf("netsh advfirewall firewall delete rule name=\"Open Port %s\" && netsh advfirewall firewall delete rule name=\"Close Port %s\"", port, port)
		}
	default:
		return fmt.Errorf("error: unknown action")
	}

	if err := checkUfw(); err == nil {
		out, err := exec.Command("sh", "-c", ufwCmd).Output()
		if err != nil {
			return fmt.Errorf("error: %s", out)
		}
		return nil
	}

	if err := checkNetsh(); err == nil {
		out, err := exec.Command("cmd", "/C", netshCmd).Output()
		if err != nil {
			return fmt.Errorf("error: %s", out)
		}
		return nil
	}

	return fmt.Errorf("error: unknown platform or firewall tool not supported, please contact support")
}

func listPorts() (string, error) {
	if err := checkUfw(); err == nil {
		out, err := exec.Command("ufw", "status").Output()
		if err != nil {
			return "", fmt.Errorf("error: %s", out)
		}
		return string(out), nil
	}

	if err := checkNetsh(); err == nil {
		out, err := exec.Command("netsh", "advfirewall", "firewall", "show", "rule", "name=all").Output()
		if err != nil {
			return "", fmt.Errorf("error: %s", out)
		}
		return string(out), nil
	}

	return "", fmt.Errorf("error: unknown platform or firewall tool not supported, please contact support")
}

/*
firewall allow [port] [...protocol]
firewall deny [port] [...protocol]
firewall remove [port] [...protocol]
firewall list
*/
func FirewallCmd(cmdinfo *CmdInfo) *CmdInfo {
	if !QC.IsRoot {
		cmdinfo.Status = "error: this command requires root service"
		return cmdinfo
	}

	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "firewall allow") {
		ws := strings.Fields(cmd)
		if len(ws) < 3 {
			cmdinfo.Status = "error: invalid number of arguments"
			return cmdinfo
		}

		port := ws[2]
		protocol := ""
		if len(ws) == 4 {
			protocol = ws[3]
		}
		err := ManagePort("allow", port, protocol)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "firewall deny") {
		ws := strings.Fields(cmd)
		if len(ws) < 3 {
			cmdinfo.Status = "error: invalid number of arguments"
			return cmdinfo
		}

		port := ws[2]
		protocol := ""
		if len(ws) == 4 {
			protocol = ws[3]
		}
		err := ManagePort("deny", port, protocol)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "firewall remove") {
		ws := strings.Fields(cmd)
		if len(ws) < 3 {
			cmdinfo.Status = "error: invalid number of arguments"
			return cmdinfo
		}

		port := ws[2]
		protocol := ""
		if len(ws) == 4 {
			protocol = ws[3]
		}
		err := ManagePort("remove", port, protocol)
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "firewall list") {
		out, err := listPorts()
		if err != nil {
			cmdinfo.Status = fmt.Sprintf("error: %s", err)
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		cmdinfo.Result = out
		return cmdinfo
	}

	cmdinfo.Status = "error: unknown command"
	return cmdinfo
}
