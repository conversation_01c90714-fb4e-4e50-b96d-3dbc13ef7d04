package ip

import (
	"fmt"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type optflag uint8

const (
	rr    = 0x07
	eol   = 0x00
	nop   = 0x01
	ts    = 0x44
	sec   = 0x02
	esec  = 0x85
	lsrr  = 0x83
	ssrr  = 0x89
	satid = 0x88
)

var optmap = map[string]optflag{
	"rr":    rr,
	"eol":   eol,
	"nop":   nop,
	"ts":    ts,
	"sec":   sec,
	"esec":  esec,
	"lsrr":  lsrr,
	"ssrr":  ssrr,
	"satid": satid,
	"any":   0xff,
}

func NewIpopts() *ipopts {
	return &ipopts{ipLayer: protocol.NewIpLayer()}

}

type ipopts struct {
	all     bool
	value   uint8
	ipLayer protocol.IPLayer
}

func (i *ipopts) SetUp(s string) error {
	if s == "any" {
		i.all = true
		return nil
	}
	if v, ok := optmap[s]; ok {
		i.value = uint8(v)
		return nil
	}
	return fmt.Errorf("not support:%v", s)

}

func (i *ipopts) Match(packet gopacket.Packet) bool {
	ver, net, err := i.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		for _, v := range v4.Options {
			if i.value == v.OptionType || i.all {
				return true
			}
		}
	}

	return false
}
