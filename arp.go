// revive:disable-line:package-comments
package mnms

import (
	"bytes"
	"context"
	"encoding/binary"
	"errors"
	"flag"
	"fmt"
	"math/big"
	"net"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/pcap"
	"github.com/qeof/q"
)

// scanRangeCmd.
//
// Usage : scan startip endip [options]
//
// [options]
//
//	-save: save to devlist (default:false)
//	-list: print it out (default:false)
//
// Example :
//
//	scan 192.168.1 ************* -save -list
func scanRangeCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 3 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	f := NewFlag("ScanCmd", flag.ContinueOnError)
	var s bool
	var l bool
	f.Bo<PERSON>(&s, "save", false, "save to devlist")
	f.<PERSON>(&l, "list", false, " print it out")
	err := f.Parse(ws[3:])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	go func() {
		scanIPRange(ws[1], ws[2], s, l)
	}()
	cmdinfo.Status = "ok"
	return cmdinfo
}

// scanCidr.
//
// Usage : scan cidr [options]
//
// [options]
//
//	-save: save to devlist (default:false)
//	-list: print it out (default:false)
//
// Example :
//
//	scan 192.168.0/24 -save -list
func scanCidrCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 2 {
		q.Q("error", len(ws))
		cmdinfo.Status = "error: invalid command"
		return cmdinfo
	}
	start, end, err := GetCIDRRange(ws[1])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	f := NewFlag("ScanCmd", flag.ContinueOnError)
	var s bool
	var l bool
	f.BoolVar(&s, "save", false, "save to devlist")
	f.BoolVar(&l, "list", false, " print it out")
	err = f.Parse(ws[2:])
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}
	go func() {
		scanIPRange(start.String(), end.String(), s, l)
	}()
	cmdinfo.Status = "ok"
	return cmdinfo
}
func scanIPRange(start, end string, save, list bool) error {
	d := NewThirdParty()
	err := scanArp(start, end, d)
	if err != nil {
		q.Q(err)
	}
	if save {
		for _, dev := range d.dev {
			QC.DevMutex.Lock()
			d, ok := QC.DevData[dev.Mac]
			QC.DevMutex.Unlock()
			if ok {
				edit := editableItem{modelName: d.ModelName, netmask: d.Netmask, hostname: d.Hostname}
				dev.Edit(edit)
			}
			InsertAndPublishDevice(dev)
		}
	}
	if list {
		for _, dev := range d.dev {
			err := SendSyslog(LOG_INFO, fmt.Sprintf("%v", "scanIP"), fmt.Sprintf("ip:%v, mac:%v", dev.IPAddress, dev.Mac))
			if err != nil {
				q.Q(err)
			}
		}
	}
	return nil
}

const timeout = 1 * time.Millisecond

// ARPCheckExisted  existent:ture, nonexistent:false
//
// if run on linux please use root
func ARPCheckExisted(ip string) (bool, error) {
	ifaces, err := GetAllInterfaces()
	if err != nil {
		return false, err
	}
	for _, iface := range ifaces {
		if r, err := arpCheck(&iface, net.ParseIP(ip)); err != nil {
			q.Q(iface.Name, err)
			continue
		} else {
			if r {
				return r, nil
			}
		}
	}

	// Wait for all interfaces' scans to complete.  They'll try to run
	// forever, but will stop on an error, so if we get past this Wait
	// it means all attempts to write have failed.
	return false, nil
}

// GetAdaptersName get AdaptersName
func GetAdaptersName(name string) (string, error) {
	switch runtime.GOOS {
	case "windows":
		ifter, err := net.InterfaceByName(name)
		if err != nil {
			return "", err
		}
		addrs, err := ifter.Addrs()
		if err != nil { // get addresses
			return "", err
		}
		if (len(addrs)) == 0 {
			return "", fmt.Errorf("addr len:%v", len(addrs))
		}
		var ipv4Addr net.IP
		for _, addr := range addrs { // get ipv4 address
			if ipv4Addr = addr.(*net.IPNet).IP.To4(); ipv4Addr != nil {
				break
			}
		}

		if ipv4Addr == nil {
			return "", fmt.Errorf("interface %s don't have an ipv4 address", name)
		}

		devices, err := pcap.FindAllDevs()
		if err != nil {
			return "", err
		}
		for _, device := range devices {
			for _, v := range device.Addresses {
				if net.IP.Equal(v.IP.To4(), ipv4Addr.To4()) {
					return device.Name, nil
				}
			}
		}

	default:
		return name, nil

	}
	return "", fmt.Errorf("can't get AdaptersName: %v", name)

}

// arpCheck checks if the given IP address is in the ARP cache of the given interface.
func arpCheck(iface *net.Interface, ip net.IP) (bool, error) {
	// We just look for IPv4 addresses, so try to find if the interface has one.
	var addr *net.IPNet
	if addrs, err := iface.Addrs(); err != nil {
		return false, err
	} else {
		//check self
		for _, a := range addrs {
			if ipnet, ok := a.(*net.IPNet); ok {
				if net.IP.Equal(ipnet.IP.To4(), ip.To4()) {
					return true, nil
				}
			}
		}
		for _, a := range addrs {
			if ipnet, ok := a.(*net.IPNet); ok {
				if ip4 := ipnet.IP.To4(); ip4 != nil {
					addr = &net.IPNet{
						IP:   ip4,
						Mask: ipnet.Mask[len(ipnet.Mask)-4:],
					}
					break
				}
			}
		}
	}
	// Sanity-check that the interface has a good address.
	if addr == nil {
		return false, errors.New("no good IP network found")
	} else if addr.IP[0] == 127 {
		return false, errors.New("skipping localhost")
	} else if addr.Mask[0] != 0xff || addr.Mask[1] != 0xff {
		return false, errors.New("mask means network is too large")
	}
	//log.Printf("Using network range %v for interface %v", addr, iface.Name)
	name, err := GetAdaptersName(iface.Name)
	if err != nil {
		return false, err
	}
	// Open up a pcap handle for packet reads/writes.
	handle, err := pcap.OpenLive(name, 65536, true, timeout)
	if err != nil {
		return false, err
	}
	defer handle.Close()

	// Start up a goroutine to read in packet data.
	//stop := make(chan struct{})
	t := time.Duration(500 * time.Millisecond)
	result := false

	end := make(chan bool)
	go func() {

		result = readARPAndCheck(handle, iface, ip, t)
		end <- true
	}()
	///for {
	// Write our scan packets out to the handle.
	if err := writeARP(handle, iface, addr, ip); err != nil {
		q.Q("error writing packets on ", iface.Name, "err:", err)
		return false, err
	}
	<-end
	// We don't know exactly how long it'll take for packets to be
	// sent back to us, but 10 seconds should be more than enough
	// time ;)
	//time.Sleep(10 * time.Second)
	//}
	return result, nil
}

func readARPAndCheck(handle *pcap.Handle, iface *net.Interface, ip net.IP, timeout time.Duration) bool {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	src := gopacket.NewPacketSource(handle, layers.LayerTypeEthernet)
	in := src.Packets()
	for {
		var packet gopacket.Packet
		select {
		case <-ctx.Done():
			return false
		case packet = <-in:
			arpLayer := packet.Layer(layers.LayerTypeARP)
			if arpLayer == nil {
				continue
			}
			arp := arpLayer.(*layers.ARP)
			if arp.Operation != layers.ARPReply || bytes.Equal([]byte(iface.HardwareAddr), arp.SourceHwAddress) {
				// This is a packet I sent.
				continue
			}

			//log.Printf("IP %v is at %v", net.IP(arp.SourceProtAddress), net.HardwareAddr(arp.SourceHwAddress))
			if bytes.Equal(arp.SourceProtAddress, ip.To4()) {
				return true
			}
			continue

		}
	}
}
func createARPPacket(iface *net.Interface, sourceProtAddress *net.IPNet, dstProtAddress net.IP) (gopacket.SerializeBuffer, error) {
	eth := layers.Ethernet{
		SrcMAC:       iface.HardwareAddr,
		DstMAC:       net.HardwareAddr{0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		EthernetType: layers.EthernetTypeARP,
	}
	arp := layers.ARP{
		AddrType:          layers.LinkTypeEthernet,
		Protocol:          layers.EthernetTypeIPv4,
		HwAddressSize:     6,
		ProtAddressSize:   4,
		Operation:         layers.ARPRequest,
		SourceHwAddress:   []byte(iface.HardwareAddr),
		SourceProtAddress: []byte(sourceProtAddress.IP),
		DstProtAddress:    []byte(dstProtAddress),
		DstHwAddress:      []byte{0, 0, 0, 0, 0, 0},
	}
	// Set up buffer and options for serialization.
	buf := gopacket.NewSerializeBuffer()
	opts := gopacket.SerializeOptions{
		FixLengths:       true,
		ComputeChecksums: true,
	}
	err := gopacket.SerializeLayers(buf, opts, &eth, &arp)
	if err != nil {
		return nil, err
	}
	return buf, nil
}

func writeARP(handle *pcap.Handle, iface *net.Interface, sour *net.IPNet, dest net.IP) error {
	// Set up all the layers' fields we can.
	p, err := createARPPacket(iface, sour, dest)
	if err != nil {
		return err
	}
	if err := handle.WritePacketData(p.Bytes()); err != nil {
		return err
	}
	//}
	return nil
}

func scanArp(srip, endip string, c collector) error {
	ifaces, err := net.Interfaces()
	if err != nil {
		return err
	}
	start, err := net.ResolveIPAddr("ip", srip)
	if err != nil {
		return err
	}

	end, err := net.ResolveIPAddr("ip", endip)
	if err != nil {
		return err
	}
	var wg sync.WaitGroup
	for _, iface := range ifaces {
		if !(iface.Flags&net.FlagUp == net.FlagUp) {
			continue
		}
		if iface.Flags&net.FlagLoopback == net.FlagLoopback {
			continue
		}
		wg.Add(1)
		// Start up a scan on each interface.
		go func(iface net.Interface) {
			defer wg.Done()
			if err := scanRange(&iface, &net.IPNet{IP: start.IP.To4()}, &net.IPNet{IP: end.IP.To4()}, c); err != nil {
				q.Q(fmt.Sprintf("interface %v\n: %v", iface.Name, err))
			}
		}(iface)
	}
	wg.Wait()
	return nil
}

func scanRange(iface *net.Interface, start, end *net.IPNet, c collector) error {
	// We just look for IPv4 addresses, so try to find if the interface has one.
	var addr *net.IPNet
	if addrs, err := iface.Addrs(); err != nil {
		return err
	} else {
		for _, a := range addrs {
			if ipnet, ok := a.(*net.IPNet); ok {
				if ip4 := ipnet.IP.To4(); ip4 != nil {
					if ip4[0] == 127 {
						continue
					}
					addr = &net.IPNet{
						IP:   ip4,
						Mask: ipnet.Mask[len(ipnet.Mask)-4:],
					}
					break
				}
			}
		}
	}
	// Sanity-check that the interface has a good address.
	if addr == nil {
		return errors.New("no good IP network found")
	}
	name, err := GetAdaptersName(iface.Name)
	if err != nil {
		return err
	}
	// Open up a pcap handle for packet reads/writes.
	handle, err := pcap.OpenLive(name, 65536, true, time.Microsecond)
	if err != nil {
		return err
	}
	defer handle.Close()

	// Start up a goroutine to read in packet data.
	stop := make(chan struct{})
	go readARP(handle, iface, stop, c)
	defer close(stop)
	for {
		// Write our scan packets out to the handle.
		if err := writeARPRange(handle, iface, addr, start, end); err != nil {
			return err
		}
		time.Sleep(10 * time.Second)
		return nil
	}
}

func readARP(handle *pcap.Handle, iface *net.Interface, stop chan struct{}, c collector) {
	src := gopacket.NewPacketSource(handle, layers.LayerTypeEthernet)
	in := src.Packets()
	for {
		var packet gopacket.Packet
		select {
		case <-stop:
			return
		case packet = <-in:
			arpLayer := packet.Layer(layers.LayerTypeARP)
			if arpLayer == nil {
				continue
			}
			arp := arpLayer.(*layers.ARP)
			if arp.Operation != layers.ARPReply || bytes.Equal([]byte(iface.HardwareAddr), arp.SourceHwAddress) {
				continue
			}
			c.add(*arp)
		}
	}
}

func writeARPRange(handle *pcap.Handle, iface *net.Interface, sour *net.IPNet, start, end *net.IPNet) error {
	// Send one packet for every address.
	for _, ip := range ipRange(start, end) {
		p, err := createARPPacket(iface, sour, ip)
		if err != nil {
			return err
		}
		if err := handle.WritePacketData(p.Bytes()); err != nil {
			return err
		}
	}
	return nil
}

func ipRange(s, end *net.IPNet) (out []net.IP) {
	startnum := binary.BigEndian.Uint32([]byte(s.IP))
	endnum := binary.BigEndian.Uint32([]byte(end.IP))
	for startnum <= endnum {
		var buf [4]byte
		binary.BigEndian.PutUint32(buf[:], startnum)
		out = append(out, net.IP(buf[:]))
		startnum++
	}
	return
}

func GetCIDRRange(cidr string) (net.IP, net.IP, error) {
	_, ipNet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid CIDR: %v", err)
	}
	startIP := ipNet.IP

	ip := ipNet.IP.To4()
	if ip == nil {
		ip = ipNet.IP.To16()
	}
	mask := ipNet.Mask
	one, bit := mask.Size()
	maskLen := bit - one
	fullMask := big.NewInt(1)
	fullMask.Lsh(fullMask, uint(maskLen))
	fullMask.Sub(fullMask, big.NewInt(1))

	endIP := big.NewInt(0).SetBytes(ip)
	endIP.Add(endIP, fullMask)

	return startIP, net.IP(endIP.Bytes()), nil
}
