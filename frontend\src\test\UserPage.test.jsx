import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";
import UsersPage from "../pages/userManagement/UsersPage";
import { it, describe, expect } from "vitest";
import UsermgmtForm from "../components/UsermgmtForm"
import { loginUser } from "../features/auth/userAuthSlice";
import { generatSecretKey } from "../features/auth/twoFactorAuthSlice";


// Mock the App object with the modal.error function
vi.mock("antd", async () => {
  const actual = await vi.importActual("antd")
  return {
    ...actual,
    App: {
      useApp: () => ({
        modal: {
          error: vi.fn(),
        },
        notification: {
          error: vi.fn(),
          success: vi.fn(),
        }
      })
    }
}})

describe("UsersPage Component", () => {

  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("renders the component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <UsersPage />
      </Provider>
    );
    expect(container).toBeTruthy();
  });
  it("renders the form without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <UsermgmtForm />
      </Provider>
    );

    expect(container).toBeTruthy();
  });

  it("Call API generatSecretKey for user emtpy", async () => {
    await store.dispatch(generatSecretKey({ user: '' }));
    const state = store.getState().twoFactorAuthSlice;
    expect(state.isError).toEqual(true);
  });

});
