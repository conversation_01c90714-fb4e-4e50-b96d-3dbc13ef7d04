package detect

import (
	"github.com/google/gonids"
)

const defaultsize = 512

func NewDetectEngineCtx() (*DetectEngineCtx, error) {
	prefilter, err := newDetectprefilter()
	if err != nil {
		return nil, err
	}

	prefilterpkt, err := newDetectprefilterpkt()
	if err != nil {
		return nil, err
	}

	detectEngine := &DetectEngineCtx{
		manageSignature:  newManageSignature(),
		iponlyfilter:     newDetectIpOnlyEngineCtx(),
		nonprefilter:     newdetectnonprefilter(),
		prefilterpayload: prefilter,
		prefilterpkt:     prefilterpkt,
		info:             newExtraData(),
	}
	return detectEngine, nil
}

// DetectEngineCtx detect engine context
type DetectEngineCtx struct {
	//======below for engine========//
	manageSignature  *manageSignature
	iponlyfilter     *detectIpOnlyEngineCtx
	nonprefilter     *detectnonprefilter
	prefilterpayload *detectprefilterPayload
	prefilterpkt     *detectprefilterpkt
	//======below for matched========//
	matcharray []*signature
	matchIds   []int
	alertCount int
	info       ExtraData
	byteValues []uint64 //byte used
}

func (d *DetectEngineCtx) clone() *DetectEngineCtx {
	return &DetectEngineCtx{
		manageSignature:  d.manageSignature,
		iponlyfilter:     d.iponlyfilter,
		nonprefilter:     d.nonprefilter,
		prefilterpayload: d.prefilterpayload,
		prefilterpkt:     d.prefilterpkt,
		matcharray:       newsignatures(d.manageSignature.size),
		matchIds:         make([]int, 0, d.manageSignature.size),
		info:             d.info,
		alertCount:       d.alertCount,
		byteValues:       make([]uint64, d.info.payloadInfo.ByteExtractMaxLocalID+1),
	}
}

func (d *DetectEngineCtx) LoadGoNidRule(r gonids.Rule) error {
	return d.manageSignature.LoadNidRule(r)
}

func (d *DetectEngineCtx) manageSignatureBuild() ([]*signature, error) {
	err := d.manageSignature.Build(d.info)
	if err != nil {
		return nil, err
	}
	ss, err := d.manageSignature.GetSignatures()
	if err != nil {
		return nil, err
	}
	return ss, nil
}

func (d *DetectEngineCtx) Build() (err error) {
	d.info = newExtraData()
	ss, err := d.manageSignatureBuild()
	if err != nil {
		return err
	}
	for _, v := range ss {
		if v == nil {
			continue
		}
		switch v.sigtype {
		case sig_type_iponly:
			err := d.iponlyfilter.LoadSignature(v)
			if err != nil {
				return err
			}
		case sig_type_like_iponly:
			err := d.nonprefilter.LoadSignature(v)
			if err != nil {
				return err
			}
		case sig_type_stream:
			if v.flags&sig_flag_prefilter == sig_flag_prefilter {
				err := d.prefilterpayload.LoadSignature(v)
				if err != nil {
					return err
				}
			} else if v.flags&sig_flag_non_prefilter == sig_flag_non_prefilter {
				err := d.nonprefilter.LoadSignature(v)
				if err != nil {
					return err
				}
			}
		case sig_type_pkt:
			if v.flags&sig_flag_prefilter == sig_flag_prefilter {
				err := d.prefilterpkt.LoadSignature(v)
				if err != nil {
					return err
				}
			} else if v.flags&sig_flag_non_prefilter == sig_flag_non_prefilter {
				err := d.nonprefilter.LoadSignature(v)
				if err != nil {
					return err
				}
			}
		}
	}
	err = d.iponlyfilter.Build()
	if err != nil {
		return err
	}

	err = d.nonprefilter.Build()
	if err != nil {
		return err
	}
	err = d.prefilterpayload.Build()
	if err != nil {
		return err
	}
	err = d.prefilterpkt.Build()
	if err != nil {
		return err
	}
	return nil
}

func (d *DetectEngineCtx) Apply() error {
	d.manageSignature.Apply()
	d.iponlyfilter.Apply()
	d.nonprefilter.Apply()
	d.prefilterpayload.Apply()
	d.prefilterpkt.Apply()
	return nil
}

// DetectPacket detect packet base on NetworkLayer
func (d *DetectEngineCtx) DetectPacket(netlinker NetLinker, packet []byte) {
	p, err := ConvertBytesToPacket(packet)
	if err != nil {
		netlinker.Default()
		return
	}
	detctx := d.clone()
	detectIponly(detctx, netlinker, p)
	detectPrefilterNonPrefilterList(detctx, p)
	detectPrefilterpkt(detctx, p)
	detectRulePacketRules(detctx, netlinker, p)

}
func (d *DetectEngineCtx) AppendSignatures() []*signature {
	for _, id := range d.matchIds {
		sig, err := d.manageSignature.FindSignatures(id)
		if err != nil {
			continue
		}
		d.matcharray = append(d.matcharray, sig)
	}
	return nil
}

func detectIponly(detctx *DetectEngineCtx, netlinker NetLinker, packet *Packet) {
	detctx.iponlyfilter.Verify(detctx, netlinker, packet)
}

func detectPrefilterNonPrefilterList(detctx *DetectEngineCtx, packet *Packet) {
	if len(detctx.nonprefilter.instancesignatures) > 0 {
		detctx.matcharray = append(detctx.matcharray, detctx.nonprefilter.instancesignatures...)
	}
}

func detectPrefilterpkt(detctx *DetectEngineCtx, packet *Packet) {
	detectPkt(detctx, packet)
	detctx.matchIds = append(detctx.matchIds, detctx.prefilterpayload.Matches(packet.payload)...)
	detctx.AppendSignatures()

}

func detectRulePacketRules(detctx *DetectEngineCtx, netlinker NetLinker, packet *Packet) {
	for _, sig := range detctx.matcharray {
		if detectPacket(detctx, sig, packet) {
			matched(detctx, netlinker, sig, packet)

		}
	}
	matched(detctx, netlinker, nil, packet)
}

func detectPkt(detctx *DetectEngineCtx, packet *Packet) {

	detctx.matchIds = append(detctx.matchIds, detctx.prefilterpkt.Matches(packet)...)

}
func detectPacket(detctx *DetectEngineCtx, sig *signature, packet *Packet) bool {
	return sig.DetectIPAndPort(packet) && sig.DetectProto(packet) && sig.DetectInspection(detctx, packet)

}

func matched(detctx *DetectEngineCtx, netlinker NetLinker, sig *signature, packet *Packet) {
	if sig == nil {
		if packet.record.acted&ActionPass == 0 && packet.record.acted&ActionDrop == 0 {
			netlinker.Default()
		}
	} else {
		if packet.record.acted&ActionPass == 0 && sig.action&ActionPass == ActionPass {
			netlinker.Pass()
			packet.record.acted |= ActionPass
		} else if packet.record.acted&ActionDrop == 0 && sig.action&ActionDrop == ActionDrop {
			netlinker.Drop()
			packet.record.acted |= ActionDrop
		}
		if detctx.alertCount < packet.record.maxAlertSzie && sig.action&ActionAlert == ActionAlert {
			info := convertToInfoMatched(*packet, sig)
			netlinker.Alert(info)
			packet.record.acted |= ActionAlert
			detctx.alertCount++
		}
	}

}
