import React, { memo, useRef, useEffect, useState } from "react";
import {
  Input,
  Button,
  Typography,
  Avatar,
  Spin,
  Alert,
  Dropdown,
  Flex,
  Empty,
} from "antd";
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  MoreOutlined,
  ClearOutlined,
  InfoCircleOutlined,
  CloseOutlined,
  ExpandOutlined,
  CompressOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { useTheme } from "antd-style";
import dayjs from "dayjs";
import markdownit from "markdown-it";
import hljs from "highlight.js";

import SessionSelector from "./session-selector";
import NewSessionModal from "./new-session-modal";
import ChatSidebar from "./chat-sidebar";
import { useChatStore } from "../../../features/chat/chat-store";
import {
  useChatWithLLMMutation,
  useUpdateLLMsessionMessagesMutation,
} from "../../../app/services/aiassistApi";

const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const ChatMessage = memo(({ message, onDelete }) => {
  if (!message.content) {
    return null;
  }
  if (message.role === "tool") {
    return null;
  }
  const theme = useTheme();
  const isUser = message.role === "user";

  const md = markdownit({
    html: true,
    breaks: true,
    highlight: (str, lang) => {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlightAuto(str).value;
        } catch (__) {}
      }
      return ""; // use external default
    },
  });

  const renderMarkdown = (content) => (
    <Typography>
      {/* biome-ignore lint/security/noDangerouslySetInnerHtml: used in demo */}
      <div
        dangerouslySetInnerHTML={{
          __html: md.render(content),
        }}
      />
    </Typography>
  );

  return (
    <div
      style={{
        display: "flex",
        flexDirection: isUser ? "row-reverse" : "row",
        marginBottom: 16,
        alignItems: "flex-start",
      }}
    >
      <Avatar
        icon={isUser ? <UserOutlined /> : <RobotOutlined />}
        style={{
          backgroundColor: isUser ? theme.colorPrimary : theme.colorSuccess,
          margin: isUser ? "0 0 0 8px" : "0 8px 0 0",
        }}
      />
      <div
        style={{
          maxWidth: "70%",
          padding: "8px 12px",
          borderRadius: 8,
          backgroundColor: isUser
            ? theme.colorPrimaryBg
            : theme.colorBgContainer,
          border: `1px solid ${theme.colorBorder}`,
          position: "relative",
        }}
      >
        <Paragraph
          style={{
            margin: 0,
            fontSize: 14,
          }}
        >
          {message.isLoading ? (
            <Spin size="small" style={{ marginRight: 8 }} />
          ) : null}
          {renderMarkdown(message.content)}
        </Paragraph>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: 4,
          }}
        >
          <Text
            type="secondary"
            style={{
              fontSize: 12,
            }}
          >
            {dayjs(message.timestamp).format("HH:mm")}
          </Text>

          {/* Delete button for both user and assistant messages */}
          {onDelete && (
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => onDelete(message.id)}
              style={{
                opacity: 0.6,
                padding: "2px 4px",
                height: "auto",
                minWidth: "auto",
              }}
              title="Delete message"
            />
          )}
        </div>
        {message.error && (
          <Alert
            message={message.error}
            type="error"
            size="small"
            style={{ marginTop: 4 }}
          />
        )}
      </div>
    </div>
  );
});

ChatMessage.displayName = "ChatMessage";

const ChatInterface = memo(({ handleToggleExpand, isExpanded, toggleChat }) => {
  const theme = useTheme();
  const messagesEndRef = useRef();
  const [inputValue, setInputValue] = useState("");
  const [chatWithLLM] = useChatWithLLMMutation();
  const [updateSessionMessages] = useUpdateLLMsessionMessagesMutation();

  const {
    messages,
    isLoading,
    error,
    session_id,
    currentSession,
    addMessage,
    deleteMessage,
    setLoading,
    setError,
  } = useChatStore();

  const thinkMessage = {
    id: "think_msg",
    content: "Thinking...",
    role: "assistant",
    timestamp: new Date(),
    isLoading: true,
  };

  // Handle message deletion
  const handleDeleteMessage = async (messageId) => {
    // Remove message from local state
    deleteMessage(messageId);

    // Get updated messages after deletion
    const updatedMessages = messages.filter((msg) => msg.id !== messageId);

    // Send updated chat history to server if we have a session
    if (currentSession && session_id) {
      try {
        await updateSessionMessages({
          session_id: session_id,
          messages: updatedMessages.map((msg) => ({
            role: msg.role,
            ...(msg.content && { content: msg.content }),
            ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
            ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
            ...(msg.name && { name: msg.name }),
          })),
        }).unwrap();
      } catch (error) {
        console.error("Failed to update chat history after deletion:", error);
        // Optionally show error to user if needed.
        //setError("Failed to sync message deletion with server");
      }
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "auto" });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !currentSession) return;

    const userMessage = {
      content: inputValue.trim(),
      role: "user",
      timestamp: new Date(),
    };

    // Add user message
    addMessage(userMessage);
    setLoading(true);
    setError(null);

    try {
      const response = await chatWithLLM({
        session_id: session_id,
        messages: [
          ...messages.map((msg) => ({
            role: msg.role,
            ...(msg.content && { content: msg.content }),
            ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
            ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
            ...(msg.name && { name: msg.name }),
          })),
          {
            role: "user",
            content: inputValue.trim(),
          },
        ],
      }).unwrap();

      // Add assistant response(s) to chat
      if (response.new_messages && response.new_messages.length > 0) {
        response.new_messages.forEach((msg) => {
          if (msg.role === "assistant") {
            addMessage({
              ...(msg.content && { content: msg.content }),
              role: "assistant",
              ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
              ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
              ...(msg.name && { name: msg.name }),
              timestamp: new Date(),
            });
          }
        });
      }
    } catch (error) {
      console.error("Chat error:", error);
      setError(error?.data?.error || "Failed to send message");
    } finally {
      setLoading(false);
    }

    // Clear input after sending
    setInputValue("");
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    // Escape key to clear input
    if (e.key === "Escape") {
      setInputValue("");
    }
  };

  return (
    <div
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "row",
        backgroundColor: theme.colorBgLayout,
      }}
    >
      {/* Sidebar - only show when expanded */}
      {isExpanded && <ChatSidebar />}

      {/* Main Chat Area */}
      <div
        style={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          minWidth: 0,
        }}
      >
        {/* Chat Header */}
        <div
          style={{
            padding: "12px 16px",
            borderBottom: `1px solid ${theme.colorBorder}`,
            backgroundColor: theme.colorBgContainer,
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              flexDirection: "column",
            }}
          >
            <Flex vertical gap={8}>
              <Flex justify="space-between">
                <Text strong>NIMBL Assistant</Text>
                <Flex gap={4}>
                  <Button
                    type="text"
                    size="small"
                    icon={
                      isExpanded ? <CompressOutlined /> : <ExpandOutlined />
                    }
                    onClick={handleToggleExpand}
                    title={isExpanded ? "Minimize" : "Expand"}
                  />
                  <Button
                    type="text"
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={toggleChat}
                    title="Close Chat"
                  />
                </Flex>
              </Flex>

              <Text type="secondary" style={{ fontSize: 12 }}>
                {currentSession
                  ? `Current: ${currentSession.provider}/${currentSession.model}`
                  : "No active session"}
              </Text>

              {/* Session Selector - only show when not expanded */}
              {!isExpanded && <SessionSelector disabled={isLoading} />}
            </Flex>
          </div>
        </div>

        {/* Messages Area */}
        <div
          style={{
            flex: 1,
            padding: "16px",
            overflowY: "auto",
            minHeight: 0,
          }}
        >
          {!currentSession ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div style={{ textAlign: "center" }}>
                  <Text type="secondary">No session active</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    Click "New Session" to start chatting
                  </Text>
                </div>
              }
            />
          ) : (
            <>
              {messages.map((message) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  onDelete={handleDeleteMessage}
                />
              ))}
              {isLoading && (
                <ChatMessage key={thinkMessage.id} message={thinkMessage} />
              )}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div style={{ padding: "0 16px" }}>
            <Alert
              message={error}
              type="error"
              closable
              onClose={() => setError(null)}
              style={{ marginBottom: 8 }}
            />
          </div>
        )}

        {/* Input Area */}
        <div
          style={{
            padding: "12px 16px",
            borderTop: `1px solid ${theme.colorBorder}`,
            backgroundColor: theme.colorBgContainer,
          }}
        >
          <div style={{ display: "flex", gap: 8 }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                currentSession
                  ? "Type your message... (Enter to send, Shift+Enter for new line)"
                  : "Select or create a session to start chatting"
              }
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading || !currentSession}
              style={{ flex: 1 }}
              aria-label="Chat message input"
              role="textbox"
              aria-multiline="true"
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={isLoading}
              disabled={!inputValue.trim() || !currentSession}
              aria-label="Send message"
              title="Send message (Enter)"
            />
          </div>
        </div>

        {/* New Session Modal */}
        <NewSessionModal />
      </div>
    </div>
  );
});

ChatInterface.displayName = "ChatInterface";

export default ChatInterface;
