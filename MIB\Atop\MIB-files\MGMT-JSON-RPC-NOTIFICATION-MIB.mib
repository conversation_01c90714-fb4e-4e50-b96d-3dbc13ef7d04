-- *****************************************************************
-- JSON-RPC-NOTIFICATION-MIB:  
-- ****************************************************************

MGMT-JSON-RPC-NOTIFICATION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    MGMTDisplayString FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtJsonRpcNotificationMib MODULE-IDENTITY
    LAST-UPDATED "201410030000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private JSON-RPC Notification MIB."
    REVISION    "201410030000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 129 }


MGMTJsonRpcNotificationDestAuthType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Type of authentication (if any)."
    SYNTAX      INTEGER { none(0), basic(1) }

mgmtJsonRpcNotificationMibObjects OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationMib 1 }

mgmtJsonRpcNotificationConfig OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationMibObjects 2 }

mgmtJsonRpcNotificationConfigDestinationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTJsonRpcNotificationConfigDestinationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of JSON-RPC Notification destinations."
    ::= { mgmtJsonRpcNotificationConfig 1 }

mgmtJsonRpcNotificationConfigDestinationEntry OBJECT-TYPE
    SYNTAX      MGMTJsonRpcNotificationConfigDestinationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Entries in this table represent a JSON-RPC Notification destination
         which can be referred to in the notification subscription table."
    INDEX       { mgmtJsonRpcNotificationConfigDestinationName }
    ::= { mgmtJsonRpcNotificationConfigDestinationTable 1 }

MGMTJsonRpcNotificationConfigDestinationEntry ::= SEQUENCE {
    mgmtJsonRpcNotificationConfigDestinationName      MGMTDisplayString,
    mgmtJsonRpcNotificationConfigDestinationUrl       MGMTDisplayString,
    mgmtJsonRpcNotificationConfigDestinationAuthType  MGMTJsonRpcNotificationDestAuthType,
    mgmtJsonRpcNotificationConfigDestinationUsername  MGMTDisplayString,
    mgmtJsonRpcNotificationConfigDestinationPassword  MGMTDisplayString,
    mgmtJsonRpcNotificationConfigDestinationAction    MGMTRowEditorState
}

mgmtJsonRpcNotificationConfigDestinationName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of destination"
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 1 }

mgmtJsonRpcNotificationConfigDestinationUrl OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "URL of the destination where the events are delivered to."
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 2 }

mgmtJsonRpcNotificationConfigDestinationAuthType OBJECT-TYPE
    SYNTAX      MGMTJsonRpcNotificationDestAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of authentication to use (if any)"
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 3 }

mgmtJsonRpcNotificationConfigDestinationUsername OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User name for the authentication process"
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 4 }

mgmtJsonRpcNotificationConfigDestinationPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Password for the authentication process"
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 5 }

mgmtJsonRpcNotificationConfigDestinationAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtJsonRpcNotificationConfigDestinationEntry 100 }

mgmtJsonRpcNotificationConfigDestinationRowEditor OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationConfig 2 }

mgmtJsonRpcNotificationConfigDestinationRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of destination"
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 1 }

mgmtJsonRpcNotificationConfigDestinationRowEditorUrl OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "URL of the destination where the events are delivered to."
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 2 }

mgmtJsonRpcNotificationConfigDestinationRowEditorAuthType OBJECT-TYPE
    SYNTAX      MGMTJsonRpcNotificationDestAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of authentication to use (if any)"
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 3 }

mgmtJsonRpcNotificationConfigDestinationRowEditorUsername OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User name for the authentication process"
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 4 }

mgmtJsonRpcNotificationConfigDestinationRowEditorPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Password for the authentication process"
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 5 }

mgmtJsonRpcNotificationConfigDestinationRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtJsonRpcNotificationConfigDestinationRowEditor 100 }

mgmtJsonRpcNotificationConfigNotificationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTJsonRpcNotificationConfigNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Table of JSON-RPC Notifications subscriptions."
    ::= { mgmtJsonRpcNotificationConfig 3 }

mgmtJsonRpcNotificationConfigNotificationEntry OBJECT-TYPE
    SYNTAX      MGMTJsonRpcNotificationConfigNotificationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry represents a subscription of a given notification to a given
         destination. If the corresponding destination is deleted, then
         subscription is deleted as well."
    INDEX       {                   mgmtJsonRpcNotificationConfigNotificationDestination,
                  mgmtJsonRpcNotificationConfigNotificationNotification }
    ::= { mgmtJsonRpcNotificationConfigNotificationTable 1 }

MGMTJsonRpcNotificationConfigNotificationEntry ::= SEQUENCE {
    mgmtJsonRpcNotificationConfigNotificationDestination   MGMTDisplayString,
    mgmtJsonRpcNotificationConfigNotificationNotification  MGMTDisplayString,
    mgmtJsonRpcNotificationConfigNotificationAction        MGMTRowEditorState
}

mgmtJsonRpcNotificationConfigNotificationDestination OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of destination"
    ::= { mgmtJsonRpcNotificationConfigNotificationEntry 1 }

mgmtJsonRpcNotificationConfigNotificationNotification OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..96))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of notification"
    ::= { mgmtJsonRpcNotificationConfigNotificationEntry 2 }

mgmtJsonRpcNotificationConfigNotificationAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtJsonRpcNotificationConfigNotificationEntry 100 }

mgmtJsonRpcNotificationConfigNotificationRowEditor OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationConfig 4 }

mgmtJsonRpcNotificationConfigNotificationRowEditorDestination OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of destination"
    ::= { mgmtJsonRpcNotificationConfigNotificationRowEditor 1 }

mgmtJsonRpcNotificationConfigNotificationRowEditorNotification OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..96))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of notification"
    ::= { mgmtJsonRpcNotificationConfigNotificationRowEditor 2 }

mgmtJsonRpcNotificationConfigNotificationRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtJsonRpcNotificationConfigNotificationRowEditor 100 }

mgmtJsonRpcNotificationMibConformance OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationMib 2 }

mgmtJsonRpcNotificationMibCompliances OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationMibConformance 1 }

mgmtJsonRpcNotificationMibGroups OBJECT IDENTIFIER
    ::= { mgmtJsonRpcNotificationMibConformance 2 }

mgmtJsonRpcNotificationConfigDestinationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtJsonRpcNotificationConfigDestinationName,
                  mgmtJsonRpcNotificationConfigDestinationUrl,
                  mgmtJsonRpcNotificationConfigDestinationAuthType,
                  mgmtJsonRpcNotificationConfigDestinationUsername,
                  mgmtJsonRpcNotificationConfigDestinationPassword,
                  mgmtJsonRpcNotificationConfigDestinationAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtJsonRpcNotificationMibGroups 1 }

mgmtJsonRpcNotificationConfigDestinationRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtJsonRpcNotificationConfigDestinationRowEditorName,
                  mgmtJsonRpcNotificationConfigDestinationRowEditorUrl,
                  mgmtJsonRpcNotificationConfigDestinationRowEditorAuthType,
                  mgmtJsonRpcNotificationConfigDestinationRowEditorUsername,
                  mgmtJsonRpcNotificationConfigDestinationRowEditorPassword,
                  mgmtJsonRpcNotificationConfigDestinationRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtJsonRpcNotificationMibGroups 2 }

mgmtJsonRpcNotificationConfigNotificationTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtJsonRpcNotificationConfigNotificationDestination,
                  mgmtJsonRpcNotificationConfigNotificationNotification,
                  mgmtJsonRpcNotificationConfigNotificationAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtJsonRpcNotificationMibGroups 3 }

mgmtJsonRpcNotificationConfigNotificationRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtJsonRpcNotificationConfigNotificationRowEditorDestination,
                  mgmtJsonRpcNotificationConfigNotificationRowEditorNotification,
                  mgmtJsonRpcNotificationConfigNotificationRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtJsonRpcNotificationMibGroups 4 }

mgmtJsonRpcNotificationMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS {                        mgmtJsonRpcNotificationConfigDestinationTableInfoGroup,
                       mgmtJsonRpcNotificationConfigDestinationRowEditorInfoGroup,
                       mgmtJsonRpcNotificationConfigNotificationTableInfoGroup,
                       mgmtJsonRpcNotificationConfigNotificationRowEditorInfoGroup }

    ::= { mgmtJsonRpcNotificationMibCompliances 1 }

END
