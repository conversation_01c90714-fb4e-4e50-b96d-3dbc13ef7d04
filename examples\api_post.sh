URL=http://localhost:27182

if [ $# -lt 2 ]; then
    echo Usage: $0 api_endpoint post_data
    exit 1
fi
apiendpoint=$URL/$1
postdata=$2
if [ "$apiendpoint" == "" ]; then
	echo 'no api endpoint specified'
	exit 1
fi
token=$(./get_token.sh)
if [ "$token" == "" ]; then
	echo 'no token'
	exit 1
fi

resp=$(curl -i -s -H "Content-Type: application/json" -H "Authorization: Bearer $token" -X POST -d "$postdata" $apiendpoint)

