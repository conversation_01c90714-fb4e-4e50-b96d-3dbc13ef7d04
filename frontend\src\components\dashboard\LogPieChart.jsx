import { theme } from "antd";
import React, { useEffect, useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTheme } from "antd-style";

const LogPieChart = ({ series }) => {
  const { token } = theme.useToken();
  const { appearance } = useTheme();
  const [pieChartData, setPieChartData] = useState({
    series: series,
    options: {
      chart: {
        background: token.colorBgContainer,
        type: "pie",
      },
      labels: [
        `Error(${series[0]})`,
        `Anomaly(${series[1]})`,
        `Normal(${series[2]})`,
      ],
      theme: {
        mode: appearance,
      },
      colors: ["#E91E63", "#FF9800", token.colorSuccess],
      legend: {
        position: "right",
      },
      plotOptions: {
        pie: {
          dataLabels: {
            offset: -15,
          },
        },
      },
      //labels: ["no data"],
      dataLabels: {
        enabled: true,
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 150,
            },
            legend: {
              position: "bottom",
              horizontalAlign: "left",
            },
          },
        },
      ],
    },
  });

  useEffect(() => {
    setPieChartData((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        theme: { mode: appearance },
        chart: { ...prev.options.chart, background: token.colorBgContainer },
      },
    }));
  }, [token, appearance]);

  useEffect(() => {
    setPieChartData((prev) => ({
      ...prev,
      series: series,
      options: {
        ...prev.options,
        labels: [
          `Error(${series[0]})`,
          `Anomaly(${series[1]})`,
          `Normal(${series[2]})`,
        ],
      },
    }));
  }, [series]);

  return (
    <ReactApexChart
      options={pieChartData.options}
      series={pieChartData.series}
      height={200}
      type="pie"
    />
  );
};

export default LogPieChart;
