package detect

import (
	"github.com/zmap/go-iptree/iptree"
)

type IpDetected struct {
	ipV4PostiveTree *iptree.IPTree
	ipv4NegatedTree *iptree.IPTree
	ipv6PostiveTree *iptree.IPTree
	ipv6NegatedTree *iptree.IPTree
	negated         bool
}

func NewIpAddressDetected(infos *iPInfo) (*IpDetected, error) {
	v := &IpDetected{}
	curr := infos
	for curr != nil {
		var tree *iptree.IPTree
		negate := curr.negated
		if negate {
			v.negated = true
			if curr.family == AFInet {
				if v.ipv4NegatedTree == nil {
					v.ipv4NegatedTree = iptree.New()
				}
				tree = v.ipv4NegatedTree
			} else if curr.family == AFInet6 {
				if v.ipv6NegatedTree == nil {
					v.ipv6NegatedTree = iptree.New()
				}
				tree = v.ipv6NegatedTree
			}
		} else {
			if curr.family == AFInet {
				if v.ipV4PostiveTree == nil {
					v.ipV4PostiveTree = iptree.New()
				}
				tree = v.ipV4PostiveTree
			} else if curr.family == AFInet6 {
				if v.ipv6PostiveTree == nil {
					v.ipv6PostiveTree = iptree.New()
				}
				tree = v.ipv6PostiveTree
			}
		}
		_, found, err := tree.GetByString(curr.String())
		if err != nil {
			return nil, err
		}
		if !found {
			err = tree.AddByString(curr.String(), empty{})
			if err != nil {
				return nil, err
			}
		}
		curr = curr.next
	}

	return v, nil
}

func (v *IpDetected) verify(family int, ip string) bool {
	if family == AFInet {
		if v.ipv4NegatedTree != nil {
			_, b, _ := v.ipv4NegatedTree.GetByString(ip)
			if b {
				return false
			}
		}
		if v.ipV4PostiveTree != nil {
			_, b, _ := v.ipV4PostiveTree.GetByString(ip)
			if b {
				return true
			} else {
				return false
			}
		}
	} else if family == AFInet6 {
		if v.ipv6NegatedTree != nil {
			_, b, _ := v.ipv6NegatedTree.GetByString(ip)
			if b {
				return false
			}
		}
		if v.ipv6PostiveTree != nil {
			_, b, _ := v.ipv6PostiveTree.GetByString(ip)
			if b {
				return true
			} else {
				return false
			}
		}
	}

	return true
}
