-- *****************************************************************
-- APS-MIB:  
-- ****************************************************************

MGMT-APS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-<PERSON><PERSON>
    <PERSON>Address FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtApsMib MODULE-IDENTITY
    LAST-UPDATED "202003050000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private Linear APS (G.8031) MIB"
    REVISION    "202003050000Z"
    DESCRIPTION
        "Added Signal Fail triggering options"
    REVISION    "201908270000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 150 }


MGMTApsCommand ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "protection group command."
    SYNTAX      INTEGER { noRequest(0), lockout(1), forceSwitch(2),
                          manualSwitchToWorking(3),
                          manualSwitchToProtecting(4), exercise(5),
                          clear(6), freeze(7), freezeClear(8) }

MGMTApsDefectState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Interface defect state."
    SYNTAX      INTEGER { ok(0), sd(1), sf(2) }

MGMTApsMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The APS protection mode."
    SYNTAX      INTEGER { oneForOne(0), onePlusOneUniDir(1),
                          onePlusOneBiDir(2) }

MGMTApsOperationalState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "APS instance operational state."
    SYNTAX      INTEGER { disabled(0), active(1), internalError(2) }

MGMTApsOperationalWarning ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "APS instance operational warning."
    SYNTAX      INTEGER { none(0), wMEPNotFound(1), pMEPNotFound(2),
                          wMEPAdminDisabled(3), pMEPAdminDisabled(4),
                          wMEPNotDownMEP(5), pMEPNotDownMEP(6),
                          wMEPDiffersFromIfindex(7),
                          pMEPDiffersFromIfindex(8) }

MGMTApsProtectionState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "protection group state."
    SYNTAX      INTEGER { noRequestWorking(0), noRequestProtect(1),
                          lockout(2), forcedSwitch(3),
                          signalFailWorking(4), signalFailProtect(5),
                          manualSwitchtoProtect(6),
                          manualSwitchtoWorking(7), waitToRestore(8),
                          doNotRevert(9), exerciseWorking(10),
                          exerciseProtect(11),
                          reverseRequestWorking(12),
                          reverseRequestProtect(13),
                          signalDegradeWorking(14),
                          signalDegradeProtect(15) }

MGMTApsRequest ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "APS request/state."
    SYNTAX      INTEGER { nr(0), dnr(1), rr(2), exer(4), wtr(5),
                          ms(7), sd(9), sfW(11), fs(13), sfP(14),
                          lo(15) }

MGMTApsSfTrigger ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The APS signal-fail triggering method."
    SYNTAX      INTEGER { link(0), mep(1) }

mgmtApsMibObjects OBJECT IDENTIFIER
    ::= { mgmtApsMib 1 }

mgmtApsCapabilities OBJECT IDENTIFIER
    ::= { mgmtApsMibObjects 1 }

mgmtApsCapabilitiesInstanceMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of created APS instances."
    ::= { mgmtApsCapabilities 1 }

mgmtApsCapabilitiesWtrSecsMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum WTR timer value in secs."
    ::= { mgmtApsCapabilities 2 }

mgmtApsCapabilitiesHoldOffMsecsMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum Hold Off timer value in msec."
    ::= { mgmtApsCapabilities 3 }

mgmtApsConfig OBJECT IDENTIFIER
    ::= { mgmtApsMibObjects 2 }

mgmtApsConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTApsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created APS instance parameters."
    ::= { mgmtApsConfig 1 }

mgmtApsConfigEntry OBJECT-TYPE
    SYNTAX      MGMTApsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created APS instance parameters."
    INDEX       { mgmtApsConfigId }
    ::= { mgmtApsConfigTable 1 }

MGMTApsConfigEntry ::= SEQUENCE {
    mgmtApsConfigId                    Integer32,
    mgmtApsConfigAdminActive           TruthValue,
    mgmtApsConfigWorkingMEPDomain      MGMTDisplayString,
    mgmtApsConfigWorkingMEPService     MGMTDisplayString,
    mgmtApsConfigWorkingMEPId          Unsigned32,
    mgmtApsConfigProtectingMEPDomain   MGMTDisplayString,
    mgmtApsConfigProtectingMEPService  MGMTDisplayString,
    mgmtApsConfigProtectingMEPId       Unsigned32,
    mgmtApsConfigMode                  MGMTApsMode,
    mgmtApsConfigTxApsEnable           TruthValue,
    mgmtApsConfigRevertive             TruthValue,
    mgmtApsConfigWaitToRestoreSecs     Unsigned32,
    mgmtApsConfigHoldOffTimerMSecs     Unsigned32,
    mgmtApsConfigWorkingIfIndex        MGMTInterfaceIndex,
    mgmtApsConfigWorkingSfTrigger      MGMTApsSfTrigger,
    mgmtApsConfigProtectingIfIndex     MGMTInterfaceIndex,
    mgmtApsConfigProtectingSfTrigger   MGMTApsSfTrigger,
    mgmtApsConfigLevel                 Integer32,
    mgmtApsConfigVid                   MGMTUnsigned16,
    mgmtApsConfigPcp                   MGMTUnsigned8,
    mgmtApsConfigSmac                  MacAddress,
    mgmtApsConfigAction                MGMTRowEditorState
}

mgmtApsConfigId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The APS instance ID"
    ::= { mgmtApsConfigEntry 1 }

mgmtApsConfigAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of this APS instance. Set to true to make it
         function normally and false to make it cease functioning."
    ::= { mgmtApsConfigEntry 2 }

mgmtApsConfigWorkingMEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name of the working MEP."
    ::= { mgmtApsConfigEntry 3 }

mgmtApsConfigWorkingMEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of the working MEP."
    ::= { mgmtApsConfigEntry 4 }

mgmtApsConfigWorkingMEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of the working MEP."
    ::= { mgmtApsConfigEntry 5 }

mgmtApsConfigProtectingMEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name of the protecting MEP."
    ::= { mgmtApsConfigEntry 6 }

mgmtApsConfigProtectingMEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of the protecting MEP."
    ::= { mgmtApsConfigEntry 7 }

mgmtApsConfigProtectingMEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID name of the protecting MEP."
    ::= { mgmtApsConfigEntry 8 }

mgmtApsConfigMode OBJECT-TYPE
    SYNTAX      MGMTApsMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the architecture and direction of the APS instance."
    ::= { mgmtApsConfigEntry 9 }

mgmtApsConfigTxApsEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Choose whether this end transmits APS PDUs. Only for 1+1,
         unidirectional."
    ::= { mgmtApsConfigEntry 10 }

mgmtApsConfigRevertive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Revertive operation can be enabled or disabled."
    ::= { mgmtApsConfigEntry 11 }

mgmtApsConfigWaitToRestoreSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Wait to restore timer in seconds - max. capabilities:WtrSecsMax - min.
         1."
    ::= { mgmtApsConfigEntry 12 }

mgmtApsConfigHoldOffTimerMSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hold off timer in 100 ms steps - max. capabilities:HoldOffMsecsMax -
         min. 0 means no hold off"
    ::= { mgmtApsConfigEntry 13 }

mgmtApsConfigWorkingIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Working port."
    ::= { mgmtApsConfigEntry 14 }

mgmtApsConfigWorkingSfTrigger OBJECT-TYPE
    SYNTAX      MGMTApsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the signal-fail triggering method for the working port."
    ::= { mgmtApsConfigEntry 15 }

mgmtApsConfigProtectingIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Protecting port."
    ::= { mgmtApsConfigEntry 16 }

mgmtApsConfigProtectingSfTrigger OBJECT-TYPE
    SYNTAX      MGMTApsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the signal-fail triggering method for the protecting port."
    ::= { mgmtApsConfigEntry 17 }

mgmtApsConfigLevel OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MD/MEG Level (0-7)."
    ::= { mgmtApsConfigEntry 18 }

mgmtApsConfigVid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID used in the L-APS PDUs. 0 means untagged"
    ::= { mgmtApsConfigEntry 19 }

mgmtApsConfigPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "PCP (priority) (default 7). The PCP value used in the VLAN tag unless
         the L-APS PDU is untagged. Must be a value in range [0; 7]. "
    ::= { mgmtApsConfigEntry 20 }

mgmtApsConfigSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address used in L-APS PDUs. Must be a unicast address. If
         all-zeros, the switch port's MAC address will be used."
    ::= { mgmtApsConfigEntry 21 }

mgmtApsConfigAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtApsConfigEntry 100 }

mgmtApsConfigRowEditor OBJECT IDENTIFIER
    ::= { mgmtApsConfig 2 }

mgmtApsConfigRowEditorId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The APS instance ID"
    ::= { mgmtApsConfigRowEditor 1 }

mgmtApsConfigRowEditorAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of this APS instance. Set to true to make it
         function normally and false to make it cease functioning."
    ::= { mgmtApsConfigRowEditor 2 }

mgmtApsConfigRowEditorWorkingMEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name of the working MEP."
    ::= { mgmtApsConfigRowEditor 3 }

mgmtApsConfigRowEditorWorkingMEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of the working MEP."
    ::= { mgmtApsConfigRowEditor 4 }

mgmtApsConfigRowEditorWorkingMEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of the working MEP."
    ::= { mgmtApsConfigRowEditor 5 }

mgmtApsConfigRowEditorProtectingMEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name of the protecting MEP."
    ::= { mgmtApsConfigRowEditor 6 }

mgmtApsConfigRowEditorProtectingMEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of the protecting MEP."
    ::= { mgmtApsConfigRowEditor 7 }

mgmtApsConfigRowEditorProtectingMEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID name of the protecting MEP."
    ::= { mgmtApsConfigRowEditor 8 }

mgmtApsConfigRowEditorMode OBJECT-TYPE
    SYNTAX      MGMTApsMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the architecture and direction of the APS instance."
    ::= { mgmtApsConfigRowEditor 9 }

mgmtApsConfigRowEditorTxApsEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Choose whether this end transmits APS PDUs. Only for 1+1,
         unidirectional."
    ::= { mgmtApsConfigRowEditor 10 }

mgmtApsConfigRowEditorRevertive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Revertive operation can be enabled or disabled."
    ::= { mgmtApsConfigRowEditor 11 }

mgmtApsConfigRowEditorWaitToRestoreSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Wait to restore timer in seconds - max. capabilities:WtrSecsMax - min.
         1."
    ::= { mgmtApsConfigRowEditor 12 }

mgmtApsConfigRowEditorHoldOffTimerMSecs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hold off timer in 100 ms steps - max. capabilities:HoldOffMsecsMax -
         min. 0 means no hold off"
    ::= { mgmtApsConfigRowEditor 13 }

mgmtApsConfigRowEditorWorkingIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Working port."
    ::= { mgmtApsConfigRowEditor 14 }

mgmtApsConfigRowEditorWorkingSfTrigger OBJECT-TYPE
    SYNTAX      MGMTApsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the signal-fail triggering method for the working port."
    ::= { mgmtApsConfigRowEditor 15 }

mgmtApsConfigRowEditorProtectingIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Protecting port."
    ::= { mgmtApsConfigRowEditor 16 }

mgmtApsConfigRowEditorProtectingSfTrigger OBJECT-TYPE
    SYNTAX      MGMTApsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Select the signal-fail triggering method for the protecting port."
    ::= { mgmtApsConfigRowEditor 17 }

mgmtApsConfigRowEditorLevel OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MD/MEG Level (0-7)."
    ::= { mgmtApsConfigRowEditor 18 }

mgmtApsConfigRowEditorVid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID used in the L-APS PDUs. 0 means untagged"
    ::= { mgmtApsConfigRowEditor 19 }

mgmtApsConfigRowEditorPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "PCP (priority) (default 7). The PCP value used in the VLAN tag unless
         the L-APS PDU is untagged. Must be a value in range [0; 7]. "
    ::= { mgmtApsConfigRowEditor 20 }

mgmtApsConfigRowEditorSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address used in L-APS PDUs. Must be a unicast address. If
         all-zeros, the switch port's MAC address will be used."
    ::= { mgmtApsConfigRowEditor 21 }

mgmtApsConfigRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtApsConfigRowEditor 100 }

mgmtApsStatus OBJECT IDENTIFIER
    ::= { mgmtApsMibObjects 3 }

mgmtApsStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTApsStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created APS instance status."
    ::= { mgmtApsStatus 1 }

mgmtApsStatusEntry OBJECT-TYPE
    SYNTAX      MGMTApsStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created APS instance status."
    INDEX       { mgmtApsStatusId }
    ::= { mgmtApsStatusTable 1 }

MGMTApsStatusEntry ::= SEQUENCE {
    mgmtApsStatusId                  Integer32,
    mgmtApsStatusOperationalState    MGMTApsOperationalState,
    mgmtApsStatusProtectionState     MGMTApsProtectionState,
    mgmtApsStatusWorkingState        MGMTApsDefectState,
    mgmtApsStatusProtectingState     MGMTApsDefectState,
    mgmtApsStatusTxApsRequest        MGMTApsRequest,
    mgmtApsStatusTxApsReSignal       MGMTUnsigned8,
    mgmtApsStatusTxApsBrSignal       MGMTUnsigned8,
    mgmtApsStatusRxApsRequest        MGMTApsRequest,
    mgmtApsStatusRxApsReSignal       MGMTUnsigned8,
    mgmtApsStatusRxApsBrSignal       MGMTUnsigned8,
    mgmtApsStatusDfopCM              TruthValue,
    mgmtApsStatusDfopPM              TruthValue,
    mgmtApsStatusDfopNR              TruthValue,
    mgmtApsStatusDfopTO              TruthValue,
    mgmtApsStatusSmac                MacAddress,
    mgmtApsStatusTxCnt               Counter64,
    mgmtApsStatusRxValidCnt          Counter64,
    mgmtApsStatusRxInvalidCnt        Counter64,
    mgmtApsStatusOperationalWarning  MGMTApsOperationalWarning
}

mgmtApsStatusId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The APS instance ID"
    ::= { mgmtApsStatusEntry 1 }

mgmtApsStatusOperationalState OBJECT-TYPE
    SYNTAX      MGMTApsOperationalState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Operational state."
    ::= { mgmtApsStatusEntry 2 }

mgmtApsStatusProtectionState OBJECT-TYPE
    SYNTAX      MGMTApsProtectionState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protection state according to to G.8031 Annex A."
    ::= { mgmtApsStatusEntry 3 }

mgmtApsStatusWorkingState OBJECT-TYPE
    SYNTAX      MGMTApsDefectState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Working interface defect state."
    ::= { mgmtApsStatusEntry 4 }

mgmtApsStatusProtectingState OBJECT-TYPE
    SYNTAX      MGMTApsDefectState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Protecting interface defect state."
    ::= { mgmtApsStatusEntry 5 }

mgmtApsStatusTxApsRequest OBJECT-TYPE
    SYNTAX      MGMTApsRequest
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted APS request."
    ::= { mgmtApsStatusEntry 6 }

mgmtApsStatusTxApsReSignal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted APS requested signal."
    ::= { mgmtApsStatusEntry 7 }

mgmtApsStatusTxApsBrSignal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted APS bridged signal."
    ::= { mgmtApsStatusEntry 8 }

mgmtApsStatusRxApsRequest OBJECT-TYPE
    SYNTAX      MGMTApsRequest
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received APS request."
    ::= { mgmtApsStatusEntry 9 }

mgmtApsStatusRxApsReSignal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received APS requested signal."
    ::= { mgmtApsStatusEntry 10 }

mgmtApsStatusRxApsBrSignal OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received APS bridged signal."
    ::= { mgmtApsStatusEntry 11 }

mgmtApsStatusDfopCM OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "FOP Configuration Mismatch - APS received on working."
    ::= { mgmtApsStatusEntry 12 }

mgmtApsStatusDfopPM OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "FOP Provisioning Mismatch."
    ::= { mgmtApsStatusEntry 13 }

mgmtApsStatusDfopNR OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "FOP No Response."
    ::= { mgmtApsStatusEntry 14 }

mgmtApsStatusDfopTO OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "FOP TimeOut."
    ::= { mgmtApsStatusEntry 15 }

mgmtApsStatusSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Source MAC address of last received LAPS PDU or all-zeros if no PDU has
         been received."
    ::= { mgmtApsStatusEntry 16 }

mgmtApsStatusTxCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of APS PDU frames transmitted."
    ::= { mgmtApsStatusEntry 17 }

mgmtApsStatusRxValidCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of valid APS PDU frames received on the protect port."
    ::= { mgmtApsStatusEntry 18 }

mgmtApsStatusRxInvalidCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of invalid APS PDU frames received on the protect port."
    ::= { mgmtApsStatusEntry 19 }

mgmtApsStatusOperationalWarning OBJECT-TYPE
    SYNTAX      MGMTApsOperationalWarning
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Operational warning."
    ::= { mgmtApsStatusEntry 101 }

mgmtApsControl OBJECT IDENTIFIER
    ::= { mgmtApsMibObjects 4 }

mgmtApsControlCommandTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTApsControlCommandEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created APS instance command. When an APS instance
         is created in the 'InstanceTable', an entry is automatically created
         here with 'no command'."
    ::= { mgmtApsControl 1 }

mgmtApsControlCommandEntry OBJECT-TYPE
    SYNTAX      MGMTApsControlCommandEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created APS instance command."
    INDEX       { mgmtApsControlCommandId }
    ::= { mgmtApsControlCommandTable 1 }

MGMTApsControlCommandEntry ::= SEQUENCE {
    mgmtApsControlCommandId       Integer32,
    mgmtApsControlCommandCommand  MGMTApsCommand
}

mgmtApsControlCommandId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The APS instance ID"
    ::= { mgmtApsControlCommandEntry 2 }

mgmtApsControlCommandCommand OBJECT-TYPE
    SYNTAX      MGMTApsCommand
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "protection group command."
    ::= { mgmtApsControlCommandEntry 3 }

mgmtApsControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTApsControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created APS clear commands."
    ::= { mgmtApsControl 2 }

mgmtApsControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTApsControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created APS clear command."
    INDEX       { mgmtApsControlStatisticsClearId }
    ::= { mgmtApsControlStatisticsClearTable 1 }

MGMTApsControlStatisticsClearEntry ::= SEQUENCE {
    mgmtApsControlStatisticsClearId     Integer32,
    mgmtApsControlStatisticsClearClear  TruthValue
}

mgmtApsControlStatisticsClearId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The APS instance ID"
    ::= { mgmtApsControlStatisticsClearEntry 2 }

mgmtApsControlStatisticsClearClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the counters of an APS instance."
    ::= { mgmtApsControlStatisticsClearEntry 4 }

mgmtApsMibConformance OBJECT IDENTIFIER
    ::= { mgmtApsMib 2 }

mgmtApsMibCompliances OBJECT IDENTIFIER
    ::= { mgmtApsMibConformance 1 }

mgmtApsMibGroups OBJECT IDENTIFIER
    ::= { mgmtApsMibConformance 2 }

mgmtApsCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsCapabilitiesInstanceMax,
                  mgmtApsCapabilitiesWtrSecsMax,
                  mgmtApsCapabilitiesHoldOffMsecsMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 1 }

mgmtApsConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsConfigId, mgmtApsConfigAdminActive,
                  mgmtApsConfigWorkingMEPDomain,
                  mgmtApsConfigWorkingMEPService,
                  mgmtApsConfigWorkingMEPId,
                  mgmtApsConfigProtectingMEPDomain,
                  mgmtApsConfigProtectingMEPService,
                  mgmtApsConfigProtectingMEPId, mgmtApsConfigMode,
                  mgmtApsConfigTxApsEnable, mgmtApsConfigRevertive,
                  mgmtApsConfigWaitToRestoreSecs,
                  mgmtApsConfigHoldOffTimerMSecs,
                  mgmtApsConfigWorkingIfIndex,
                  mgmtApsConfigWorkingSfTrigger,
                  mgmtApsConfigProtectingIfIndex,
                  mgmtApsConfigProtectingSfTrigger,
                  mgmtApsConfigLevel, mgmtApsConfigVid,
                  mgmtApsConfigPcp, mgmtApsConfigSmac,
                  mgmtApsConfigAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 2 }

mgmtApsConfigRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsConfigRowEditorId,
                  mgmtApsConfigRowEditorAdminActive,
                  mgmtApsConfigRowEditorWorkingMEPDomain,
                  mgmtApsConfigRowEditorWorkingMEPService,
                  mgmtApsConfigRowEditorWorkingMEPId,
                  mgmtApsConfigRowEditorProtectingMEPDomain,
                  mgmtApsConfigRowEditorProtectingMEPService,
                  mgmtApsConfigRowEditorProtectingMEPId,
                  mgmtApsConfigRowEditorMode,
                  mgmtApsConfigRowEditorTxApsEnable,
                  mgmtApsConfigRowEditorRevertive,
                  mgmtApsConfigRowEditorWaitToRestoreSecs,
                  mgmtApsConfigRowEditorHoldOffTimerMSecs,
                  mgmtApsConfigRowEditorWorkingIfIndex,
                  mgmtApsConfigRowEditorWorkingSfTrigger,
                  mgmtApsConfigRowEditorProtectingIfIndex,
                  mgmtApsConfigRowEditorProtectingSfTrigger,
                  mgmtApsConfigRowEditorLevel,
                  mgmtApsConfigRowEditorVid,
                  mgmtApsConfigRowEditorPcp,
                  mgmtApsConfigRowEditorSmac,
                  mgmtApsConfigRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 3 }

mgmtApsStatusTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsStatusId, mgmtApsStatusOperationalState,
                  mgmtApsStatusProtectionState,
                  mgmtApsStatusWorkingState,
                  mgmtApsStatusProtectingState,
                  mgmtApsStatusTxApsRequest,
                  mgmtApsStatusTxApsReSignal,
                  mgmtApsStatusTxApsBrSignal,
                  mgmtApsStatusRxApsRequest,
                  mgmtApsStatusRxApsReSignal,
                  mgmtApsStatusRxApsBrSignal, mgmtApsStatusDfopCM,
                  mgmtApsStatusDfopPM, mgmtApsStatusDfopNR,
                  mgmtApsStatusDfopTO, mgmtApsStatusSmac,
                  mgmtApsStatusTxCnt, mgmtApsStatusRxValidCnt,
                  mgmtApsStatusRxInvalidCnt,
                  mgmtApsStatusOperationalWarning }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 4 }

mgmtApsControlCommandTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsControlCommandId,
                  mgmtApsControlCommandCommand }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 5 }

mgmtApsControlStatisticsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtApsControlStatisticsClearId,
                  mgmtApsControlStatisticsClearClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtApsMibGroups 6 }

mgmtApsMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtApsCapabilitiesInfoGroup,
                       mgmtApsConfigTableInfoGroup,
                       mgmtApsConfigRowEditorInfoGroup,
                       mgmtApsStatusTableInfoGroup,
                       mgmtApsControlCommandTableInfoGroup,
                       mgmtApsControlStatisticsClearTableInfoGroup }

    ::= { mgmtApsMibCompliances 1 }

END
