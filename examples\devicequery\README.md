## LLM Providers supported

- OpenAI
- Ollama
- Gemini
- Openrouter

## Building

```bash
go mod tidy
go build
```

## API keys

API keys to LLM providers can be set via -apikey flag or environment variables.

- OpenAI API key: Set it as an environment variable:
```bash
    export OPENAI_API_KEY="YOUR_API_KEY_HERE"
```

- Ollama API key: Set it as an environment variable:
```bash
    export OLLAMA_API_KEY="YOUR_API_KEY_HERE"
```

- Gemini API key: Set it as an environment variable:
```bash
    export GEMINI_API_KEY="YOUR_API_KEY_HERE"
```
- Openrouter API key: Set it as an environment variable:
```bash
    export OPENROUTER_API_KEY="YOUR_API_KEY_HERE"
```

## Run with ollama

./devicequery -apikey YOURKEY  -baseurl https://**************.sslip.io/v1 -jsonfile ../devices.json -llm ollama -model qwen3:8b

## Run with openai
export OPENAI_API_KEY="YOUR_API_KEY_HERE"
./devicequery  -jsonfile ../devices.json -llm openai -model o4-mini

## Run with gemini

export GEMINI_API_KEY="YOUR_API_KEY_HERE"
./devicequery  -jsonfile ../devices.json -llm gemini -model gemini-1.5-flash-latest

## Run with openrouter
export OPENROUTER_API_KEY="YOUR_API_KEY_HERE"
./devicequery  -jsonfile ../devices.json -llm openrouter -model openai/gpt-3.5-turbo

There are many free models available on OpenRouter.

Not all models on OpenRouter support function/tool calling but 
some do:

 - openai/gpt-3.5-turbo
 - meta-llama/llama-4-maverick:free
   - This model supports function/tool calling but is not as powerful as the other models.
 - list of all free models: https://openrouter.ai/models?q=free&max_price=0
 
## Example English Queries

how many devices contained model name pattern EHG7504 in the name

Show me the IP address and hostname for MAC 00-60-E9-1F-A6-03

How many devices are online?

what are all the unique OUI prefixes of mac addresses?

how many devices have prefix mac 00-60-E9?

Find devices scanned by nms1 with kernel version 5.60

Which devices have gwd capability true?

Show devices where the timestamp unix string is 1725944217

List devices with modelname EH7520-4G-4SFP and are online.

What is the AP for device with IPAddress ***********?

Find devices with hostname 'switch'

Show MAC and IPAddress for devices with modelname 'RHG7528-R' and an AP version containing 'V2.68'

## Example English to SQL Queries
### **1. Basic Filters**
**Example: List all online devices**  
```sql
SELECT * 
FROM devices 
WHERE IsOnline = true;
```

**Example: Find devices with a specific IP address**  
```sql
SELECT * 
FROM devices 
WHERE IPAddress = '**************';
```

**Example: Show devices that use the `gwd` scanning protocol**  
```sql
SELECT * 
FROM devices 
WHERE ScanProto = 'gwd';
```

---

### **2. Aggregations & Grouping**
**Example: Count devices per OUI**  
```sql
SELECT 
  SUBSTR(MAC, 1, 8) AS OUI, 
  COUNT(*) AS device_count
FROM devices
GROUP BY SUBSTR(MAC, 1, 8)
ORDER BY device_count DESC;
```

**Example: Count devices per vendor (OUI)**  
```sql
SELECT 
  SUBSTR(MAC, 1, 8) AS OUI, 
  COUNT(*) AS device_count
FROM devices
GROUP BY SUBSTR(MAC, 1, 8)
ORDER BY device_count DESC;
```

**Example: Total devices with SNMP enabled**  
```sql
SELECT 
  COUNT(*) AS snmp_enabled_devices
FROM devices
WHERE TopologyProto = 'snmp';
```

---

### **3. Advanced Filters & Joins**
**Example: Find devices with a specific SNMP community string**  
```sql
SELECT * 
FROM devices 
WHERE ReadCommunity = 'public' OR WriteCommunity = 'private';
```

**Example: Show devices that are not assigned an IP address**  
```sql
SELECT * 
FROM devices 
WHERE IPAddress IS NULL OR IPAddress = '';
```

**Example: Filter devices with a specific hostname**  
```sql
SELECT * 
FROM devices 
WHERE Hostname LIKE '%switch%';
```

---

### **4. Custom Analysis**
**Example: List devices with both `gwd` and SNMP enabled**  
```sql
SELECT * 
FROM devices 
WHERE ScanProto = 'gwd' AND TopologyProto = 'snmp';
```

**Example: Find devices with outdated firmware (e.g., kernel < 5.0)**  
```sql
SELECT * 
FROM devices 
WHERE Kernel < '5.0';
```

**Example: Count devices per subnet (based on IP)**  
```sql
SELECT 
  SUBSTR(IPAddress, 1, INSTR(IPAddress, '.') - 1) AS subnet, 
  COUNT(*) AS devices_in_subnet
FROM devices
GROUP BY SUBSTR(IPAddress, 1, INSTR(IPAddress, '.') - 1);
```

---

### **5. Debugging & Troubleshooting**
**Example: Find devices with no ARP misses**  
```sql
SELECT * 
FROM devices 
WHERE ARPMissed = 0;
```

**Example: List devices with empty credentials**  
```sql
SELECT * 
FROM devices 
WHERE ReadCommunity = '' OR WriteCommunity = '';
```

**Example: Find devices with null `Capabilities`**  
```sql
SELECT * 
FROM devices 
WHERE Capabilities IS NULL;
```

---

### **6. Customized Views**
**Example: Show unique OUIs and their device counts**  
```sql
SELECT 
  SUBSTR(MAC, 1, 8) AS OUI, 
  COUNT(*) AS device_count
FROM devices
GROUP BY SUBSTR(MAC, 1, 8)
ORDER BY device_count DESC;
```

**Example: Export a list of online devices with their IPs**  
```sql
SELECT MAC, IPAddress, IsOnline
FROM devices
WHERE IsOnline = true;
```
