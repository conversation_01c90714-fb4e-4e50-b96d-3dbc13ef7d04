// devicequery_multillm/main.go
package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strings"

	// Adjusted import path for the llm package
	"devicequery/llm" // Assuming your module is devicequery_multillm
)

// Make sure database.go and device.go are in the same package "main"
// or adjust imports if they are in different packages.
// For simplicity, keeping them in package main.

const sqlToolName = "execute_sql_query" // Generic name, mapped by clients

func main() {
	// --- CLI Flags ---
	jsonFilePath := flag.String("jsonfile", "sample_devices.json", "Path to JSON file containing device data")
	dbFilePath := flag.String("dbfile", "devices.db", "Path to SQLite database file")

	llmProviderFlag := flag.String("llm", string(llm.ProviderGemini), fmt.Sprintf("LLM provider: %s, %s, %s", llm.ProviderGemini, llm.ProviderOpenAI, llm.ProviderOllama))
	modelFlag := flag.String("model", "", "LLM model name to use (e.g., gemini-1.5-flash-latest, gpt-4o, qwen2:7b)")
	apiKeyFlag := flag.String("apikey", "", "API key for the LLM provider (Gemini, OpenAI). Uses ENV VAR if not set.")
	baseURLFlag := flag.String("baseurl", "", "Base URL for OpenAI-compatible APIs (e.g., Ollama: http://localhost:11434/v1)")
	flag.Parse()

	// --- 1. Load and Parse JSON Data ---
	jsonData, err := ioutil.ReadFile(*jsonFilePath)
	if err != nil {
		log.Fatalf("Error reading JSON file %s: %v", *jsonFilePath, err)
	}
	var rawDeviceData map[string]json.RawMessage
	err = json.Unmarshal(jsonData, &rawDeviceData)
	if err != nil {
		log.Fatalf("Error unmarshalling JSON data into map: %v", err)
	}
	devices, err := ProcessRawDeviceData(rawDeviceData) // From device.go
	if err != nil {
		log.Printf("Warning: Error processing raw device data: %v", err)
	}
	log.Printf("Successfully processed %d devices from JSON.", len(devices))

	// --- 2. Initialize Database ---
	err = initDB(*dbFilePath) // From database.go
	if err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}
	defer closeDB()

	// --- 3. Insert Data into SQLite ---
	if len(devices) > 0 {
		err = insertDevices(devices) // From database.go
		if err != nil {
			log.Fatalf("Error inserting devices into database: %v", err)
		}
	} else {
		log.Println("No devices processed from JSON to insert.")
	}

	// --- 4. Get Table Schema for LLM Context ---
	tableSchema, err := getTableSchemaForLLM() // From database.go
	if err != nil {
		log.Fatalf("Error getting table schema for LLM: %v", err)
	}

	// --- 5. Initialize LLM Client ---
	llmConfig := llm.LLMConfig{
		Provider:    llm.LLMProviderType(*llmProviderFlag),
		Model:       *modelFlag,
		APIKey:      *apiKeyFlag,
		BaseURL:     *baseURLFlag,
		TableSchema: tableSchema,
	}

	// Set default models and API keys if not provided by flags
	if llmConfig.Model == "" {
		switch llmConfig.Provider {
		case llm.ProviderOpenRouter:
			// Default to a model known to support function/tool calling via OpenRouter.
			// The free Llama 3.3 8B model (meta-llama/llama-3.3-8b-instruct:free)
			// was causing "No endpoints found that support tool use" error.
			llmConfig.Model = "openai/gpt-3.5-turbo"
		case llm.ProviderGemini:
			llmConfig.Model = "gemini-1.5-flash-latest"
		case llm.ProviderOpenAI:
			llmConfig.Model = "gpt-4o" // or gpt-3.5-turbo if preferred for cost/speed
		case llm.ProviderOllama:
			// Ollama model must be specified as it refers to a locally available model
			if llmConfig.Model == "" {
				log.Fatal("Error: -model flag is required for 'ollama' LLM provider (e.g., -model llama3:8b).")
			}
		}
	}
	if llmConfig.APIKey == "" {
		switch llmConfig.Provider {
		case llm.ProviderGemini:
			llmConfig.APIKey = os.Getenv("GEMINI_API_KEY")
			log.Println("gemini API key set")
		case llm.ProviderOpenAI:
			llmConfig.APIKey = os.Getenv("OPENAI_API_KEY")
			log.Println("openai API key set")
		case llm.ProviderOllama:
			llmConfig.APIKey = os.Getenv("OLLAMA_API_KEY")
			log.Println("ollama API key set")
		case llm.ProviderOpenRouter:
			llmConfig.APIKey = os.Getenv("OPENROUTER_API_KEY")
			log.Println("openrouter API key set")
		}
	}
	
	if llmConfig.APIKey == "" && (llmConfig.Provider == llm.ProviderGemini || llmConfig.Provider == llm.ProviderOpenAI) {
		log.Fatalf("Error: API key is required for %s. Set via -apikey flag or environment variable (e.g., GEMINI_API_KEY, OPENAI_API_KEY).", llmConfig.Provider)
	}
	if llmConfig.Provider == llm.ProviderOllama && llmConfig.BaseURL == "" {
		llmConfig.BaseURL = "http://localhost:11434/v1" // Default Ollama URL
		log.Printf("Info: -baseurl not provided for Ollama, defaulting to %s", llmConfig.BaseURL)
	}
	log.Println("baseURL set to ", llmConfig.BaseURL)
	var client llm.LLMClient
	switch llmConfig.Provider {
	case llm.ProviderGemini:
		client = &llm.GeminiClient{}
	case llm.ProviderOpenRouter:
		client = &llm.OpenAIClient{}
	case llm.ProviderOpenAI:
		client = &llm.OpenAIClient{}
	case llm.ProviderOllama:
		client = &llm.OpenAIClient{} // Uses the OpenAI client structure
	default:
		log.Fatalf("Unsupported LLM provider: %s", llmConfig.Provider)
	}

	err = client.Init(llmConfig)
	if err != nil {
		log.Fatalf("Error initializing LLM client for provider %s: %v", llmConfig.Provider, err)
	}
	defer client.Close()

	// --- 6. Start User Interaction Loop ---
	log.Println("Setup complete. You can now ask questions about the devices.")
	log.Printf("Using LLM: %s, Model: %s", llmConfig.Provider, llmConfig.Model)
	log.Println("Type 'exit' or 'quit' to end.")

	scanner := bufio.NewScanner(os.Stdin)
	chatHistory := []llm.Message{} // Store conversation history

	for {
		fmt.Print("You: ")
		if !scanner.Scan() {
			if err := scanner.Err(); err != nil {
				log.Printf("Error reading from stdin: %v", err)
			}
			break
		}
		userInput := scanner.Text()
		trimmedInput := strings.TrimSpace(userInput)

		if strings.ToLower(trimmedInput) == "exit" || strings.ToLower(trimmedInput) == "quit" {
			break
		}
		if trimmedInput == "" {
			continue
		}

		chatHistory = append(chatHistory, llm.Message{Role: "user", Content: trimmedInput})

		ctx := context.Background()
		llmResponse, err := client.GenerateResponse(ctx, chatHistory)
		if err != nil {
			log.Printf("Error from LLM provider %s: %v", llmConfig.Provider, err)
			fmt.Println("Assistant: Sorry, I encountered an error. Please try again.")
			// Remove the last user message from history if LLM call failed before assistant could respond
			if len(chatHistory) > 0 {
				chatHistory = chatHistory[:len(chatHistory)-1]
			}
			continue
		}

		// Add assistant's raw response (could be content or tool_calls) to history
		chatHistory = append(chatHistory, llmResponse.Message)

		// Handle tool calls if any
		if len(llmResponse.Message.ToolCalls) > 0 {
			fmt.Println("Assistant: (Thinking with tools...)")
			toolResponses := []llm.Message{}
			for _, toolCall := range llmResponse.Message.ToolCalls {
				log.Printf("LLM requested tool call: ID=%s, Type=%s, FunctionName=%s, Args=%s\n",
					toolCall.ID, toolCall.Type, toolCall.Function.Name, toolCall.Function.Arguments)

				if toolCall.Function.Name == sqlToolName { // Generic tool name
					argsMap, err := llm.UnmarshalArguments(toolCall.Function.Arguments)
					if err != nil {
						log.Printf("Error unmarshalling tool arguments: %v", err)
						toolResponses = append(toolResponses, llm.Message{
							Role:       "tool",
							ToolCallID: toolCall.ID,
							Name:       toolCall.Function.Name,
							Content:    fmt.Sprintf("Error: Could not parse arguments for tool %s: %v", toolCall.Function.Name, err),
						})
						continue
					}

					sqlQuery, ok := argsMap["sql_query"].(string)
					if !ok {
						log.Printf("Error: 'sql_query' argument missing or not a string in tool call.")
						toolResponses = append(toolResponses, llm.Message{
							Role:       "tool",
							ToolCallID: toolCall.ID,
							Name:       toolCall.Function.Name,
							Content:    fmt.Sprintf("Error: 'sql_query' argument missing or invalid for tool %s.", toolCall.Function.Name),
						})
						continue
					}

					// Execute the SQL query (this function is from database.go)
					// executeSQLQuery returns (string_result, error_for_llm_if_any_not_actual_go_error)
					queryResultStr, _ := executeSQLQuery(sqlQuery) // We pass nil as the "error" to LLM is part of the string result

					toolResponses = append(toolResponses, llm.Message{
						Role:       "tool",
						ToolCallID: toolCall.ID,
						Name:       toolCall.Function.Name, // Name of the function that was called
						Content:    queryResultStr,
					})
				} else {
					log.Printf("Warning: LLM requested unknown tool: %s", toolCall.Function.Name)
					toolResponses = append(toolResponses, llm.Message{
						Role:       "tool",
						ToolCallID: toolCall.ID,
						Name:       toolCall.Function.Name,
						Content:    fmt.Sprintf("Error: Tool '%s' is not implemented.", toolCall.Function.Name),
					})
				}
			}

			// Add all tool responses to history
			chatHistory = append(chatHistory, toolResponses...)

			// Send tool responses back to the LLM
			finalResponse, err := client.GenerateResponse(ctx, chatHistory)
			if err != nil {
				log.Printf("Error from LLM after tool call: %v", err)
				fmt.Println("Assistant: Sorry, I encountered an error after processing the tool request.")
				continue
			}
			fmt.Printf("Assistant: %s\n", finalResponse.Message.Content)
			chatHistory = append(chatHistory, finalResponse.Message) // Add final assistant message
		} else {
			// No tool calls, just print the content
			fmt.Printf("Assistant: %s\n", llmResponse.Message.Content)
			// The response is already added to chatHistory above.
		}
		if len(chatHistory) > 20 { // Basic history truncation
			chatHistory = chatHistory[len(chatHistory)-10:] // Keep last 10 messages (5 turns approx)
			log.Println("Chat history truncated.")
		}
	}
	log.Println("Exiting program.")
}
