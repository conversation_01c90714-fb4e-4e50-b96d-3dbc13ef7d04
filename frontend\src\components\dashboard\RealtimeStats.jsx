import React, { useState, useEffect } from "react";
import { StatisticCard } from "@ant-design/pro-components";
import { useGetRealtimeDataQuery } from "../../app/services/anomalyApi";
import { Statistic, Typography, Flex, Radio, theme } from "antd";
import ReactApexChart from "react-apexcharts";
import { useTheme } from "antd-style";

const AnomalyRealtimeStatics = ({ clientName }) => {
  // realtime statistic
  const { appearance } = useTheme();
  const { token } = theme.useToken();
  const { data: realTimeData } = useGetRealtimeDataQuery(clientName, {
    pollingInterval: 60000,
  });
  const [last10Alerts, setLast10Alerts] = useState([]);
  const [last10Messages, setLast10Messages] = useState([]);
  const [totalAlerts, setTotalAlerts] = useState(0);
  const [totalMessages, setTotalMessages] = useState(0);
  useEffect(() => {
    if (realTimeData) {
      console.log("realTimeData", realTimeData);
      // get last 10 alerts from the last_hour_alerts
      var last10alert = realTimeData.last_hour_alerts.slice(-10);
      var last10msgs = realTimeData.last_hour_messages.slice(-10);

      setLast10Alerts(last10alert);
      setLast10Messages(last10msgs);
      setTotalAlerts(realTimeData.total_alerts);
      setTotalMessages(realTimeData.total_messages);
    }
  }, [realTimeData]);

  const chartOptions = {
    chart: {
      type: "line",
      height: 50,
      background: token.colorBgContainer,
      toolbar: {
        show: false,
      },
    },
    // xaxis 10:32 ~ now ( 10 mins ago)
    xaxis: {
      type: "text",
      categories: Array.from({ length: 10 }, (_, i) => {
        console.log(i);
        const now = new Date();
        now.setMinutes(now.getMinutes() - 9 + i);
        // reutrn {hour:minute  ex:19:03}
        const hour = String(now.getHours()).padStart(2, "0");
        const minute = String(now.getMinutes()).padStart(2, "0");
        return `${hour}:${minute}`;
      }),
    },
    yaxis: {
      title: {
        text: "Counts",
      },
    },
    theme: {
      mode: appearance,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    tooltip: {
      enabled: true,
    },
  };
  // get last 10 alerts

  const chartSeries = [
    {
      name: "Alerts",
      data: last10Alerts,
    },
    {
      name: "Messages",
      data: last10Messages,
    },
  ];

  return (
    <StatisticCard.Group direction="row">
      <div
        style={{
          padding: "16px",
          border: "1px solid #f0f0f0",
          borderRadius: "4px",
        }}
      >
        <div>
          <Flex justify="space-between">
            <Typography.Title level={5}>Realtime detection</Typography.Title>
          </Flex>
          <ReactApexChart
            options={chartOptions}
            series={chartSeries}
            type="line"
            height={150}
          />
        </div>
        <Flex justify="space-between">
          <Statistic
            title="Log service"
            value={realTimeData?.syslog || " "}
            layout="horizontal"
          />
          <Statistic
            title="Total messages"
            value={totalMessages || "N/A"}
            layout="horizontal"
          />
          <Statistic
            title="Total alerts"
            value={totalAlerts || "N/A"}
            layout="horizontal"
          />
        </Flex>
      </div>
    </StatisticCard.Group>
  );
};

export default AnomalyRealtimeStatics;
