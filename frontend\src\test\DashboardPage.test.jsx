import { render } from "@testing-library/react";
import { Provider } from "react-redux";
import { describe, expect, it } from "vitest";
import { store } from "../app/store";
import DashboardPage from "../pages/dashboard/DashboardPage";
import DashboardTable from "../components/dashboard/DashboardTable";
import Pie<PERSON><PERSON> from "../components/dashboard/PieChart";
import Syslog<PERSON><PERSON> from "../components/dashboard/SyslogChart";
import { getSyslogsData } from "../features/dashboard/dashboardSlice";
import { loginUser } from "../features/auth/userAuthSlice";
import { getInventoryData } from "../features/inventory/inventorySlice";

vi.mock("rc-resize-observer");
vi.mock("react-apexcharts");

vi.mock("antd", async () => {
  const actual = await vi.importActual("antd");
  return {
    ...actual,
    App: {
      useApp: () => ({
        modal: {
          error: vi.fn(),
        },
      }),
    },
  };
});

describe("DashboardPage", () => {
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });
  it("Should handle getSyslogsData correctly when successful", async () => {
    var edate = new Date();
    edate.setDate(edate.getDate() + 1);

    var sdate = new Date();
    sdate.setDate(sdate.getDate() - 7);

    const splitSData = sdate.toISOString().split("T")[0].replaceAll("-", "/");
    const splitEData = edate.toISOString().split("T")[0].replaceAll("-", "/");

    const finalStartDate = `${splitSData} 00:00:00`;
    const finalEndDate = `${splitEData} 00:00:00`;
    await store.dispatch(
      getSyslogsData({
        start: finalStartDate,
        end: finalEndDate,
      })
    );
    const updatedState = store.getState().debugCmd;
    expect(updatedState.cmdResponse).not.to.equal("");
  });

  it("Should handle getInventoryData correctly when successful", async () => {
    await store.dispatch(getInventoryData());
    const updatedState = store.getState().debugCmd;
    expect(updatedState.cmdResponse).not.to.equal("");
  });

  it("renders the DashboardPage component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <DashboardPage />
      </Provider>
    );
    expect(container).toBeTruthy();
  });

  it("renders the DashboardTable component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <DashboardTable />
      </Provider>
    );
    expect(container).toBeTruthy();
  });

  it("renders the PieChart component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <PieChart />
      </Provider>
    );
    expect(container).toBeTruthy();
  });

  it("renders the SyslogChart component without errors", () => {
    const { container } = render(
      <Provider store={store}>
        <SyslogChart />
      </Provider>
    );
    expect(container).toBeTruthy();
  });
});
