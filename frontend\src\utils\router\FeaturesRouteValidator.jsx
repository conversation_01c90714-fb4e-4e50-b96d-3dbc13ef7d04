import React from "react";
import { useSelector } from "react-redux";
import { licenseAlertSelector } from "../../features/socketControl/licenseAlertSlice";
import { Navigate } from "react-router-dom";

const FeaturesRouteValidator = ({ children, feature }) => {
  const { featureEnabled } = useSelector(licenseAlertSelector);
  return featureEnabled.includes(feature) ? (
    children
  ) : (
    <Navigate to="/dashboard/device" replace={true} />
  );
};

export default FeaturesRouteValidator;
