// cypress/e2e/logPage/logPage.cy.js

import dayjs from 'dayjs';

describe('LogPage Component', () => {
  beforeEach(() => {
    cy.visit('/login');
    cy.get('[data-testid="username"]').type('admin');
    cy.get('[data-testid="password"]').type('default');
    cy.get('[data-testid="submit"]').click();
    cy.url().should('not.include', '/login');
    cy.visit('/eventlogs');
    // Wait for the initial data load to finish
    cy.wait(1000); 
  });

  it('should load the log page and display log data', () => {
    cy.get('.ant-pro-table').should('exist');
    cy.contains('Log List').should('be.visible');
    cy.get('.ant-table-row').should('have.length.greaterThan', 0);
  });

  it('should export the log data as a PDF file', () => {
    cy.get('button').contains('Export').click();
    cy.contains('PDF').click();
    cy.readFile('cypress/downloads/Syslog_List_15042025_132702.pdf').should('exist');
  });

  it('should export the log data as a CSV file', () => {
    cy.get('button').contains('Export').click();
    cy.contains('CSV').click();
    cy.readFile('cypress/downloads/Syslog_List_15042025_132702.csv').should('exist');
  });

});