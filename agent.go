package mnms

/*
#include "agent/monocypher.c"
#include <stdlib.h>
*/
import "C"

import (
	"bytes"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/pcap"
	"github.com/qeof/q"
	"golang.org/x/crypto/curve25519"
)

const agentfilter = "ether proto 0xdada"

// 0xdada converted to decimal is 56026
const (
	agentTcpPort   = 56026
	ethernetLength = 1500
	tcpKmaxLength  = 4096 * 2
)

type AgentMessages struct {
	Kind    string `json:"kind"`
	Ip      string `json:"ip"`
	Url     string `json:"url"`
	Version string `json:"version"`
	Status  string `json:"status"`
	Mac     string `json:"mac"`
	Size    string `json:"size"`
}

type Encryption struct {
	nonce        []byte
	sharedSecret []byte
	msgAuthCode  []byte
}

type OpenvpnKeys struct {
	CaCert     string `json:"cacert"`
	ClientCert string `json:"clientcert"`
	ClientKey  string `json:"clientkey"`
	StaticKey  string `json:"statickey"`
	Timestamp  string `json:"timestamp"`
}

type PortInfo struct {
	PortName         string `json:"portName"`
	PortStatus       bool   `json:"portStatus"`
	PortMode         string `json:"portMode"`
	Speed            string `json:"speed"`
	InOctets         string `json:"inOctets"`
	InErrors         string `json:"inErrors"`
	InUcastPkts      string `json:"inUcastPkts"`
	InMulticastPkts  string `json:"inMulticastPkts"`
	InBroadcastPkts  string `json:"inBroadcastPkts"`
	OutOctets        string `json:"outOctets"`
	OutErrors        string `json:"outErrors"`
	OutUcastPkts     string `json:"outUcastPkts"`
	OutMulticastPkts string `json:"outMulticastPkts"`
	OutBroadcastPkts string `json:"outBroadcastPkts"`
	EnableStatus     bool   `json:"enableStatus"`
}

type PowerInfo struct {
	PowerId string `json:"powerId"`
	Status  bool   `json:"status"`
}

type PortAndPowerInfo struct {
	Timestamp   string      `json:"timestamp"`
	PortStatus  []PortInfo  `json:"portStatus"`
	PowerStatus []PowerInfo `json:"powerStatus"`
}
type TrapSetting struct {
	ServerIp   string `json:"serverIp"`
	ServerPort string `json:"serverPort"`
	Community  string `json:"community"`
}

type SyslogSetting struct {
	LogToServer string `json:"logToServer"`
	ServerIp    string `json:"serverIp"`
	ServerPort  string `json:"serverPort"`
	LogLevel    string `json:"logLevel"`
	LogToFlash  string `json:"logToFlash"`
}

type MotorInfo struct {
	Sn       string              `json:"sn"`
	Ethernet []MotorEthernetInfo `json:"ethernet"`
	Mdr      []MotorMdrInfo      `json:"mdr"`
	Profinet MotorProfinetInfo   `json:"profinet"`
}

type MotorEthernetInfo struct {
	Port string `json:"port"`
	Link string `json:"link"`
}

type MotorMdrInfo struct {
	Zone             string `json:"zone"`
	Sn               string `json:"sn"`
	DriveTemperature string `json:"driveTemperature"`
	DriveCurrent     string `json:"driveCurrent"`
	Voltage          string `json:"voltage"`
	SpeedCode        string `json:"speedCode"`
	RunningTime      string `json:"runningTime"`
	Temperature      string `json:"temperature"`
	Mode             string `json:"mode"`
	Holding          string `json:"holding"`
	Speed            string `json:"speed"`
	Direction        string `json:"direction"`
	Level            string `json:"level"`
	SensorType       string `json:"sensorType"`
	SensorStatus     string `json:"sensorStatus"`
	ZoneStatus       string `json:"zoneStatus"`
}

type MotorProfinetInfo struct {
	NodeName    string `json:"nodeName"`
	ModuleInId  string `json:"moduleInId"`
	ModuleOutId string `json:"moduleOutId"`
}

type GpsInfo struct {
	Supported string `json:"supported"`
	Enabled   string `json:"enabled"`
	Lasttime  string `json:"lasttime"`
	Latitude  string `json:"latitude"`
	Longitude string `json:"longitude"`
}

func init() {
	QC.OpenvpnData = make(map[string]OpenvpnKeys)
}

func byteHexToString(input []byte) string {
	var output string
	for i := 0; i < 16; i++ {
		output = fmt.Sprintf("%02X", input)
	}
	return output
}

func stringToByteHex(input string) []byte {
	data, err := hex.DecodeString(input)
	if err != nil {
		return nil
	}
	return data
}

func PublishPortAndPower(portandpowerInfo *map[string]PortAndPowerInfo) error {
	// send all topology info to root
	if QC.RootURL == "" {
		return fmt.Errorf("skip publishing devices, no root")
	}

	jsonBytes, err := json.Marshal(*portandpowerInfo)
	if err != nil {
		q.Q(err)
		return err
	}
	resp, err := PostWithToken(QC.RootURL+"/api/v1/agent/ports", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err, QC.RootURL)
	}
	if resp != nil {
		res := make(map[string]interface{})
		_ = json.NewDecoder(resp.Body).Decode(&res)
		defer resp.Body.Close()
	}
	return nil
}

func InsertPortAndPower(portandpowerInfo map[string]PortAndPowerInfo) error {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	QC.PortAndPowerMutex.Lock()
	for k, v := range portandpowerInfo {
		v.Timestamp = timestamp
		QC.PortAndPowerInfo[k] = v
	}
	QC.PortAndPowerMutex.Unlock()
	return nil
}

// Version example X.Y.Z or vX.Y.Z
func TransferVersionToNumber(version string) (int, error) {
	if version == "" {
		return 0, fmt.Errorf("error : version is nil")
	}
	no := 0
	var buf string
	buf = version
	if strings.HasPrefix(version, "v") {
		buf = strings.TrimPrefix(buf, "v")
	}
	nos := strings.Split(buf, ".")
	if len(nos) != 3 {
		return 0, fmt.Errorf("error : version = %s, version format is wrong, should be xx.yy.zz for example", version)
	}
	for i, n := range nos {
		j, err := strconv.Atoi(n)
		if err != nil || j >= 100 || j < 0 {
			return 0, fmt.Errorf("error : version = %s, version value is greater than 100", version)
		}
		no += j * int(math.Pow(float64(100), float64(2-i)))
	}
	return no, nil
}

func CheckBbnimAndAgentVersion(bbnimVersion, agentSupportVersion string) error {
	var msg string
	q.Q(bbnimVersion, agentSupportVersion)
	bbnimVersionInt, status1 := TransferVersionToNumber(bbnimVersion)
	if status1 != nil {
		q.Q(status1)
		return status1
	}
	agentSupportVersionInt, status2 := TransferVersionToNumber(agentSupportVersion)
	if status2 != nil {
		q.Q(status2)
		return status2
	}
	if bbnimVersionInt > agentSupportVersionInt {
		msg = fmt.Sprintf("The agentclient supports bbnim version %s, but the current bbnim version is %s. The agentclient version is outdated",
			agentSupportVersion, bbnimVersion)
		q.Q(msg)
	}
	if bbnimVersionInt < agentSupportVersionInt {
		msg = fmt.Sprintf("The agentclient supports bbnim version %s, but the current bbnim version is %s. The bbnim version is outdated",
			agentSupportVersion, bbnimVersion)
		q.Q(msg)
	}
	if bbnimVersionInt == agentSupportVersionInt {
		msg = fmt.Sprintf("The agentclient supports bbnim version %s, bbnim version is %s. The bbnim version match the agent version.",
			agentSupportVersion, bbnimVersion)
		q.Q(msg)
	}
	syslogerr := SendSyslog(LOG_INFO, "CheckAgentVersion", msg)
	if syslogerr != nil {
		return syslogerr
	}
	return nil
}

func NewEncryptionContext() (*Encryption, error) {
	// peer x25519 private key
	ephemeral := make([]byte, curve25519.ScalarSize)
	ephemeral = []byte{
		0x6D, 0x6E, 0x6D, 0x73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	}
	// device x25519 private key : agentlcient
	devicePrivateKey := make([]byte, curve25519.ScalarSize)
	devicePrivateKey = []byte{
		0x61, 0x67, 0x65, 0x6E, 0x74, 0x63, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	}
	// device public key : agentlcient
	devicePublicKey, err := curve25519.X25519(devicePrivateKey, curve25519.Basepoint)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	// share secret key
	sharedSecret, err := curve25519.X25519(ephemeral, devicePublicKey)
	if err != nil {
		q.Q(err)
		return nil, err
	}

	nonce := []byte{
		0x61, 0x67, 0x65, 0x6E, 0x74, 0x63, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0,
		0x61, 0x67, 0x65, 0x6E, 0x74, 0x63, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0,
	}
	msgAuthCode := []byte{0, 0, 0, 0, 0, 0}

	encryption := Encryption{
		nonce:        nonce,
		sharedSecret: sharedSecret,
		msgAuthCode:  msgAuthCode,
	}
	return &encryption, nil
}

func getInterfaceMacAddr(agentip string) (net.HardwareAddr, string, error) {
	ifaces, err := net.Interfaces()
	if err != nil {
		return nil, "", err
	}
	var found bool = false
	var clientip string
	for _, v := range ifaces {
		if v.Flags&net.FlagUp == net.FlagUp && v.Flags&net.FlagLoopback != net.FlagLoopback {
			addrs, err := v.Addrs()
			if err != nil { // get addresses
				return nil, "", err
			}
			for _, ip := range addrs {
				if ipv4Addr := ip.(*net.IPNet).IP.To4(); ipv4Addr != nil {
					// q.Q(agentip, ip.String())
					net_agentIP := net.ParseIP(agentip)
					_, ipnet, _ := net.ParseCIDR(ip.String())
					if ipnet.Contains(net_agentIP) {
						found = true
						clientip = ip.String()
						break
					}
				}
			}
			if found {
				// q.Q(v.HardwareAddr.String())
				return v.HardwareAddr, clientip, nil
			}
		}
	}
	return nil, "", fmt.Errorf("not found mac address")
}

func StartAgentServer() {
	allDevs, err := pcap.FindAllDevs()
	if err != nil {
		q.Q("error: exiting agent main", err)
		return
	}
	var ifaceNames []string
	for _, dev := range allDevs {
		if BogusIf(dev.Name, dev.Description) {
			continue
		}
		ifaceNames = append(ifaceNames, dev.Name)
	}
	q.Q("start agent ethernet server")
	for _, ifaceName := range ifaceNames {
		go func(ifaceName string) {
			err := listeningAgent(ifaceName)
			if err != nil {
				q.Q(err)
			}
		}(ifaceName)
	}
	q.Q("start agent tcp server")
	go func() {
		addr := net.JoinHostPort("", strconv.Itoa(agentTcpPort))
		l, err := net.Listen("tcp", addr)
		if err != nil {
			q.Q("agent tcp server listen faild: ", addr, err.Error())
			return
		}
		q.Q("listen: ", addr)
		defer l.Close()
		for {
			conn, err := l.Accept()
			if err != nil {
				continue
			}
			go func() {
				defer conn.Close()
				startTCPServer(conn)
			}()
		}
	}()
}

func listeningAgent(ifaceName string) error {
	// Linux UDP sockets do not get broadcast, capture raw frames
	handle, err := pcap.OpenLive(ifaceName, 65536, true, pcap.BlockForever)
	if err != nil {
		q.Q("error: cannot open interface", ifaceName)
		return err
	}
	// q.Q("opened interface", ifaceName)
	defer handle.Close()

	var options gopacket.SerializeOptions
	options.FixLengths = true
	options.ComputeChecksums = true

	var filter string = agentfilter
	err = handle.SetBPFFilter(filter)
	if err != nil {
		q.Q(err)
		return err
	}

	// init encryption
	encryption, err := NewEncryptionContext()
	if err != nil {
		q.Q("error: encryption key fail")
		return err
	}

	lenMessages := 0
	packetSource := gopacket.NewPacketSource(handle, handle.LinkType())
	for packet := range packetSource.Packets() {
		ethernetLayer := packet.Layer(layers.LayerTypeEthernet)
		if ethernetLayer != nil {
			header := ethernetLayer.LayerContents()
			messages := ethernetLayer.LayerPayload()
			lenMessages = len(messages)
			for v, k := range messages {
				if k == '\t' || k == '\n' || k == 0x0 || k == '\r' || k == ' ' {
					lenMessages = v
					break
				}
			}
			if header[12] == 0xda && header[13] == 0xda {
				if header[0] != 0xff || header[1] != 0xff || header[2] != 0xff ||
					header[3] != 0xff || header[4] != 0xff || header[5] != 0xff {
					continue
				}

				// decode base64
				encryption_messages, _ := base64.StdEncoding.DecodeString(string(messages[:lenMessages]))
				// decryption
				// encryption_messages_str := string(messages[:lenMessages])
				// encryption_messages := stringToByteHex(encryption_messages_str)
				plain_text, err := DecryptCipherText(*encryption, encryption_messages)
				if err != nil {
					q.Q(err)
					continue
				}
				// fill AgentMessages
				var msg AgentMessages
				err = json.Unmarshal(plain_text[:], &msg)
				if err != nil {
					q.Q(err)
					continue
				}
				q.Q(msg)
				if !strings.HasPrefix(msg.Kind, "announce") {
					continue
				}
				// find which network interface is in the same domain as the device ip
				srcMac, nmsSeviceCidr, err := getInterfaceMacAddr(msg.Ip)
				if err != nil {
					q.Q(err)
					continue
				}
				nmsSeviceIp := strings.Split(nmsSeviceCidr, "/")
				if len(nmsSeviceIp) < 1 {
					q.Q("Get NmsService IP address error")
					continue
				}

				nmsServiceURL := ""
				if QC.NmsServiceURL == "" {
					q.Q("No NmsService url, NmsService use default url")
					nmsServiceURL = fmt.Sprintf("http://%s:%d", nmsSeviceIp[0], QC.Port)
					q.Q(nmsServiceURL)
				} else {
					nmsServiceURL = QC.NmsServiceURL
				}
				// agent client url max length is 2048
				if len(nmsServiceURL) > 2048 {
					q.Q("NmsService url length is greater than 2048")
					continue
				}
				// check agent version
				//status := CheckBbnimAndAgentVersion(QC.Version, msg.Version)
				//if status != nil {
				//	q.Q(status)
				//}

				// encrypto
				agentneed := AgentMessages{
					Ip:      nmsSeviceIp[0],
					Url:     nmsServiceURL,
					Kind:    "announce-resp",
					Version: QC.Version,
				}
				jsonBytes, err := json.Marshal(agentneed)
				if err != nil {
					q.Q(err)
					continue
				}
				bytePlainText := []byte(jsonBytes)
				bytePlainTextLen := len(bytePlainText)
				encryptionText, _, err := EncryptCipherText(encryption.sharedSecret, encryption.nonce, bytePlainText, bytePlainTextLen)
				if err != nil {
					q.Q(err)
					continue
				}
				// encode base64
				b64_text := base64.StdEncoding.EncodeToString([]byte(encryptionText))
				// send ethernet frame to device
				q.Q(srcMac, header[6:12])
				//q.Q(encryptionText, b64_text)
				err = agentSend(handle, options, srcMac, header[6:12], []byte(b64_text))
				if err != nil {
					q.Q(err)
					continue
				}
			}

		}

	}

	return nil
}

func agentSend(handle *pcap.Handle, options gopacket.SerializeOptions,
	srcMac []byte, dstMac []byte, msg []byte,
) error {
	ethernetLayer := &layers.Ethernet{
		SrcMAC:       srcMac,
		DstMAC:       dstMac,
		EthernetType: 0xdada,
	}
	buffer_ethernet := gopacket.NewSerializeBuffer()
	err := gopacket.SerializeLayers(buffer_ethernet, options,
		ethernetLayer,
		gopacket.Payload(msg))
	if err != nil {
		q.Q(err)
		return err
	}
	if len(buffer_ethernet.Bytes()) >= 1460 {
		q.Q("error: agclient bad size")
		return errors.New("error: agclient bad size")
	}
	err = handle.WritePacketData(buffer_ethernet.Bytes())
	if err != nil {
		q.Q(err)
		return err
	}
	return nil
}

func EncryptCipherText(key, nonce, plain_text []byte, text_size int) ([]byte, []byte, error) {
	msgAuthCode := make([]byte, 16)
	cipher_text := make([]byte, text_size, ethernetLength-14)
	msgAuthCode[0] = 0
	msgAuthCode[1] = 0
	msgAuthCode[2] = 0
	msgAuthCode[3] = 0
	msgAuthCode[4] = 0
	msgAuthCode[5] = 0

	if len(plain_text) < 1 {
		return nil, nil, fmt.Errorf("error: plain text length is zero")
	}

	var c_msgAuthCode *C.uchar = (*C.uchar)(unsafe.Pointer(&msgAuthCode[0]))
	var c_cipher_text *C.uchar = (*C.uchar)(unsafe.Pointer(&cipher_text[0]))
	var c_key *C.uchar = (*C.uchar)(unsafe.Pointer(&key[0]))
	var c_nonce *C.uchar = (*C.uchar)(unsafe.Pointer(&nonce[0]))
	var c_plain_text *C.uchar = (*C.uchar)(unsafe.Pointer(&plain_text[0]))
	var c_text_size C.size_t = C.size_t(text_size)

	C.crypto_lock(c_msgAuthCode, c_cipher_text, c_key, c_nonce, c_plain_text, c_text_size)
	/*
		defer C.free(unsafe.Pointer(c_msgAuthCode))
		defer C.free(unsafe.Pointer(c_key))
		defer C.free(unsafe.Pointer(c_plain_text))
		defer C.free(unsafe.Pointer(c_nonce))
		defer C.free(unsafe.Pointer(c_cipher_text))

	*/
	cipher_text = C.GoBytes(unsafe.Pointer(c_cipher_text), C.int(c_text_size))
	msgAuthCode = C.GoBytes(unsafe.Pointer(c_msgAuthCode), 16)
	if len(cipher_text) < 1 {
		return nil, nil, fmt.Errorf("error: cipher text length is zero")
	}
	return cipher_text, msgAuthCode, nil
}

func DecryptCipherText(encrypt Encryption, cipher_text []byte) ([]byte, error) {
	key := encrypt.sharedSecret
	nonce := encrypt.nonce
	msgAuthCode := encrypt.msgAuthCode
	text_size := len(cipher_text)
	plain_text := make([]byte, text_size, ethernetLength-14)

	if len(cipher_text) < 1 {
		return nil, fmt.Errorf("error: cipher text length is zero")
	}

	var c_msgAuthCode *C.uchar = (*C.uchar)(unsafe.Pointer(&msgAuthCode[0]))
	var c_cipher_text *C.uchar = (*C.uchar)(unsafe.Pointer(&cipher_text[0]))
	var c_key *C.uchar = (*C.uchar)(unsafe.Pointer(&key[0]))
	var c_nonce *C.uchar = (*C.uchar)(unsafe.Pointer(&nonce[0]))
	var c_plain_text *C.uchar = (*C.uchar)(unsafe.Pointer(&plain_text[0]))
	var c_text_size C.size_t = C.size_t(text_size)

	C.crypto_unlock(c_plain_text, c_key, c_nonce, c_msgAuthCode, c_cipher_text, c_text_size)
	/*
		defer C.free(unsafe.Pointer(c_msgAuthCode))
		defer C.free(unsafe.Pointer(c_key))
		defer C.free(unsafe.Pointer(c_plain_text))
		defer C.free(unsafe.Pointer(c_nonce))
		defer C.free(unsafe.Pointer(c_cipher_text))

	*/
	plain_text = C.GoBytes(unsafe.Pointer(c_plain_text), C.int(c_text_size))

	if len(plain_text) < 1 {
		return nil, fmt.Errorf("error: plain text length is zero")
	}
	return plain_text, nil
}

// Wait for the agent packet and get the packet size after receiving the packet.
func getTransferPacketSize(con net.Conn, timout int) (int64, error) {
	err := con.SetReadDeadline(time.Now().Add(time.Duration(timout) * time.Second))
	if err != nil {
		return 0, err
	}
	//q.Q(endian)
	packethead := make([]byte, 4)
	for {
		n, err := con.Read(packethead)
		if err != nil {
			return 0, err
		}
		if n > 0 {
			var filesize int32 = 0
			buf := bytes.NewReader(packethead)
			binary.Read(buf, binary.BigEndian, &filesize)
			//q.Q(filesize)
			return int64(filesize), nil
		}
	}
}

// receive agent packet
func receivePackage(con net.Conn, file_size *int64) (error, []byte) {
	err := con.SetReadDeadline(time.Now().Add(300 * time.Second))
	if err != nil {
		return err, nil
	}
	// tmp := file_size
	var dataBuffer []byte
	buf := make([]byte, tcpKmaxLength)
	for {
		n, err := con.Read(buf)
		if err != nil {
			return err, nil
		}
		dataBuffer = append(dataBuffer, buf[:n]...)
		//q.Q(n, *file_size, len(dataBuffer))

		if int64(len(dataBuffer)) == (*file_size + 1) {
			return nil, dataBuffer[:*file_size]
		}
		if int64(len(dataBuffer)) > (*file_size + 1) {
			return fmt.Errorf("error: data length does not match"), nil
		}
	}
}

func readEncryptedPacket(conn net.Conn, encryption *Encryption) ([]byte, error) {
	// Wait for the agent to send the packet. If the agent disconnects,
	//  the `getTransferPacketSize` will leave the loop with timeout.
	lenMessages, err := getTransferPacketSize(conn, 10)
	if err != nil {
		err = fmt.Errorf("error: header error")
		return nil, err
	}
	if lenMessages > tcpKmaxLength {
		err = fmt.Errorf("error: packet size is too large")
		return nil, err
	}
	err, messages := receivePackage(conn, &lenMessages)
	if err != nil {
		err = fmt.Errorf("error: get tcp agent messages error")
		return nil, err
	}
	// decode base64
	encryption_messages, _ := base64.StdEncoding.DecodeString(string(messages[:lenMessages]))
	plain_text, err := DecryptCipherText(*encryption, encryption_messages)
	if err != nil {
		err = fmt.Errorf("error: Crypto unlock message fail")
		return nil, err
	}
	return plain_text[:], nil
}

func startTCPServer(conn net.Conn) {
	// init agent messages struct
	agentMsgs := AgentMessages{
		Kind: "tcp-announce-resp",
	}
	// init encryption
	encryption, err := NewEncryptionContext()
	if err != nil {
		q.Q("error: encryption key fail")
		agentMsgs.Status = "error: encryption key fail"
		err = replyToAgent(conn, agentMsgs)
		if err != nil {
			q.Q(err)
		}
		return
	}

	q.Q("new connect")
	for {
		body, err := readEncryptedPacket(conn, encryption)
		if err != nil {
			agentMsgs.Status = err.Error()
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
			return
		}
		// fill agent messages struct
		var msg AgentMessages
		err = json.Unmarshal(body, &msg)
		if err != nil {
			q.Q(err)
			agentMsgs.Status = "error: message format error"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
			return
		}
		// msg.Kind = tcp-announce : check the TCP connection status of the agent.
		// msg.Kind = tcp-post-commands : agent post commands to bbnim commands list.
		// msg.Kind = tcp-get-commands : agent get commands from bbnim commands list.
		// msg.Kind = tcp-send-devinfos : agent send device information to bbnim device list.
		// msg.Kind = tcp-send-topoinfo : agent send topology information to bbnim topology list.
		// msg.Kind = tcp-get-file : agent get file from bbnim.
		// msg.kind = tcp-post-ssh-tunnels : agent post ssh tunnels to bbnim to handshake.
		if strings.HasPrefix(msg.Kind, "tcp-announce") {
			// feedback messages
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-post-commands") {
			agentMsgs.Kind = "tcp-post-commands-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			//q.Q(string(plain_text[:]))
			// fill commands
			cmdList := []CmdInfo{}
			//TODO: tcp client side should send []CmdInfo
			err = json.Unmarshal(body, &cmdList)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: message format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			for _, v := range cmdList {
				//FIXME code duplication -- HandleCommands() in http.go
				err = ValidateCommand(v.Command)
				if err != nil {
					q.Q(err)
					agentMsgs.Status = "error: invalid command"
					err = replyToAgent(conn, agentMsgs)
					if err != nil {
						q.Q("error: can't reply to agent", err)
					}
					return
				}
			}
			UpdateCmds(cmdList)
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			jsonBytes, err := json.Marshal(cmdList)
			if err != nil {
				q.Q(err)
				return
			}
			// forward request to root
			resp, err := PostWithToken(QC.RootURL+"/api/v1/commands", QC.AdminToken, bytes.NewBuffer(jsonBytes))
			if err != nil {
				q.Q(err)
				return
			}
			resp.Body.Close()

		} else if strings.HasPrefix(msg.Kind, "tcp-get-commands") {
			agentMsgs.Kind = "tcp-get-commands-resp"
			if msg.Mac == "" {
				agentMsgs.Status = "error : mac parameter wrong"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			size_int := 0
			if v, err := strconv.Atoi(msg.Size); err != nil {
				agentMsgs.Status = "error : size parameter wrong"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			} else {
				size_int = v
				if v <= 0 {
					size_int = 0
				}
			}
			// feedback messages
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			// fill AgentMessages
			var msg AgentMessages
			err = json.Unmarshal(body, &msg)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: message format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			if !strings.HasPrefix(msg.Kind, "tcp-get-commands") {
				return
			}
			//_, stat := getTransferPacketSize(conn, 10)
			//q.Q(stat)
			//time.Sleep(300 * time.Microsecond)
			// send data
			cmddata := make(map[string]CmdInfo)

			//q.Q("agent wants to get agent commands")
			index := 0
			QC.CmdMutex.Lock()
			for k, v := range QC.CmdData {
				if v.Status == "" &&
					strings.Contains(v.Command, msg.Mac) &&
					strings.HasPrefix(v.Command, "agent") {
					if size_int == 0 {
						cmddata[k] = v
					} else if index < size_int {
						index++
						cmddata[k] = v
					} else {
						break
					}
				}
			}
			QC.CmdMutex.Unlock()
			//q.Q(cmddata)
			err = replyToAgent(conn, cmddata)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-send-devinfo") {
			agentMsgs.Kind = "tcp-send-devinfo-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			//q.Q(string(plain_text[:]))
			// fill device information
			devinfo := make(map[string]DevInfo)
			err = json.Unmarshal(body, &devinfo)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: device information format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			for _, v := range devinfo {
				if strings.Contains(v.Scanproto, "agent") {
					InsertAgentDevices(v)
				}
			}
			//q.Q(devinfo)
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-send-port-powerinfo") {
			agentMsgs.Kind = "tcp-send-port-powerinfo-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			//q.Q(string(plain_text[:]))
			// fill port power information
			portAndPowerInfo := make(map[string]PortAndPowerInfo)
			err = json.Unmarshal(body, &portAndPowerInfo)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: port power information format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					return
				}
			}
			if QC.IsRoot {
				InsertPortAndPower(portAndPowerInfo)
			} else {
				PublishPortAndPower(&portAndPowerInfo)
			}
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-send-topoinfo") {
			agentMsgs.Kind = "tcp-send-topoinfo-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			//q.Q(string(plain_text[:]))
			// fill topology information
			var topoinfo Topology
			err = json.Unmarshal(body, &topoinfo)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: topology information format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					return
				}
			}
			if QC.IsRoot {
				InsertTopology(topoinfo)
			} else {
				PublishTopology(topoinfo)
			}
			//q.Q(topoinfo)
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-get-file") {
			agentMsgs.Kind = "tcp-get-file-resp"
			if msg.Mac == "" {
				agentMsgs.Status = "error : mac parameter wrong"
				q.Q(agentMsgs.Status)
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}

			// The file URL location obtained from the agent
			fileurl := msg.Url

			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			// Notify the agent to start transferring files
			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}

			// get agent messages information
			var msg AgentMessages
			err = json.Unmarshal(body, &msg)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: message format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			// check agent packet kind
			if !strings.HasPrefix(msg.Kind, "tcp-get-file") {
				return
			}

			// start send firmware file
			fileBuffer, err := DownloadURLFile(fileurl)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: download file error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			file_size := len(fileBuffer)
			q.Q("file size is ", file_size)

			// Calculate file size
			header := make([]byte, 4)
			//header[0] = (byte)((file_size % 16) + ((file_size/16)%16)*16)
			//header[1] = (byte)(((file_size / 256) % 16) + ((file_size/(256*16))%16)*16)
			//header[2] = (byte)(((file_size / (256 * 256)) % 16) + ((file_size/(256*256*16))%16)*16)
			//header[3] = (byte)(((file_size / (256 * 256 * 256)) % 16) + ((file_size/(256*256*256*16))%16)*16)

			binary.BigEndian.PutUint32(header, uint32(file_size))

			q.Q(header)
			// send file-sized information packets
			_, err = conn.Write(header)
			if err != nil {
				q.Q(err)
				return
			}

			time.Sleep(1 * time.Second)
			// send file to agent
			fakefile := bytes.NewReader(fileBuffer)
			buf := make([]byte, 1024)
			for {
				n, err := fakefile.Read(buf)
				if err == io.EOF {
					break
				}
				_, err = conn.Write(buf[:n])
				if err != nil {
					break
				}
			}
			if err != nil {
				q.Q("error: send file error", err)
				agentMsgs.Status = "error: send file error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			time.Sleep(time.Second * 2)
			q.Q("wait download status")
			// wait download status
			body, err = readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			// get agent messages information
			err = json.Unmarshal(body, &msg)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: message format error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			q.Q(msg)
			if strings.HasPrefix(msg.Status, "error") {
				q.Q(msg.Status)
			}
			// reply file transfer completed
			agentMsgs.Kind = "tcp-get-file-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		} else if strings.HasPrefix(msg.Kind, "tcp-post-ssh-tunnels") {
			agentMsgs.Kind = "tcp-post-ssh-tunnels-resp"
			agentMsgs.Status = "ok"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}

			body, err := readEncryptedPacket(conn, encryption)
			if err != nil {
				agentMsgs.Status = err.Error()
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}

			// forwards request body to root
			ioReader := bytes.NewReader(body)
			resp, err := PostWithToken(QC.RootURL+"/api/v1/ssh/tunnels", QC.AdminToken, ioReader)
			if err != nil {
				q.Q(err)
				agentMsgs.Status = "error: post ssh tunnels error"
				err = replyToAgent(conn, agentMsgs)
				if err != nil {
					q.Q(err)
				}
				return
			}
			defer resp.Body.Close()
			// feedback messages
			err = replyToAgent(conn, resp.Body)
			if err != nil {
				q.Q(err)
			}
		} else {
			// feedback messages
			agentMsgs.Status = "error : unknown kind"
			err = replyToAgent(conn, agentMsgs)
			if err != nil {
				q.Q(err)
			}
		}
	}
}

// reply agent messages
func replyToAgent(conn net.Conn, messages any) error {
	// init encryption
	encryption, err := NewEncryptionContext()
	if err != nil {
		return fmt.Errorf("error: encryption key fail")
	}

	jsonBytes, err := json.Marshal(messages)
	if err != nil {
		return err
	}
	bytePlainText := []byte(jsonBytes)
	bytePlainTextLen := len(bytePlainText)
	encryptionText, _, err := EncryptCipherText(encryption.sharedSecret, encryption.nonce, bytePlainText, bytePlainTextLen)
	if err != nil {
		return err
	}
	// encode base64
	b64_text := base64.StdEncoding.EncodeToString([]byte(encryptionText))
	// send messages
	header := make([]byte, 4)
	mesgLength := len(b64_text)
	//q.Q(mesgLength)
	//header[0] = (byte)((mesgLength % 16) + ((mesgLength/16)%16)*16)
	//header[1] = (byte)(((mesgLength / 256) % 16) + ((mesgLength/(256*16))%16)*16)
	//header[2] = (byte)(((mesgLength / (256 * 256)) % 16) + ((mesgLength/(256*256*16))%16)*16)
	//header[3] = (byte)(((mesgLength / (256 * 256 * 256)) % 16) + ((mesgLength/(256*256*256*16))%16)*16)
	//q.Q(mesgLength)
	binary.BigEndian.PutUint32(header, uint32(mesgLength))
	//q.Q(header)
	packet := append(header, []byte(b64_text)...)
	//q.Q(packet)
	_, err = conn.Write(packet)
	if err != nil {
		return err
	}
	return nil
}
