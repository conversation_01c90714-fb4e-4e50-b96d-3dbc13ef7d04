## Cluster Test
To test the cluster, we need to run the root first. For later test, we should get the root's ip address. Assume the root's ip address is
************ and the root's port is 27182. Then we can run the following command to start the root.
```bash
$ ./bbrootsvc -n root
```
### Testing run network agent in the different machine

Find a machine which can connect to the root machine. And run network agent.
```bash
$ ./bbnmssvc -n c1 -r http://************:27182
```
To verify the network agent is running, you can copy the following script to the machine and run it. Beware that you need to install jq first. And root url should be changed to the proper one. (If you want to run this script in the root machine, you can change the root url to http://localhost:27182)
```bash
#!/bin/bash

# Step 1: Get the token
token_response=$(curl -s -X POST http://************:27182/api/v1/login -d '{"user":"admin","password":"default"}')
token=$(echo "$token_response" | jq -r '.token')

# Step 2: Make a GET request with the token in the headers
info_response=$(curl -s -X GET http://************:27182/api/v1/info -H "Authorization: Bearer $token")

# Print the response
echo "$info_response" | jq '.'

# Step 2: Make a GET request with the token in the headers
register_response=$(curl -s -X GET http://************:27182/api/v1/register -H "Authorization: Bearer $token")

# Print the response
echo "$register_response" | jq '.'
```
Check response should like the following JSON, the second JSON object should has c1 object and in this object the status should be `active`.
```json
{
  "num_devices": 5,
  "num_clients": 4,
  "name": "root",
  "port": 27182,
  "root_url": "",
  "is_root": true,
  "remote_syslog_server_addr": "",
  "syslog_local_path": "syslog_mnms.log",
  "syslog_file_size": 100,
  "syslog_compress": true,
  "mqtt_broker_addr": ":11883",
  "syslog_server_addr": ":5514",
  "trap_server_addr": ":5162",
  "cmd_interval": 5,
  "register_interval": 60,
  "gwd_interval": 60,
  "domain": "",
  "snmp_options": {
    "port": 161,
    "community": "private",
    "version": 1,
    "timeout": 2000000000
  }
}
{
  "c1": {
    "name": "c1",
    "num_devices": 4,
    "num_cmds": 0,
    "num_logs_received": 0,
    "num_logs_sent": 0,
    "start": 1687764967,
    "now": 1687829807,
    "num_goroutines": 38,
    "ip_addresses": [],
    "status": "inactive"
  },
}
```
You can add more network agent to the cluster, just repeat the above steps.