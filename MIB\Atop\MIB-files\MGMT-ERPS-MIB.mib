-- *****************************************************************
-- ERPS-MIB:  
-- ****************************************************************

MGMT-ERPS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-<PERSON><PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVlanListQuarter FROM MGMT-TC
    ;

mgmtErpsMib MODULE-IDENTITY
    LAST-UPDATED "201911010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MIB for Ethernet Ring Protection Switching, ERPS."
    REVISION    "201911010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 72 }


MGMTErpsCommand ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies the ERPS command."
    SYNTAX      INTEGER { noRequest(0), forceSwitchToPort0(1),
                          forceSwitchToPort1(2),
                          manualSwitchToPort0(3),
                          manualSwitchToPort1(4), clear(5) }

MGMTErpsNodeState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies a node state."
    SYNTAX      INTEGER { init(0), idle(1), protection(2), ms(3),
                          fs(4), pending(5) }

MGMTErpsOperState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies an operational state."
    SYNTAX      INTEGER { disabled(0), active(1), internalError(2) }

MGMTErpsOperWarning ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Operational warnings of an ERPS instance."
    SYNTAX      INTEGER { none(0), port0NotMemberOfControlVlan(1),
                          port1NotMemberOfControlVlan(2),
                          port0UntagsControlVlan(3),
                          port1UntagsControlVlan(4),
                          port0MepNotFound(5), port1MepNotFound(6),
                          port0MepAdminDisabled(7),
                          port1MepAdminDisabled(8),
                          port0MepNotDownMep(9),
                          port1MepNotDownMep(10),
                          port0AndMepIfindexDiffer(11),
                          port1AndMepIfindexDiffer(12),
                          portMepShadowsPort0Mip(13),
                          portMepShadowsPort1Mip(14),
                          mepShadowsPort0Mip(15),
                          mepShadowsPort1Mip(16),
                          connectedRingDoesntExist(17),
                          connectedRingIsAnInterconnectedSubRing(18),
                          connectedRingIsNotOperative(19),
                          connectedRingInterfaceConflict(20),
                          connectedRingDoesntProtectControlVlan(21) }

MGMTErpsRequestState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies a request state."
    SYNTAX      INTEGER { noRequest(0), manualSwitch(7),
                          signalFailed(11), forceSwitch(13),
                          event(14) }

MGMTErpsRingPort ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies a particular logical ring port."
    SYNTAX      INTEGER { ringPort0(0), ringPort1(1) }

MGMTErpsRingType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies the ERPS ring type."
    SYNTAX      INTEGER { major(0), sub(1), interconnectedSub(2) }

MGMTErpsRplMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies the Ring Protection Link mode. Use 'none' if port is neither
         RPL owner nor neighbor."
    SYNTAX      INTEGER { none(0), owner(1), neighbor(2) }

MGMTErpsSfTrigger ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Signal fail can either come from the physical link on a given port or
         from a Down-MEP."
    SYNTAX      INTEGER { link(0), mep(1) }

MGMTErpsVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "Specifies the ERPS protocol version."
    SYNTAX      INTEGER { v1(0), v2(1) }

mgmtErpsMibObjects OBJECT IDENTIFIER
    ::= { mgmtErpsMib 1 }

mgmtErpsCapabilities OBJECT IDENTIFIER
    ::= { mgmtErpsMibObjects 1 }

mgmtErpsCapabilitiesInstanceMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of created ERPS instances."
    ::= { mgmtErpsCapabilities 1 }

mgmtErpsCapabilitiesWtrSecsMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum WTR timer value in secs."
    ::= { mgmtErpsCapabilities 2 }

mgmtErpsCapabilitiesGuardTimeMsecsMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum Guard timer value in msec."
    ::= { mgmtErpsCapabilities 3 }

mgmtErpsCapabilitiesHoldOffMsecsMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum Hold Off timer value in msec."
    ::= { mgmtErpsCapabilities 4 }

mgmtErpsConfig OBJECT IDENTIFIER
    ::= { mgmtErpsMibObjects 2 }

mgmtErpsConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTErpsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the ERPS group configuration table."
    ::= { mgmtErpsConfig 1 }

mgmtErpsConfigEntry OBJECT-TYPE
    SYNTAX      MGMTErpsConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an ERPS group."
    INDEX       { mgmtErpsConfigGroupIndex }
    ::= { mgmtErpsConfigTable 1 }

MGMTErpsConfigEntry ::= SEQUENCE {
    mgmtErpsConfigGroupIndex              Integer32,
    mgmtErpsConfigAdminActive             TruthValue,
    mgmtErpsConfigVersion                 MGMTErpsVersion,
    mgmtErpsConfigRingType                MGMTErpsRingType,
    mgmtErpsConfigVirtualChannel          TruthValue,
    mgmtErpsConfigConnectedRingId         Unsigned32,
    mgmtErpsConfigConnectedRingPropagate  TruthValue,
    mgmtErpsConfigRingId                  MGMTUnsigned8,
    mgmtErpsConfigNodeId                  MacAddress,
    mgmtErpsConfigLevel                   MGMTUnsigned8,
    mgmtErpsConfigControlVlan             MGMTUnsigned16,
    mgmtErpsConfigPcp                     MGMTUnsigned8,
    mgmtErpsConfigPort0SfTrigger          MGMTErpsSfTrigger,
    mgmtErpsConfigPort0If                 MGMTInterfaceIndex,
    mgmtErpsConfigPort0MEPDomain          MGMTDisplayString,
    mgmtErpsConfigPort0MEPService         MGMTDisplayString,
    mgmtErpsConfigPort0MEPId              Unsigned32,
    mgmtErpsConfigPort0Smac               MacAddress,
    mgmtErpsConfigPort1SfTrigger          MGMTErpsSfTrigger,
    mgmtErpsConfigPort1If                 MGMTInterfaceIndex,
    mgmtErpsConfigPort1MEPDomain          MGMTDisplayString,
    mgmtErpsConfigPort1MEPService         MGMTDisplayString,
    mgmtErpsConfigPort1MEPId              Unsigned32,
    mgmtErpsConfigPort1Smac               MacAddress,
    mgmtErpsConfigRevertive               TruthValue,
    mgmtErpsConfigWaitToRestoreTime       Unsigned32,
    mgmtErpsConfigGuardTime               Unsigned32,
    mgmtErpsConfigHoldOffTime             Unsigned32,
    mgmtErpsConfigRplMode                 MGMTErpsRplMode,
    mgmtErpsConfigRplPort                 MGMTErpsRingPort,
    mgmtErpsConfigProtectedVlans0Kto1K    MGMTVlanListQuarter,
    mgmtErpsConfigProtectedVlans1Kto2K    MGMTVlanListQuarter,
    mgmtErpsConfigProtectedVlans2Kto3K    MGMTVlanListQuarter,
    mgmtErpsConfigProtectedVlans3Kto4K    MGMTVlanListQuarter,
    mgmtErpsConfigAction                  MGMTRowEditorState
}

mgmtErpsConfigGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "ERPS group index number. Valid range is (1..max groups). The maximum
         group number is platform-specific and can be retrieved from the ERPS
         capabilities."
    ::= { mgmtErpsConfigEntry 1 }

mgmtErpsConfigAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of this ERPS instance. Set to true to make it
         function normally and false to make it cease functioning."
    ::= { mgmtErpsConfigEntry 2 }

mgmtErpsConfigVersion OBJECT-TYPE
    SYNTAX      MGMTErpsVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "ERPS protocol version."
    ::= { mgmtErpsConfigEntry 3 }

mgmtErpsConfigRingType OBJECT-TYPE
    SYNTAX      MGMTErpsRingType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of ring."
    ::= { mgmtErpsConfigEntry 4 }

mgmtErpsConfigVirtualChannel OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Whether to use a virtual channel. Controls whether to use a virtual
         channel with a sub-ring."
    ::= { mgmtErpsConfigEntry 5 }

mgmtErpsConfigConnectedRingId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "For a sub-ring on an interconnection node, this must reference the
         instance ID of the ring to which this sub-ring is connected."
    ::= { mgmtErpsConfigEntry 6 }

mgmtErpsConfigConnectedRingPropagate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether the ring referenced by connected_ring_inst shall
         propagate R-APS flush PDUs whenever this sub-ring's topology changes."
    ::= { mgmtErpsConfigEntry 7 }

mgmtErpsConfigRingId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Ring ID is used - along with the control VLAN - to identify R-APS
         PDUs as belonging to a particular ring."
    ::= { mgmtErpsConfigEntry 8 }

mgmtErpsConfigNodeId OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Node ID is used inside the R-APS specific PDU to uniquely identify
         this node (switch) on the ring."
    ::= { mgmtErpsConfigEntry 9 }

mgmtErpsConfigLevel OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MD/MEG Level of R-APS PDUs we transmit."
    ::= { mgmtErpsConfigEntry 10 }

mgmtErpsConfigControlVlan OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN on which R-APS PDUs are transmitted and received on the ring
         ports."
    ::= { mgmtErpsConfigEntry 11 }

mgmtErpsConfigPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value used in the VLAN tag of the R-APS PDUs."
    ::= { mgmtErpsConfigEntry 12 }

mgmtErpsConfigPort0SfTrigger OBJECT-TYPE
    SYNTAX      MGMTErpsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects whether Signal Fail (SF) comes from the link state of a given
         interface, or from a Down-MEP."
    ::= { mgmtErpsConfigEntry 13 }

mgmtErpsConfigPort0If OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index of ring protection port 0."
    ::= { mgmtErpsConfigEntry 14 }

mgmtErpsConfigPort0MEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name name of Port0 MEP."
    ::= { mgmtErpsConfigEntry 15 }

mgmtErpsConfigPort0MEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of Port0 MEP."
    ::= { mgmtErpsConfigEntry 16 }

mgmtErpsConfigPort0MEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of Port0 MEP."
    ::= { mgmtErpsConfigEntry 17 }

mgmtErpsConfigPort0Smac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address (must be unicast) used in R-APS PDUs sent on this
         ring-port."
    ::= { mgmtErpsConfigEntry 18 }

mgmtErpsConfigPort1SfTrigger OBJECT-TYPE
    SYNTAX      MGMTErpsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects whether Signal Fail (SF) comes from the link state of a given
         interface, or from a Down-MEP."
    ::= { mgmtErpsConfigEntry 19 }

mgmtErpsConfigPort1If OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index of ring protection port 1."
    ::= { mgmtErpsConfigEntry 20 }

mgmtErpsConfigPort1MEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name name of Port1 MEP."
    ::= { mgmtErpsConfigEntry 21 }

mgmtErpsConfigPort1MEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of Port1 MEP."
    ::= { mgmtErpsConfigEntry 22 }

mgmtErpsConfigPort1MEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of Port1 MEP."
    ::= { mgmtErpsConfigEntry 23 }

mgmtErpsConfigPort1Smac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address (must be unicast) used in R-APS PDUs sent on this
         ring-port."
    ::= { mgmtErpsConfigEntry 24 }

mgmtErpsConfigRevertive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Revertive (true) or Non-revertive (false) mode."
    ::= { mgmtErpsConfigEntry 25 }

mgmtErpsConfigWaitToRestoreTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Wait-to-Restore time in seconds. Valid range is 1-720."
    ::= { mgmtErpsConfigEntry 26 }

mgmtErpsConfigGuardTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Guard time in ms. Valid range is 10-2000 ms."
    ::= { mgmtErpsConfigEntry 27 }

mgmtErpsConfigHoldOffTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hold off time in ms. Value is rounded down to 100ms precision. Valid
         range is 0-10000 ms"
    ::= { mgmtErpsConfigEntry 28 }

mgmtErpsConfigRplMode OBJECT-TYPE
    SYNTAX      MGMTErpsRplMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Ring Protection Link mode."
    ::= { mgmtErpsConfigEntry 29 }

mgmtErpsConfigRplPort OBJECT-TYPE
    SYNTAX      MGMTErpsRingPort
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether it is port0 or port1 that is the RPL."
    ::= { mgmtErpsConfigEntry 30 }

mgmtErpsConfigProtectedVlans0Kto1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigEntry 31 }

mgmtErpsConfigProtectedVlans1Kto2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigEntry 32 }

mgmtErpsConfigProtectedVlans2Kto3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigEntry 33 }

mgmtErpsConfigProtectedVlans3Kto4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Fourth quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigEntry 34 }

mgmtErpsConfigAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtErpsConfigEntry 100 }

mgmtErpsConfigRowEditor OBJECT IDENTIFIER
    ::= { mgmtErpsConfig 2 }

mgmtErpsConfigRowEditorGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "ERPS group index number. Valid range is (1..max groups). The maximum
         group number is platform-specific and can be retrieved from the ERPS
         capabilities."
    ::= { mgmtErpsConfigRowEditor 1 }

mgmtErpsConfigRowEditorAdminActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The administrative state of this ERPS instance. Set to true to make it
         function normally and false to make it cease functioning."
    ::= { mgmtErpsConfigRowEditor 2 }

mgmtErpsConfigRowEditorVersion OBJECT-TYPE
    SYNTAX      MGMTErpsVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "ERPS protocol version."
    ::= { mgmtErpsConfigRowEditor 3 }

mgmtErpsConfigRowEditorRingType OBJECT-TYPE
    SYNTAX      MGMTErpsRingType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of ring."
    ::= { mgmtErpsConfigRowEditor 4 }

mgmtErpsConfigRowEditorVirtualChannel OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Whether to use a virtual channel. Controls whether to use a virtual
         channel with a sub-ring."
    ::= { mgmtErpsConfigRowEditor 5 }

mgmtErpsConfigRowEditorConnectedRingId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "For a sub-ring on an interconnection node, this must reference the
         instance ID of the ring to which this sub-ring is connected."
    ::= { mgmtErpsConfigRowEditor 6 }

mgmtErpsConfigRowEditorConnectedRingPropagate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether the ring referenced by connected_ring_inst shall
         propagate R-APS flush PDUs whenever this sub-ring's topology changes."
    ::= { mgmtErpsConfigRowEditor 7 }

mgmtErpsConfigRowEditorRingId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Ring ID is used - along with the control VLAN - to identify R-APS
         PDUs as belonging to a particular ring."
    ::= { mgmtErpsConfigRowEditor 8 }

mgmtErpsConfigRowEditorNodeId OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Node ID is used inside the R-APS specific PDU to uniquely identify
         this node (switch) on the ring."
    ::= { mgmtErpsConfigRowEditor 9 }

mgmtErpsConfigRowEditorLevel OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MD/MEG Level of R-APS PDUs we transmit."
    ::= { mgmtErpsConfigRowEditor 10 }

mgmtErpsConfigRowEditorControlVlan OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN on which R-APS PDUs are transmitted and received on the ring
         ports."
    ::= { mgmtErpsConfigRowEditor 11 }

mgmtErpsConfigRowEditorPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value used in the VLAN tag of the R-APS PDUs."
    ::= { mgmtErpsConfigRowEditor 12 }

mgmtErpsConfigRowEditorPort0SfTrigger OBJECT-TYPE
    SYNTAX      MGMTErpsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects whether Signal Fail (SF) comes from the link state of a given
         interface, or from a Down-MEP."
    ::= { mgmtErpsConfigRowEditor 13 }

mgmtErpsConfigRowEditorPort0If OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index of ring protection port 0."
    ::= { mgmtErpsConfigRowEditor 14 }

mgmtErpsConfigRowEditorPort0MEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name name of Port0 MEP."
    ::= { mgmtErpsConfigRowEditor 15 }

mgmtErpsConfigRowEditorPort0MEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of Port0 MEP."
    ::= { mgmtErpsConfigRowEditor 16 }

mgmtErpsConfigRowEditorPort0MEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of Port0 MEP."
    ::= { mgmtErpsConfigRowEditor 17 }

mgmtErpsConfigRowEditorPort0Smac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address (must be unicast) used in R-APS PDUs sent on this
         ring-port."
    ::= { mgmtErpsConfigRowEditor 18 }

mgmtErpsConfigRowEditorPort1SfTrigger OBJECT-TYPE
    SYNTAX      MGMTErpsSfTrigger
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects whether Signal Fail (SF) comes from the link state of a given
         interface, or from a Down-MEP."
    ::= { mgmtErpsConfigRowEditor 19 }

mgmtErpsConfigRowEditorPort1If OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index of ring protection port 1."
    ::= { mgmtErpsConfigRowEditor 20 }

mgmtErpsConfigRowEditorPort1MEPDomain OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Domain name name of Port1 MEP."
    ::= { mgmtErpsConfigRowEditor 21 }

mgmtErpsConfigRowEditorPort1MEPService OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..14))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Service name of Port1 MEP."
    ::= { mgmtErpsConfigRowEditor 22 }

mgmtErpsConfigRowEditorPort1MEPId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MEPID of Port1 MEP."
    ::= { mgmtErpsConfigRowEditor 23 }

mgmtErpsConfigRowEditorPort1Smac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Source MAC address (must be unicast) used in R-APS PDUs sent on this
         ring-port."
    ::= { mgmtErpsConfigRowEditor 24 }

mgmtErpsConfigRowEditorRevertive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Revertive (true) or Non-revertive (false) mode."
    ::= { mgmtErpsConfigRowEditor 25 }

mgmtErpsConfigRowEditorWaitToRestoreTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Wait-to-Restore time in seconds. Valid range is 1-720."
    ::= { mgmtErpsConfigRowEditor 26 }

mgmtErpsConfigRowEditorGuardTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Guard time in ms. Valid range is 10-2000 ms."
    ::= { mgmtErpsConfigRowEditor 27 }

mgmtErpsConfigRowEditorHoldOffTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Hold off time in ms. Value is rounded down to 100ms precision. Valid
         range is 0-10000 ms"
    ::= { mgmtErpsConfigRowEditor 28 }

mgmtErpsConfigRowEditorRplMode OBJECT-TYPE
    SYNTAX      MGMTErpsRplMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Ring Protection Link mode."
    ::= { mgmtErpsConfigRowEditor 29 }

mgmtErpsConfigRowEditorRplPort OBJECT-TYPE
    SYNTAX      MGMTErpsRingPort
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether it is port0 or port1 that is the RPL."
    ::= { mgmtErpsConfigRowEditor 30 }

mgmtErpsConfigRowEditorProtectedVlans0Kto1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigRowEditor 31 }

mgmtErpsConfigRowEditorProtectedVlans1Kto2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigRowEditor 32 }

mgmtErpsConfigRowEditorProtectedVlans2Kto3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigRowEditor 33 }

mgmtErpsConfigRowEditorProtectedVlans3Kto4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Fourth quarter of bit-array indicating whether a VLAN is protected by
         this ring instance ('1') or not ('0')."
    ::= { mgmtErpsConfigRowEditor 34 }

mgmtErpsConfigRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtErpsConfigRowEditor 100 }

mgmtErpsStatus OBJECT IDENTIFIER
    ::= { mgmtErpsMibObjects 3 }

mgmtErpsStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTErpsStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains status per EPRS group."
    ::= { mgmtErpsStatus 1 }

mgmtErpsStatusEntry OBJECT-TYPE
    SYNTAX      MGMTErpsStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Status."
    INDEX       { mgmtErpsStatusGroupIndex }
    ::= { mgmtErpsStatusTable 1 }

MGMTErpsStatusEntry ::= SEQUENCE {
    mgmtErpsStatusGroupIndex                 Integer32,
    mgmtErpsStatusOperState                  MGMTErpsOperState,
    mgmtErpsStatusOperWarning                MGMTErpsOperWarning,
    mgmtErpsStatusNodeState                  MGMTErpsNodeState,
    mgmtErpsStatusTxRapsActive               TruthValue,
    mgmtErpsStatusTxInfoUpdateTimeSecs       MGMTUnsigned64,
    mgmtErpsStatusTxInfoRequest              MGMTErpsRequestState,
    mgmtErpsStatusTxInfoVersion              MGMTUnsigned8,
    mgmtErpsStatusTxInfoRb                   TruthValue,
    mgmtErpsStatusTxInfoDnf                  TruthValue,
    mgmtErpsStatusTxInfoBpr                  MGMTErpsRingPort,
    mgmtErpsStatusTxInfoNodeId               MacAddress,
    mgmtErpsStatusTxInfoSmac                 MacAddress,
    mgmtErpsStatusCFOPTo                     TruthValue,
    mgmtErpsStatusPort0StatusBlocked         TruthValue,
    mgmtErpsStatusPort0StatusSf              TruthValue,
    mgmtErpsStatusPort0StatusFopPm           TruthValue,
    mgmtErpsStatusPort0StatusUpdateTimeSecs  MGMTUnsigned64,
    mgmtErpsStatusPort0StatusRequest         MGMTErpsRequestState,
    mgmtErpsStatusPort0StatusVersion         MGMTUnsigned8,
    mgmtErpsStatusPort0StatusRb              TruthValue,
    mgmtErpsStatusPort0StatusDnf             TruthValue,
    mgmtErpsStatusPort0StatusBpr             MGMTErpsRingPort,
    mgmtErpsStatusPort0StatusNodeId          MacAddress,
    mgmtErpsStatusPort0StatusSmac            MacAddress,
    mgmtErpsStatusPort0RxErrorCnt            Counter64,
    mgmtErpsStatusPort0RxOwnCnt              Counter64,
    mgmtErpsStatusPort0RxGuardCnt            Counter64,
    mgmtErpsStatusPort0RxFOPPmCnt            Counter64,
    mgmtErpsStatusPort0RxNrCnt               Counter64,
    mgmtErpsStatusPort0RxNrRbCnt             Counter64,
    mgmtErpsStatusPort0RxSfCnt               Counter64,
    mgmtErpsStatusPort0RxFxCnt               Counter64,
    mgmtErpsStatusPort0RxMsCnt               Counter64,
    mgmtErpsStatusPort0RxEventCnt            Counter64,
    mgmtErpsStatusPort0TxNrCnt               Counter64,
    mgmtErpsStatusPort0TxNrRbCnt             Counter64,
    mgmtErpsStatusPort0TxSfCnt               Counter64,
    mgmtErpsStatusPort0TxFsCnt               Counter64,
    mgmtErpsStatusPort0TxMsCnt               Counter64,
    mgmtErpsStatusPort0TxEventCnt            Counter64,
    mgmtErpsStatusPort0SfCn                  Counter64,
    mgmtErpsStatusPort0FlushCnt              Counter64,
    mgmtErpsStatusPort1StatusBlocked         TruthValue,
    mgmtErpsStatusPort1StatusSf              TruthValue,
    mgmtErpsStatusPort1StatusFopPm           TruthValue,
    mgmtErpsStatusPort1StatusUpdateTimeSecs  MGMTUnsigned64,
    mgmtErpsStatusPort1StatusRequest         MGMTErpsRequestState,
    mgmtErpsStatusPort1StatusVersion         MGMTUnsigned8,
    mgmtErpsStatusPort1StatusRb              TruthValue,
    mgmtErpsStatusPort1StatusDnf             TruthValue,
    mgmtErpsStatusPort1StatusBpr             MGMTErpsRingPort,
    mgmtErpsStatusPort1StatusNodeId          MacAddress,
    mgmtErpsStatusPort1StatusSmac            MacAddress,
    mgmtErpsStatusPort1RxErrorCnt            Counter64,
    mgmtErpsStatusPort1RxOwnCnt              Counter64,
    mgmtErpsStatusPort1RxGuardCnt            Counter64,
    mgmtErpsStatusPort1RxFOPPmCnt            Counter64,
    mgmtErpsStatusPort1RxNrCnt               Counter64,
    mgmtErpsStatusPort1RxNrRbCnt             Counter64,
    mgmtErpsStatusPort1RxSfCnt               Counter64,
    mgmtErpsStatusPort1RxFxCnt               Counter64,
    mgmtErpsStatusPort1RxMsCnt               Counter64,
    mgmtErpsStatusPort1RxEventCnt            Counter64,
    mgmtErpsStatusPort1TxNrCnt               Counter64,
    mgmtErpsStatusPort1TxNrRbCnt             Counter64,
    mgmtErpsStatusPort1TxSfCnt               Counter64,
    mgmtErpsStatusPort1TxFsCnt               Counter64,
    mgmtErpsStatusPort1TxMsCnt               Counter64,
    mgmtErpsStatusPort1TxEventCnt            Counter64,
    mgmtErpsStatusPort1SfCn                  Counter64,
    mgmtErpsStatusPort1FlushCnt              Counter64
}

mgmtErpsStatusGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "ERPS group index number. Valid range is (1..max groups). The maximum
         group number is platform-specific and can be retrieved from the ERPS
         capabilities."
    ::= { mgmtErpsStatusEntry 1 }

mgmtErpsStatusOperState OBJECT-TYPE
    SYNTAX      MGMTErpsOperState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The operational state of ERPS instance."
    ::= { mgmtErpsStatusEntry 2 }

mgmtErpsStatusOperWarning OBJECT-TYPE
    SYNTAX      MGMTErpsOperWarning
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Operational warnings of ERPS instance."
    ::= { mgmtErpsStatusEntry 3 }

mgmtErpsStatusNodeState OBJECT-TYPE
    SYNTAX      MGMTErpsNodeState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies protection/node state of ERPS."
    ::= { mgmtErpsStatusEntry 4 }

mgmtErpsStatusTxRapsActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Specifies whether we are currently supposed to be transmitting R-APS
         PDUs on our ring ports."
    ::= { mgmtErpsStatusEntry 5 }

mgmtErpsStatusTxInfoUpdateTimeSecs OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time in seconds since boot that this structure was last updated."
    ::= { mgmtErpsStatusEntry 6 }

mgmtErpsStatusTxInfoRequest OBJECT-TYPE
    SYNTAX      MGMTErpsRequestState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Request/state according to G.8032, table 10-3."
    ::= { mgmtErpsStatusEntry 7 }

mgmtErpsStatusTxInfoVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Version of received/used R-APS Protocol. 0 means v1, 1 means v2, etc."
    ::= { mgmtErpsStatusEntry 8 }

mgmtErpsStatusTxInfoRb OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RB (RPL blocked) bit of R-APS info. See Figure 10-3 of G.8032."
    ::= { mgmtErpsStatusEntry 9 }

mgmtErpsStatusTxInfoDnf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "DNF (Do Not Flush) bit of R-APS info. See Figure 10-3 of G.8032."
    ::= { mgmtErpsStatusEntry 10 }

mgmtErpsStatusTxInfoBpr OBJECT-TYPE
    SYNTAX      MGMTErpsRingPort
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "BPR (Blocked Port Reference) of R-APS info. See Figure 10-3 of G.8032."
    ::= { mgmtErpsStatusEntry 11 }

mgmtErpsStatusTxInfoNodeId OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Node ID of this request."
    ::= { mgmtErpsStatusEntry 12 }

mgmtErpsStatusTxInfoSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Source MAC address used in the request/state."
    ::= { mgmtErpsStatusEntry 13 }

mgmtErpsStatusCFOPTo OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Failure of Protocol - R-APS Rx Time Out."
    ::= { mgmtErpsStatusEntry 14 }

mgmtErpsStatusPort0StatusBlocked OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Specifies whether ring port is blocked or not."
    ::= { mgmtErpsStatusEntry 15 }

mgmtErpsStatusPort0StatusSf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Specifies the Signal Fail state of ring port after hold-off timer
         has expired."
    ::= { mgmtErpsStatusEntry 16 }

mgmtErpsStatusPort0StatusFopPm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:This boolean indicates whether there are two RPL owners on the
         ring."
    ::= { mgmtErpsStatusEntry 17 }

mgmtErpsStatusPort0StatusUpdateTimeSecs OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Time in seconds since boot that this structure was last updated."
    ::= { mgmtErpsStatusEntry 18 }

mgmtErpsStatusPort0StatusRequest OBJECT-TYPE
    SYNTAX      MGMTErpsRequestState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Request/state according to G.8032, table 10-3."
    ::= { mgmtErpsStatusEntry 19 }

mgmtErpsStatusPort0StatusVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Version of received/used R-APS Protocol. 0 means v1, 1 means v2,
         etc. "
    ::= { mgmtErpsStatusEntry 20 }

mgmtErpsStatusPort0StatusRb OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:RB (RPL blocked) bit of R-APS info."
    ::= { mgmtErpsStatusEntry 21 }

mgmtErpsStatusPort0StatusDnf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:DNF (Do Not Flush) bit of R-APS info."
    ::= { mgmtErpsStatusEntry 22 }

mgmtErpsStatusPort0StatusBpr OBJECT-TYPE
    SYNTAX      MGMTErpsRingPort
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:BPR (Blocked Port Reference) of R-APS info."
    ::= { mgmtErpsStatusEntry 23 }

mgmtErpsStatusPort0StatusNodeId OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Node ID of this request."
    ::= { mgmtErpsStatusEntry 24 }

mgmtErpsStatusPort0StatusSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:The Source MAC address used in the request/state."
    ::= { mgmtErpsStatusEntry 25 }

mgmtErpsStatusPort0RxErrorCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received erroneous R-APS PDUs."
    ::= { mgmtErpsStatusEntry 26 }

mgmtErpsStatusPort0RxOwnCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received R-APS PDUs with our own node ID."
    ::= { mgmtErpsStatusEntry 27 }

mgmtErpsStatusPort0RxGuardCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received R-APS PDUs during guard timer."
    ::= { mgmtErpsStatusEntry 28 }

mgmtErpsStatusPort0RxFOPPmCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received R-APS PDUs causing FOP-PM."
    ::= { mgmtErpsStatusEntry 29 }

mgmtErpsStatusPort0RxNrCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received NR R-APS PDUs."
    ::= { mgmtErpsStatusEntry 30 }

mgmtErpsStatusPort0RxNrRbCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received NR, RB R-APS PDUs."
    ::= { mgmtErpsStatusEntry 31 }

mgmtErpsStatusPort0RxSfCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received SF R-APS PDUs."
    ::= { mgmtErpsStatusEntry 32 }

mgmtErpsStatusPort0RxFxCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received FS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 33 }

mgmtErpsStatusPort0RxMsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received MS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 34 }

mgmtErpsStatusPort0RxEventCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of received Event R-APS PDUs."
    ::= { mgmtErpsStatusEntry 35 }

mgmtErpsStatusPort0TxNrCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted NR R-APS PDUs."
    ::= { mgmtErpsStatusEntry 36 }

mgmtErpsStatusPort0TxNrRbCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted NR, RB R-APS PDUs."
    ::= { mgmtErpsStatusEntry 37 }

mgmtErpsStatusPort0TxSfCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted SF R-APS PDUs."
    ::= { mgmtErpsStatusEntry 38 }

mgmtErpsStatusPort0TxFsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted FS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 39 }

mgmtErpsStatusPort0TxMsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted MS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 40 }

mgmtErpsStatusPort0TxEventCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of transmitted Event R-APS PDUs."
    ::= { mgmtErpsStatusEntry 41 }

mgmtErpsStatusPort0SfCn OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of local signal fails."
    ::= { mgmtErpsStatusEntry 42 }

mgmtErpsStatusPort0FlushCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port0:Number of FDB flushes (same for both rings)."
    ::= { mgmtErpsStatusEntry 43 }

mgmtErpsStatusPort1StatusBlocked OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Specifies whether ring port is blocked or not."
    ::= { mgmtErpsStatusEntry 44 }

mgmtErpsStatusPort1StatusSf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Specifies the Signal Fail state of ring port after hold-off timer
         has expired."
    ::= { mgmtErpsStatusEntry 45 }

mgmtErpsStatusPort1StatusFopPm OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:This boolean indicates whether there are two RPL owners on the
         ring."
    ::= { mgmtErpsStatusEntry 46 }

mgmtErpsStatusPort1StatusUpdateTimeSecs OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Time in seconds since boot that this structure was last updated."
    ::= { mgmtErpsStatusEntry 47 }

mgmtErpsStatusPort1StatusRequest OBJECT-TYPE
    SYNTAX      MGMTErpsRequestState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Request/state according to G.8032, table 10-3."
    ::= { mgmtErpsStatusEntry 48 }

mgmtErpsStatusPort1StatusVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Version of received/used R-APS Protocol. 0 means v1, 1 means v2,
         etc. "
    ::= { mgmtErpsStatusEntry 49 }

mgmtErpsStatusPort1StatusRb OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:RB (RPL blocked) bit of R-APS info."
    ::= { mgmtErpsStatusEntry 50 }

mgmtErpsStatusPort1StatusDnf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:DNF (Do Not Flush) bit of R-APS info."
    ::= { mgmtErpsStatusEntry 51 }

mgmtErpsStatusPort1StatusBpr OBJECT-TYPE
    SYNTAX      MGMTErpsRingPort
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:BPR (Blocked Port Reference) of R-APS info."
    ::= { mgmtErpsStatusEntry 52 }

mgmtErpsStatusPort1StatusNodeId OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Node ID of this request."
    ::= { mgmtErpsStatusEntry 53 }

mgmtErpsStatusPort1StatusSmac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:The Source MAC address used in the request/state."
    ::= { mgmtErpsStatusEntry 54 }

mgmtErpsStatusPort1RxErrorCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received erroneous R-APS PDUs."
    ::= { mgmtErpsStatusEntry 55 }

mgmtErpsStatusPort1RxOwnCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received R-APS PDUs with our own node ID."
    ::= { mgmtErpsStatusEntry 56 }

mgmtErpsStatusPort1RxGuardCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received R-APS PDUs during guard timer."
    ::= { mgmtErpsStatusEntry 57 }

mgmtErpsStatusPort1RxFOPPmCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received R-APS PDUs causing FOP-PM."
    ::= { mgmtErpsStatusEntry 58 }

mgmtErpsStatusPort1RxNrCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received NR R-APS PDUs."
    ::= { mgmtErpsStatusEntry 59 }

mgmtErpsStatusPort1RxNrRbCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received NR, RB R-APS PDUs."
    ::= { mgmtErpsStatusEntry 60 }

mgmtErpsStatusPort1RxSfCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received SF R-APS PDUs."
    ::= { mgmtErpsStatusEntry 61 }

mgmtErpsStatusPort1RxFxCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received FS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 62 }

mgmtErpsStatusPort1RxMsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received MS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 63 }

mgmtErpsStatusPort1RxEventCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of received Event R-APS PDUs."
    ::= { mgmtErpsStatusEntry 64 }

mgmtErpsStatusPort1TxNrCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted NR R-APS PDUs."
    ::= { mgmtErpsStatusEntry 65 }

mgmtErpsStatusPort1TxNrRbCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted NR, RB R-APS PDUs."
    ::= { mgmtErpsStatusEntry 66 }

mgmtErpsStatusPort1TxSfCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted SF R-APS PDUs."
    ::= { mgmtErpsStatusEntry 67 }

mgmtErpsStatusPort1TxFsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted FS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 68 }

mgmtErpsStatusPort1TxMsCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted MS R-APS PDUs."
    ::= { mgmtErpsStatusEntry 69 }

mgmtErpsStatusPort1TxEventCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of transmitted Event R-APS PDUs."
    ::= { mgmtErpsStatusEntry 70 }

mgmtErpsStatusPort1SfCn OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of local signal fails."
    ::= { mgmtErpsStatusEntry 71 }

mgmtErpsStatusPort1FlushCnt OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Port1:Number of FDB flushes (same for both rings)."
    ::= { mgmtErpsStatusEntry 72 }

mgmtErpsControl OBJECT IDENTIFIER
    ::= { mgmtErpsMibObjects 4 }

mgmtErpsControlCommandTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTErpsControlCommandEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the ERPS group control table."
    ::= { mgmtErpsControl 1 }

mgmtErpsControlCommandEntry OBJECT-TYPE
    SYNTAX      MGMTErpsControlCommandEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents dynamic control elements an ERPS
         group."
    INDEX       { mgmtErpsControlCommandGroupIndex }
    ::= { mgmtErpsControlCommandTable 1 }

MGMTErpsControlCommandEntry ::= SEQUENCE {
    mgmtErpsControlCommandGroupIndex  Integer32,
    mgmtErpsControlCommandCommand     MGMTErpsCommand
}

mgmtErpsControlCommandGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "ERPS group index number. Valid range is (1..max groups). The maximum
         group number is platform-specific and can be retrieved from the ERPS
         capabilities."
    ::= { mgmtErpsControlCommandEntry 1 }

mgmtErpsControlCommandCommand OBJECT-TYPE
    SYNTAX      MGMTErpsCommand
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Control command to execute. Always returns none when read."
    ::= { mgmtErpsControlCommandEntry 2 }

mgmtErpsControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTErpsControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of created ERPS clear commands."
    ::= { mgmtErpsControl 2 }

mgmtErpsControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTErpsControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a created ERPS clear command."
    INDEX       { mgmtErpsControlStatisticsClearGroupIndex }
    ::= { mgmtErpsControlStatisticsClearTable 1 }

MGMTErpsControlStatisticsClearEntry ::= SEQUENCE {
    mgmtErpsControlStatisticsClearGroupIndex  Integer32,
    mgmtErpsControlStatisticsClearClear       TruthValue
}

mgmtErpsControlStatisticsClearGroupIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "ERPS group index number. Valid range is (1..max groups). The maximum
         group number is platform-specific and can be retrieved from the ERPS
         capabilities."
    ::= { mgmtErpsControlStatisticsClearEntry 1 }

mgmtErpsControlStatisticsClearClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the counters of an ERPS instance."
    ::= { mgmtErpsControlStatisticsClearEntry 4 }

mgmtErpsMibConformance OBJECT IDENTIFIER
    ::= { mgmtErpsMib 2 }

mgmtErpsMibCompliances OBJECT IDENTIFIER
    ::= { mgmtErpsMibConformance 1 }

mgmtErpsMibGroups OBJECT IDENTIFIER
    ::= { mgmtErpsMibConformance 2 }

mgmtErpsCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsCapabilitiesInstanceMax,
                  mgmtErpsCapabilitiesWtrSecsMax,
                  mgmtErpsCapabilitiesGuardTimeMsecsMax,
                  mgmtErpsCapabilitiesHoldOffMsecsMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 1 }

mgmtErpsConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsConfigGroupIndex, mgmtErpsConfigAdminActive,
                  mgmtErpsConfigVersion, mgmtErpsConfigRingType,
                  mgmtErpsConfigVirtualChannel,
                  mgmtErpsConfigConnectedRingId,
                  mgmtErpsConfigConnectedRingPropagate,
                  mgmtErpsConfigRingId, mgmtErpsConfigNodeId,
                  mgmtErpsConfigLevel, mgmtErpsConfigControlVlan,
                  mgmtErpsConfigPcp, mgmtErpsConfigPort0SfTrigger,
                  mgmtErpsConfigPort0If, mgmtErpsConfigPort0MEPDomain,
                  mgmtErpsConfigPort0MEPService,
                  mgmtErpsConfigPort0MEPId, mgmtErpsConfigPort0Smac,
                  mgmtErpsConfigPort1SfTrigger, mgmtErpsConfigPort1If,
                  mgmtErpsConfigPort1MEPDomain,
                  mgmtErpsConfigPort1MEPService,
                  mgmtErpsConfigPort1MEPId, mgmtErpsConfigPort1Smac,
                  mgmtErpsConfigRevertive,
                  mgmtErpsConfigWaitToRestoreTime,
                  mgmtErpsConfigGuardTime, mgmtErpsConfigHoldOffTime,
                  mgmtErpsConfigRplMode, mgmtErpsConfigRplPort,
                  mgmtErpsConfigProtectedVlans0Kto1K,
                  mgmtErpsConfigProtectedVlans1Kto2K,
                  mgmtErpsConfigProtectedVlans2Kto3K,
                  mgmtErpsConfigProtectedVlans3Kto4K,
                  mgmtErpsConfigAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 2 }

mgmtErpsConfigRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsConfigRowEditorGroupIndex,
                  mgmtErpsConfigRowEditorAdminActive,
                  mgmtErpsConfigRowEditorVersion,
                  mgmtErpsConfigRowEditorRingType,
                  mgmtErpsConfigRowEditorVirtualChannel,
                  mgmtErpsConfigRowEditorConnectedRingId,
                  mgmtErpsConfigRowEditorConnectedRingPropagate,
                  mgmtErpsConfigRowEditorRingId,
                  mgmtErpsConfigRowEditorNodeId,
                  mgmtErpsConfigRowEditorLevel,
                  mgmtErpsConfigRowEditorControlVlan,
                  mgmtErpsConfigRowEditorPcp,
                  mgmtErpsConfigRowEditorPort0SfTrigger,
                  mgmtErpsConfigRowEditorPort0If,
                  mgmtErpsConfigRowEditorPort0MEPDomain,
                  mgmtErpsConfigRowEditorPort0MEPService,
                  mgmtErpsConfigRowEditorPort0MEPId,
                  mgmtErpsConfigRowEditorPort0Smac,
                  mgmtErpsConfigRowEditorPort1SfTrigger,
                  mgmtErpsConfigRowEditorPort1If,
                  mgmtErpsConfigRowEditorPort1MEPDomain,
                  mgmtErpsConfigRowEditorPort1MEPService,
                  mgmtErpsConfigRowEditorPort1MEPId,
                  mgmtErpsConfigRowEditorPort1Smac,
                  mgmtErpsConfigRowEditorRevertive,
                  mgmtErpsConfigRowEditorWaitToRestoreTime,
                  mgmtErpsConfigRowEditorGuardTime,
                  mgmtErpsConfigRowEditorHoldOffTime,
                  mgmtErpsConfigRowEditorRplMode,
                  mgmtErpsConfigRowEditorRplPort,
                  mgmtErpsConfigRowEditorProtectedVlans0Kto1K,
                  mgmtErpsConfigRowEditorProtectedVlans1Kto2K,
                  mgmtErpsConfigRowEditorProtectedVlans2Kto3K,
                  mgmtErpsConfigRowEditorProtectedVlans3Kto4K,
                  mgmtErpsConfigRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 3 }

mgmtErpsStatusTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsStatusGroupIndex, mgmtErpsStatusOperState,
                  mgmtErpsStatusOperWarning, mgmtErpsStatusNodeState,
                  mgmtErpsStatusTxRapsActive,
                  mgmtErpsStatusTxInfoUpdateTimeSecs,
                  mgmtErpsStatusTxInfoRequest,
                  mgmtErpsStatusTxInfoVersion, mgmtErpsStatusTxInfoRb,
                  mgmtErpsStatusTxInfoDnf, mgmtErpsStatusTxInfoBpr,
                  mgmtErpsStatusTxInfoNodeId,
                  mgmtErpsStatusTxInfoSmac, mgmtErpsStatusCFOPTo,
                  mgmtErpsStatusPort0StatusBlocked,
                  mgmtErpsStatusPort0StatusSf,
                  mgmtErpsStatusPort0StatusFopPm,
                  mgmtErpsStatusPort0StatusUpdateTimeSecs,
                  mgmtErpsStatusPort0StatusRequest,
                  mgmtErpsStatusPort0StatusVersion,
                  mgmtErpsStatusPort0StatusRb,
                  mgmtErpsStatusPort0StatusDnf,
                  mgmtErpsStatusPort0StatusBpr,
                  mgmtErpsStatusPort0StatusNodeId,
                  mgmtErpsStatusPort0StatusSmac,
                  mgmtErpsStatusPort0RxErrorCnt,
                  mgmtErpsStatusPort0RxOwnCnt,
                  mgmtErpsStatusPort0RxGuardCnt,
                  mgmtErpsStatusPort0RxFOPPmCnt,
                  mgmtErpsStatusPort0RxNrCnt,
                  mgmtErpsStatusPort0RxNrRbCnt,
                  mgmtErpsStatusPort0RxSfCnt,
                  mgmtErpsStatusPort0RxFxCnt,
                  mgmtErpsStatusPort0RxMsCnt,
                  mgmtErpsStatusPort0RxEventCnt,
                  mgmtErpsStatusPort0TxNrCnt,
                  mgmtErpsStatusPort0TxNrRbCnt,
                  mgmtErpsStatusPort0TxSfCnt,
                  mgmtErpsStatusPort0TxFsCnt,
                  mgmtErpsStatusPort0TxMsCnt,
                  mgmtErpsStatusPort0TxEventCnt,
                  mgmtErpsStatusPort0SfCn,
                  mgmtErpsStatusPort0FlushCnt,
                  mgmtErpsStatusPort1StatusBlocked,
                  mgmtErpsStatusPort1StatusSf,
                  mgmtErpsStatusPort1StatusFopPm,
                  mgmtErpsStatusPort1StatusUpdateTimeSecs,
                  mgmtErpsStatusPort1StatusRequest,
                  mgmtErpsStatusPort1StatusVersion,
                  mgmtErpsStatusPort1StatusRb,
                  mgmtErpsStatusPort1StatusDnf,
                  mgmtErpsStatusPort1StatusBpr,
                  mgmtErpsStatusPort1StatusNodeId,
                  mgmtErpsStatusPort1StatusSmac,
                  mgmtErpsStatusPort1RxErrorCnt,
                  mgmtErpsStatusPort1RxOwnCnt,
                  mgmtErpsStatusPort1RxGuardCnt,
                  mgmtErpsStatusPort1RxFOPPmCnt,
                  mgmtErpsStatusPort1RxNrCnt,
                  mgmtErpsStatusPort1RxNrRbCnt,
                  mgmtErpsStatusPort1RxSfCnt,
                  mgmtErpsStatusPort1RxFxCnt,
                  mgmtErpsStatusPort1RxMsCnt,
                  mgmtErpsStatusPort1RxEventCnt,
                  mgmtErpsStatusPort1TxNrCnt,
                  mgmtErpsStatusPort1TxNrRbCnt,
                  mgmtErpsStatusPort1TxSfCnt,
                  mgmtErpsStatusPort1TxFsCnt,
                  mgmtErpsStatusPort1TxMsCnt,
                  mgmtErpsStatusPort1TxEventCnt,
                  mgmtErpsStatusPort1SfCn,
                  mgmtErpsStatusPort1FlushCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 4 }

mgmtErpsControlCommandTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsControlCommandGroupIndex,
                  mgmtErpsControlCommandCommand }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 5 }

mgmtErpsControlStatisticsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtErpsControlStatisticsClearGroupIndex,
                  mgmtErpsControlStatisticsClearClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtErpsMibGroups 6 }

mgmtErpsMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtErpsCapabilitiesInfoGroup,
                       mgmtErpsConfigTableInfoGroup,
                       mgmtErpsConfigRowEditorInfoGroup,
                       mgmtErpsStatusTableInfoGroup,
                       mgmtErpsControlCommandTableInfoGroup,
                       mgmtErpsControlStatisticsClearTableInfoGroup }

    ::= { mgmtErpsMibCompliances 1 }

END
