describe('User Management Tests', () => {
    const adminUser = {
      name: 'admin',
      role: 'admin',
      email: '<EMAIL>'
    };
  
    // Add base URL configuration
    before(() => {
      Cypress.config('baseUrl', 'http://localhost:3000'); 
    });
  
    beforeEach(() => {
      cy.intercept('GET', '**/api/v1/users**', { fixture: 'users.json' }).as('getUsers');
      
      // Set session storage values
      cy.window().then((win) => {
        win.sessionStorage.setItem('nmsuserrole', 'admin');
        win.sessionStorage.setItem('nmsuser', 'admin');
        win.sessionStorage.setItem('sessionid', 'mock-session-id');
      });
      
      cy.visit('/login');
      cy.get('[data-testid="username"]').type('admin');
        cy.get('[data-testid="password"]').type('default');
        cy.get('[data-testid="submit"]').click();
        cy.visit('/usermanagement');

    });
  
    it('should display the users table with correct columns', () => {
      // Wait for either the API call or table render
      cy.get('.ant-pro-table').should('exist');
      cy.contains('Username').should('be.visible');
      cy.contains('Email').should('be.visible');
      cy.contains('Role').should('be.visible');
      cy.contains('Two Factor Auth').should('be.visible');
      
      // Optional: Add timeout for API call
      cy.wait('@getUsers', { timeout: 10000 }).then((interception) => {
        assert.isNotNull(interception.response.body, 'API call completed');
      });
  
      it('should display users data correctly', () => {
        cy.fixture('users.json').then((users) => {
          users.forEach((user, index) => {
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(1)`).should('contain', user.name);
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(2)`).should('contain', user.email);
            cy.get(`tbody tr:nth-child(${index + 1}) td:nth-child(3)`).should('contain', user.role);
          });
        });
      });
  
      it('should allow searching users', () => {
        cy.get('.ant-pro-table-search input').type('admin');
        cy.get('tbody tr').should('have.length', 1);
        cy.contains('admin').should('exist');
      });
    });
  
    context('User CRUD Operations', () => {
      it('should open add user modal', () => {
        cy.contains('Add New').click();
        cy.get('.ant-modal-title').should('contain', 'Add new user');
      });
  
    it('should create a new user', () => {
        // Mock the API response for user creation
        cy.intercept('POST', '**/api/v1/users', {
          statusCode: 200,
          body: { message: 'User created successfully' }
        }).as('createUser');
    
        // Open the add user modal
        cy.contains('Add New').click();
        
        // Wait for modal animation and form rendering
        cy.get('.ant-modal-content', { timeout: 10000 }).should('be.visible');
    
        // Fill the form using data-testid attributes (recommended)
        cy.get('[data-testid="username"]').type('newuser');
        cy.get('[data-testid="email"]').type('<EMAIL>');
        cy.get('[data-testid="password"]').type('Test@1234');
        
        // Select role dropdown
        cy.get('[data-testid="role"]').click();
        cy.get('.ant-select-item-option-content').contains('User').click();
    
        // Submit the form
        cy.get('[data-testid="submit-button"]').click();
    
        // Verify API call
        cy.wait('@createUser').its('request.body').should('deep.equal', {
          email: '<EMAIL>',
          name: 'newuser',
          password: 'Test@1234',
          role: 'user'
        });
    
        // Verify success notification
        cy.contains('User has been added').should('be.visible');
      });
    });
  
    context('Two-Factor Authentication', () => {
      it('should show 2FA toggle for admin users', () => {
        cy.get('.ant-switch').first().should('exist');
      });
    });
  
    context('Authorization Checks', () => {
      it('should hide admin actions for regular users', () => {
        cy.window().then((win) => {
          win.sessionStorage.setItem('nmsuserrole', 'user');
        });
        
        cy.reload();
        cy.contains('Add New').should('not.exist');
        cy.get('[aria-label="edit"]').should('not.exist');
        cy.get('[aria-label="delete"]').should('not.exist');
      });
  
      it('should prevent self-deletion', () => {
        cy.get(`td:contains(${adminUser.name})`).siblings().last()
          .find('[aria-label="delete"]').should('not.exist');
      });
    });
  });