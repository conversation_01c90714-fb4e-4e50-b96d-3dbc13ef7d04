package mnms

import (
	"testing"
)

// TestGetMachineInfo tests the GetMachineInfo function
func TestGetMachineInfo(t *testing.T) {
	machineInfo, err := GetMachineInfo()
	if err != nil {
		t.<PERSON>("error getting machine info: %v", err)
	}

	t.Logf("Hostname: %s", machineInfo.Hostname)
	t.Logf("OS: %s", machineInfo.OS)
	t.Logf("Architecture: %s", machineInfo.Architecture)

	t.Logf("CPU Info:")
	for _, info := range machineInfo.CPUInfo {
		t.Logf("  - CPU: %s", info.ModelName)
		t.Logf("    Cores: %d", info.Cores)
	}

	t.Logf("Total Memory: %d bytes", machineInfo.TotalMemory)
	t.Logf("Free Memory: %d bytes", machineInfo.FreeMemory)

	t.Logf("Network Interfaces:")
	for _, iface := range machineInfo.NetworkInterfaces {
		t.Logf("  - Name: %s", iface.Name)
		t.Logf("    MAC Address: %s", iface.HardwareAddr)
		t.Logf("    IP Addresses:")
		for _, addr := range iface.Addrs {
			t.Logf("      - %s", addr.Addr)
		}
	}

	t.Logf("Machine ID: %s", machineInfo.MachineID)
	t.Logf("Client: %s", machineInfo.Client)
}
