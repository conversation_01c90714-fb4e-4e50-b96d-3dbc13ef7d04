package ids

import "github.com/google/gopacket/pcap"

type ethInfo struct {
	name        string
	description string
	flags       uint32
}

const (
	pacap_lopback                          uint32 = 0x00000001
	pacap_up                               uint32 = 0x00000002
	pacap_if_running                       uint32 = 0x00000004
	pacp_if_wireless                       uint32 = 0x00000008
	pacp_if_connection_status              uint32 = 0x00000030
	pacp_if_connection_status_unlnown      uint32 = 0x00000000
	pacp_if_connection_status_connected    uint32 = 0x00000010
	pacp_if_connection_status_disconnected uint32 = 0x00000020
	pacp_if_connection_not_applicable      uint32 = 0x00000030
)

// GetEthName return all of ethname
func GetEthName() ([]ethInfo, error) {
	eths := []ethInfo{}
	devices, err := pcap.FindAllDevs()
	if err != nil {
		return nil, err
	}
	for _, d := range devices {
		if d.Flags&pacap_up == pacap_up && d.Flags&pacp_if_connection_status_connected == pacp_if_connection_status_connected {
			eths = append(eths, ethInfo{name: d.Name, description: d.Description, flags: d.Flags})
		}
	}

	return eths, nil
}
