import { Select, Space, Card } from "antd";
import React from "react";
import ReactApex<PERSON>hart from "react-apexcharts";

const RealtimeTraffic = ({
  portData,
  selectedPort,
  setSelectedPort,
  lineChartState,
}) => {
  return (
    <Card title="Real-Time traffic">
      <Space direction="vertical" style={{ width: "100%" }}>
        <Select
          value={selectedPort}
          onChange={(value) => {
            setSelectedPort(value);
          }}
          options={portData.map((item) => ({
            value: item.portName,
            label: item.portName,
          }))}
          style={{
            width: 120,
          }}
          placeholder="select port"
        />
        <ReactApexChart
          type="line"
          series={lineChartState.series}
          options={lineChartState.options}
          height={400}
        />
      </Space>
    </Card>
  );
};

export default RealtimeTraffic;
