import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Button,
  Typography,
  List,
  Space,
  Modal,
  Form,
  Collapse,
} from "antd";
import PropTypes from "prop-types";
import ReactMarkdown from "react-markdown";

import protectedApi from "../../utils/apis/protectedApis";
const MessageDisplay = ({ text, who, time }) => {
  const [message, setMessage] = useState("");

  useEffect(() => {
    console.log("MessageDisplay:", text);
    // Clean and prepare the message for display
    // let cleanedText = text.startsWith("data:")
    //   ? text.substring(5).trim()
    //   : text.trim();
    // cleanedText = cleanedText.replace(/\\n/g, "\n");
    setMessage(text);
  }, [text]);

  return (
    <div>
      <Space direction="vertical" size={4} style={{ width: "100%" }}>
        <Space direction="horizontal">
          <Typography.Text strong>{who || "AI"}</Typography.Text>
          <Typography.Text type="secondary">
            {time ? new Date(time).toLocaleString() : ""}
          </Typography.Text>
        </Space>
        {/* Render the text with markdown formatting */}
        <ReactMarkdown>{message}</ReactMarkdown>
        {/* <pre>{message}</pre> */}
      </Space>
    </div>
  );
};

MessageDisplay.propTypes = {
  text: PropTypes.string.isRequired,
  who: PropTypes.string.isRequired,
  time: PropTypes.string.isRequired,
};

const AIAssistModal = ({ open, onCancel }) => {
  const [items, setItems] = useState([]);
  const [inputText, setInputText] = useState("");

  const appendItem = (newItem) => {
    setItems((prevItems) => [...prevItems, { ...newItem, time: new Date() }]);
  };

  const handleSend = async () => {
    appendItem({ text: inputText, who: "you" });

    try {
      console.log("Sending message to AI:", inputText);

      const response = await protectedApi({
        url: "api/v1/ai-assist/actions/chat",
        method: "POST",
        data: { query: inputText },
      });

      // Ensure the response is not empty
      if (response.status !== 200) {
        throw new Error(`Server returned an error: ${response.status}`);
      }

      console.log("Response from server:", response);

      // Extract the `data` field from the response
      var sseData = response.data; // The `data` field is a single string
      console.log("SSE-like Data Field:", sseData);
      // remove data: prefix
      if (sseData.startsWith("data:")) {
        sseData = sseData.substring(5).trim();
      }
      // replace \\n to \n
      sseData = sseData.replace(/\\n/g, "\n");

      appendItem({ text: sseData, who: "AI" });
      // Split the string into individual messages
      // sseData.split("\n\n").map((chunk) => {
      //   console.log(chunk);
      //   if (chunk.startsWith("data:")) {
      //     var msg = chunk.substring(5).trim(); // Remove "data:" prefix
      //     appendItem({ text: msg, who: "AI" });
      //   } else {
      //     appendItem({ text: chunk, who: "AI" });
      //   }
      // });

      // // Filter out any null values and append each message to the items
      // messages
      //   .filter((msg) => msg) // Remove null or empty messages
      //   .forEach((msg) => appendItem({ text: msg, who: "AI" }));
    } catch (error) {
      console.error("Error during API processing:", error.message);
    }
  };
  const handleClear = () => {
    setItems([]);
  };

  return (
    <Modal
      width={1200}
      title="Nimbl AI Assist"
      size="large"
      open={open}
      onOk={onCancel}
      onCancel={onCancel}
      footer={null}
    >
      <div style={{ display: "flex", gap: "10px", marginBottom: "10px" }}>
        <Input
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
        />
        <Button type="primary" onClick={handleSend}>
          Send
        </Button>
        <Button onClick={handleClear}>Clear</Button>
      </div>
      {/* <Collapse bordered={false} defaultActiveKey={["1"]}>
        <Collapse.Panel header="Settings" key="1">
          <Form layout="vertical">
            <Form.Item label="AI Assist Type">
              <Input />
            </Form.Item>
            <Form.Item label="AI Assist Model">
              <Input />
            </Form.Item>
          </Form>
        </Collapse.Panel>
      </Collapse> */}
      <List
        style={{ height: "500px", overflow: "auto" }}
        dataSource={items}
        renderItem={(item) => (
          <List.Item>
            <MessageDisplay text={item.text} who={item.who} time={item.time} />
          </List.Item>
        )}
      />
    </Modal>
  );
};

export default AIAssistModal;
