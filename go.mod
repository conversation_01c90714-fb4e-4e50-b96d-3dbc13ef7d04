module mnms

go 1.24.0

toolchain go1.24.1

require (
	github.com/bitfield/script v0.21.4
	github.com/deejross/go-snmplib v0.0.0-20190126151901-0ff3fc321a43
	github.com/denisbrodbeck/machineid v1.0.1
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/dlclark/regexp2 v1.11.5
	github.com/eclipse/paho.mqtt.golang v1.4.3
	github.com/gin-gonic/gin v1.10.0
	github.com/go-chi/jwtauth/v5 v5.1.0
	github.com/google/gopacket v1.1.19
	github.com/google/uuid v1.6.0
	github.com/gorilla/websocket v1.5.0
	github.com/gosnmp/gosnmp v1.36.1
	github.com/icza/backscanner v0.0.0-20240328210400-b40c3a86dec5
	github.com/influxdata/go-syslog/v3 v3.0.0
	github.com/lestrrat-go/jwx/v2 v2.0.6
	github.com/mochi-co/mqtt v1.3.2
	github.com/pkg/errors v0.9.1
	github.com/pquerna/otp v1.4.0
	github.com/qeof/q v0.0.0-20230109172759-b2ed79367b5b
	github.com/slayercat/gosnmp v1.24.1
	github.com/vishvananda/netlink v1.1.0
	github.com/ziutek/telnet v0.0.0-20180329124119-c3b780dc415b
	golang.org/x/crypto v0.37.0
	golang.org/x/text v0.24.0
	golang.zx2c4.com/wireguard/wgctrl v0.0.0-20230215201556-9c5414ab4bde
	inet.af/tcpproxy v0.0.0-20221017015627-91f861402626
)

require (
	github.com/AkihiroSuda/go-netfilter-queue v0.0.0-20230310003200-24cd054ca0f8
	github.com/MakeNowJust/heredoc v1.0.0
	github.com/antlabs/httparser v0.0.10
	github.com/asaskevich/govalidator v0.0.0-20230301143203-a9d515a09cc2
	github.com/cavaliergopher/grab/v3 v3.0.1
	github.com/danomagnum/gologix v0.20.2-beta
	github.com/emersion/go-imap/v2 v2.0.0-alpha.6
	github.com/emersion/go-smtp v0.19.0
	github.com/fsnotify/fsnotify v1.7.0
	github.com/glebarez/go-sqlite v1.22.0
	github.com/go-chi/cors v1.2.1
	github.com/golang-module/carbon/v2 v2.4.1
	github.com/google/generative-ai-go v0.19.0
	github.com/google/gonids v0.0.0-20221214051315-c9bac16ccf6a
	github.com/greyreo/pcre2 v0.0.0-20220901083435-8656ca878145
	github.com/jinzhu/copier v0.4.0
	github.com/klauspost/oui v0.0.0-20150225163751-35b4deb627f8
	github.com/mholt/archiver/v3 v3.5.1
	github.com/oschwald/geoip2-golang v1.11.0
	github.com/panjf2000/ants/v2 v2.11.3
	github.com/pin/tftp v2.1.0+incompatible
	github.com/rasky/go-xdr v0.0.0-20170124162913-1a41d1a06c93
	github.com/sashabaranov/go-openai v1.40.0
	github.com/shirou/gopsutil v3.21.11+incompatible
	github.com/simonvetter/modbus v1.6.0
	github.com/stretchr/testify v1.10.0
	github.com/wasilibs/go-re2 v1.4.1
	github.com/yanlinLiu0424/godivert v0.0.0-20241125091452-a0320be457fa
	github.com/zmap/go-iptree v0.0.0-20210731043055-d4e632617837
	golang.org/x/net v0.39.0
	google.golang.org/api v0.228.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	cloud.google.com/go v0.115.0 // indirect
	cloud.google.com/go/ai v0.8.0 // indirect
	cloud.google.com/go/auth v0.15.0 // indirect
	cloud.google.com/go/auth/oauth2adapt v0.2.8 // indirect
	cloud.google.com/go/compute/metadata v0.6.0 // indirect
	cloud.google.com/go/longrunning v0.5.7 // indirect
	github.com/andybalholm/brotli v1.0.5 // indirect
	github.com/asergeyev/nradix v0.0.0-20170505151046-3872ab85bb56 // indirect
	github.com/boombuler/barcode v1.0.1-0.20190219062509-6c824513bacc // indirect
	github.com/bytedance/sonic v1.13.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/decred/dcrd/dcrec/secp256k1/v4 v4.1.0 // indirect
	github.com/djherbis/buffer v1.2.0 // indirect
	github.com/dsnet/compress v0.0.2-0.20210315054119-f66993602bf5 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/emersion/go-message v0.16.0 // indirect
	github.com/emersion/go-sasl v0.0.0-20220912192320-0145f2c60ead // indirect
	github.com/emersion/go-textwrapper v0.0.0-20200911093747-65d896831594 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.8 // indirect
	github.com/gammazero/deque v0.1.0 // indirect
	github.com/gammazero/workerpool v1.1.2 // indirect
	github.com/gin-contrib/sse v1.1.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.26.0 // indirect
	github.com/goburrow/serial v0.1.0 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/s2a-go v0.1.9 // indirect
	github.com/googleapis/enterprise-certificate-proxy v0.3.6 // indirect
	github.com/googleapis/gax-go/v2 v2.14.1 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.2 // indirect
	github.com/klauspost/cpuid/v2 v2.2.10 // indirect
	github.com/klauspost/pgzip v1.2.6 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lestrrat-go/blackmagic v1.0.1 // indirect
	github.com/lestrrat-go/httpcc v1.0.1 // indirect
	github.com/lestrrat-go/httprc v1.0.4 // indirect
	github.com/lestrrat-go/iter v1.0.2 // indirect
	github.com/lestrrat-go/option v1.0.0 // indirect
	github.com/magefile/mage v1.14.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/nwaples/rardecode v1.1.3 // indirect
	github.com/oschwald/maxminddb-golang v1.13.0 // indirect
	github.com/pelletier/go-toml/v2 v2.2.4 // indirect
	github.com/pierrec/lz4/v4 v4.1.18 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/pquerna/ffjson v0.0.0-20190930134022-aa0246cd15f7 // indirect
	github.com/prometheus/procfs v0.9.0 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/rogpeppe/go-internal v1.13.1 // indirect
	github.com/rs/xid v1.4.0 // indirect
	github.com/seancfoley/bintree v1.2.1 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/tetratelabs/wazero v1.5.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/ulikunitz/xz v0.5.11 // indirect
	github.com/vishvananda/netns v0.0.2 // indirect
	github.com/xi2/xz v0.0.0-20171230120015-48954b6210f8 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.59.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.59.0 // indirect
	go.opentelemetry.io/otel v1.34.0 // indirect
	go.opentelemetry.io/otel/metric v1.34.0 // indirect
	go.opentelemetry.io/otel/trace v1.34.0 // indirect
	golang.org/x/arch v0.16.0 // indirect
	golang.org/x/oauth2 v0.28.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/time v0.11.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250106144421-5f5ef82da422 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250313205543-e70fdf4c4cb4 // indirect
	google.golang.org/grpc v1.71.0 // indirect
	google.golang.org/protobuf v1.36.6 // indirect
	modernc.org/libc v1.37.6 // indirect
	modernc.org/mathutil v1.6.0 // indirect
	modernc.org/memory v1.7.2 // indirect
	modernc.org/sqlite v1.28.0 // indirect
)

require (
	bitbucket.org/creachadair/shell v0.0.7 // indirect
	github.com/awcullen/opcua v0.6.0-beta
	github.com/emersion/go-imap v1.2.1
	github.com/flier/gohs v1.2.3
	github.com/go-chi/chi/v5 v5.0.11
	github.com/go-git/go-billy/v5 v5.5.0
	github.com/go-ping/ping v1.1.0
	github.com/itchyny/gojq v0.12.7 // indirect
	github.com/itchyny/timefmt-go v0.1.3 // indirect
	github.com/kardianos/service v1.2.2
	github.com/kr/pretty v0.3.1 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/liupeidong0620/gateway v0.0.0-20201219131650-d90e929317ca
	github.com/seancfoley/ipaddress-go v1.5.3
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/slayercat/GoSNMPServer v0.1.2
	github.com/spf13/cobra v1.9.1
	github.com/tatsushid/go-fastping v0.0.0-20160109021039-d7bb493dee3e
	github.com/willscott/go-nfs v0.0.1
	github.com/willscott/go-nfs-client v0.0.0-20200605172546-271fa9065b33
	golang.org/x/sys v0.32.0
	golang.zx2c4.com/wireguard/windows v0.5.3
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
	gopkg.in/yaml.v3 v3.0.1
)

replace github.com/AkihiroSuda/go-netfilter-queue => github.com/yanlinLiu0424/go-netfilter-queue v0.0.0-20240216085248-5cbe02b26883

replace github.com/google/gonids => github.com/yanlinLiu0424/gonids v0.0.0-20250507083853-97be5e246cab
