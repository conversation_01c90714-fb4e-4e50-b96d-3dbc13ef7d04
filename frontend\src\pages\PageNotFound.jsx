import { Button, Result } from "antd";
import React from "react";
import { useNavigate } from "react-router-dom";

const PageNotFound = () => {
  const navigate = useNavigate();
  const handleOnBackClick = () => {
    navigate("/dashboard");
  };
  return (
    <Result
      status="404"
      title="404"
      subTitle="Sorry, the page you visited does not exist."
      extra={
        <Button type="primary" onClick={handleOnBackClick}>
          Back to dashboard
        </Button>
      }
    />
  );
};

export default PageNotFound;
