-- *****************************************************************
-- DHCP6-RELAY-MIB:  
-- ****************************************************************

MGMT-DHCP6-RELAY-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtDhcp6RelayMib MODULE-IDENTITY
    LAST-UPDATED "201804200000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private mib for dhcp6_relays"
    REVISION    "201804200000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 145 }


mgmtDhcp6RelayMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMib 1 }

mgmtDhcp6RelayConfig OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMibObjects 2 }

mgmtDhcp6RelayConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6RelayConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure Dhcp6_Relay for a specific vlan."
    ::= { mgmtDhcp6RelayConfig 1 }

mgmtDhcp6RelayConfigEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6RelayConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each vlan interface can be configured for dhcp relay"
    INDEX       { mgmtDhcp6RelayConfigVlanInterface,
                  mgmtDhcp6RelayConfigRelayVlanInterface }
    ::= { mgmtDhcp6RelayConfigTable 1 }

MGMTDhcp6RelayConfigEntry ::= SEQUENCE {
    mgmtDhcp6RelayConfigVlanInterface       MGMTInterfaceIndex,
    mgmtDhcp6RelayConfigRelayVlanInterface  MGMTInterfaceIndex,
    mgmtDhcp6RelayConfigRelayDestination    InetAddressIPv6
}

mgmtDhcp6RelayConfigVlanInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan Interface."
    ::= { mgmtDhcp6RelayConfigEntry 1 }

mgmtDhcp6RelayConfigRelayVlanInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Relay Vlan Interface."
    ::= { mgmtDhcp6RelayConfigEntry 2 }

mgmtDhcp6RelayConfigRelayDestination OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Ipv6 address of the DHCP server that requests are being relayed to. The
         default address is the multicast address ALL_DHCP_SERVERS (FF05::1:3)"
    ::= { mgmtDhcp6RelayConfigEntry 3 }

mgmtDhcp6RelayStatus OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMibObjects 3 }

mgmtDhcp6RelayStatusStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6RelayStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to containing statistics for Dhcp6_Relay for a specific
         vlan. Statistics can be cleared"
    ::= { mgmtDhcp6RelayStatus 1 }

mgmtDhcp6RelayStatusStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6RelayStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Vlan interface being serviced by relay agent"
    INDEX       { mgmtDhcp6RelayStatusStatisticsVlanInterface,
                  mgmtDhcp6RelayStatusStatisticsRelayVlanInterface }
    ::= { mgmtDhcp6RelayStatusStatisticsTable 1 }

MGMTDhcp6RelayStatusStatisticsEntry ::= SEQUENCE {
    mgmtDhcp6RelayStatusStatisticsVlanInterface       MGMTInterfaceIndex,
    mgmtDhcp6RelayStatusStatisticsRelayVlanInterface  MGMTInterfaceIndex,
    mgmtDhcp6RelayStatusStatisticsTxToServer          Integer32,
    mgmtDhcp6RelayStatusStatisticsRxFromServer        Integer32,
    mgmtDhcp6RelayStatusStatisticsServerPktDropped    Integer32,
    mgmtDhcp6RelayStatusStatisticsTxToClient          Integer32,
    mgmtDhcp6RelayStatusStatisticsRxFromClient        Integer32,
    mgmtDhcp6RelayStatusStatisticsClientPktDropped    Integer32
}

mgmtDhcp6RelayStatusStatisticsVlanInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan Interface."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 1 }

mgmtDhcp6RelayStatusStatisticsRelayVlanInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Relay Vlan Interface."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 2 }

mgmtDhcp6RelayStatusStatisticsTxToServer OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets sent to dhcpv6 server from vlan."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 3 }

mgmtDhcp6RelayStatusStatisticsRxFromServer OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets received from dhcpv6 server on vlan."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 4 }

mgmtDhcp6RelayStatusStatisticsServerPktDropped OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets from dhcpv6 server to vlan dropped."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 5 }

mgmtDhcp6RelayStatusStatisticsTxToClient OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets sent to dhcpv6 client from vlan."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 6 }

mgmtDhcp6RelayStatusStatisticsRxFromClient OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets received from dhcpv6 client on vlan."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 7 }

mgmtDhcp6RelayStatusStatisticsClientPktDropped OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of packets from dhcpv6 client to vlan dropped."
    ::= { mgmtDhcp6RelayStatusStatisticsEntry 8 }

mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeaf OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayStatus 2 }

mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeafNumIntfMissing OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of server packets dropped because interface option is missing."
    ::= { mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeaf 1 }

mgmtDhcp6RelayControl OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMibObjects 4 }

mgmtDhcp6RelayControlLeaf OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayControl 1 }

mgmtDhcp6RelayControlLeafClearAllStatistics OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The action to clear all statistics. True clears the statistics data.
         False does nothing."
    ::= { mgmtDhcp6RelayControlLeaf 1 }

mgmtDhcp6RelayMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMib 2 }

mgmtDhcp6RelayMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMibConformance 1 }

mgmtDhcp6RelayMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcp6RelayMibConformance 2 }

mgmtDhcp6RelayConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6RelayConfigVlanInterface,
                  mgmtDhcp6RelayConfigRelayVlanInterface,
                  mgmtDhcp6RelayConfigRelayDestination }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6RelayMibGroups 1 }

mgmtDhcp6RelayStatusStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6RelayStatusStatisticsVlanInterface,
                  mgmtDhcp6RelayStatusStatisticsRelayVlanInterface,
                  mgmtDhcp6RelayStatusStatisticsTxToServer,
                  mgmtDhcp6RelayStatusStatisticsRxFromServer,
                  mgmtDhcp6RelayStatusStatisticsServerPktDropped,
                  mgmtDhcp6RelayStatusStatisticsTxToClient,
                  mgmtDhcp6RelayStatusStatisticsRxFromClient,
                  mgmtDhcp6RelayStatusStatisticsClientPktDropped }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6RelayMibGroups 2 }

mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeafInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeafNumIntfMissing }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6RelayMibGroups 3 }

mgmtDhcp6RelayControlLeafInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6RelayControlLeafClearAllStatistics }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6RelayMibGroups 4 }

mgmtDhcp6RelayMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcp6RelayConfigTableInfoGroup,
                       mgmtDhcp6RelayStatusStatisticsTableInfoGroup,
                       mgmtDhcp6RelayStatusStatisticsInterfaceMissingLeafInfoGroup,
                       mgmtDhcp6RelayControlLeafInfoGroup }

    ::= { mgmtDhcp6RelayMibCompliances 1 }

END
