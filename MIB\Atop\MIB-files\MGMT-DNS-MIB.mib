-- *****************************************************************
-- DNS-MIB:  
-- ****************************************************************

MGMT-DNS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    <PERSON>TInterfaceIndex FROM MGMT-TC
    <PERSON><PERSON>pAddress FROM MGMT-TC
    ;

mgmtDnsMib MODULE-IDENTITY
    LAST-UPDATED "201411250000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the DNS MIB."
    REVISION    "201411250000Z"
    DESCRIPTION
        "Support multiple DNS server settings and default domain name
         configuration."
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 53 }


MGMTDnsConfigType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the configured DNS server type or default
         domain name type."
    SYNTAX      INTEGER { none(0), static(1), dhcpv4(2),
                          dhcpv4Vlan(3), dhcpv6(4), dhcpv6Vlan(5) }

mgmtDnsMibObjects OBJECT IDENTIFIER
    ::= { mgmtDnsMib 1 }

mgmtDnsCapabilities OBJECT IDENTIFIER
    ::= { mgmtDnsMibObjects 1 }

mgmtDnsCapabilitiesSupportDhcp4ConfigServer OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support setting DNS server from DHCPv4."
    ::= { mgmtDnsCapabilities 1 }

mgmtDnsCapabilitiesSupportDhcp6ConfigServer OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support setting DNS server from DHCPv6."
    ::= { mgmtDnsCapabilities 2 }

mgmtDnsCapabilitiesSupportDefaultDomainName OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support setting default domain name."
    ::= { mgmtDnsCapabilities 3 }

mgmtDnsCapabilitiesSupportDhcp4DomainName OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support setting default domain name from DHCPv4."
    ::= { mgmtDnsCapabilities 4 }

mgmtDnsCapabilitiesSupportDhcp6DomainName OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support setting default domain name from DHCPv6."
    ::= { mgmtDnsCapabilities 5 }

mgmtDnsCapabilitiesNsCntMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of supported name servers."
    ::= { mgmtDnsCapabilities 6 }

mgmtDnsConfig OBJECT IDENTIFIER
    ::= { mgmtDnsMibObjects 2 }

mgmtDnsConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDnsConfig 1 }

mgmtDnsConfigGlobalsProxy OBJECT IDENTIFIER
    ::= { mgmtDnsConfigGlobals 1 }

mgmtDnsConfigGlobalsProxyAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the DNS-Proxy feature."
    ::= { mgmtDnsConfigGlobalsProxy 1 }

mgmtDnsConfigGlobalsDefaultDomainName OBJECT IDENTIFIER
    ::= { mgmtDnsConfigGlobals 2 }

mgmtDnsConfigGlobalsDefaultDomainNameSetting OBJECT-TYPE
    SYNTAX      MGMTDnsConfigType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Default domain name administrative type. A default domain name is used
         as the suffix of the given name in DNS lookup. none(0) means no default
         domain name is used and thus no domain name suffix is appended in DNS
         lookup. static(1) means the default domain name will be manually set.
         dhcpv4(2) means default domain name will be determined by DHCPv4
         discovery. dhcpv4Vlan(3) means default domain name will be determined
         by DHCPv4 discovery on a specific IP VLAN interface. dhcpv6(4) means
         default domain name will be determined by DHCPv6 discovery.
         dhcpv6Vlan(5) means default domain name will be determined by DHCPv6
         discovery on a specific IP VLAN interface."
    ::= { mgmtDnsConfigGlobalsDefaultDomainName 1 }

mgmtDnsConfigGlobalsDefaultDomainNameStaticName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The static default domain name. It will be a reference only when
         DomainNameSetting is static(1)."
    ::= { mgmtDnsConfigGlobalsDefaultDomainName 2 }

mgmtDnsConfigGlobalsDefaultDomainNameDhcpIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ifIndex of specific VLAN interface that default domain name will be
         retrieved from DHCP. It will be a reference only when DomainNameSetting
         is either dhcpv4Vlan(3) or dhcpv6Vlan(5)."
    ::= { mgmtDnsConfigGlobalsDefaultDomainName 3 }

mgmtDnsConfigServers OBJECT IDENTIFIER
    ::= { mgmtDnsConfig 2 }

mgmtDnsConfigServersTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDnsConfigServersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing DNS server configuration."
    ::= { mgmtDnsConfigServers 1 }

mgmtDnsConfigServersEntry OBJECT-TYPE
    SYNTAX      MGMTDnsConfigServersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtDnsConfigServersPrecedence }
    ::= { mgmtDnsConfigServersTable 1 }

MGMTDnsConfigServersEntry ::= SEQUENCE {
    mgmtDnsConfigServersPrecedence       Unsigned32,
    mgmtDnsConfigServersSetting          MGMTDnsConfigType,
    mgmtDnsConfigServersStaticIpAddress  MGMTIpAddress,
    mgmtDnsConfigServersStaticIfIndex    MGMTInterfaceIndex
}

mgmtDnsConfigServersPrecedence OBJECT-TYPE
    SYNTAX      Unsigned32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Table index also represents the precedence in selecting target DNS
         server: less index value means higher priority in round-robin
         selection. Only one server is working at a time, that is when the
         chosen server is active, system marks the designated server as target
         and stops selection. When the active server becomes inactive, system
         starts another round of selection starting from the next available
         server setting."
    ::= { mgmtDnsConfigServersEntry 1 }

mgmtDnsConfigServersSetting OBJECT-TYPE
    SYNTAX      MGMTDnsConfigType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Per precedence DNS server administrative type. The DNS server setting
         will be used in DNS lookup. none(0) denotes no DNS server is used and
         thus domain name lookup always fails. static(1) denotes the DNS server
         address will be manually set, in either IPv4 or IPv6 address form.
         dhcpv4(2) denotes DNS server address will be determined by DHCPv4
         discovery. dhcpv4Vlan(3) denotes DNS server address will be determined
         by DHCPv4 discovery on a specifc IP VLAN interface. dhcpv6(4) denotes
         DNS server address will be determined by DHCPv6 discovery.
         dhcpv6Vlan(5) denotes DNS server address will be determined by DHCPv6
         discovery on a specifc IP VLAN interface."
    ::= { mgmtDnsConfigServersEntry 2 }

mgmtDnsConfigServersStaticIpAddress OBJECT-TYPE
    SYNTAX      MGMTIpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The static DNS server address. It will be a reference only when Setting
         is static(1)"
    ::= { mgmtDnsConfigServersEntry 3 }

mgmtDnsConfigServersStaticIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ifIndex of specific VLAN interface that DNS server address will be
         retrieved from DHCP and where the server resides. It will be a
         reference only when Setting is either dhcpv4Vlan(3) or dhcpv6Vlan(5)."
    ::= { mgmtDnsConfigServersEntry 4 }

mgmtDnsStatus OBJECT IDENTIFIER
    ::= { mgmtDnsMibObjects 3 }

mgmtDnsStatusGlobals OBJECT IDENTIFIER
    ::= { mgmtDnsStatus 1 }

mgmtDnsStatusGlobalsDefaultDomainName OBJECT IDENTIFIER
    ::= { mgmtDnsStatusGlobals 1 }

mgmtDnsStatusGlobalsDefaultDomainNameSuffix OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The suffix of the given domain name used in DNS lookup."
    ::= { mgmtDnsStatusGlobalsDefaultDomainName 1 }

mgmtDnsStatusServers OBJECT IDENTIFIER
    ::= { mgmtDnsStatus 2 }

mgmtDnsStatusServersTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDnsStatusServersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying DNS server information."
    ::= { mgmtDnsStatusServers 1 }

mgmtDnsStatusServersEntry OBJECT-TYPE
    SYNTAX      MGMTDnsStatusServersEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtDnsStatusServersPrecedence }
    ::= { mgmtDnsStatusServersTable 1 }

MGMTDnsStatusServersEntry ::= SEQUENCE {
    mgmtDnsStatusServersPrecedence        Unsigned32,
    mgmtDnsStatusServersConfiguredType    MGMTDnsConfigType,
    mgmtDnsStatusServersReferenceIfIndex  MGMTInterfaceIndex,
    mgmtDnsStatusServersIpAddress         MGMTIpAddress
}

mgmtDnsStatusServersPrecedence OBJECT-TYPE
    SYNTAX      Unsigned32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Table index also represents the precedence in selecting target DNS
         server: less index value means higher priority in round-robin
         selection. Only one server is working at a time, that is when the
         chosen server is active, system marks the designated server as target
         and stops selection. When the active server becomes inactive, system
         starts another round of selection starting from the next available
         server setting."
    ::= { mgmtDnsStatusServersEntry 1 }

mgmtDnsStatusServersConfiguredType OBJECT-TYPE
    SYNTAX      MGMTDnsConfigType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Per precedence DNS server configured type.none(0) denotes no DNS server
         is used and thus domain name lookup always fails. static(1) denotes the
         DNS server address will be manually set, in either IPv4 or IPv6 address
         form. dhcpv4(2) denotes DNS server address will be determined by DHCPv4
         discovery. dhcpv4Vlan(3) denotes DNS server address will be determined
         by DHCPv4 discovery on a specifc IP VLAN interface. dhcpv6(4) denotes
         DNS server address will be determined by DHCPv6 discovery.
         dhcpv6Vlan(5) denotes DNS server address will be determined by DHCPv6
         discovery on a specifc IP VLAN interface."
    ::= { mgmtDnsStatusServersEntry 2 }

mgmtDnsStatusServersReferenceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The ifIndex of specific VLAN interface that DNS server address will be
         retrieved from DHCP and where the server resides. It will be a
         reference only when Setting is either dhcpv4Vlan(3) or dhcpv6Vlan(5)."
    ::= { mgmtDnsStatusServersEntry 3 }

mgmtDnsStatusServersIpAddress OBJECT-TYPE
    SYNTAX      MGMTIpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DNS server address that will be used for domain name lookup."
    ::= { mgmtDnsStatusServersEntry 4 }

mgmtDnsMibConformance OBJECT IDENTIFIER
    ::= { mgmtDnsMib 2 }

mgmtDnsMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDnsMibConformance 1 }

mgmtDnsMibGroups OBJECT IDENTIFIER
    ::= { mgmtDnsMibConformance 2 }

mgmtDnsCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsCapabilitiesSupportDhcp4ConfigServer,
                  mgmtDnsCapabilitiesSupportDhcp6ConfigServer,
                  mgmtDnsCapabilitiesSupportDefaultDomainName,
                  mgmtDnsCapabilitiesSupportDhcp4DomainName,
                  mgmtDnsCapabilitiesSupportDhcp6DomainName,
                  mgmtDnsCapabilitiesNsCntMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 1 }

mgmtDnsConfigGlobalsProxyInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsConfigGlobalsProxyAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 2 }

mgmtDnsConfigGlobalsDefaultDomainNameInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsConfigGlobalsDefaultDomainNameSetting,
                  mgmtDnsConfigGlobalsDefaultDomainNameStaticName,
                  mgmtDnsConfigGlobalsDefaultDomainNameDhcpIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 3 }

mgmtDnsConfigServersTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsConfigServersPrecedence,
                  mgmtDnsConfigServersSetting,
                  mgmtDnsConfigServersStaticIpAddress,
                  mgmtDnsConfigServersStaticIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 4 }

mgmtDnsStatusGlobalsDefaultDomainNameInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsStatusGlobalsDefaultDomainNameSuffix }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 5 }

mgmtDnsStatusServersTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDnsStatusServersPrecedence,
                  mgmtDnsStatusServersConfiguredType,
                  mgmtDnsStatusServersReferenceIfIndex,
                  mgmtDnsStatusServersIpAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDnsMibGroups 6 }

mgmtDnsMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDnsCapabilitiesInfoGroup,
                       mgmtDnsConfigGlobalsProxyInfoGroup,
                       mgmtDnsConfigGlobalsDefaultDomainNameInfoGroup,
                       mgmtDnsConfigServersTableInfoGroup,
                       mgmtDnsStatusGlobalsDefaultDomainNameInfoGroup,
                       mgmtDnsStatusServersTableInfoGroup }

    ::= { mgmtDnsMibCompliances 1 }

END
