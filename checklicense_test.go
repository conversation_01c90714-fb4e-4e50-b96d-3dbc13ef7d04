package mnms

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/json"
	"errors"
	"io"
	"os"
	"path/filepath"
	"testing"

	"github.com/denisbrodbeck/machineid"
)

// TestNimblicenseMethods tests the NimblLicense methods.
func TestNimblicenseMethods(t *testing.T) {
	// Test unmarshal
	jsonStr := `
	{
		"program":"mnms",
		"version":"v1.0.2",
		"generated":"https://license.blackbeartechhive.com",
		"numClients":5,
		"numOfDevice":6,
		"machineID":"machineID",
		"enabledFeatures":"anomalies,idps"
	}`
	var licens NimblLicense
	err := json.Unmarshal([]byte(jsonStr), &licens)
	if err != nil {
		t.Fatalf("Failed to unmarshal json: %v", err)
	}
	// check values
	if licens.Program != "mnms" {
		t.Fatalf("Expected program to be 'mnms' but got '%v'", licens.Program)
	}
	if licens.Version != "v1.0.2" {
		t.Fatalf("Expected version to be 'v1.0.2' but got '%v'", licens.Version)
	}
	if licens.Generated != "https://license.blackbeartechhive.com" {
		t.Fatalf("Expected generated to be 'https://license.blackbeartechhive.com' but got '%v'", licens.Generated)
	}
	if licens.NumClients != 5 {
		t.Fatalf("Expected numClients to be 5 but got '%v'", licens.NumClients)
	}
	if licens.NumDevice != 6 {
		t.Fatalf("Expected numOfDevice to be 6 but got '%v'", licens.NumDevice)
	}
	if licens.MachineID != "machineID" {
		t.Fatalf("Expected machineID to be 'machineID' but got '%v'", licens.MachineID)
	}
	if licens.EnabledFeatures != "anomalies,idps" {
		t.Fatalf("Expected enabledFeatures to be 'anomalies,idps' but got '%v'", licens.EnabledFeatures)
	}
	if !licens.HasFeatureAnomalyDetection() {
		t.Fatalf("Expected HasFeatureAnomalyDetection() to be true but got false")
	}
	if !licens.HasFeatureIdps() {
		t.Fatalf("Expected HasFeatureIdps() to be true but got false")
	}
	var lic2 NimblLicense
	// Test other case
	jsonStr = `
	{
		"program":"mnms",
		"version":"v1.0.2",
		"generated":"https://license.blackbeartechhive.com",
		"numClients":5,
		"numOfDevice":6,
		"machineID":"machineID",
		"enabledFeatures":"idps"
	}`
	err = json.Unmarshal([]byte(jsonStr), &lic2)
	if err != nil {
		t.Fatalf("Failed to unmarshal json: %v", err)
	}
	if lic2.HasFeatureAnomalyDetection() {
		t.Fatalf("Expected HasFeatureAnomalyDetection() to be false but got true")
	}
	if !lic2.HasFeatureIdps() {
		t.Fatalf("Expected HasFeatureIdps() to be true but got false")
	}
}

// writeTestLicenseFile(ciphertextc []byte)
func writeTestLicenseFile(ciphertext []byte) error {
	// Get working directory
	wd, err := os.Getwd()
	if err != nil {
		return err
	}
	nmskey := filepath.Join(wd, "testnmskey")
	if !FileExists(nmskey) {
		CreateFile(nmskey)
	}
	err = os.WriteFile(nmskey, ciphertext, 0o644)
	if err != nil {
		return err
	}
	return nil
}

func cleanTestLicenseFile() {
	wd, _ := os.Getwd()

	nmskey := filepath.Join(wd, "testnmskey")
	if !FileExists(nmskey) {
		return
	}
	os.Remove(nmskey)

}

func TestVerifyLicense_valid_machineid(t *testing.T) {
	// Test CheckMachineID with a matching ID
	id, err := machineid.ID()
	if err != nil {
		t.Fatalf("Failed to get machine ID: %v", err)
	}
	keyText := make(map[string]interface{})
	keyText["program"] = "mnms"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = "https://license.blackbeartechhive.com"
	keyText["numClients"] = 5
	keyText["numOfDevice"] = 6
	keyText["machineID"] = id
	keyText["contact"] = "<EMAIL>"
	key := []byte("nmskeygeneratoruniquekey")
	jsonkeybyte, _ := json.Marshal(keyText)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err = io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)
	writeTestLicenseFile(ciphertext)
	defer cleanTestLicenseFile()

	_, err = loadLicenseToQC("testnmskey")
	if err != nil {
		t.Fatalf("Failed to load license: %v", err)
	}
	err = QC.License.checkContent()
	if err != nil {
		t.Fatalf("CheckLicense() error = %v, expectedError %v", err, nil)
	}
}

func TestVerifyLicense_Invalid_machineid(t *testing.T) {
	// Test CheckMachineID with a non-matching ID, expect an error
	keyText := make(map[string]interface{})
	keyText["program"] = "mnms"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = "https://license.blackbeartechhive.com"
	keyText["numClients"] = 5
	keyText["numOfDevice"] = 6
	keyText["machineID"] = "invalid"
	keyText["contact"] = "<EMAIL>"
	key := []byte("nmskeygeneratoruniquekey")
	jsonkeybyte, _ := json.Marshal(keyText)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err = io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)

	err = writeTestLicenseFile(ciphertext)
	defer cleanTestLicenseFile()
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}

	loadLicenseToQC("testnmskey")
	err = QC.License.checkContent()

	expectedError := errors.New("Machine ID is diffrent from license machine id")
	if err.Error() != expectedError.Error() {
		t.Logf("CheckMachineID() error = %v, expectedError %v", expectedError, err)
	}

}

func TestEmptyfile(t *testing.T) {
	err := writeTestLicenseFile([]byte{})
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}
	defer cleanTestLicenseFile()

	_, err = loadLicenseToQC("testnmskey")

	if err == nil {
		t.Fatalf("Expected an error from VerifyLicense but got none")
	}
}

func TestVerifyLicense_service(t *testing.T) {
	id, err := machineid.ID()
	if err != nil {
		t.Fatalf("Failed to get machine ID: %v", err)
	}
	keyText := make(map[string]interface{})
	keyText["program"] = "mnms"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = "https://license.blackbeartechhive.com"
	keyText["numClients"] = 0
	keyText["numOfDevice"] = 12
	keyText["machineID"] = id
	keyText["contact"] = "<EMAIL>"
	key := []byte("nmskeygeneratoruniquekey")
	jsonkeybyte, _ := json.Marshal(keyText)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)
	err = writeTestLicenseFile(ciphertext)
	defer cleanTestLicenseFile()
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}
	loadLicenseToQC("testnmskey")
	err = QC.License.checkContent()
	if err != nil {
		t.Logf("you have license for %v network services but using %v network services", keyText["numClients"], NetworkServicesAliveCount())
	}
}

func TestVerifyLicense_device(t *testing.T) {
	id, err := machineid.ID()
	if err != nil {
		t.Fatalf("Failed to get machine ID: %v", err)
	}
	keyText := make(map[string]interface{})
	keyText["program"] = "mnms"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = "https://license.blackbeartechhive.com"
	keyText["numClients"] = 1
	keyText["numOfDevice"] = 0
	keyText["machineID"] = id
	keyText["contact"] = "<EMAIL>"
	key := []byte("nmskeygeneratoruniquekey")
	jsonkeybyte, _ := json.Marshal(keyText)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}

	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)

	err = writeTestLicenseFile(ciphertext)
	defer cleanTestLicenseFile()
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}

	loadLicenseToQC("testnmskey")
	err = QC.License.checkContent()
	if err != nil {
		t.Logf("you have license for %v device but using %v device", keyText["numOfDevice"], len(QC.DevData)-1)
	}
}
func TestVerifyLicense_modifyinfo(t *testing.T) {
	id, err := machineid.ID()
	if err != nil {
		t.Fatalf("Failed to get machine ID: %v", err)
	}
	keyText := make(map[string]interface{})
	keyText["program"] = "m"
	keyText["version"] = "v1.0.2"
	keyText["generated"] = ""
	keyText["numClients"] = 7
	keyText["numOfDevice"] = 12
	keyText["machineID"] = id
	keyText["contact"] = "abc"
	key := []byte("nmskeygeneratoruniquekey")
	jsonkeybyte, _ := json.Marshal(keyText)
	block, err := aes.NewCipher(key)
	if err != nil {
		panic(err)
	}
	ciphertext := make([]byte, aes.BlockSize+len(jsonkeybyte))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		panic(err)
	}
	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], jsonkeybyte)
	err = writeTestLicenseFile(ciphertext)
	defer cleanTestLicenseFile()
	if err != nil {
		t.Fatalf("Failed to write to file: %v", err)
	}

	_, err = loadLicenseToQC("nmskey")
	t.Log(err)

	/* TODO: Fix this test
	if err == nil {
		t.Fatalf("Expected an error from VerifyLicense but got none")
	} else if !strings.Contains(err.Error(), "license file is corrupt and cannot be opened") {
		t.Fatalf("Expected 'license file is corrupt and cannot be opened' but got: %v", err)
	}
	*/
}
