import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Form, Input, Modal, Space, Typography } from "antd";
import React from "react";

const ManualTopoForm = ({ open, loading, onCancel, onOk }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={600}
      forceRender
      maskClosable={false}
      title="Add manual topology"
      okText="Add"
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            console.log(values);
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="dynamic_form_nest_item"
        style={{
          maxWidth: 600,
        }}
        form={form}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="mac"
          label="Device macaddress"
          rules={[
            {
              required: true,
              message: "Missing device mac",
            },
          ]}
        >
          <Input placeholder="Device macaddress" />
        </Form.Item>
        <Form.List
          name="linkData"
          rules={[
            {
              validator: async (_, linkData) => {
                if (!linkData || linkData.length < 1) {
                  return Promise.reject(
                    new Error("At least 1 link data required")
                  );
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map(({ key, name, ...restField }, i) => (
                <div key={key}>
                  <Typography.Text>Link data {i + 1}:</Typography.Text>
                  <Space
                    style={{
                      display: "flex",
                      marginBottom: 8,
                    }}
                    align="baseline"
                  >
                    <Form.Item
                      {...restField}
                      name={[name, "target"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing target mac",
                        },
                      ]}
                    >
                      <Input placeholder="Target mac" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "sourcePort"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing source port",
                        },
                      ]}
                    >
                      <Input placeholder="Source port" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "targetPort"]}
                      rules={[
                        {
                          required: true,
                          message: "Missing target port",
                        },
                      ]}
                    >
                      <Input placeholder="Target port" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                </div>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  Add field
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  );
};

export default ManualTopoForm;
