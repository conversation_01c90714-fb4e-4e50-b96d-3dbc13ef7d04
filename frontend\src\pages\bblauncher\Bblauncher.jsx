import React, { useEffect, useState } from "react";
import {
  useAddServicesFromLicenseMutation,
  useDeleteLicenseMutation,
  useDeletePrivateKeyMutation,
  useDownloadInstallerMutation,
  useGetInstallerFileListQuery,
  useGetLauncherStatusQuery,
  useGetRunOutputQuery,
  useGetShellServicesQuery,
  useKilAllServicesMutation,
  useStartAllServicesMutation,
  useUninstallNimblMutation,
  useUpdateServicesMutation,
} from "../../app/launcherApi/launcherApi";
import {
  App,
  Button,
  Card,
  Col,
  Divider,
  Flex,
  Image,
  Input,
  Row,
  Select,
  Space,
  Spin,
  Statistic,
  Tag,
  Typography,
  Upload,
} from "antd";
import { HomeOutlined, UploadOutlined } from "@ant-design/icons";

import useWebSocket from "react-use-websocket";
import { baseURL } from "../../utils/apis/publicLauncherApis";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { CheckServerStatus } from "../../features/comman/serverStatusSlice";
import { useThemeMode } from "antd-style";
import logo from "../../assets/images/NIMBL_Logo.svg";
import atopDarklogo from "../../assets/images/darkmode-logo.svg";

const Bblauncher = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [EditItem, setEditItem] = useState("");
  const { appearance } = useThemeMode();
  const { lastMessage } = useWebSocket(
    `${baseURL}/api/v1/services/run/socket`,
    {
      onOpen: () => {
        console.log("Socket connection established for launcher.");
      },
      onClose: () => {
        console.log("Socket connection closed for launcher.");
      },
      shouldReconnect: (closeEvent) => true,
    }
  );
  useEffect(() => {
    if (lastMessage !== null) {
      getrunOutput();
    }
  }, [lastMessage]);

  const { notification } = App.useApp();
  const [selectedNimblFile, setSelectedNimblFile] = useState(null);
  const [privKeyPath, setPrivKeyPath] = useState("");
  const { data } = useGetInstallerFileListQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );
  const { data: launcherStatus, refetch: refetchStatus } =
    useGetLauncherStatusQuery({}, { refetchOnMountOrArgChange: true });
  const { data: runOutput, refetch: getrunOutput } = useGetRunOutputQuery();
  const { data: servicesData } = useGetShellServicesQuery(
    {},
    {
      refetchOnMountOrArgChange: true,
      skip: EditItem !== "",
    }
  );

  useEffect(() => {
    const modified = (servicesData?.shellCommands || []).map((item) =>
      item
        .replace("nimbl/", "")
        .replaceAll("linux_amd64/", "")
        .replaceAll("windows_amd64/", "")
    );
    setEditItem(
      modified.toString().replaceAll(".exe", "").replaceAll(",", "\n") || ""
    );
  }, [servicesData]);

  const [downloadInstaller, { isLoading }] = useDownloadInstallerMutation();
  const [uninstallNimbl, {}] = useUninstallNimblMutation();
  const [deleteLicense, {}] = useDeleteLicenseMutation();
  const [deletePrivateKey, {}] = useDeletePrivateKeyMutation();
  const [addServicesFromLicense, {}] = useAddServicesFromLicenseMutation();
  const [startAllServices, {}] = useStartAllServicesMutation();
  const [updateService, {}] = useUpdateServicesMutation();
  const [killAllService, {}] = useKilAllServicesMutation();

  const HandleDownloadInstaller = async () => {
    try {
      await downloadInstaller({ filename: selectedNimblFile }).unwrap();
      notification.success({
        message: "downloaded nimbl installer successfully!",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    } finally {
      setSelectedNimblFile(null);
    }
  };
  const HandleGenerateServices = async () => {
    try {
      await addServicesFromLicense({ privKeyPath }).unwrap();
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    } finally {
      setPrivKeyPath("");
      setEditItem("");
    }
  };

  const HandleStartAllServices = async () => {
    if (EditItem === "") {
      notification.error({
        message: `service list input should not be empty!`,
      });
      return;
    }
    try {
      await updateService(EditItem.split("\n")).unwrap();
      await startAllServices({}).unwrap();
    } catch (error) {
      notification.error({
        message:
          error?.data?.error || error?.data || error || "somthing went wrong !",
      });
    } finally {
      setEditItem("");
    }
  };

  const HandleKillAllServices = async () => {
    try {
      await killAllService({}).unwrap();
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    }
  };

  const HandleUninstallNimbl = async () => {
    try {
      await uninstallNimbl({}).unwrap();
      notification.success({
        message: "uninstalled nimbl installer successfully!",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    } finally {
      setSelectedNimblFile(null);
    }
  };
  const HandleDeleteNmskey = async () => {
    try {
      await deleteLicense({}).unwrap();
      notification.success({
        message: "nmskey deleted successfully!",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    }
  };

  const HandleDeletePrivKey = async () => {
    try {
      await deletePrivateKey({}).unwrap();
      notification.success({
        message: "nmskey deleted successfully!",
      });
    } catch (error) {
      notification.error({ message: error?.data?.error || error?.data });
    }
  };

  const handleOnChange = ({ file, fileList, event }) => {
    if (file.status === "done") {
      refetchStatus();
      notification.success({
        message: `${file.name} file uploaded successfully`,
      });
    } else if (file.status === "error") {
      notification.error({ message: `${file.name} file upload failed.` });
    }
  };

  const handleOnChangePrivKey = ({ file, fileList, event }) => {
    if (file.status === "done") {
      refetchStatus();
      notification.success({
        message: `${file.name} file uploaded successfully`,
      });
    } else if (file.status === "error") {
      notification.error({ message: `${file.name} file upload failed.` });
    }
  };

  return (
    <Space direction="vertical" size={0} style={{ width: "100%" }}>
      <Flex
        style={{
          width: "100%",
          height: 48,
          fontSize: 24,
          fontWeight: 500,
          paddingBlock: 30,
          paddingInline: 15,
        }}
        justify="space-between"
        align="center"
      >
        <a
          href="https://blackbeartechhive.com"
          target="_blank"
          rel="noreferrer"
        >
          <Image
            height={50}
            alt="NIMBL"
            src={appearance === "dark" ? atopDarklogo : logo}
            preview={false}
          />
        </a>
        <div style={{ fontWeight: "650" }}>LAUNCHER (beta)</div>
        <Button
          onClick={() => {
            dispatch(CheckServerStatus());
            navigate("/");
          }}
          title="GO TO HOME PAGE"
          style={{ fontSize: "20px" }}
        >
          <HomeOutlined />
        </Button>
      </Flex>
      <Row gutter={[16, 16]}>
        <Col span={16}>
          <Card title="Install and Start">
            <Space direction="vertical" style={{ width: "100%" }} size={20}>
              <Typography.Text>
                To install Nimbl binary, select version in the list and click
                [Download] button. To remove current Nimbl binaries please click
                [Uninstall] button
              </Typography.Text>
              <Space>
                Select a Nimbl file to download:
                <Select
                  style={{ width: 300 }}
                  placeholder="Select a Nimbl file"
                  options={(data?.fileList || []).map((item) => ({
                    label: item,
                    value: item,
                  }))}
                  value={selectedNimblFile}
                  onChange={(v) => setSelectedNimblFile(v)}
                />
                <Button
                  type="primary"
                  onClick={() => HandleDownloadInstaller()}
                >
                  Download
                </Button>
                <Button
                  type="primary"
                  danger
                  onClick={() => HandleUninstallNimbl()}
                >
                  uninstall
                </Button>
              </Space>
              {isLoading && (
                <Flex align="center" justify="center">
                  <Spin size="default" tip="Downloading...">
                    <div style={{ minWidth: 200, height: 50 }}>{""}</div>
                  </Spin>
                </Flex>
              )}
            </Space>
            <Divider orientation="left" orientationMargin="0">
              License
            </Divider>
            <Space direction="vertical">
              <Space>
                Upload NIMBL nmskey license file:
                <Upload
                  name="file"
                  multiple={false}
                  onChange={handleOnChange}
                  action={`${baseURL}/api/v1/upload/license`}
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />}>Upload nmskey</Button>
                </Upload>
                <Button
                  type="primary"
                  danger
                  onClick={() => HandleDeleteNmskey()}
                >
                  Delete license
                </Button>
              </Space>
              <Space>
                Upload privatekey file:
                <Upload
                  name="file"
                  multiple={false}
                  onChange={handleOnChangePrivKey}
                  action={`${baseURL}/api/v1/upload/privkey`}
                  showUploadList={false}
                >
                  <Button icon={<UploadOutlined />}>Upload privatekey</Button>
                </Upload>
                <Button
                  type="primary"
                  danger
                  onClick={() => HandleDeletePrivKey()}
                >
                  Delete privatekey
                </Button>
              </Space>
            </Space>
            <Divider orientation="left" orientationMargin="0">
              Services
            </Divider>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Flex style={{ width: "100%" }} justify="flex-end">
                <Button type="primary" onClick={() => HandleGenerateServices()}>
                  generate services
                </Button>
              </Flex>
              <Flex gap={20} style={{ width: "100%" }} align="flex-end">
                <Input.TextArea
                  value={EditItem}
                  onChange={(e) => setEditItem(e.target.value)}
                  rows={10}
                />
              </Flex>
              <Space size={20}>
                <Button type="primary" onClick={() => HandleStartAllServices()}>
                  start
                </Button>
                <Button
                  type="primary"
                  danger
                  onClick={() => HandleKillAllServices()}
                >
                  stop
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Launcher Status" styles={{ body: { paddingTop: 0 } }}>
            <Divider orientation="left" orientationMargin="0">
              Installation
            </Divider>
            <Tag color={launcherStatus?.nimbleInstalled ? "Green" : "red"}>
              {launcherStatus?.nimbleInstalled
                ? "NIMBL Installed"
                : "NIMBL not Installed"}
            </Tag>
            <Divider orientation="left" orientationMargin="0">
              License
            </Divider>
            <Row>
              <Col span={8}>
                <Statistic
                  title="isvalid"
                  formatter={() => (
                    <Tag
                      color={launcherStatus?.license?.valid ? "Green" : "red"}
                    >
                      {launcherStatus?.license?.valid ? "valid" : "invalid"}
                    </Tag>
                  )}
                />
              </Col>
              <Col span={16}>
                <Statistic
                  title="Features enabled"
                  value={
                    launcherStatus?.license?.data?.enabledFeatures ||
                    "no features enabled"
                  }
                />
              </Col>
            </Row>
            <Divider orientation="left" orientationMargin="0">
              Running services
            </Divider>
            <Flex gap={10} wrap="wrap">
              {(launcherStatus?.runningService || []).length > 0 ? (
                (launcherStatus?.runningService || []).map((item, i) => (
                  <Tag key={`${i}+${item}`} color="Green">
                    {item}
                  </Tag>
                ))
              ) : (
                <Tag color="red">no running services found</Tag>
              )}
            </Flex>
          </Card>
          <div className="terminal">
            {runOutput &&
              runOutput?.output.split("\n").map((line, index) => (
                <div
                  className="terminal__line"
                  key={`terminal-line-${index}-${line}`}
                >
                  {line}
                </div>
              ))}
          </div>
        </Col>
      </Row>
    </Space>
  );
};

export default Bblauncher;
