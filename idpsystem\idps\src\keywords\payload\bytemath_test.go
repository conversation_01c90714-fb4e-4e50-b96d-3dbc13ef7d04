package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestByteMathItem(t *testing.T) {
	var tests = []struct {
		content  string
		expected byteMath
	}{

		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 2, oper +, rvalue 10, result bar;sid:1;)`,
			expected: byteMath{
				result: "bar",
				nbytes: 4,
				offset: 2,
				op:     "+",
				rvalue: 10,
				endian: byteMathEndIanDefault,
				base:   byteMathBaseDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 0, oper +,rvalue 248, result var, relative;sid:1;)`,
			expected: byteMath{
				result: "var",
				nbytes: 4,
				offset: 0,
				op:     "+",
				rvalue: 248,
				flags:  byteMathFlagRelative,
				endian: byteMathEndIanDefault,
				base:   byteMathBaseDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 2, oper +,rvalue 39, result bar, relative;sid:1;)`,
			expected: byteMath{
				result: "bar",
				rvalue: 39,
				nbytes: 4,
				offset: 2,
				op:     "+",
				flags:  byteMathFlagRelative,
				endian: byteMathEndIanDefault,
				base:   byteMathBaseDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 2, oper +,rvalue 39, result bar, dce;sid:1;)`,
			expected: byteMath{
				result: "bar",
				rvalue: 39,
				nbytes: 4,
				offset: 2,
				op:     "+",
				flags:  byteMathFlagEndian,
				endian: endianDCE,
				base:   byteMathBaseDefault,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 2, oper +,rvalue 39, result bar,relative, string dec;sid:1;)`,
			expected: byteMath{
				result: "bar",
				rvalue: 39,
				nbytes: 4,
				offset: 2,
				op:     "+",
				flags:  byteMathFlagRelative | byteMathFlagString,
				endian: bigEndian,
				base:   baseDec,
			},
		},
		{
			content: `alert ip any any -> any any (msg:"TestByteTest";byte_math:bytes 4, offset 2, oper +,rvalue 39, result bar,relative,string dec, bitmask 0x8f40;sid:1;)`,
			expected: byteMath{
				result:            "bar",
				rvalue:            39,
				nbytes:            4,
				offset:            2,
				op:                "+",
				flags:             byteMathFlagRelative | byteMathFlagString | byteMathFlagBitmask,
				endian:            bigEndian,
				base:              baseDec,
				bitmaskVal:        0x8f40,
				bitmaskShiftCount: 6,
			},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			if v, ok := d.RetrieveData().(*byteMath); ok {
				if v.nbytes != test.expected.nbytes {
					t.Error("nbytes error: unexpected")
				}
				if v.offset != test.expected.offset {
					t.Error("offset error: unexpected")
				}
				if v.op != test.expected.op {
					t.Error("op error: unexpected")
				}
				if v.result != test.expected.result {
					t.Error("result error: unexpected")
				}
				if v.rvalue != test.expected.rvalue {
					t.Error("rvalue error: unexpected")
				}
				if v.flags != test.expected.flags {
					t.Error("flags error: unexpected")
				}

				if v.flags&byteMathFlagString > 0 && v.base != test.expected.base {
					t.Error("byteBase error: unexpected")
				}
				if v.flags&byteMathFlagEndian > 0 && v.endian != test.expected.endian {
					t.Error("byteEndian error: unexpected")
				}
				if v.flags&byteMathFlagBitmask > 0 && v.bitmaskVal != test.expected.bitmaskVal {
					t.Error("bitmask error: unexpected")
				}
				if v.flags&byteMathFlagBitmask > 0 && v.bitmaskShiftCount != test.expected.bitmaskShiftCount {
					t.Error("bitmaskShiftCount error: unexpected")
				}

			}
		})
	}
}
