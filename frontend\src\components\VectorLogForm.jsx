import { Checkbox, Form, Input, Modal } from "antd";
import React, { useEffect } from "react";

const VectorLogForm = ({
  open,
  onCreate,
  onEdit,
  onCancel,
  isEdit,
  initialValues,
  loadingSave,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title={isEdit ? "Edit Vector Log" : "Add Vector Log"}
      okText={isEdit ? "EDIT" : "ADD"}
      cancelText="CANCEL"
      confirmLoading={loadingSave}
      onCancel={() => {
        form.setFieldsValue({ text: "", comment: "", normal: true });
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            if (isEdit) {
              onEdit(values);
            } else {
              onCreate(values);
            }
            form.setFieldsValue({ text: "", comment: "", normal: true });
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_vector_log">
        <Form.Item
          name="text"
          label="Log"
          rules={[
            {
              required: true,
              message: "Please input the log!",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item name="normal" valuePropName="checked">
          <Checkbox>Normal</Checkbox>
        </Form.Item>
        <Form.Item name="comment" label="Comment">
          <Input.TextArea />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default VectorLogForm;
