-- *****************************************************************
-- SSH-MIB:  
-- ****************************************************************

MGMT-SSH-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    ;

mgmtSshMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the SSH MIB"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 49 }


mgmtSshMibObjects OBJECT IDENTIFIER
    ::= { mgmtSshMib 1 }

mgmtSshConfig OBJECT IDENTIFIER
    ::= { mgmtSshMibObjects 2 }

mgmtSshConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtSshConfig 1 }

mgmtSshConfigGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the SSH functionality."
    ::= { mgmtSshConfigGlobals 1 }

mgmtSshMibConformance OBJECT IDENTIFIER
    ::= { mgmtSshMib 2 }

mgmtSshMibCompliances OBJECT IDENTIFIER
    ::= { mgmtSshMibConformance 1 }

mgmtSshMibGroups OBJECT IDENTIFIER
    ::= { mgmtSshMibConformance 2 }

mgmtSshConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSshConfigGlobalsAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSshMibGroups 1 }

mgmtSshMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtSshConfigGlobalsInfoGroup }

    ::= { mgmtSshMibCompliances 1 }

END
