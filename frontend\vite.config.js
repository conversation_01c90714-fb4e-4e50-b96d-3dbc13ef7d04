import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import includePaths from "rollup-plugin-includepaths";
import { VitePWA } from "vite-plugin-pwa";

const pwaOptions = {
  base: "/",
  includeAssets: ["favicon.svg"],
  manifest: {
    name: "NIMBL",
    short_name: "NIMBL",
    icons: [
      {
        src: "/android-chrome-192x192.png",
        sizes: "192x192",
        type: "image/png",
      },
      {
        src: "/android-chrome-512x512.png",
        sizes: "512x512",
        type: "image/png",
      },
      {
        src: "pwa-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any",
      },
      {
        src: "pwa-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "maskable",
      },
    ],
    theme_color: "#ffffff",
    background_color: "#ffffff",
    display: "standalone",
  },
  devOptions: {
    enabled: true,
    /* when using generateSW the PWA plugin will switch to classic */
    type: "module",
    navigateFallback: "index.html",
  },
};

pwaOptions.srcDir = "src";
pwaOptions.filename = "claims-sw.js";
pwaOptions.registerType = "autoUpdate";
pwaOptions.selfDestroying = true;

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), includePaths({ paths: ["./src"] }), VitePWA(pwaOptions)],
  build: {
    outDir: "../dist",
    emptyOutDir: true, // also necessary
    sourcemap: false,
    chunkSizeWarningLimit: 7000,
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "src/setupTests.js",
  },
  server: {
    port: 3000,
    host: true,
    strictPort: true,
    open: true,
  },
  optimizeDeps: {
    exclude: ["js-big-decimal"], // beacuse linux has some cache issue
  },
});
