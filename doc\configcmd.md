# BBNIM config guide

## Command

1. ### network setting

   #### request

   ```sh
   config network set mac currentIp newip mask gateway hostname dhcp
   ```

   example:
  
   ```sh
   config network set 00-60-E9-21-2B-9E ********* ********* *********** 0.0.0.0 switch2 0
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:44:12+08:00", Command:"config network set 00-60-E9-21-2B-9E ********* ********* *********** 0.0.0.0 switch2 0", Result:"", Status:"ok", Name:""}
   ```
2.  ### syslog setting

   #### request

   ```sh
   config syslog set mac enable serverip serverport loglevel logtoflash
   ```

   example:
   
   ```sh
   config syslog set 00-60-E9-21-2B-9E 1 ******* 1234 7 1
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:44:12+08:00", Command:"config syslog set 00-60-E9-21-2B-9E 1 ******* 1234 7 1", Result:"", Status:"ok", Name:""}
   ``` 
   #### Syslog Get
   
   #### request

   ```sh
   config syslog get mac
   ```

   example:

   ```sh
   config syslog get 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"config syslog get 00-60-E9-21-2B-9E", Result:"{\"status\":\"1\",\"server_ip\":\"*******\",\"server_port\":\"1234\",\"server_level\":\"5\",\"logToflash\":\"1\"}", Status:"ok", Name:""}
   ```
3. ### User Authentication 
   
   #### request

   ```sh
   config user mac username password
   ```

   example:
   
   ```sh
   config user 00-60-E9-21-2B-9E admin default
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:44:12+08:00", Command:"config user 00-60-E9-21-2B-9E admin default", Result:"", Status:"ok", Name:""}
   ``` 

4. ### syslog path

   #### request

   ```sh
   config local syslog path param
   ```

   example:

   ```sh
   config local syslog path ./testlog
   ```

   #### response

   ```sh
   &mnms.CmdInfo{Timestamp:"2023-02-21T17:03:55+08:00", Command:"config syslogpath ./testlog", Result:"", Status:"ok", Name:""}
   ```

5. ### syslog max size

   #### request

   ```sh
   config local syslog maxsize param
   ```

   example:

   ```sh
   config local syslog maxsize 200
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-22T11:12:11+08:00", Command:"config local syslog maxsize 200", Result:"", Status:"ok", Name:""}
   ```  

6. ### enable /disbale syslog compress 

   #### request

   ```sh
   config local syslog compress param
   ```

   example:

   ```sh
   config local syslog compress false
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-22T11:16:31+08:00", Command:"config local syslog compress true", Result:"", Status:"ok", Name:""}
   ```

7. ### syslog read

   #### request with time

   ```sh
   config local syslog read starttime endtime 
   ```

   without time

   ```sh
   config local syslog read 
   ```

   example:

   ```sh
   config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00
   ```

   with max line

   note: if without max line, that mean read all of lines

   ```sh
   config local syslog read 5
   ```

   with time and line

   ```sh
   config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-22T14:06:21+08:00", Command:"config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00", Result:"[{\"Facility\":0,\"Severity\":0,\"Priority\":0,\"Timestamp\":\"2023-02-21T22:17:12Z\",\"Hostname\":\"************\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"123456\"},{\"Facility\":0,\"Severity\":0,\"Priority\":0,\"Timestamp\":\"2023-02-21T22:17:13Z\",\"Hostname\":\"************\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"123456\"},{\"Facility\":0,\"Severity\":0,\"Priority\":0,\"Timestamp\":\"2023-02-21T22:17:16Z\",\"Hostname\":\"************\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"99\"},{\"Facility\":0,\"Severity\":0,\"Priority\":0,\"Timestamp\":\"2023-02-21T22:17:20Z\",\"Hostname\":\"************\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"1111\"},{\"Facility\":5,\"Severity\":7,\"Priority\":47,\"Timestamp\":\"2023-02-22T10:23:12Z\",\"Hostname\":\"LAPTOP-ERS90EE1\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"test\"},{\"Facility\":5,\"Severity\":7,\"Priority\":47,\"Timestamp\":\"2023-02-22T10:23:24Z\",\"Hostname\":\"LAPTOP-ERS90EE1\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"test\"},{\"Facility\":0,\"Severity\":6,\"Priority\":6,\"Timestamp\":\"2023-02-22T10:29:45+08:00\",\"Hostname\":\"local\",\"Appname\":\"d:\\\\NMS\\\\mnms\\\\issue169\\\\mnms\\\\__debug_bin.exe\",\"ProcID\":\"96452\",\"MsgID\":\"RFC5424Formatter\",\"Message\":\"hekko\"},{\"Facility\":0,\"Severity\":0,\"Priority\":0,\"Timestamp\":\"2023-02-22T11:09:15Z\",\"Hostname\":\"************\",\"Appname\":null,\"ProcID\":null,\"MsgID\":null,\"Message\":\"1111\"}]", Status:"ok", Name:""}
   ```

8. ### beep

    #### request

   ```sh
   beep mac
   ```

   example:

   ```sh
   beep 00-60-E9-21-2B-9E
   ```

    #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:54:17+08:00", Command:"beep 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
    #### gwd beep

    #### request

   ```sh
   gwd beep mac
   ```

   example:

   ```sh
   gwd beep 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:54:17+08:00", Command:"gwd beep 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
9. ### reset

   #### request

   ```sh
   reset mac
   ```

   example:

   ```sh
   reset 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:54:17+08:00", Command:"reset 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
   #### gwd reset

   #### request

   ```sh
   gwd reset mac
   ```

   example:

   ```sh
   gwd reset 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T16:54:17+08:00", Command:"gwd reset 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
10. ### Snmp enable/disable

   #### enable

   #### request

   ```sh
   snmp enable mac
   ```

   example:

   ```sh
   snmp enable 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2023-02-21T17:00:14+08:00", Command:"snmp enable 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
   #### disable

   #### request
   ```
   snmp disable mac
   ```

   example:

   ```
   snmp disable 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   &mnms.CmdInfo{Timestamp:"2023-02-21T17:01:46+08:00", Command:"config snmp disable 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
11. ### Trap setting add/delete/get

   #### add

   #### request
   ```
   snmp trap add mac serverip serverport community
   ```

   example:

   ```
   snmp trap add 00-11-22-33-44-55 *********01 162 public
   ```

   #### response

   ```sh
   &mnms.CmdInfo{Timestamp:"2023-02-21T17:01:46+08:00", Command:"snmp trap add 00-60-E9-21-2B-9E *********01 162 public", Result:"", Status:"ok", Name:""}
   ```
   #### delete

   #### request

   ```
   snmp trap del mac serverip serverport community
   ```

   example:

   ```
   snmp trap del 00-11-22-33-44-55 *********01 162 public
   ```

   #### response

   ```sh
   &mnms.CmdInfo{Timestamp:"2023-02-21T17:01:46+08:00", Command:"snmp trap del 00-60-E9-21-2B-9E *********01 162 public", Result:"", Status:"ok", Name:""}
   ```
    #### get

   #### request

   ```
   snmp trap get mac
   ```

   example:

   ```
   snmp trap get 00-11-22-33-44-55
   ```

   #### response

   ```sh
   &mnms.CmdInfo{Timestamp:"2023-02-21T17:01:46+08:00", Command:"snmp trap get 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
12. ### Network setting via GWD
   
   #### request

   ```sh
   gwd config network set mac ip newip mask gateway hostname
   ```

   example:

   ```sh
   gwd config network set 00-60-E9-21-2B-9E ********* ********* *********** 0.0.0.0 test1
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"gwd config network set 00-60-E9-21-2B-9E ********* ********* *********** 0.0.0.0 test1", Result:"", Status:"ok", Name:""}
   ```
   example: DHCP enable case

   ```sh
   gwd config network set 00-60-E9-21-2B-9E ********* 0.0.0.0 *********** 0.0.0.0 test1
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"gwd config network set 00-60-E9-21-2B-9E ********* 0.0.0.0 *********** 0.0.0.0 test1", Result:"", Status:"ok", Name:""}
   ```
13. ### Erase target device mtd and restore default settings.
   
   #### request

   ```sh
   mtderase mac
   ```

   example:

   ```sh
   mtderase 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"mtderase 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
14. ### Syslog settings via SNMP.
   
   #### request

   ```sh
   snmp config syslog set mac enable serverip serverport loglevel logtoflash
   ```

   example:

   ```sh
   snmp config syslog set 00-60-E9-21-2B-9E 1 ******* 1234 7 1
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"snmp config syslog set 00-60-E9-21-2B-9E 1 ******* 1234 7 1", Result:"", Status:"ok", Name:""}
   ```
   #### Syslog Get via SNMP.
   
   #### request

   ```sh
   snmp config syslog get mac
   ```

   example:

   ```sh
   snmp config syslog get 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"snmp config syslog get 00-60-E9-21-2B-9E", Result:"{\"status\":\"1\",\"server_ip\":\"*******\",\"server_port\":\"1234\",\"server_level\":\"5\",\"logToflash\":\"1\"}", Status:"ok", Name:""}
   ```
15. ### Firmware Update
   
   #### request

    ```sh
   firmware update [mac address] [file url]
   ```

   example:

   ```sh
   firmware update 00-60-E9-21-2B-9E http://*********:27182/api/v1/files/EHG750X-K770A770.dld
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"firmware update 00-60-E9-21-2B-9E http://*********:27182/api/v1/files/EHG750X-K770A770.dld", Result:"", Status:"ok", Name:""}
   ```

16. ### Mqtt Commands
   
    #### mqtt pub

    #### request

   ```sh
   mqtt pub tcpaddress topic data
   ```

   example:

   ```sh
   mqtt pub ************:1883 topictest testmesage
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"mqtt pub ************:1883 topictest testmesage", Result:"", Status:"ok", Name:""}
   ```
    #### mqtt sub
    
    ##### request

   ```sh
    mqtt sub tcpaddress topic
   ```

   example:

   ```sh
   mqtt sub ************:1883 topictest
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"mqtt sub ************:1883 topictest", Result:"", Status:"ok", Name:""}
   ```
   #### mqtt unsub

   #### request

   ```sh
    mqtt unsub tcpaddress topic
   ```

   example:

   ```sh
   mqtt unsub ************:1883 topictest
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"mqtt unsub ************:1883 topictest", Result:"", Status:"ok", Name:""}
   ```
   #### mqtt list

   #### request

   ```sh
    mqtt list
   ```

   example:

   ```sh
   mqtt list
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"mqtt list", Result:"", Status:"ok", Name:""}
   ```
17. ### Save Running configurations

   #### request

   ```sh
    config save mac
   ```

   example:

   ```sh
   config save 00-60-E9-21-2B-9E
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"config save 00-60-E9-21-2B-9E", Result:"", Status:"ok", Name:""}
   ```
18. ### Switch cli configurations

   #### request

   ```sh
    switch mac [cli cmd...]
   ```

   example:

   ```sh
   switch 00-60-E9-21-2B-9E show ip
   ```

   #### response

   ```sh
   mnms.CmdInfo{Timestamp:"2024-03-21T16:44:12+08:00", Command:"switch mac 00-60-E9-21-2B-9E show ip", Result:"", Status:"ok", Name:""}

