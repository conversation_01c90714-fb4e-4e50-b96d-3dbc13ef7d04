package main

import (
	"log"
	"os"
	"os/signal"

	"mnms/idpsystem/idps"

	"github.com/google/gonids"
)

func main() {

	i, err := idps.NewIdps(false, false)
	if err != nil {
		panic(err)
	}
	v := `drop tls $EXTERNAL_NET any <> $HOME_NET any (msg:"icmpv4 selftest drop";sid:789;)`
	r, err := gonids.ParseRule(v)
	if err != nil {
		panic(err)
	}
	err = i.AddRule(r)
	if err != nil {
		panic(err)
	}
	err = i.RegisterEvent(getevent())
	if err != nil {
		panic(err)
	}

	err = i.ApplyRules()
	if err != nil {
		panic(err)
	}
	err = i.Start()
	if err != nil {
		panic(err)
	}
	defer i.Close()
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	<-c
}

func getevent() func(idps.EventMessage) {
	v := func(event idps.EventMessage) {
		log.Printf("%v", event)
	}
	return v
}
