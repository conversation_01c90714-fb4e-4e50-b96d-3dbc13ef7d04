import {
  createAsyncThunk,
  createSlice,
  createSelector,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";
import { setFeatueEnabled } from "../socketControl/licenseAlertSlice";

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

export const RequestClusterInfo = createAsyncThunk(
  "clusterInfoSlice/RequestClusterInfo",
  async (params, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/register", {});
      const data = await response.data;
      if (response.status === 200) {
        await sleep(2000);
        return data;
      } else {
        return thunkAPI.rejectWithValue("Config read cluster info failed !");
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

export const RootClusterInfo = createAsyncThunk(
  "clusterInfoSlice/RootClusterInfo",
  async (params, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/info", {});
      const data = await response.data;
      if (response.status === 200) {
        console.log(data);
        thunkAPI.dispatch(setFeatueEnabled(data));
        await sleep(2000);
        return data;
      } else {
        return thunkAPI.rejectWithValue("Read root info failed !");
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const clusterInfoSlice = createSlice({
  name: "clusterInfoSlice",
  initialState: {
    clusterInfoData: [],
    rootInfoData: {},
    fetching: false,
    rootinfofetching: false,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(RequestClusterInfo.fulfilled, (state, { payload }) => {
        state.clusterInfoData = payload;
        state.fetching = false;
      })
      .addCase(RequestClusterInfo.pending, (state, { payload }) => {
        state.clusterInfoData = [];
        state.fetching = true;
      })
      .addCase(RequestClusterInfo.rejected, (state, { payload }) => {
        state.clusterInfoData = [];
        state.fetching = false;
      })
      .addCase(RootClusterInfo.fulfilled, (state, { payload }) => {
        state.rootInfoData = payload;
        if (payload.license !== undefined) {
          state.rootInfoData.anomaly_license =
            payload.license.featureAnomalyDetection;
          state.rootInfoData.idps_license = payload.license.featureIdps;
          state.rootInfoData.licensePath = payload.license.path;
          state.rootInfoData.maxClients = payload.license.numClients;
          state.rootInfoData.maxDevices = payload.license.numOfDevice;
        }

        state.rootinfofetching = false;
      })
      .addCase(RootClusterInfo.pending, (state, { payload }) => {
        state.rootInfoData = {};
        state.rootinfofetching = true;
      })
      .addCase(RootClusterInfo.rejected, (state, { payload }) => {
        state.rootInfoData = {};
        state.rootinfofetching = false;
      });
  },
});

export const {} = clusterInfoSlice.actions;
export const clusterInfoSelector = createSelector(
  (state) => state.clusterInfoData,
  ({ clusterInfoData, fetching, rootInfoData, rootinfofetching }) => ({
    clusterInfoData,
    fetching,
    rootInfoData,
    rootinfofetching,
  })
);

export default clusterInfoSlice;
