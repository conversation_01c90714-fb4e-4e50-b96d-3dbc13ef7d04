package ips

import (
	"math"
	"math/rand"
	"time"

	"github.com/google/gonids"
)

// all dns server ip ,to do get dns ip
// const DnsNet = "$DNS_SERVERS"

type Ipser interface {
	Start() error      //run  service
	Build() error      //Build rules whether it is correct
	ApplyRules() error //apply all rules
	Close() error      //close service
	Enablelo(bool)
	AddGonidsRule(*gonids.Rule) error //Add Rule of String
}

func RandUint16() uint32 {
	rand.NewSource(time.Now().UnixNano())
	return uint32(rand.Intn(math.MaxUint16 + 1))
}
