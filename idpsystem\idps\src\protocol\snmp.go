package protocol

import (
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/gosnmp/gosnmp"
)

//var Layersnmp = gopacket.RegisterLayerType(int(snmpProto), gopacket.LayerTypeMetadata{Name: snmpProto.String(), Decoder: gopacket.DecodeFunc(decodeSnmp)})

type snmpParser struct {
}

func (p *snmpParser) Parse(pack gopacket.Packet) bool {
	up := udpParser{}
	b := up.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeSnmp(up.GetUdp().Payload)
	return err == nil
}

type snmp struct {
	layers.BaseLayer
}

func decodeSnmp(data []byte) (*snmp, error) {

	snmp := &snmp{}
	err := snmp.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return snmp, nil
}

func (s *snmp) DecodeFromBytes(data []byte) error {
	h := gosnmp.Default
	_, err := h.SnmpDecodePacket(data)
	if err != nil {
		return err
	}

	s.BaseLayer = layers.BaseLayer{Contents: data, Payload: data}
	return nil
}
