package payload

import (
	"slices"
)

func detectGetLastSMFromLists(list *list, ids []detectedId) *Detect {
	return retrieveLastDetectByDetectedID(list.tail, ids)
}
func retrieveLastDetectByDetectedID(node *Detect, ids []detectedId) *Detect {
	for node != nil {
		if slices.Contains(ids, node.detectedID) {
			return node
		}
		node = node.prev
	}
	return nil
}

func isalpha(c byte) bool {
	if c >= '0' && c <= '9' {
		return false
	}
	return true
}
