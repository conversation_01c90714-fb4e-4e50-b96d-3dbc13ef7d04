package llm

import (
	"testing"

	"github.com/google/generative-ai-go/genai"
)

func TestConvertArgsToSchema_WithProperties(t *testing.T) {
	args := map[string]interface{}{
		"properties": map[string]interface{}{
			"foo": map[string]interface{}{
				"type":        "integer",
				"description": "The foo integer",
			},
			"bar": map[string]interface{}{
				"type":        "boolean",
				"description": "The bar boolean",
			},
		},
	}
	schema := convertArgsToSchema(args)
	if len(schema) != 2 {
		t.Fatalf("Expected 2 schema entries, got %d", len(schema))
	}

	// verify foo
	if s, ok := schema["foo"]; !ok {
		t.<PERSON>rror(`Expected key "foo" in schema`)
	} else {
		if s.Type != genai.TypeInteger {
			t.Errorf(`Expected foo Type Integer, got %v`, s.Type)
		}
		if s.Description != "The foo integer" {
			t.<PERSON>(`Expected foo Description "The foo integer", got %q`, s.Description)
		}
	}

	// verify bar
	if s, ok := schema["bar"]; !ok {
		t.<PERSON>rror(`Expected key "bar" in schema`)
	} else {
		if s.Type != genai.TypeBoolean {
			t.Errorf(`Expected bar Type Boolean, got %v`, s.Type)
		}
		if s.Description != "The bar boolean" {
			t.Errorf(`Expected bar Description "The bar boolean", got %q`, s.Description)
		}
	}
}

func TestConvertArgsToSchema_Flat(t *testing.T) {
	args := map[string]interface{}{
		"baz": map[string]interface{}{
			"description": "Baz description",
		},
	}
	schema := convertArgsToSchema(args)
	if len(schema) != 1 {
		t.Fatalf("Expected 1 schema entry, got %d", len(schema))
	}

	if s, ok := schema["baz"]; !ok {
		t.Error(`Expected key "baz" in schema`)
	} else {
		if s.Type != genai.TypeString {
			t.Errorf(`Expected baz Type String, got %v`, s.Type)
		}
		if s.Description != "Baz description" {
			t.Errorf(`Expected baz Description "Baz description", got %q`, s.Description)
		}
	}
}
