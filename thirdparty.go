package mnms

import (
	"bytes"
	_ "embed"
	"strings"
	"sync"

	"github.com/google/gopacket/layers"
	"github.com/klauspost/oui"
)

//go:embed oui.txt
var b []byte
var ouidb oui.StaticDB

func init() {
	db, _ := oui.OpenStatic(bytes.NewReader(b))
	ouidb = db
}

func NewThirdParty() *thirdPartydev {
	return &thirdPartydev{dev: make(map[string]DevInfo), m: new(sync.Mutex)}
}

type thirdPartydev struct {
	m   *sync.Mutex
	dev map[string]DevInfo
}

var atopMacPrefix = "00-60-E9"

// add others device of Vendor, not include atop devices
func (a *thirdPartydev) add(d layers.ARP) {
	a.m.Lock()
	defer a.m.Unlock()
	mac := byteToHexString(d.SourceHwAddress, "-")
	if !strings.HasPrefix(mac, atopMacPrefix) {
		dev := DevInfo{}
		dev.Mac = mac
		dev.IPAddress = byteToString(d.SourceProtAddress, ".")
		if ouidb != nil {
			entry, _ := ouidb.Query(mac)
			if entry != nil {
				dev.Hostname = entry.Manufacturer
			}
		}

		dev.ScannedBy = QC.Name
		dev.Scanproto = "arp"
		dev.Type = "IP discovered"
		dev.Tag = "third party"
		a.dev[dev.Mac] = dev

	}
}

type collector interface {
	add(layers.ARP)
}
