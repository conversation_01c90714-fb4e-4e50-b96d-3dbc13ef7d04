package encrypt

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/hex"
	"errors"

	"golang.org/x/crypto/pbkdf2"
)

func GenerateEncryptedKeySeed(pass string) ([]byte, error) {
	seed := make([]byte, 256)

	for i := range seed {
		seed[i] = 0xA5
	}

	masterkey := pbkdf2.Key([]byte(pass), []byte(seed[:12]), 4096, 32, sha256.New)

	aes, err := aes.NewCipher(masterkey)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(aes)
	if err != nil {
		return nil, err
	}

	pt := seed[12 : len(seed)-16]

	// encrypt in place
	gcm.Seal(pt[:0], seed[:12], pt, nil)

	return seed, nil
}

var Secret = []byte("mjnwmtssecret")

// Adjust<PERSON>ey adjusts the key to a valid AES key length (16, 24, or 32 bytes)
func AdjustKey(key []byte) ([]byte, error) {
	switch len(key) {
	case 16, 24, 32:
		return key, nil
	default:
		// Adjust key to the nearest valid length
		if len(key) < 16 {
			newKey := make([]byte, 16)
			copy(newKey, key)
			return newKey, nil
		} else if len(key) > 16 && len(key) < 24 {
			newKey := make([]byte, 24)
			copy(newKey, key)
			return newKey, nil
		} else if len(key) > 24 && len(key) < 32 {
			newKey := make([]byte, 32)
			copy(newKey, key)
			return newKey, nil
		} else if len(key) > 32 {
			return key[:32], nil
		}
		return nil, errors.New("key length not suitable for AES encryption")
	}
}

func DecryptedKey(ciphertext string) (string, error) {
	adjustedKey, err := AdjustKey(Secret)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(adjustedKey)
	if err != nil {
		return "", err
	}

	ciphertextBytes, err := hex.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	if len(ciphertextBytes) < aes.BlockSize {
		return "", errors.New("ciphertext too short")
	}

	iv := ciphertextBytes[:aes.BlockSize]
	ciphertextBytes = ciphertextBytes[aes.BlockSize:]

	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertextBytes, ciphertextBytes)

	return string(ciphertextBytes), nil
}
