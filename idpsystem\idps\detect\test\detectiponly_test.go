package test

import (
	"fmt"
	"mnms/idpsystem/idps/detect"
	"testing"

	"github.com/google/gonids"
)

func TestIponlyV4(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
	}{
		{
			rule: []string{
				`alert tcp *********** any -> any any (msg:"Testing src ip (sid 1)"; sid:1;)`,
				`alert tcp any any -> *********** any (msg:"Testing dst ip (sid 2)"; sid:2;)`,
				`alert tcp ***********/24 any -> ***********/16 any (msg:"Testing src/dst ip (sid 7)"; content:"Hi all";sid:7;)`,
				`alert tcp *********** any -> *********** any (msg:"Testing src/dst ip (sid 3)"; sid:3;)`,
				`alert tcp *********** any -> *********** any (msg:"Testing src/dst ip (sid 4)"; sid:4;)`,
				`alert tcp ***********/24 any -> any any (msg:"Testing src/dst ip (sid 5)"; sid:5;)`,
				`alert tcp any any -> ***********/16 any (msg:"Testing src/dst ip (sid 6)"; sid:6;)`,
			},
			existed:    []int{1, 2, 3, 4, 5, 6, 7},
			notexisted: []int{},
			packet:     []byte("Hi all!"),
			ip:         []string{"***********", "***********"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
		{
			rule: []string{
				`alert tcp *********** any -> any any (msg:"Testing src ip (sid 1)"; sid:1;)`,
				`alert tcp any any -> *********** any (msg:"Testing dst ip (sid 2)"; sid:2;)`,
				`alert tcp *********** any -> *********** any (msg:"Testing src/dst ip (sid 3)"; sid:3;)`,
				`alert tcp *********** any -> *********** any (msg:"Testing src/dst ip (sid 4)"; sid:4;)`,
				`alert tcp ***********/24 any -> any any (msg:"Testing src/dst ip (sid 5)"; sid:5;)`,
				`alert tcp any any -> ***********/16 any (msg:"Testing src/dst ip (sid 6)"; sid:6;)`,
				`alert tcp ***********/24 any -> ***********/16 any (msg:"Testing src/dst ip (sid 7)"; content:"Hi all";sid:7;)`,
			},
			existed:    []int{},
			notexisted: []int{1, 2, 3, 4, 5, 6, 7},
			packet:     []byte("Hi all!"),
			ip:         []string{"**********", "*************"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
		{
			rule: []string{
				"alert tcp *********** any -> ***********/16 any (msg:\"Testing src/dst ip (sid 1)\"; sid:1;)",
				`alert tcp [***********,***********,***********] any -> *********** any (msg:"Testing src/dst ip (sid 2)"; sid:2;)`,
				`alert tcp [***********/24,!***********] any -> *********** any (msg:"Testing src/dst ip (sid 3)"; sid:3;)`,
				`alert tcp [*********/8,!***********/16,***********/24,!***********] any -> [***********/24,!***********] any (msg:"Testing src/dst ip (sid 4)"; sid:4;)`,
				`alert tcp any any -> !*********** any (msg:"Testing src/dst ip (sid 5)"; sid:5;)`,
				`alert tcp any any -> [***********/16,!***********/24,***********] any (msg:"Testing src/dst ip (sid 6)"; sid:6;)`,
				`alert tcp [************/24,***********,*************,**************,*************,*************,*************,*************,*************,**************,**************,**************,**************,**************,*************,**************,**************,************,***********,*************,*************] any -> *********** any (msg:"ET RBN Known Russian Business Network IP TCP - BLOCKING (246)"; sid:7;)`,
			},
			existed:    []int{},
			notexisted: []int{1, 2, 3, 4, 5, 6, 7},
			packet:     []byte("Hi all!"),
			ip:         []string{"***********", "***********"},
			port:       []uint16{10, 20},
			link:       newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TcpPacketWithFlag(test.ip[0], test.port[0], test.ip[1], test.port[1], TcpTestData{}, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}

func TestIponlyV6(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
	}{

		{
			rule: []string{
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> any any (msg:\"Testing src ip (sid 1)\"; sid:1;)",
				"alert tcp any any -> 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562 any (msg:\"Testing dst ip (sid 2)\"; sid:2;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562 any (msg:\"Testing src/dst ip (sid 3)\"; sid:3;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> 3FFE:FFFF:7654:FEDA:1245:BA98:3210:0/96 any (msg:\"Testing src/dst ip (sid 4)\"; sid:4;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:0:0:0:0/64 any -> any any (msg:\"Testing src/dst ip (sid 5)\"; sid:5;)",
				"alert tcp any any -> 3FFE:FFFF:7654:FEDA:0:0:0:0/64 any (msg:\"Testing src/dst ip (sid 6)\"; sid:6;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:0:0:0:0/64 any -> 3FFE:FFFF:7654:FEDA:0:0:0:0/64 any (msg:\"Testing src/dst ip (sid 7)\"; content:\"Hi all\";sid:7;)",
			},
			existed:    []int{1, 2, 3, 4, 5, 6, 7},
			notexisted: []int{},
			packet:     []byte("Hi all!"),
			ip:         []string{"3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565", "3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562"},
			port:       []uint16{41420, 80},
			link:       newTestNetLinkerID(),
		},
		{
			rule: []string{
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> any any (msg:\"Testing src ip (sid 1)\"; sid:1;)",
				"alert tcp any any -> 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562 any (msg:\"Testing dst ip (sid 2)\"; sid:2;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562 any (msg:\"Testing src/dst ip (sid 3)\"; sid:3;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565 any -> !3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562/96 any (msg:\"Testing src/dst ip (sid 4)\"; sid:4;)",
				"alert tcp !3FFE:FFFF:7654:FEDA:0:0:0:0/64 any -> any any (msg:\"Testing src/dst ip (sid 5)\"; sid:5;)",
				"alert tcp any any -> !3FFE:FFFF:7654:FEDA:0:0:0:0/64 any (msg:\"Testing src/dst ip (sid 6)\"; sid:6;)",
				"alert tcp 3FFE:FFFF:7654:FEDA:0:0:0:0/64 any -> 3FFE:FFFF:7654:FEDB:0:0:0:0/64 any (msg:\"Testing src/dst ip (sid 7)\"; content:\"Hi all\";sid:7;)",
			},
			existed:    []int{},
			notexisted: []int{1, 2, 3, 4, 5, 6, 7},
			packet:     []byte("Hi all!"),
			ip:         []string{"3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562", "3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565"},
			port:       []uint16{41420, 80},
			link:       newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := TCPPacketIPv6(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}

func TestIponlyMixed(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip1        []string
		port1      []uint16
		ip2        []string
		port2      []uint16
		link       *testNetLinkerID
	}{
		{
			rule: []string{
				"alert tcp 3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565,*********** any -> 3FFE:FFFF:7654:FEDA:0:0:0:0/64,*********** any (msg:\"Testing src/dst ip (sid 1)\"; sid:1;)",
				"alert tcp [***********,3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565,***********,***********,!***********/24] any -> [3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,***********/24] any (msg:\"Testing src/dst ip (sid 2)\"; sid:2;)",
				"alert tcp [3FFE:FFFF:7654:FEDA:0:0:0:0/64,!3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,***********] any -> [3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,***********] any (msg:\"Testing src/dst ip (sid 3)\"; sid:3;)",
				"alert tcp [3FFE:FFFF:0:0:0:0:0:0/32,!3FFE:FFFF:7654:FEDA:0:0:0:0/64,3FFE:FFFF:7654:FEDA:0:0:0:0/64,!3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,***********] any -> [3FFE:FFFF:7654:FEDA:0:0:0:0/64,***********/24,!3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565] any (msg:\"Testing src/dst ip (sid 4)\"; sid:4;)",
				"alert tcp any any -> [!3FBE:FFFF:7654:FEDA:1245:BA98:3210:4565,!**********] any (msg:\"Testing src/dst ip (sid 5)\"; sid:5;)",
				"alert tcp any any -> [3FFE:FFFF:7654:FEDA:0:0:0:0/64,!3FFE:FFFF:7654:FEDA:0:0:0:0/64,3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,***********] any (msg:\"Testing src/dst ip (sid 6)\"; sid:6;)",
				"alert tcp [************/24,3FFE:FFFF:7654:FEDA:1245:BA98:3210:4565,***********,*************,**************,*************,*************,*************,*************,*************,**************,**************,**************,**************,**************,*************,**************,**************,************,***********,*************,*************] any -> [3FFE:FFFF:7654:FEDA:1245:BA98:3210:4562,*********/8] any (msg:\"ET RBN Known Russian Business Network IP TCP - BLOCKING (246)\"; sid:7;)",
			},
			existed:    []int{},
			notexisted: []int{1, 2, 3, 4, 5, 6, 7},
			packet:     []byte("Hi all!"),
			ip1:        []string{"3FBE:FFFF:7654:FEDA:1245:BA98:3210:4562", "3FBE:FFFF:7654:FEDA:1245:BA98:3210:4565"},
			port1:      []uint16{41424, 80},
			ip2:        []string{"**********", "**********"},
			port2:      []uint16{41424, 80},

			link: newTestNetLinkerID(),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index+1), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p1, err := TCPPacketIPv6(test.ip1[0], test.port1[0], test.ip1[1], test.port1[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			p2, err := TCPPacketIPV4(test.ip2[0], test.port2[0], test.ip2[1], test.port2[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p1)
			d.DetectPacket(test.link, p2)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Errorf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Errorf("id:%v should not found", v)
				}
			}

		})
	}
}
