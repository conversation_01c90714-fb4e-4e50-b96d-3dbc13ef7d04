package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"

	"github.com/qeof/q"
)

type Link struct {
	Source      string `json:"source"`      // source mac
	Target      string `json:"target"`      // target mac
	SourcePort  string `json:"sourcePort"`  // source port
	TargetPort  string `json:"targetPort"`  // target port
	Edge        string `json:"edge"`        // edge data
	BlockedPort string `json:"blockedPort"` // blocked port
	LinkType    string `json:"linkType"`    // link type (agent,manual, etc)
}

type Topology struct {
	Id          string `json:"id"`          // id for node
	IpAddress   string `json:"ipAddress"`   // IpAddress for node
	ModelName   string `json:"modelname"`   // ModelName for node
	Services    string `json:"services"`    // Network service name
	LastUpdated int    `json:"lastUpdated"` // Network service name
	LinkData    []Link `json:"linkData"`    // original topology link data
	TopoType    string `json:"topoType"`    // type of topology
}

func PublishTopology(topologyData Topology) error {
	// send all topology info to root
	if QC.RootURL == "" {
		return fmt.Errorf("skip publishing devices, no root")
	}
	topologyData.Services = QC.Name
	topologyData.TopoType = "agent"
	for i, link := range topologyData.LinkData {
		edgeData := createEdge(link.Source, link.Target)
		topologyData.LinkData[i].Edge = edgeData
		topologyData.LinkData[i].LinkType = "agent"
	}

	jsonBytes, err := json.Marshal(topologyData)
	if err != nil {
		q.Q(err)
		return err
	}
	resp, err := PostWithToken(QC.RootURL+"/api/v1/topology", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		q.Q(err, QC.RootURL)
	}
	if resp != nil {
		res := make(map[string]interface{})
		_ = json.NewDecoder(resp.Body).Decode(&res)
		// q.Q(res)
		// save close here
		defer resp.Body.Close()
	}
	return nil
}

func InsertTopology(topoDesc Topology) error {
	// add all device as node to improve the topology
	AddAllDeviceAsNode()

	// INFO: not checking equal or exist because updating the latest timestamp so if divice offline we can remove that topology
	// INFO: this is specially for when agent client will not send toplogy information if device offline
	topoDesc.LastUpdated = int(time.Now().Unix())
	// insert agent topology as it is
	if topoDesc.TopoType == "agent" {
		QC.TopologyMutex.Lock()
		QC.TopologyData[topoDesc.Id] = topoDesc
		QC.TopologyMutex.Unlock()
		return nil
	}
	// insert manual topology link
	dev, err := FindDev(topoDesc.Id)
	if err != nil || dev == nil {
		return fmt.Errorf("error: not found device %s", topoDesc.Id)
	}
	QC.TopologyMutex.Lock()
	topodata, ok := QC.TopologyData[topoDesc.Id]
	QC.TopologyMutex.Unlock()
	// check if device is in topology, if not create new topology
	if !ok {
		// create new topology
		topodata = Topology{
			Id:          topoDesc.Id,
			IpAddress:   dev.IPAddress,
			ModelName:   dev.ModelName,
			LastUpdated: int(time.Now().Unix()),
			Services:    dev.ScannedBy,
			TopoType:    "manual",
			LinkData:    topoDesc.LinkData,
		}
	}
	topodata.TopoType = "manual"
	topodata.LinkData = topoDesc.LinkData
	QC.TopologyMutex.Lock()
	QC.TopologyData[topodata.Id] = topodata
	QC.TopologyMutex.Unlock()
	return nil
}

func AddAllDeviceAsNode() {
	for _, dev := range QC.DevData {
		if dev.Mac != "11-22-33-44-55-66" {
			QC.TopologyMutex.Lock()
			topodata, ok := QC.TopologyData[dev.Mac]
			// check if device is in topology, if not create new node
			if !ok {
				// create new node
				topodata = Topology{
					Id:          dev.Mac,
					IpAddress:   dev.IPAddress,
					ModelName:   dev.ModelName,
					LastUpdated: int(time.Now().Unix()),
					Services:    dev.ScannedBy,
				}
			}
			QC.TopologyData[topodata.Id] = topodata
			QC.TopologyMutex.Unlock()
		}
	}
}

// Helper function to create edge identifier
func createEdge(source, target string) string {
	if source < target {
		return source + "_" + target
	}
	return target + "_" + source
}
