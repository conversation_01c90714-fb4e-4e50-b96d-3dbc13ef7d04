package idpsystem

import (
	"sync"
	"time"
)

func newCategoryInfo() *categoryInfo {
	c := &categoryInfo{m: new(sync.RWMutex)}
	currentTime := time.Now()
	c.Info = map[uint32]RuleInfo{}
	c.Time = currentTime.Format(time.RFC3339)
	c.Enable = true
	return c
}

type categoryInfo struct {
	m      *sync.RWMutex       `json:"-"`
	Time   string              `json:"created_time"`
	Name   Category            `json:"name"`
	Enable bool                `json:"enablle"`
	Info   map[uint32]RuleInfo `json:"-"`
}

func (c *categoryInfo) addInfo(r RuleInfo) {
	c.m.Lock()
	defer c.m.Unlock()
	c.Info[r.Sid] = r
}

func (c *categoryInfo) getInfos() []RuleInfo {
	c.m.RLock()
	defer c.m.RUnlock()
	r := make([]RuleInfo, 0, len(c.Info))
	for _, v := range c.Info {
		r = append(r, v)
	}
	return r
}

func (c *categoryInfo) getInfo(sid uint32) (RuleInfo, error) {
	c.m.RLock()
	defer c.m.RUnlock()
	if v, ok := c.Info[sid]; ok {
		return v, nil
	} else {
		return RuleInfo{}, ErrorNoSid
	}
}
