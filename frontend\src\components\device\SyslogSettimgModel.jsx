import { Checkbox, Form, Input, InputNumber, Modal, Select } from "antd";
import React from "react";

const ipPattern =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

const SyslogSettimgModel = ({ open, onCancel, onOk, loading }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="Devices Syslog setting"
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_syslogSetting">
        <Form.Item name="logToFlash" valuePropName="checked">
          <Checkbox>Log to Flash</Checkbox>
        </Form.Item>
        <Form.Item
          name="logLevel"
          label="Log Level"
          required
          rules={[{ required: true, message: "Please select the log level !" }]}
        >
          <Select>
            <Select.Option value={0}>0: (LOG EMERG)</Select.Option>
            <Select.Option value={1}>1: (LOG_ALERT)</Select.Option>
            <Select.Option value={2}>2: (LOG_CRIT)</Select.Option>
            <Select.Option value={3}>3: (LOG_ERR)</Select.Option>
            <Select.Option value={4}>4: (LOG_WARNING)</Select.Option>
            <Select.Option value={5}>5: (LOG_NOTICE)</Select.Option>
            <Select.Option value={6}>6: (LOG_INFO)</Select.Option>
            <Select.Option value={7}>7: (LOG_DEBUG)</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="logToServer" valuePropName="checked">
          <Checkbox>Log to Server</Checkbox>
        </Form.Item>
        <Form.Item
          name="serverIP"
          label="Server IP"
          required
          rules={[
            {
              required: true,
              message: "Please input the server ip !",
            },
            {
              pattern: ipPattern,
              message: "input should be ip address!",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="serverPort"
          label="Server Service Port"
          required
          rules={[{ required: true, message: "Please input the port !" }]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default SyslogSettimgModel;
