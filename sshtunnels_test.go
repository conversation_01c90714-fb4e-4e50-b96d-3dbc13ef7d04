package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"golang.org/x/crypto/ssh"
)

func Test_SshServer(t *testing.T) {
	port, err := randomOpenPort()
	if err != nil {
		t.Error(err)
	}
	QC.SshServerPort = port
	t.Log("SshServerPort:", QC.SshServerPort)
	go func() {
		RunSSHServer()
	}()
	// time.Sleep(5 * time.Second)
	// Retry mechanism with a timeout
	timeout := time.After(30 * time.Second) // Total timeout duration
	tick := time.Tick(5 * time.Second)      // Retry interval

	connected := false
	for {
		select {
		case <-timeout:
			t.Log("Timeout")
			return
		case <-tick:
			dialTest, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", QC.SshServerPort))
			if err != nil {
				t.Log("Failed to connect to the SSH server:", err)
				continue
			}
			dialTest.Close()
			connected = true
		}
		if connected {
			break
		}
	}

	// add fake device if not exists
	devinfo := DevInfo{Mac: "11-22-33-44-55-66"}
	if _, ok := QC.DevData[devinfo.Mac]; !ok {
		QC.DevData[devinfo.Mac] = devinfo
	}

	randPort, err := randomOpenPort()
	if err != nil {
		t.Error(err)
		return
	}
	listenPort := randPort
	randPort, err = randomOpenPort()
	if err != nil {
		t.Error(err)
		return
	}
	remotePort := randPort

	var sshConnection ssh.Conn
	// Fake SSH client to connect to the server
	go func() {
		// SSH client configuration, no auth, with username only
		config := &ssh.ClientConfig{
			User:            devinfo.Mac,
			HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		}

		// Dial the SSH server normally to check connectivity
		// Like ssh -R 8081:localhost:18081 id@root-hostname -p 6422
		address := fmt.Sprintf("localhost:%d", QC.SshServerPort)
		c, err := net.Dial("tcp", address)
		if err != nil {
			t.Errorf("Failed to dial server: %v", err)
			return
		}
		sshConn, chans, reqs, err := ssh.NewClientConn(c, address, config)
		if err != nil {
			t.Errorf("Failed to create client connection: %v", err)
			return
		}
		t.Log("Connected to the ssh server")
		sshConnection = sshConn

		// Request port forwarding (reverse tunnel)
		req := struct {
			Addr  string
			Rport uint32
		}{
			Addr:  "0.0.0.0",
			Rport: uint32(listenPort),
		}

		ok, _, err := sshConn.SendRequest("tcpip-forward", true, ssh.Marshal(&req))
		if err != nil || !ok {
			t.Errorf("Failed to request port forwarding: %v", err)
			return
		}
		t.Log("Requested port forwarding on port", listenPort)

		go func() {
			for req := range reqs {
				t.Log("Request:", req.Type)
			}
		}()

		go func() {
			for newChannel := range chans {
				t.Log("New channel:", newChannel.ChannelType())
				if newChannel.ChannelType() != "forwarded-tcpip" {
					newChannel.Reject(ssh.UnknownChannelType, "unknown channel type")
					continue
				}

				channel, _, err := newChannel.Accept()
				if err != nil {
					t.Errorf("Failed to accept channel: %v", err)
					return
				}
				t.Log("Accepted channel")

				go func() {
					t.Log("Dialing remote port", remotePort)
					tcpConn, err := net.Dial("tcp", fmt.Sprintf("localhost:%d", remotePort))
					if err != nil {
						t.Errorf("Failed to dial remote port: %v", err)
						return
					}
					t.Log("Connected to remote port")

					go func() {
						_, err = io.Copy(tcpConn, channel)
						if err != nil {
							tcpConn.Close()
							channel.Close()
							t.Errorf("Failed to copy data: %v", err)
						}
					}()
					go func() {
						_, err = io.Copy(channel, tcpConn)
						if err != nil {
							tcpConn.Close()
							channel.Close()
							t.Errorf("Failed to copy data: %v", err)
						}
					}()
				}()
			}
		}()
	}()

	// fake http server to listen on the port and respond to the client
	t.Log("Starting HTTP server on port", remotePort)
	server := HttpServer(remotePort)
	defer server.Close()

	// Give the client some time to connect and interact with the server
	time.Sleep(10 * time.Second)

	// Send request to the listening port
	t.Log("Sending request to listen port", listenPort)
	resp, err := http.Get(fmt.Sprintf("http://localhost:%d/", listenPort))
	if err != nil {
		t.Errorf("Failed to send request to remote port: %v", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Errorf("Failed to read response body: %v", err)
		return
	}

	t.Log("Response:", string(body))
	if string(body) != "Hello, world!" {
		t.Log("Unexpected response: ", body)
	}

	// check ssh connection
	t.Log(QC.SshConnections)
	if _, ok := QC.SshConnections[listenPort]; !ok {
		t.Log("Failed to add ssh connection")
	}

	// close connections
	sshConnection.Close()
}

func Test_SSHHandler(t *testing.T) {
	t.Run("POST method", func(t *testing.T) {
		QC.IsRoot = true

		// send 5 requests
		autoRunResps := make([]SshDeviceAutoRunResponse, 0)
		for i := 0; i < 5; i++ {
			fakeMac := fmt.Sprintf("11-22-33-44-55-%d%d", i, i)
			reqBody := map[string]string{
				"mac":       fakeMac,
				"nms_url":   "http://localhost:27183",
				"root_host": "localhost",
			}
			jsonReqBody, _ := json.Marshal(reqBody)
			req, err := http.NewRequest("POST", "/api/v1/ssh/tunnels", bytes.NewBuffer(jsonReqBody))
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			handler := http.HandlerFunc(HandleSshTunnels)
			handler.ServeHTTP(rr, req)

			if status := rr.Code; status != http.StatusOK {
				t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
			}

			var resp SshDeviceAutoRunResponse
			err = json.Unmarshal(rr.Body.Bytes(), &resp)
			if err != nil {
				t.Fatal(err)
			}
			t.Log(resp)
			autoRunResps = append(autoRunResps, resp)
		}

		// check the response data, port is duplicated or not
		ports := make(map[int]bool)
		for _, resp := range autoRunResps {
			if _, ok := ports[resp.ListenPort]; ok {
				t.Fatal("Port is duplicated")
			}
			ports[resp.ListenPort] = true
		}
	})
}
