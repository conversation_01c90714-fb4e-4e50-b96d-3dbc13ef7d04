-- *****************************************************************
-- DHCP6-CLIENT-MIB:  
-- ****************************************************************

MGMT-DHCP6-CLIENT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Counter64 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtDhcp6ClientMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the DHCPv6 Client MIB"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 126 }


mgmtDhcp6ClientMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMib 1 }

mgmtDhcp6ClientCapabilities OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibObjects 1 }

mgmtDhcp6ClientCapabilitiesMaxNumberOfInterfaces OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of DHCPv6 client interfaces supported by the device."
    ::= { mgmtDhcp6ClientCapabilities 1 }

mgmtDhcp6ClientConfig OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibObjects 2 }

mgmtDhcp6ClientConfigInterface OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientConfig 1 }

mgmtDhcp6ClientConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6ClientConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing DHCPv6 client interface entries."
    ::= { mgmtDhcp6ClientConfigInterface 1 }

mgmtDhcp6ClientConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6ClientConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtDhcp6ClientConfigInterfaceIfIndex }
    ::= { mgmtDhcp6ClientConfigInterfaceTable 1 }

MGMTDhcp6ClientConfigInterfaceEntry ::= SEQUENCE {
    mgmtDhcp6ClientConfigInterfaceIfIndex      MGMTInterfaceIndex,
    mgmtDhcp6ClientConfigInterfaceRapidCommit  TruthValue,
    mgmtDhcp6ClientConfigInterfaceAction       MGMTRowEditorState
}

mgmtDhcp6ClientConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtDhcp6ClientConfigInterfaceEntry 1 }

mgmtDhcp6ClientConfigInterfaceRapidCommit OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the rapid-commit capability in DHCPv6 message exchanges."
    ::= { mgmtDhcp6ClientConfigInterfaceEntry 2 }

mgmtDhcp6ClientConfigInterfaceAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcp6ClientConfigInterfaceEntry 100 }

mgmtDhcp6ClientConfigInterfaceTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientConfigInterface 2 }

mgmtDhcp6ClientConfigInterfaceTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtDhcp6ClientConfigInterfaceTableRowEditor 1 }

mgmtDhcp6ClientConfigInterfaceTableRowEditorRapidCommit OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the rapid-commit capability in DHCPv6 message exchanges."
    ::= { mgmtDhcp6ClientConfigInterfaceTableRowEditor 2 }

mgmtDhcp6ClientConfigInterfaceTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtDhcp6ClientConfigInterfaceTableRowEditor 100 }

mgmtDhcp6ClientStatus OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibObjects 3 }

mgmtDhcp6ClientStatusInterface OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientStatus 1 }

mgmtDhcp6ClientStatusInterfaceInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6ClientStatusInterfaceInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying per DHCPv6 client interface information
         derived from DHCPv6 server."
    ::= { mgmtDhcp6ClientStatusInterface 1 }

mgmtDhcp6ClientStatusInterfaceInformationEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6ClientStatusInterfaceInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtDhcp6ClientStatusInterfaceInformationIfIndex }
    ::= { mgmtDhcp6ClientStatusInterfaceInformationTable 1 }

MGMTDhcp6ClientStatusInterfaceInformationEntry ::= SEQUENCE {
    mgmtDhcp6ClientStatusInterfaceInformationIfIndex            MGMTInterfaceIndex,
    mgmtDhcp6ClientStatusInterfaceInformationAddress            InetAddressIPv6,
    mgmtDhcp6ClientStatusInterfaceInformationServerAddress      InetAddressIPv6,
    mgmtDhcp6ClientStatusInterfaceInformationDnsServerAddress   InetAddressIPv6,
    mgmtDhcp6ClientStatusInterfaceInformationPreferredLifetime  Counter64,
    mgmtDhcp6ClientStatusInterfaceInformationValidLifetime      Counter64,
    mgmtDhcp6ClientStatusInterfaceInformationT1                 Counter64,
    mgmtDhcp6ClientStatusInterfaceInformationT2                 Counter64
}

mgmtDhcp6ClientStatusInterfaceInformationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 1 }

mgmtDhcp6ClientStatusInterfaceInformationAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IPv6 address determined from DHCPv6 for this interface."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 2 }

mgmtDhcp6ClientStatusInterfaceInformationServerAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IPv6 address of the bounded DHCPv6 server for this interface."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 3 }

mgmtDhcp6ClientStatusInterfaceInformationDnsServerAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The DNS server address retrieved from DHCPv6."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 4 }

mgmtDhcp6ClientStatusInterfaceInformationPreferredLifetime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The recorded Preferred-Lifetime for the DHCPv6 client interface. From
         RFC-4862 and RFC-3315: It is the preferred lifetime for the IPv6
         address, expressed in units of seconds. When the preferred lifetime
         expires, the address becomes deprecated."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 5 }

mgmtDhcp6ClientStatusInterfaceInformationValidLifetime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The recorded Valid-Lifetime for the DHCPv6 client interface. From
         RFC-4862 and RFC-3315: It is the valid lifetime for the IPv6 address,
         expressed in units of seconds. The valid lifetime must be greater than
         or equal to the preferred lifetime. When the valid lifetime expires,
         the address becomes invalid."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 6 }

mgmtDhcp6ClientStatusInterfaceInformationT1 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The recorded T1 for the DHCPv6 client interface. From RFC-3315: It is
         the time at which the client contacts the server from which the address
         is obtained to extend the lifetimes of the non-temporary address
         assigned; T1 is a time duration relative to the current time expressed
         in units of seconds."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 7 }

mgmtDhcp6ClientStatusInterfaceInformationT2 OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The recorded T2 for the DHCPv6 client interface. From RFC-3315: It is
         the time at which the client contacts any available server to extend
         the lifetimes of the non-temporary address assigned; T2 is a time
         duration relative to the current time expressed in units of seconds."
    ::= { mgmtDhcp6ClientStatusInterfaceInformationEntry 8 }

mgmtDhcp6ClientStatusInterfaceStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6ClientStatusInterfaceStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying per DHCPv6 client interface control
         message statistics in DHCPv6 message exchanges."
    ::= { mgmtDhcp6ClientStatusInterface 2 }

mgmtDhcp6ClientStatusInterfaceStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6ClientStatusInterfaceStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of counters."
    INDEX       { mgmtDhcp6ClientStatusInterfaceStatisticsIfIndex }
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsTable 1 }

MGMTDhcp6ClientStatusInterfaceStatisticsEntry ::= SEQUENCE {
    mgmtDhcp6ClientStatusInterfaceStatisticsIfIndex        MGMTInterfaceIndex,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxSolicit      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxRequest      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxConfirm      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxRenew        Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxRebind       Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxRelease      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxDecline      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxInfoRequest  Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxError        Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxDrop         Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsTxUnknown      Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxAdvertise    Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxReply        Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxReconfigure  Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxError        Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxDrop         Unsigned32,
    mgmtDhcp6ClientStatusInterfaceStatisticsRxUnknown      Unsigned32
}

mgmtDhcp6ClientStatusInterfaceStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 1 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxSolicit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 SOLICIT message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 2 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 REQUEST message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 3 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxConfirm OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 CONFIRM message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 4 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxRenew OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 RENEW message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 5 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxRebind OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 REBIND message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 6 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxRelease OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 RELEASE message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 7 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxDecline OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 DECLINE message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 8 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxInfoRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 INFORMATION-REQUEST message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 9 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxError OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 message error count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 10 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxDrop OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 message drop count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 11 }

mgmtDhcp6ClientStatusInterfaceStatisticsTxUnknown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Transmitted DHCPv6 unknown message type count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 12 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxAdvertise OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 ADVERTISE message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 13 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxReply OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 REPLY message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 14 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxReconfigure OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 RECONFIGURE message count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 15 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxError OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 message error count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 16 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxDrop OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 message drop count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 17 }

mgmtDhcp6ClientStatusInterfaceStatisticsRxUnknown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received DHCPv6 unknown message type count."
    ::= { mgmtDhcp6ClientStatusInterfaceStatisticsEntry 18 }

mgmtDhcp6ClientControl OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibObjects 4 }

mgmtDhcp6ClientControlInterface OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientControl 1 }

mgmtDhcp6ClientControlInterfaceRestart OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientControlInterface 1 }

mgmtDhcp6ClientControlInterfaceRestartIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtDhcp6ClientControlInterfaceRestart 1 }

mgmtDhcp6ClientMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMib 2 }

mgmtDhcp6ClientMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibConformance 1 }

mgmtDhcp6ClientMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcp6ClientMibConformance 2 }

mgmtDhcp6ClientCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6ClientCapabilitiesMaxNumberOfInterfaces }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 1 }

mgmtDhcp6ClientConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6ClientConfigInterfaceIfIndex,
                  mgmtDhcp6ClientConfigInterfaceRapidCommit,
                  mgmtDhcp6ClientConfigInterfaceAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 2 }

mgmtDhcp6ClientConfigInterfaceTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtDhcp6ClientConfigInterfaceTableRowEditorIfIndex,
                  mgmtDhcp6ClientConfigInterfaceTableRowEditorRapidCommit,
                  mgmtDhcp6ClientConfigInterfaceTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 3 }

mgmtDhcp6ClientStatusInterfaceInformationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6ClientStatusInterfaceInformationIfIndex,
                  mgmtDhcp6ClientStatusInterfaceInformationAddress,
                  mgmtDhcp6ClientStatusInterfaceInformationServerAddress,
                  mgmtDhcp6ClientStatusInterfaceInformationDnsServerAddress,
                  mgmtDhcp6ClientStatusInterfaceInformationPreferredLifetime,
                  mgmtDhcp6ClientStatusInterfaceInformationValidLifetime,
                  mgmtDhcp6ClientStatusInterfaceInformationT1,
                  mgmtDhcp6ClientStatusInterfaceInformationT2 }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 4 }

mgmtDhcp6ClientStatusInterfaceStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6ClientStatusInterfaceStatisticsIfIndex,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxSolicit,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxRequest,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxConfirm,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxRenew,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxRebind,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxRelease,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxDecline,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxInfoRequest,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxError,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxDrop,
                  mgmtDhcp6ClientStatusInterfaceStatisticsTxUnknown,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxAdvertise,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxReply,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxReconfigure,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxError,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxDrop,
                  mgmtDhcp6ClientStatusInterfaceStatisticsRxUnknown }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 5 }

mgmtDhcp6ClientControlInterfaceRestartInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6ClientControlInterfaceRestartIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6ClientMibGroups 6 }

mgmtDhcp6ClientMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcp6ClientCapabilitiesInfoGroup,
                       mgmtDhcp6ClientConfigInterfaceTableInfoGroup,
                       mgmtDhcp6ClientConfigInterfaceTableRowEditorInfoGroup,
                       mgmtDhcp6ClientStatusInterfaceInformationTableInfoGroup,
                       mgmtDhcp6ClientStatusInterfaceStatisticsTableInfoGroup,
                       mgmtDhcp6ClientControlInterfaceRestartInfoGroup }

    ::= { mgmtDhcp6ClientMibCompliances 1 }

END
