import { configureStore } from '@reduxjs/toolkit';
import twoFactorAuthSlice, {
  generatSecretKey,
  clearState,
  logoutUser,
  clearAuthData,
} from '../features/auth/twoFactorAuthSlice';
import { beforeEach, describe, expect, it } from 'vitest';

describe('twoFactorAuthSlice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        twoFactorAuth: twoFactorAuthSlice.reducer,
      },
    });
  });

  it('should clear state when clearState action is dispatched', () => {
    store.dispatch(clearState());

    const state = store.getState().twoFactorAuth;
    expect(state.isError).toBe(false);
    expect(state.isSuccess).toBe(false);
    expect(state.isFetching).toBe(false);
  });

  it('should clear auth data when clearAuthData action is dispatched', () => {
    store.dispatch(clearAuthData());

    const state = store.getState().twoFactorAuth;
    expect(state.user).toBe('');
    expect(state.role).toBe('');
    expect(state.isError).toBe(false);
    expect(state.isSuccess).toBe(false);
    expect(state.isFetching).toBe(false);
  });

  it('should clear user data when logoutUser action is dispatched', () => {
    store.dispatch(logoutUser());

    const state = store.getState().twoFactorAuth;
    expect(state.account).toBe('');
    expect(state.issuer).toBe('');
    expect(state.secret).toBe('');
    expect(state.user).toBe('');
    expect(state.isSuccess).toBe(false);
    expect(state.isFetching).toBe(false);
    expect(state.errorMessage).toBe('');
  });

  it('should handle logoutUser', () => {
    const initialState = {
      account: 'accountValue',
      issuer: 'issuerValue',
      secret: 'secretValue',
      user: 'userValue',
      isSuccess: true,
      isFetching: true,
      errorMessage: 'errorValue',
    };
    const nextState = twoFactorAuthSlice.reducer(initialState, logoutUser());
    expect(nextState).toEqual({
      account: '',
      issuer: '',
      secret: '',
      user: '',
      isSuccess: false,
      isFetching: false,
      errorMessage: '',
    });
  });
  it("should handle generatSecretKey.fulfilled", () => {
    const payload = {
      account: "account123",
      issuer: "issuer123",
      secret: "secret123",
      user: "user123",
    };

    store.dispatch(generatSecretKey.fulfilled(payload));

    const state = store.getState().twoFactorAuth;
    expect(state.account).toEqual(payload.account);
    expect(state.issuer).toEqual(payload.issuer);
    expect(state.secret).toEqual(payload.secret);
    expect(state.user).toEqual(payload.user);
    expect(state.isFetching).toBe(false);
    expect(state.isSuccess).toBe(true);
  });

});
