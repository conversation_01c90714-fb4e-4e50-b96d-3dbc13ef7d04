package icmp

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewV4hdr() *v4hdr {
	return &v4hdr{layer: protocol.NewIcmpParser()}
}

type v4hdr struct {
	mpm.Algorithm
	layer *protocol.IcmpParser
}

func (v *v4hdr) RetrieveBffer(packet gopacket.Packet) []byte {
	return v.getData(packet)
}

// mpm build
func (v *v4hdr) Build() error {
	if v.Algorithm != nil {
		err := v.Algorithm.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

// mpm MatcheIds
func (v *v4hdr) MatchIds(packet gopacket.Packet) []int {
	if v.Algorithm == nil {
		return []int{}
	}
	p := v.getData(packet)
	if p == nil {
		return []int{}
	}
	return v.Algorithm.MatcheIds(p)
}

// // mpm AddContent
func (v *v4hdr) AddContent(contents mpm.Content) error {
	if v.Algorithm == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		v.Algorithm = a
	}
	return v.Algorithm.AddContent(contents)
}

func (v *v4hdr) getData(packet gopacket.Packet) []byte {
	b := v.layer.Parse(packet)
	if !b {
		return nil
	}
	proto, lay, err := v.layer.GetIcmp()
	if err != nil {
		return nil
	}
	if proto == layers.IPProtocolICMPv4 {
		return lay.LayerContents()
	}
	return nil
}
