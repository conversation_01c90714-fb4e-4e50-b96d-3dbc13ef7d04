# BBNIM idps guide

### Support OS

- Windows
- Unix (only test on ubuntu)

### Prepare

- #### Linux (only test on ubuntu)

  ```shell
  sudo apt update
  sudo apt-get install -y libpcap-dev
  sudo apt-get install -y libnetfilter-queue-dev
  sudo apt-get install -y libhyperscan-dev
  ```

  idps use iptables-nfqueue

  path:./conf.yaml

  ```yaml
  env:
    queue-num: 0
  ```

  if run first or current path without conf.yaml

  the default queue-num is 0

  In case of the host situation, these are the two most simple `iptables` rules;

  ```sh
  iptables -A INPUT -j NFQUEUE --queue-num 0 --queue-bypass
  iptables -A OUTPUT -j NFQUEUE --queue-num 0 --queue-bypass
  ```

  you can set queue-num to 2000

  `iptables` rules command should be

  ```sh
  iptables -A INPUT -j NFQUEUE --queue-num 2000 --queue-bypass
  iptables -A OUTPUT -j NFQUEUE --queue-num 2000 --queue-bypass
  ```

  To erase all `iptables` rules, enter:

  ```sh
  sudo iptables -F
  ```

- #### Windows

  install bbnim_idps_release

### Note

#### unix

- ##### if internet can't get any of packets, please run make clean



## How to test

1. go to the path mnms\idpsystem\idps
2. run make test



**note**: it was not recommend run test by remote,because if any issue ,it could cause network disconnected

# Feature

- Every events will be stroed in local side. 

- we can use commnad  or  restful api  to catch event and info.

- counting  numbers of rule matched 

- events stored with interval 1 second.

  example :

  if  rule matched  with 10 times  in 1 second

  it will count 10 time and store 1 of event.

   





# Rule 

Refer suricata:
https://docs.suricata.io/en/latest/rules/payload-keywords.html

### Action

Valid actions are:

- alert - generate an alert.
- pass -  accept packet and generate alert.
- drop - drop packet and generate alert.



### Source and destination(Expression)

alert http <font color=#FF0000>$HOME_NET</font> any -> <font color=#FF0000>$EXTERNAL_NET</font> any 


| Example                     | Meaning                                  |
| --------------------------- | ---------------------------------------- |
| !*******                    | Every IP address but *******             |
| ![*******, *******]         | Every IP address but ******* and ******* |
| $HOME_NET                   | all ip of localhost                      |
| [$EXTERNAL_NET, !$HOME_NET] | EXTERNAL_NET and not HOME_NET            |
| [10.0.0.0/24, !********]    | 10.0.0.0/24 except for ********          |



### Ports (source and destination)

alert http $HOME_NET <font color=#FF0000>any</font> -> $EXTERNAL_NET <font color=#FF0000>any</font>


| Example       | Meaning                                |
| ------------- | -------------------------------------- |
| [80, 81, 82]  | port 80, 81 and 82                     |
| [80: 82]      | Range from 80 till 82                  |
| [1024: ]      | From 1024 till the highest port-number |
| !80           | Every port but 80                      |
| [80:100,!99]  | Range from 80 till 100 but 99 excluded |
| [1:80,![2,4]] | Range from 1-80, except ports 2 and 4  |

### Direction

```go
source -> destination
source <> destination  (both directions) or source <> destination  (both directions)
```


### Protocol

- tcp
- udp
- icmp
- ftp 
- tls (this includes ssl)
- smb
- dns
- ssh 
- smtp  
- imap 
- modbus 
- enip 
- nfs
- ntp
- dhcp
- snmp
- tftp
- sip    


### Payloadkey

- #### https://docs.suricata.io/en/latest/rules/payload-keywords.html




## Command

**All command will update info automatically**

### Rules import

```
 Usage :-cc [clientname] idps rules import [url]
            [url]: http://xxx
            [clientname]: client1
        Description:import rules to client
        Example :
        -cc client idps rules import http://xxx
```

### Rules Delete

```
 Usage : -cc [clientname]  idps rules delete [name]
             [name]: selftest_icmp
             [clientname]: client1
        Description:delete rules for client
        Example:
        -cc client1 idps rules delete  selftest_icmp
        -cc client1 idps rules delete  selftest_tcp
```




### Rules add

```
Usage :-cc [clientname] idps rules add [category] [rule]
           [clientname]: client1
           [category]:name
           [rule]:rule
        Description:add rules into category
        Example:
        -cc client1 idps rules add icmp_category drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:"icmpv4 selftest drop";sid:789;)
```

### Records delete

```
Usage :-cc [clientname] idps records delete 
           [clientname]: client1
			-f: file name of records(option)
			-d: date(format:2024-08-09)(option)
        Description:delete record
        
       	Example :
		Description: delete file name: alert.log data:2024-08-12
		-cc client1 idps records delete -f alert.log -d 2024-08-12

		Description: delete all of file on 2024-08-12
		-cc client1 idps records delete -d 2024-08-12

		Description: delete all of file
		-cc client1 idps records delete
```

### Records Search 

```
Usage :-cc [clientname] idps records search
           [clientname]: client1
			-f: file name of record
			-st: start time (format:2006-01-02-15:04)
			-et end time (format:2006-01-02-15:04)
        Description:search record
        Example:
        -cc client1 idps records search -f alert.log -st 2024-08-14-15:04 -et 2024-09-14-15:04
```







## Restful api

| Method | URL                                | parameters          | Description                      |
| ------ | ---------------------------------- | ------------------- | -------------------------------- |
| POST   | /api/v1/idps/report?client=client1 | client: client name | post  info to http server        |
| Get    | /api/v1/idps/report?client=client1 | client: client name | get the infos of specific client |
| Get    | /api/v1/idps/report                |                     | get the infos of all client      |







main data struct:

```go
type IdpsRePortInfo struct {
	StartTime  string         `json:"start_time"`
	Rules      []IdpsRule     `json:"rules"`
	Event      []EventMessage `json:"event"`
	RecordList RecordList     `json:"recordlist"`
    RulePacketsTotal []RulePacketsTotal `json:"rulepackets"`
}
```

detail 

```go
type IdpsRule struct {
	Time    string        `json:"created_time"`
	Name    string        `json:"name"`
	Content []RuleContent `json:"contents"`
}
type EventMessage struct {
	Id          uint32 `json:"id"`
	Timestamp   string `json:"timestamp"`
	Type        string `json:"type"`
	InInterface string `json:"inInterface"`
	Srcip       string `json:"srcip"`
	SrcPort     uint16 `json:"srcPort"`
	Destip      string `json:"destip"`
	DestPort    uint16 `json:"destPort"`
	Protocol    string `json:"protocol"`
	Description string `json:"description"`
	Rulename    string `json:"rulename"`
}
type RecordList struct {
	Year map[string]Month `json:"year,omitempty"`
}
type Month struct {
	Month map[string]Day `json:"month,omitempty"`
}
type Day struct {
	Day map[string][]FileInfo `json:"day,omitempty"`
}
type FileInfo struct {
	Name string `json:"name,omitempty"`
    Size int64  `json:"size,omitempty"` (byte)
}
type RulePacketsTotal struct {
	Sid       uint64 `json:"sid"`
	Name      string `json:"name"`
	Action    string `json:"action"`
	Counts    uint64 `json:"counts"`
	Timestamp string `json:"-"`
}
```



 **client with name** 

```json
 {
    "start_time": "2024-05-29 15:12:13",
    "rules": [
        {
            "created_time": "2024-05-29T15:12:13+08:00",
            "name": "atop_device",
            "contents": [
                {
                    "sid": 50000000,
                    "value": "alert udp any any <> any 55954 (msg:\"alert atop device from GWD\";sid:50000000;)"
                },
                {
                    "sid": 50000001,
                    "value": "alert tls any any <> any 56026 (msg:\"alert atop device from agent\";sid:50000001;)"
                }
            ]
        },
        {
            "created_time": "2024-05-29T15:17:05+08:00",
            "name": "dhcp-events",
            "contents": [
                {
                    "sid": 2227000,
                    "value": "alert dhcp any any -> any any (msg:\"SURICATA DHCP malformed options\";sid:2227000;)"
                },
                {
                    "sid": 2227001,
                    "value": "alert dhcp any any -> any any (msg:\"SURICATA DHCP truncated options\";sid:2227001;)"
                }
            ]
        },
        {
            "created_time": "2024-05-29T15:17:05+08:00",
            "name": "dns-events",
            "contents": [
                {
                    "sid": 2240004,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS Not a request\"; sid:2240004;)"
                },
                {
                    "sid": 2240005,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS Not a response\";sid:2240005;)"
                },
                {
                    "sid": 2240006,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS Z flag set\"; sid:2240006; rev:2;)"
                },
                {
                    "sid": 2240007,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS Invalid opcode\"; sid:2240007;)"
                },
                {
                    "sid": 2240002,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS malformed request data\";sid:2240002;)"
                },
                {
                    "sid": 2240003,
                    "value": "alert dns any any -> any any (msg:\"SURICATA DNS malformed response data\";sid:2240003;)"
                }
            ]
        },
        {
            "created_time": "2024-05-29T15:17:05+08:00",
            "name": "selftest_icmp",
            "contents": [
                {
                    "sid": 789,
                    "value": "drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:\"icmpv4 selftest drop\";sid:789;)"
                }
            ]
        },
        {
            "created_time": "2024-05-29T15:17:05+08:00",
            "name": "selftest_tls",
            "contents": [
                {
                    "sid": 123,
                    "value": "alert tls $EXTERNAL_NET any <> $HOME_NET any (msg:\"tls selftest alert\";sid:123;)"
                }
            ]
        }
    ],
    "event": null,
  	 "recordlist": [
            {
                "date": "2024-08-16",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 7653624
                    },
                    {
                        "name": "dns.log",
                        "size": 3373314
                    },
                    {
                        "name": "drop.log",
                        "size": 1956318
                    },
                    {
                        "name": "icmp.log",
                        "size": 1956318
                    },
                    {
                        "name": "tls.log",
                        "size": 4259878
                    },
                    {
                        "name": "udp.log",
                        "size": 20432
                    }
                ]
            },
            {
                "date": "2024-08-17",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 4904689
                    },
                    {
                        "name": "dns.log",
                        "size": 1734984
                    },
                    {
                        "name": "drop.log",
                        "size": 1892709
                    },
                    {
                        "name": "icmp.log",
                        "size": 1892709
                    },
                    {
                        "name": "tls.log",
                        "size": 3168521
                    },
                    {
                        "name": "udp.log",
                        "size": 1184
                    }
                ]
            },
            {
                "date": "2024-08-19",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 2387729
                    },
                    {
                        "name": "dns.log",
                        "size": 1606317
                    },
                    {
                        "name": "drop.log",
                        "size": 1746454
                    },
                    {
                        "name": "icmp.log",
                        "size": 1746454
                    },
                    {
                        "name": "tls.log",
                        "size": 781412
                    }
                ]
            },
            {
                "date": "2024-08-20",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4665099
                    },
                    {
                        "name": "icmp.log",
                        "size": 4665099
                    }
                ]
            },
            {
                "date": "2024-08-21",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4711179
                    },
                    {
                        "name": "icmp.log",
                        "size": 4711179
                    }
                ]
            },
            {
                "date": "2024-08-22",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4720759
                    }
                ]
            },
            {
                "date": "2024-08-23",
                "files": [
                    {
                        "name": "icmp.log",
                        "size": 3494007
                    }
                ]
            },
            {
                "date": "2024-08-29",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 3276
                    },
                    {
                        "name": "icmp.log",
                        "size": 9555
                    }
                ]
            }
        ],
        "rulepackets": [
            {
                "sid": 50000000,
                "name": "atop_device",
                "action": "alert",
                "counts": 20
            },
            {
                "sid": 50000001,
                "name": "atop_device",
                "action": "alert",
                "counts": 0
            }
        ]
}
```

**all client**

```json
{
    "client6": {
        "rules": null,
        "event": null,
         "recordlist": [
            {
                "date": "2024-08-16",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 7653624
                    },
                    {
                        "name": "dns.log",
                        "size": 3373314
                    },
                    {
                        "name": "drop.log",
                        "size": 1956318
                    },
                    {
                        "name": "icmp.log",
                        "size": 1956318
                    },
                    {
                        "name": "tls.log",
                        "size": 4259878
                    },
                    {
                        "name": "udp.log",
                        "size": 20432
                    }
                ]
            },
            {
                "date": "2024-08-17",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 4904689
                    },
                    {
                        "name": "dns.log",
                        "size": 1734984
                    },
                    {
                        "name": "drop.log",
                        "size": 1892709
                    },
                    {
                        "name": "icmp.log",
                        "size": 1892709
                    },
                    {
                        "name": "tls.log",
                        "size": 3168521
                    },
                    {
                        "name": "udp.log",
                        "size": 1184
                    }
                ]
            },
            {
                "date": "2024-08-19",
                "files": [
                    {
                        "name": "alert.log",
                        "size": 2387729
                    },
                    {
                        "name": "dns.log",
                        "size": 1606317
                    },
                    {
                        "name": "drop.log",
                        "size": 1746454
                    },
                    {
                        "name": "icmp.log",
                        "size": 1746454
                    },
                    {
                        "name": "tls.log",
                        "size": 781412
                    }
                ]
            },
            {
                "date": "2024-08-20",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4665099
                    },
                    {
                        "name": "icmp.log",
                        "size": 4665099
                    }
                ]
            },
            {
                "date": "2024-08-21",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4711179
                    },
                    {
                        "name": "icmp.log",
                        "size": 4711179
                    }
                ]
            },
            {
                "date": "2024-08-22",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 4720759
                    }
                ]
            },
            {
                "date": "2024-08-23",
                "files": [
                    {
                        "name": "icmp.log",
                        "size": 3494007
                    }
                ]
            },
            {
                "date": "2024-08-29",
                "files": [
                    {
                        "name": "drop.log",
                        "size": 3276
                    },
                    {
                        "name": "icmp.log",
                        "size": 9555
                    }
                ]
            }
        ],
        "rulepackets": [
            {
                "sid": 50000000,
                "name": "atop_device",
                "action": "alert",
                "counts": 20
            },
            {
                "sid": 50000001,
                "name": "atop_device",
                "action": "alert",
                "counts": 0
            }
        ]
        
    }
}
```











# Quickstart

## Prepare

- #### Linux (only test on ubuntu)

  ```shell
  sudo apt update
  sudo apt-get install -y libpcap-dev
  sudo apt-get install -y libnetfilter-queue-dev
  sudo apt-get install -y libhyperscan-dev
  ```

  idps use iptables-nfqueue

  path:./conf.yaml
  
  ```yaml
  env:
    queue-num: 0
  ```

  if run first or current path without conf.yaml

  the default queue-num is 0

  In case of the host situation, these are the two most simple `iptables` rules;
  
  ```sh
  iptables -A INPUT -j NFQUEUE --queue-num 0 --queue-bypass
  iptables -A OUTPUT -j NFQUEUE --queue-num 0 --queue-bypass
  ```

  you can set queue-num to 2000

  `iptables` rules command should be
  
  ```sh
  iptables -A INPUT -j NFQUEUE --queue-num 2000 --queue-bypass
  iptables -A OUTPUT -j NFQUEUE --queue-num 2000 --queue-bypass
  ```

  To erase all `iptables` rules, enter:
  
  ```sh
  sudo iptables -F
  ```



- #### Windows

  install windivert



## Help

```
Usage bbidpsvc.exe:
  -O string
        debug log output (default "stderr")
  -P string
        debug log pattern string
  -daemon string
        run | start | stop | restart | install |  uninstall  | status
  -debuglog
        enable debug log, this will override -P to .*
  -ds
        dump stack trace when exiting with non zero code
  -ic int
        command processing interval (default 5)
  -ir int
        Network service registration interval (default 60)
  -n string
        name
  -p int
        port (default 27184)
  -r string
        root URL
  -rs string
        remote syslog server address
  -version
        print version
```

## Start

refer:https://reqrypt.org/windivert-doc.html#introduction

### Step1

run root service 

```sh
./bbrootsvc -n root 
```

### Step2

run cilent of idps 

```sh
./bbidpsvc -n client -r http://127.0.0.1:27182 
```

### Step3

run web and input script 

```sh
idps rules import https://nms.blackbeartechhive.com/api/v1/files/exampleRule.rar
```

run bbctl

```sh
./bbctl -cc client idps rules import "https://nms.blackbeartechhive.com/api/v1/files/exampleRule.rar"
```

