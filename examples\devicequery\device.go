// devicequery/device.go
package main

import (
	"encoding/json"
	"log"
	"strconv"
	"time"
)

// Device represents a single device entry.
// Fields are tagged for JSON unmarshalling and used for reflection by database.go
type Device struct {
	MAC              string          `json:"mac"` // Will be PRIMARY KEY
	ModelName        string          `json:"modelname"`
	Timestamp        time.Time       `json:"-"` // Populated from TimestampUnixStr, not directly from JSON
	TimestampUnixStr string          `json:"timestamp_unix_str"` // Original Unix timestamp string from JSON, renamed for clarity
	ScanProto        string          `json:"scanproto"`
	IPAddress        string          `json:"ipaddress"`
	Netmask          string          `json:"netmask"`
	Gateway          string          `json:"gateway"`
	Hostname         string          `json:"hostname"`
	Kernel           string          `json:"kernel"`
	AP               string          `json:"ap"` // Application/Firmware version
	ScannedBy        string          `json:"scannedby"`
	ARPMissed        int             `json:"arpmissed"`
	Lock             bool            `json:"lock"`
	ReadCommunity    string          `json:"readcommunity"`
	WriteCommunity   string          `json:"writecommunity"`
	IsDHCP           bool            `json:"isdhcp"`
	IsOnline         bool            `json:"isonline"`
	TopologyProto    string          `json:"topologyproto"`
	SvcDiscoVia      string          `json:"svcdiscovia"`
	Capabilities     json.RawMessage `json:"capabilities"` // Store as JSON string in DB
	DeviceErrors     json.RawMessage `json:"device_errors"`  // Store as JSON string in DB
	Username         string          `json:"username"`
	Password         string          `json:"password"`
	TunneledURL      string          `json:"tunneled_url"`
	SNMPSupported    string          `json:"snmpSupported"` // Stored as TEXT as it's string in JSON
	SNMPEnabled      string          `json:"snmpEnabled"`   // Stored as TEXT
}

// tempDeviceHolder is used for unmarshalling the initial JSON structure for each device object.
type tempDeviceHolder struct {
	MAC              string          `json:"mac"`
	ModelName        string          `json:"modelname"`
	TimestampUnixStr string          `json:"timestamp"` // Matches the "timestamp" key in the input JSON
	ScanProto        string          `json:"scanproto"`
	IPAddress        string          `json:"ipaddress"`
	Netmask          string          `json:"netmask"`
	Gateway          string          `json:"gateway"`
	Hostname         string          `json:"hostname"`
	Kernel           string          `json:"kernel"`
	AP               string          `json:"ap"`
	ScannedBy        string          `json:"scannedby"`
	ARPMissed        int             `json:"arpmissed"`
	Lock             bool            `json:"lock"`
	ReadCommunity    string          `json:"readcommunity"`
	WriteCommunity   string          `json:"writecommunity"`
	IsDHCP           bool            `json:"isdhcp"`
	IsOnline         bool            `json:"isonline"`
	TopologyProto    string          `json:"topologyproto"`
	SvcDiscoVia      string          `json:"svcdiscovia"`
	Capabilities     json.RawMessage `json:"capabilities"`
	DeviceErrors     json.RawMessage `json:"device_errors"`
	Username         string          `json:"username"`
	Password         string          `json:"password"`
	TunneledURL      string          `json:"tunneled_url"`
	SNMPSupported    string          `json:"snmpSupported"`
	SNMPEnabled      string          `json:"snmpEnabled"`
}

// ProcessRawDeviceData converts the raw map from JSON (map[string]json.RawMessage)
// into a slice of fully processed Device structs.
func ProcessRawDeviceData(rawData map[string]json.RawMessage) ([]Device, error) {
	var devices []Device
	if rawData == nil {
		return devices, nil
	}

	for macKeyFromMap, deviceJSON := range rawData {
		var temp tempDeviceHolder
		if err := json.Unmarshal(deviceJSON, &temp); err != nil {
			log.Printf("Warning: Failed to unmarshal device data for key %s: %v. Skipping.", macKeyFromMap, err)
			continue
		}

		if temp.MAC == "" {
			log.Printf("Warning: MAC address missing in device object for key %s. Using key as MAC.", macKeyFromMap)
			temp.MAC = macKeyFromMap // Fallback, though the provided JSON has 'mac' field in object
		} else if temp.MAC != macKeyFromMap {
			log.Printf("Warning: MAC address mismatch for key %s. Map key: %s, Object MAC: %s. Using Object MAC: %s.", macKeyFromMap, macKeyFromMap, temp.MAC, temp.MAC)
		}


		var t time.Time
		unixTimestamp, err := strconv.ParseInt(temp.TimestampUnixStr, 10, 64)
		if err == nil && temp.TimestampUnixStr != "" { // Ensure string is not empty
			t = time.Unix(unixTimestamp, 0).UTC()
		} else if temp.TimestampUnixStr != "" { // Log error only if there was a string to parse
			log.Printf("Warning: Could not parse timestamp string '%s' for MAC %s: %v. Timestamp will be zero.", temp.TimestampUnixStr, temp.MAC, err)
		}

		device := Device{
			MAC:              temp.MAC,
			ModelName:        temp.ModelName,
			Timestamp:        t,
			TimestampUnixStr: temp.TimestampUnixStr, // Store the original string
			ScanProto:        temp.ScanProto,
			IPAddress:        temp.IPAddress,
			Netmask:          temp.Netmask,
			Gateway:          temp.Gateway,
			Hostname:         temp.Hostname,
			Kernel:           temp.Kernel,
			AP:               temp.AP,
			ScannedBy:        temp.ScannedBy,
			ARPMissed:        temp.ARPMissed,
			Lock:             temp.Lock,
			ReadCommunity:    temp.ReadCommunity,
			WriteCommunity:   temp.WriteCommunity,
			IsDHCP:           temp.IsDHCP,
			IsOnline:         temp.IsOnline,
			TopologyProto:    temp.TopologyProto,
			SvcDiscoVia:      temp.SvcDiscoVia,
			Capabilities:     temp.Capabilities,
			DeviceErrors:     temp.DeviceErrors,
			Username:         temp.Username,
			Password:         temp.Password,
			TunneledURL:      temp.TunneledURL,
			SNMPSupported:    temp.SNMPSupported,
			SNMPEnabled:      temp.SNMPEnabled,
		}
		devices = append(devices, device)
	}
	return devices, nil
}
