-- *****************************************************************
-- PRIVILEGE-MIB:  
-- ****************************************************************

MGMT-PRIVILEGE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    MGMTDisplayString FROM MGMT-TC
    ;

mgmtPrivilegeMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Privilege"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 59 }


mgmtPrivilegeMibObjects OBJECT IDENTIFIER
    ::= { mgmtPrivilegeMib 1 }

mgmtPrivilegeConfig OBJECT IDENTIFIER
    ::= { mgmtPrivilegeMibObjects 2 }

mgmtPrivilegeConfigWebTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPrivilegeConfigWebEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure web privilege"
    ::= { mgmtPrivilegeConfig 1 }

mgmtPrivilegeConfigWebEntry OBJECT-TYPE
    SYNTAX      MGMTPrivilegeConfigWebEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each module has a set of parameters"
    INDEX       { mgmtPrivilegeConfigWebModuleName }
    ::= { mgmtPrivilegeConfigWebTable 1 }

MGMTPrivilegeConfigWebEntry ::= SEQUENCE {
    mgmtPrivilegeConfigWebModuleName    MGMTDisplayString,
    mgmtPrivilegeConfigWebConfigRoPriv  Unsigned32,
    mgmtPrivilegeConfigWebConfigRwPriv  Unsigned32,
    mgmtPrivilegeConfigWebStatusRoPriv  Unsigned32,
    mgmtPrivilegeConfigWebStatusRwPriv  Unsigned32
}

mgmtPrivilegeConfigWebModuleName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Module name."
    ::= { mgmtPrivilegeConfigWebEntry 1 }

mgmtPrivilegeConfigWebConfigRoPriv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Web privilege of read-only configuration."
    ::= { mgmtPrivilegeConfigWebEntry 2 }

mgmtPrivilegeConfigWebConfigRwPriv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Web privilege of read-write configuration."
    ::= { mgmtPrivilegeConfigWebEntry 3 }

mgmtPrivilegeConfigWebStatusRoPriv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Web privilege of read-only status."
    ::= { mgmtPrivilegeConfigWebEntry 4 }

mgmtPrivilegeConfigWebStatusRwPriv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Web privilege of read-write status."
    ::= { mgmtPrivilegeConfigWebEntry 5 }

mgmtPrivilegeMibConformance OBJECT IDENTIFIER
    ::= { mgmtPrivilegeMib 2 }

mgmtPrivilegeMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPrivilegeMibConformance 1 }

mgmtPrivilegeMibGroups OBJECT IDENTIFIER
    ::= { mgmtPrivilegeMibConformance 2 }

mgmtPrivilegeConfigWebInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPrivilegeConfigWebModuleName,
                  mgmtPrivilegeConfigWebConfigRoPriv,
                  mgmtPrivilegeConfigWebConfigRwPriv,
                  mgmtPrivilegeConfigWebStatusRoPriv,
                  mgmtPrivilegeConfigWebStatusRwPriv }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPrivilegeMibGroups 1 }

mgmtPrivilegeMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPrivilegeConfigWebInfoGroup }

    ::= { mgmtPrivilegeMibCompliances 1 }

END
