package mnms

import (
	"strings"

	"github.com/qeof/q"
)

// runRootCmd
func runRootCmd(cmdinfo *CmdInfo) *CmdInfo {
	defer sendCmdSyslog("RunCmd", cmdinfo)

	if !QC.IsRoot {
		q.Q("not running as root, will not retrieveRootCmd")
		cmdinfo.Status = "error: not running as root"
		return cmdinfo
	}
	if cmdinfo.Kind != "root" {
		q.Q("not a root cmd", cmdinfo)
		cmdinfo.Status = "error: not a root cmd"
		return cmdinfo
	}
	if cmdinfo.Command == "" {
		q.Q("error: empty command", cmdinfo)
		cmdinfo.Status = "error: empty command"
		return cmdinfo
	}
	switch {
	case strings.HasPrefix(cmdinfo.Command, "config local syslog "):
		return RootConfigSyslogCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "wg "):
		return WgCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "ssh "):
		return SshCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "firewall "):
		return FirewallCmd(cmdinfo)
	case strings.HasPrefix(cmdinfo.Command, "service "):
		return ServiceCmd(cmdinfo)
	default:
		q.Q("unrecognized", cmdinfo.Command)
		cmdinfo.Status = "error: unknown command"
		return cmdinfo
	}
}

func RootConfigSyslogCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command

	if QC.IsRoot {
		if strings.HasPrefix(cmd, "config local syslog path") {
			return SyslogSetPathCmd(cmdinfo)
		}
		if strings.HasPrefix(cmd, "config local syslog maxsize") {
			return SyslogSetMaxSizeCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog compress") {
			return SyslogSetCompressCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog read") {
			return ReadSyslogCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog remote") {
			return SyslogSetRemoteCmd(cmdinfo)
		}

		if strings.HasPrefix(cmd, "config local syslog backup-after-forward") {
			return SyslogSetBakAfterFwdCmd(cmdinfo)
		}
	}

	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error:  this command requires to run kind is root or unknown command "
	return cmdinfo
}
