package ip

import (
	"fmt"
	"mnms/idpsystem/idps/src/protocol"
	"strconv"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type FragoffsetOperator int

const (
	less FragoffsetOperator = iota + 1
	more
	not
)

func NewFragoffset() *fragoffset {
	return &fragoffset{ipLayer: protocol.NewIpLayer()}
}

type fragoffset struct {
	offset  uint16
	ipLayer protocol.IPLayer
	ck      func(p, d uint16) bool
}

func (f *fragoffset) SetUp(s string) error {
	if len(s) < 2 {
		return fmt.Errorf("invalid format for Fragoffset: %s", s)
	}
	var fo FragoffsetOperator
	op := s[0]
	switch op {
	case '>':
		fo = more
	case '<':
		fo = less
	case '!':
		fo = not
	default:
		return fmt.Errorf("invalid format for Fragoffset: %s", s)
	}
	num := s[1:]
	off, err := strconv.ParseUint(num, 10, 16)
	if err != nil {
		return err
	}
	f.offset = uint16(off)
	fun, err := ceratFunction(fo)
	if err != nil {
		return err
	}
	f.ck = fun
	return nil
}

func (f *fragoffset) Match(packet gopacket.Packet) bool {

	ver, net, err := f.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		if f.ck != nil {
			return f.ck(v4.FragOffset, f.offset)
		}
	case protocol.IPV6:
		if frag := packet.Layer(layers.LayerTypeIPv6Fragment); frag != nil {
			if fag, ok := frag.(*layers.IPv6Fragment); ok {
				return f.ck(fag.FragmentOffset, f.offset)
			}
		}
	}

	return false
}
func ceratFunction(v FragoffsetOperator) (func(uint16, uint16) bool, error) {

	switch v {
	case less:
		return func(poffset, doffset uint16) bool { return poffset < doffset }, nil
	case more:
		return func(poffset, doffset uint16) bool {
			return poffset > doffset
		}, nil
	default:
		return func(poffset, doffset uint16) bool { return poffset == doffset }, nil
	}
}
