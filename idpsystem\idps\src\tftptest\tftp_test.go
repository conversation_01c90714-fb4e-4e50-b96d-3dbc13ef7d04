package tftp_test

import (
	"fmt"
	"io"
	"log"
	"mnms/idpsystem/idps/ids"
	"net"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/google/gonids"
	"github.com/pin/tftp"
)

type server struct {
	name string
	mu   sync.Mutex
}

const testmessage = "79845612346sdafdssdsfddsf"
const testfile = "source.txt"
const testproto = "tftp"

var count = 0

func NewServer() (*server, error) {
	path := testfile
	file, err := os.Create(path)
	if err != nil {
		return nil, err
	}
	_, err = file.WriteString(testmessage)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	s := &server{name: path}
	return s, nil
}

func (b *server) handleWrite(filename string, wt io.WriterTo) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	_, err = wt.WriteTo(file)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Can't receive %s: %v\n", filename, err)
		return err
	}
	defer file.Close()
	return nil
}

func (b *server) handleRead(filename string, rf io.ReaderFrom) error {
	b.mu.Lock()
	defer b.mu.Unlock()
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	_, err = rf.ReadFrom(file)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Can't send %s: %v\n", filename, err)
		return err
	}
	return nil
}

func TestDetect(t *testing.T) {
	i, err := ids.NewIds(false, false)
	if err != nil {
		t.Fatal(err)
	}
	i.Enablelo(true)
	s := `alert tftp $HOME_NET any <> $HOME_NET any (msg:"test tftp";sid:1;)`
	r, err := gonids.ParseRule(s)
	if err != nil {
		t.Fatal(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	i.RegisterMatchEvent(event())

	err = i.Run()
	if err != nil {
		t.Fatal(err)
	}
	err = i.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = i.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	defer i.Close()
	err = runTftp()
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Second * 3)
	if count == 0 {
		t.Fatalf("test fail,not detect proto:%v", testproto)
	}
}

func event() func(ids.Event) {
	v := func(event ids.Event) {
		count++
		log.Printf("%v", event)
	}
	return v
}

func runTftp() error {
	server, err := NewServer()
	if err != nil {
		return err
	}
	s := tftp.NewServer(server.handleRead, server.handleWrite)

	conn, err := net.ListenUDP("udp", &net.UDPAddr{Port: 70})
	if err != nil {
		return err
	}

	go s.Serve(conn)
	defer s.Shutdown()

	c, err := tftp.NewClient("127.0.0.1:70")
	if err != nil {
		return err
	}
	c.SetTimeout(time.Second * 3)
	wt, err := c.Receive(testfile, "octet")
	if err != nil {
		return err
	}
	path := "testfile.txt"
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	// Optionally obtain transfer size before actual data.
	if _, ok := wt.(tftp.IncomingTransfer).Size(); ok {
		return err
	}
	_, err = wt.WriteTo(file)
	if err != nil {
		return err
	}
	return nil
}
