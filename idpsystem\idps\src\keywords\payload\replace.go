package payload

import (
	"encoding/binary"
	"errors"
	"fmt"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type replaceList struct {
	ct    *content
	found []byte
	next  *replaceList
}

func replaceSetUp(l *list, v string) error {
	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("replace needs preceding content option for raw sig")
	}
	replen := uint16(len(v))
	ct, _ := dc.data.(*content)
	if ct.flags&contentNegated > 0 {
		return errors.New("can't have a relative negated keyword set along with a replacement")
	}
	if ct.contentLen != replen {
		return errors.New("can't have a content length different from replace length")
	}
	ct.replaceLen = replen
	ct.replace = &v
	ct.flags |= replace
	return nil
}
func newReplaceList() *replaceList {
	return &replaceList{}
}

func replaceAddToList(replist *replaceList, found []byte, ct *content) *replaceList {
	if ct.contentLen != ct.replaceLen {
		return nil
	}
	newlist := newReplaceList()
	newlist.found = found
	newlist.ct = ct
	newlist.next = replist
	return newlist
}

func replaceMatch(p *Packet) {
	if p.replace != nil {
		replaceExecuteInternal(p)
		p.replace = nil
	}
}

func replaceExecuteInternal(p *Packet) {
	curr := p.replace
	for curr != nil {
		for i, v := range *curr.ct.replace {
			curr.found[i] = byte(v)
		}
		curr = curr.next
	}
	setNetworkLayerForChecksum(p.packet)
}

func setNetworkLayerForChecksum(packet gopacket.Packet) {
	var netlayer gopacket.NetworkLayer
	if net := packet.NetworkLayer(); net != nil {
		netlayer = net
	} else {
		return
	}
	tcpipchecksum := &tcpipchecksum{}
	err := tcpipchecksum.SetNetworkLayerForChecksum(netlayer)
	if err != nil {
		return
	}
	if trans := packet.TransportLayer(); trans != nil {
		u, err := tcpipchecksum.CalculateChecksum(trans, trans.LayerPayload())
		if err != nil {
			return
		}
		switch layer := trans.(type) {
		case *layers.TCP:
			layer.Checksum = u
			binary.BigEndian.PutUint16(layer.Contents[16:], layer.Checksum)
		case *layers.UDP:
			layer.Checksum = u
			binary.BigEndian.PutUint16(layer.Contents[6:], layer.Checksum)

		}

	}

}

type tcpipchecksum struct {
	pseudoheader []byte
}

func (i *tcpipchecksum) SetNetworkLayerForChecksum(l gopacket.NetworkLayer) error {
	switch v := l.(type) {
	case *layers.IPv4:
		i.pseudoheader = i.createIPv4PseudoHeader(v)
	case *layers.IPv6:
		i.pseudoheader = i.createIPv6PseudoHeader(v)
	default:
		return fmt.Errorf("unsupported network layer type: %v", l.LayerType())
	}
	return nil
}

func (i *tcpipchecksum) CalculateChecksum(tp gopacket.TransportLayer, payload []byte) (uint16, error) {
	bytes := tp.LayerContents()
	switch tp.(type) {
	case *layers.TCP:
		bytes[16] = 0
		bytes[17] = 0
	case *layers.UDP:
		bytes[6] = 0
		bytes[7] = 0
	default:
		return 0, fmt.Errorf("unsupported transport layer type: %v", tp.LayerType())
	}

	buffer := append(i.pseudoheader, tp.LayerContents()...)
	buffer = append(buffer, payload...)
	var sum uint32
	for i := 0; i < len(buffer)-1; i += 2 {
		sum += uint32(binary.BigEndian.Uint16(buffer[i : i+2]))
	}
	if len(buffer)%2 == 1 {
		sum += uint32(buffer[len(buffer)-1]) << 8
	}
	for sum > 0xFFFF {
		sum = (sum >> 16) + (sum & 0xFFFF)
	}
	return ^uint16(sum), nil
}

func (i *tcpipchecksum) createIPv4PseudoHeader(ipv4 *layers.IPv4) []byte {
	pseudo := make([]byte, 12)
	copy(pseudo[0:4], ipv4.SrcIP.To4())
	copy(pseudo[4:8], ipv4.DstIP.To4())
	pseudo[8] = 0
	pseudo[9] = byte(ipv4.Protocol)
	binary.BigEndian.PutUint16(pseudo[10:12], uint16(len(ipv4.Payload)))
	return pseudo
}

func (i *tcpipchecksum) createIPv6PseudoHeader(ipv6 *layers.IPv6) []byte {
	pseudo := make([]byte, 40)
	copy(pseudo[0:16], ipv6.SrcIP.To16())
	copy(pseudo[16:32], ipv6.DstIP.To16())
	binary.BigEndian.PutUint32(pseudo[32:36], uint32(len(ipv6.Payload)))
	pseudo[39] = byte(ipv6.NextHeader)
	return pseudo
}
