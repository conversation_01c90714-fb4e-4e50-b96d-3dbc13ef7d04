package keywords

import (
	"mnms/idpsystem/idps/mpm"

	"mnms/idpsystem/idps/src/keywords/payload"

	"github.com/google/gonids"
	"github.com/google/gopacket"
)

type PacketConfig struct {
	GoPacket   gopacket.Packet
	Payload    []byte   //data we want to detect
	ByteValues []uint64 //byte used
}

type Detecter interface {
	DetectContentInspection(PacketConfig) bool
	Build() error
	RetrieveData() any
	PrefilterData() *mpm.Content
	DataPos() gonids.DataPos
}

type DetectManagement struct {
	stkbuffer StickyBuffer
	list      *payload.Detect
}

func NewDetecter(id int, r gonids.Rule, d *payload.Data) (Detecter, error) {
	list, err := payload.NewdetectContent(id, r, d)
	if err != nil {
		return nil, err
	}
	if list == nil {
		return nil, nil
	}
	err = list.Validate()
	if err != nil {
		return nil, err
	}
	dm := &DetectManagement{list: list}

	if list.DataPos() != gonids.PayloadData {
		b, err := NewStickyBuffer(list.DataPos().String())
		if err != nil {
			return nil, err
		}
		dm.stkbuffer = b
	}
	return dm, nil
}

func (d *DetectManagement) DetectContentInspection(p PacketConfig) bool {
	pconf := payload.PacketConfig{
		GoPacket:   p.GoPacket,
		Payload:    p.Payload,
		PayloadLen: uint32(len(p.Payload)),
		Buffer:     p.Payload,
		BufferLen:  uint32(len(p.Payload)),
		ByteValues: p.ByteValues,
	}
	if d.stkbuffer != nil {
		buffer := d.stkbuffer.RetrieveBffer(p.GoPacket)
		pconf.Buffer = buffer
		pconf.BufferLen = uint32(len(buffer))
	}
	packet := payload.NewPacket(pconf)
	return d.list.DetectContentInspection(packet)

}

func (d *DetectManagement) Build() error {

	return d.list.Build()
}

func (d *DetectManagement) RetrieveData() any {

	return d.list.RetrieveData()
}

func (d *DetectManagement) PrefilterData() *mpm.Content {
	return d.list.PrefilterData()
}

func (d *DetectManagement) DataPos() gonids.DataPos {
	return d.list.DataPos()
}
