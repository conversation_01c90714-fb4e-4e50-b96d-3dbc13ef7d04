import {
  createAsyncThunk,
  createSlice,
  createSelector,
} from "@reduxjs/toolkit";
import publicApis from "../../utils/apis/publicApis";

export const CheckServerStatus = createAsyncThunk(
  "serverStatusSlice/CheckServerStatus",
  async (_, thunkAPI) => {
    try {
      const response = await publicApis.get("/api");
      let data = await response.data;
      if (response.status === 200 && data === "mnms says hello") {
        return data;
      } else {
        return thunkAPI.rejectWithValue(data);
      }
    } catch (e) {
      if (e.response && e.response.data !== "") {
        return thunkAPI.rejectWithValue(e.response.data);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const serverStatusSlice = createSlice({
  name: "serverStatusSlice",
  initialState: { serverOnlineStatus: false, loadingServerStatus: true },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(CheckServerStatus.fulfilled, (state, { payload }) => {
        state.serverOnlineStatus = true;
        state.loadingServerStatus = false;
      })
      .addCase(CheckServerStatus.rejected, (state, { payload }) => {
        state.serverOnlineStatus = false;
        state.loadingServerStatus = false;
      })
      .addCase(CheckServerStatus.pending, (state) => {
        state.serverOnlineStatus = false;
        state.loadingServerStatus = true;
      });
  },
});

export const {} = serverStatusSlice.actions;
export const serverStatusSelector = createSelector(
  (state) => state.serverStatus,
  ({ serverOnlineStatus, loadingServerStatus }) => ({
    serverOnlineStatus,
    loadingServerStatus,
  })
);
export default serverStatusSlice;
