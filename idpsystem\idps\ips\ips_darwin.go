//go:build darwin

package ips

import (
	"fmt"

	"github.com/google/gonids"
)



type UIps struct {
	
}

func NewIps() (Ipser, error) {
	return nil, fmt.<PERSON>rrorf("not supported on darwin")
}

func (u *UIps) Start() error {
	return fmt.Errorf("not supported on darwin")
}
func (w *UIps) Enablelo(b bool) {

}

func (w *UIps) AddGonidsRule(r *gonids.Rule) error {
	return fmt.Errorf("not supported on darwin")
}

func (u *UIps) ApplyRules() error {
	return fmt.<PERSON><PERSON>rf("not supported on darwin")
}

// RunAllRules make all rules work
func (U *UIps) Build() error {
	return fmt.Errorf("not supported on darwin")
}

func (u *UIps) RegisterMatchEvent(e Eventfunc) {
}

func (u *UIps) Close() error {
	return fmt.<PERSON><PERSON><PERSON>("not supported on darwin")
}

