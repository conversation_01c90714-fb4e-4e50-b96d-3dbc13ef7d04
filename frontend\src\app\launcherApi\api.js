import { createApi } from "@reduxjs/toolkit/query/react";
import publicLauncherApis from "../../utils/apis/publicLauncherApis";

const axiosBaseQuery = async (args, api) => {
  try {
    let {
      url,
      params = undefined,
      body,
      method,
      responseType = "json",
    } = typeof args == "string" ? { url: args } : args;
    const result = await publicLauncherApis({
      url,
      method,
      data: body,
      params,
      responseType,
    });
    return { data: result.data };
  } catch (axiosError) {
    let err = axiosError;
    return {
      error: {
        status: err.response?.status,
        data: err.response?.data || err.message,
      },
    };
  }
};

export const launcherApi = createApi({
  reducerPath: "launcherApi",
  baseQuery: axiosBaseQuery,
  tagTypes: ["launcher"],
  endpoints: (builder) => ({}),
});
