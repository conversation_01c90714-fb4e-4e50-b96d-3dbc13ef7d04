package payload

import (
	"errors"
	"fmt"
)

func depthSetUp(l *list, v string) error {

	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("depth needs " +
			"preceding content, uricontent option, http_client_body, " +
			"http_server_body, http_header option, http_raw_header option, " +
			"http_method option, http_cookie, http_raw_uri, " +
			"http_stat_msg, http_stat_code, http_user_agent, " +
			"http_host, http_raw_host or " +
			"file_data/dce_stub_data sticky buffer options")
	}
	d, _ := dc.data.(*content)
	if d.flags&depth > 0 {
		return errors.New("can't use multiple depths for the same content")

	}
	if (d.flags&within) > 0 || (d.flags&distance > 0) {
		return errors.New("can't use a relative " +
			"keyword like within/distance with a absolute " +
			"relative keyword like depth/offset for the same " +
			"content")
	}
	if d.flags&contentNegated > 0 && d.flags&fastpPattern > 0 {
		return errors.New("can't have a relative " +
			"negated keyword set along with 'fast_pattern'")

	}
	if (d.flags & fastpPatternOnly) > 0 {
		return errors.New("can't have a relative " +
			"keyword set along with 'fast_pattern:only;'")

	}
	if v[0] != '-' && isalpha(v[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(v, l, &index) {
			return fmt.Errorf("unknown byte_ keyword var seen in depth - %s", v)
		}
		d.depth = uint16(index)
		d.flags |= depthVar
	} else {
		if stringParseUint16(&d.depth, 0, v) < 0 {
			return fmt.Errorf("invalid value for depth:%v", v)
		}
		if d.depth < d.contentLen {
			return fmt.Errorf("depth:%v smaller than"+
				"content of len %v",
				d.depth, d.contentLen)
		}
		d.depth += d.offset
	}
	d.flags |= depth
	return nil
}
