package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
)

// DeviceCapabilities defines the structure for the "capabilities" field.
type DeviceCapabilities struct {
	Gwd bool `json:"gwd"`
}

// Device defines the structure for each device entry.
// Note: JSON tags are used to map JSON keys to struct fields,
// especially when Go conventions (PascalCase) differ from JSON (snake_case or camelCase).
type Device struct {
	Mac            string              `json:"mac"`
	ModelName      string              `json:"modelname"`
	Timestamp      string              `json:"timestamp"` // Could be int64 if parsed
	ScanProto      string              `json:"scanproto"`
	IPAddress      string              `json:"ipaddress"`
	Netmask        string              `json:"netmask"`
	Gateway        string              `json:"gateway"`
	Hostname       string              `json:"hostname"`
	Kernel         string              `json:"kernel"`
	AP             string              `json:"ap"` // "ap" field in JSON
	ScannedBy      string              `json:"scannedby"`
	ArpMissed      int                 `json:"arpmissed"`
	Lock           bool                `json:"lock"`
	ReadCommunity  string              `json:"readcommunity"`
	WriteCommunity string              `json:"writecommunity"`
	IsDHCP         bool                `json:"isdhcp"`
	IsOnline       bool                `json:"isonline"`
	TopologyProto  string              `json:"topologyproto"`
	SvcDiscoVia    string              `json:"svcdiscovia"`
	Capabilities   *DeviceCapabilities `json:"capabilities"` // Pointer to handle null
	DeviceErrors   interface{}         `json:"device_errors"` // Use interface{} for null or varied structure
	Username       string              `json:"username"`
	Password       string              `json:"password"`
	TunneledURL    string              `json:"tunneled_url"`
	SnmpSupported  string              `json:"snmpSupported"`
	SnmpEnabled    string              `json:"snmpEnabled"`
}

func main() {
	// Expecting the filename as a command-line argument
	if len(os.Args) < 2 {
		log.Fatalf("Usage: %s <input_json_file>", os.Args[0])
	}
	filePath := os.Args[1]

	// Open the JSON file
	file, err := os.Open(filePath)
	if err != nil {
		log.Fatalf("Error opening file %s: %v", filePath, err)
	}
	defer file.Close()

	// Read the file content
	byteValue, err := io.ReadAll(file)
	if err != nil {
		log.Fatalf("Error reading file %s: %v", filePath, err)
	}

	// The input JSON is an object where keys are MAC addresses (strings)
	// and values are Device objects.
	var inputData map[string]Device

	// Unmarshal the JSON data into our map
	if err := json.Unmarshal(byteValue, &inputData); err != nil {
		log.Fatalf("Error unmarshaling JSON: %v", err)
	}

	// Create a slice to hold all device objects
	// Pre-allocate with known size for efficiency
	allDevices := make([]Device, 0, len(inputData))

	// Iterate over the map and add each device (the value part) to the slice
	for _, device := range inputData {
		allDevices = append(allDevices, device)
	}

	// Marshal the slice of devices into JSON for output
	// Use MarshalIndent for pretty-printing
	outputJSON, err := json.MarshalIndent(allDevices, "", "  ")
	if err != nil {
		log.Fatalf("Error marshaling output JSON: %v", err)
	}

	// Print the resulting JSON to stdout
	fmt.Println(string(outputJSON))
}
