#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <stdint.h>
#include <sys/types.h>
#include <ctype.h>

#ifdef WIN32
#include <WinSock2.h>
#include <Ws2tcpip.h>
#include <iphlpapi.h>
#endif

#ifdef LINUX
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <linux/if.h>
#include <netdb.h>
#include <fcntl.h>

typedef unsigned int u_int;
typedef unsigned short u_short;
typedef unsigned char u_char;
#endif

#include "pcap.h"
#include "monocypher.h"

int main(int argc, char *argv[])
{
	int i;
	unsigned char mac[16];
	unsigned char crypto_text[256];
	unsigned char shared_key[256]={0x61, 0x67, 0x65, 0x6E, 0x74, 0x63, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0, 0, 0, 0, 0,
					0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
	unsigned char nonce[12]={0x61, 0x67, 0x65, 0x6E, 0x74, 0x63, 0x6C, 0x69, 0x65, 0x6E, 0x74, 0};
	unsigned char plain_text[256] = "helloo";
	mac[0]=0;
	mac[1]=0;
	mac[2]=0;
	mac[3]=0;
	mac[4]=0;
	mac[5]=0;

	// encryption
	crypto_lock(mac,crypto_text,shared_key,nonce,plain_text,strlen(plain_text));
	printf("plain_text = %s\n",plain_text);
	printf("crypto_text = ");
	for(i=0; i< strlen(crypto_text) ;i++){
		printf("%02x ",crypto_text[i]);
	}
	printf("\n");
	// decryption
	memset(plain_text,'\0',sizeof(plain_text));
	crypto_unlock(plain_text,shared_key,nonce,mac,crypto_text,strlen(crypto_text));
	printf("decrypt crypto_text = %s\n",plain_text);

	return 0;
}