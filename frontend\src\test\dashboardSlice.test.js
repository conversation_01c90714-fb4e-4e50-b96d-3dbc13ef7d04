import { configureStore } from "@reduxjs/toolkit";
import dashboardSlice, {
  getSyslogsData,
  clearSyslogsData,
  dashboardSliceSelector,
} from "../features/dashboard/dashboardSlice";
import protectedApis from "../utils/apis/protectedApis";
import {  describe, expect, it, vi } from "vitest";
import { store } from "../app/store";
import { loginUser } from "../features/auth/userAuthSlice";
vi.mock("../utils/apis/protectedApis");

describe("dashboardSlice", () => {
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("should dispatch getSyslogsData and update syslogsData and scanning state on successful API response", async () => {
    const mockData = [
      { id: 1, message: "Log 1" },
      { id: 2, message: "Log 2" },
    ];
    protectedApis.get.mockResolvedValueOnce({ data: mockData, status: 200 });

    await store.dispatch(
      getSyslogsData({ start: "2022-01-01", end: "2022-12-31" })
    );

    const state = store.getState().dashboard;
    expect(state.syslogsData).toEqual(mockData);
    expect(state.scanning).toBe(false);
  });

  it("should dispatch getSyslogsData and update scanning state on pending API request", () => {
    protectedApis.get.mockResolvedValueOnce(new Promise(() => {}));

    store.dispatch(getSyslogsData({ start: "2022-01-01", end: "2022-12-31" }));

    const state = store.getState().dashboard;
    expect(state.scanning).toBe(true);
  });

  it("should dispatch getSyslogsData and update scanning state on rejected API response", async () => {
    const error = { response: { statusText: "Error" } };
    protectedApis.get.mockRejectedValueOnce(error);

    await store.dispatch(
      getSyslogsData({ start: "2022-01-01", end: "2022-12-31" })
    );

    const state = store.getState().dashboard;
    expect(state.scanning).toBe(false);
  });

  it("should dispatch clearSyslogsData and reset syslogsData and scanning state", () => {
    store.dispatch(clearSyslogsData());

    const state = store.getState().dashboard;
    expect(state.syslogsData).toEqual([]);
    expect(state.scanning).toBe(false);
  });

  it("should select the correct dashboardSlice state using dashboardSliceSelector", () => {
    const initialState = {
      dashboard: {
        syslogsData: [{ id: 1, message: "Log 1" }],
        scanning: true,
      },
    };
    const selectedState = dashboardSliceSelector(initialState);
    expect(selectedState.syslogsData).toEqual([{ id: 1, message: "Log 1" }]);
    expect(selectedState.scanning).toBe(true);
  });
  it("Dashboard API - Success with valid data", async () => {
    await store.dispatch(getSyslogsData());
    const state = store.getState().dashboard;
    expect(state.scanning).toEqual(false);
    expect(state.syslogsData).toEqual([]);
  });

});
 