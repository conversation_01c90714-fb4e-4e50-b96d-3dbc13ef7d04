default: monocypher 
CC=gcc
EXE=

ifeq ($(OS),Windows_NT)
	DEBUG=-g
	INC=-I C:/WpdPack/Include -I.
	CFLAGS=-std=c99 -DWIN32 $(INC) $(DEBUG)
	LIBS=-L C:/Win10pcap/x64 -lwpcap -liphlpapi -lws2_32 
	EXE=.exe
endif

ifeq ($(shell uname),Linux)
	DEBUG=-g

	LIBS=-lpcap
	INC=-I /usr/local/include -I.
	CFLAGS=-std=c99 -DLINUX $(INC) $(DEBUG)
endif


INCFILES=monocypher.h

monocypher.o: main.c $(INCFILES)

monocypher: main.o monocypher.o monocypher.o
	gcc -o monocypher$(EXE) $^  $(LIBS)

.PHONY: clean
clean:
	rm -rf *.o monocypher$(EXE)

