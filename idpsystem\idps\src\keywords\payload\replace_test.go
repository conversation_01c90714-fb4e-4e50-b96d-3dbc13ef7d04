package payload

import (
	"fmt"
	"strings"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func TestReplace(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		replace  []string
		packet   []byte
	}{

		{
			content:  "alert tcp any any -> any any (msg:\"Nothing..\";content:\"th\"; replace:\"TH\"; content:\"patter\"; replace:\"matter\"; sid:1;)",
			expected: true,
			replace:  []string{"TH", "matter"},
			packet: []byte{0x45, 0x00,
				0x00, 0x85, 0x00, 0x01, 0x00, 0x00, 0x40, 0x06,
				0x7c, 0x70, 0x7f, 0x00, 0x00, 0x01, 0x7f, 0x00,
				0x00, 0x01, 0x00, 0x14, 0x00, 0x50, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x02,
				0x20, 0x00, 0xc9, 0xad, 0x00, 0x00, 0x48, 0x69,
				0x2c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69,
				0x73, 0x20, 0x61, 0x20, 0x62, 0x69, 0x67, 0x20,
				0x74, 0x65, 0x73, 0x74, 0x20, 0x74, 0x6f, 0x20,
				0x63, 0x68, 0x65, 0x63, 0x6b, 0x20, 0x63, 0x6f,
				0x6e, 0x74, 0x65, 0x6e, 0x74, 0x20, 0x6d, 0x61,
				0x74, 0x63, 0x68, 0x65, 0x73, 0x20, 0x6f, 0x66,
				0x20, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x65,
				0x64, 0x20, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
				0x6e, 0x73, 0x20, 0x62, 0x65, 0x74, 0x77, 0x65,
				0x65, 0x6e, 0x20, 0x6d, 0x75, 0x6c, 0x74, 0x69,
				0x70, 0x6c, 0x65, 0x20, 0x63, 0x68, 0x75, 0x6e,
				0x6b, 0x73, 0x21},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p, err := NewPacketByByte(test.packet)
			if err != nil {
				t.Fatal(err)
			}
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
			if app := p.packet.ApplicationLayer(); app != nil {
				for _, v := range test.replace {
					if !strings.Contains(string(app.LayerContents()), v) {
						t.Errorf("not found replace string,should found:%v", v)
					}
				}

			}

		})
	}
}

func TestChecksum(t *testing.T) {
	var tests = []struct {
		packet []byte
	}{

		{
			packet: []byte{0x45, 0x00,
				0x00, 0x85, 0x00, 0x01, 0x00, 0x00, 0x40, 0x06,
				0x7c, 0x70, 0x7f, 0x00, 0x00, 0x01, 0x7f, 0x00,
				0x00, 0x01, 0x00, 0x14, 0x00, 0x50, 0x00, 0x00,
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x02,
				0x20, 0x00, 0xc9, 0xad, 0x00, 0x00, 0x48, 0x69,
				0x2c, 0x20, 0x74, 0x68, 0x69, 0x73, 0x20, 0x69,
				0x73, 0x20, 0x61, 0x20, 0x62, 0x69, 0x67, 0x20,
				0x74, 0x65, 0x73, 0x74, 0x20, 0x74, 0x6f, 0x20,
				0x63, 0x68, 0x65, 0x63, 0x6b, 0x20, 0x63, 0x6f,
				0x6e, 0x74, 0x65, 0x6e, 0x74, 0x20, 0x6d, 0x61,
				0x74, 0x63, 0x68, 0x65, 0x73, 0x20, 0x6f, 0x66,
				0x20, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x74, 0x65,
				0x64, 0x20, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
				0x6e, 0x73, 0x20, 0x62, 0x65, 0x74, 0x77, 0x65,
				0x65, 0x6e, 0x20, 0x6d, 0x75, 0x6c, 0x74, 0x69,
				0x70, 0x6c, 0x65, 0x20, 0x63, 0x68, 0x75, 0x6e,
				0x6b, 0x73, 0x21},
		}, {
			packet: []byte{
				0x45, 0x00,
				0x00, 0x41, 0x60, 0xc9, 0x40, 0x00, 0x80, 0x11,
				0x7f, 0xdc, 0xc0, 0xa8, 0x04, 0x15, 0x8e, 0xfa,
				0xc6, 0x4e, 0xd6, 0xca, 0x01, 0xbb, 0x00, 0x2d,
				0xfd, 0x82, 0x4d, 0xe8, 0x93, 0x47, 0x19, 0x84,
				0x33, 0xa1, 0x0a, 0xd8, 0xf9, 0xc8, 0xb0, 0xd5,
				0x97, 0xda, 0x1f, 0x90, 0x78, 0x60, 0xd1, 0x0a,
				0xcc, 0xb1, 0xfe, 0x82, 0x68, 0xfc, 0x81, 0x0c,
				0xd5, 0xa2, 0x7c, 0xcc, 0xb8, 0x35, 0x6b},
		},
	}
	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index), func(t *testing.T) {
			p, err := NewPacketByByte(test.packet)
			if err != nil {
				t.Fatal(err)
			}
			var checksum uint16
			var calcuchecksum uint16
			var netlayer gopacket.NetworkLayer
			var payload []byte
			if net := p.packet.NetworkLayer(); net != nil {
				netlayer = net
			} else {
				t.Fatal("not found network layer")
			}
			if trans := p.packet.TransportLayer(); trans != nil {
				payload = trans.LayerPayload()
				switch trans := trans.(type) {

				case *layers.TCP:
					checksum = trans.Checksum
				case *layers.UDP:
					checksum = trans.Checksum
				}
				tcpipchecksum := &tcpipchecksum{}
				err := tcpipchecksum.SetNetworkLayerForChecksum(netlayer)
				if err != nil {
					t.Error(err)
				}
				calcuchecksum, err = tcpipchecksum.CalculateChecksum(trans, payload)
				if err != nil {
					t.Error(err)
				}
				if calcuchecksum != checksum {
					t.Error("checksum not equal")
				}
			}

		})
	}
}
