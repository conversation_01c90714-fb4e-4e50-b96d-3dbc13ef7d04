package mnms

// this is the main entry point for the mnms tcp proxy service
// this will do the following:
// 1. proxy tcp connection from root to client through vpn
// 2. proxy udp connection from client to device

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/qeof/q"
	"inet.af/tcpproxy"
)

type qWriter int

func (w qWriter) Write(p []byte) (n int, err error) {
	// q.Q(string(p))
	return len(p), nil
}

type TcpProxyInfo struct {
	proxy tcpproxy.Proxy `json:"-"`
	From  string         `json:"from"`
	To    string         `json:"to"`
}

func init() {
	// map[QC.Name][from]TcpProxyInfo
	QC.TcpProxyData = make(map[string]map[string]TcpProxyInfo)

	// redirect log to q.Q
	log.SetOutput(new(qWriter))
}

func TcpProxy(from, to string) error {
	QC.TcpProxyMutex.Lock()
	defer QC.TcpProxyMutex.Unlock()
	proxyData, ok := QC.TcpProxyData[QC.Name]
	if ok {
		_, ok = proxyData[from]
		if ok {
			q.Q("tcpproxy already running")
			return nil
		}
	}

	if proxyData == nil {
		proxyData = make(map[string]TcpProxyInfo)
		QC.TcpProxyData[QC.Name] = proxyData
	}

	p := TcpProxyInfo{
		proxy: tcpproxy.Proxy{},
		From:  from,
		To:    to,
	}
	p.proxy.AddRoute(from, tcpproxy.To(to))
	err := p.proxy.Start()
	if err != nil {
		return err
	}
	QC.TcpProxyData[QC.Name][from] = p
	q.Q("tcpproxy", from, to)
	return nil
}

func TcpProxyClose(from string) error {
	QC.TcpProxyMutex.Lock()
	defer QC.TcpProxyMutex.Unlock()
	proxyData, ok := QC.TcpProxyData[QC.Name]
	if !ok {
		return fmt.Errorf("no such tcpproxy %s", from)
	}
	if proxyData == nil {
		return fmt.Errorf("no such tcpproxy %s", from)
	}
	p, ok := proxyData[from]
	if !ok {
		return fmt.Errorf("no such tcpproxy %s", from)
	}
	err := p.proxy.Close()
	if err != nil {
		return err
	}
	delete(proxyData, from)
	q.Q("tcpproxy closed", from)
	return nil
}

// TcpProxyPub publish tcpproxy data to root
func TcpProxyPub(interval int) {
	for {
		time.Sleep(time.Duration(interval) * time.Second)
		if QC.RootURL == "" {
			q.Q("no root url")
			continue
		}

		// data, ok := QC.TcpProxyData[QC.Name]
		// if !ok || data == nil {
		// 	continue
		// }
		QC.TcpProxyMutex.Lock()
		proxies := QC.TcpProxyData
		QC.TcpProxyMutex.Unlock()
		jsonBytes, err := json.Marshal(proxies)
		if err != nil {
			q.Q(err)
			continue
		}
		resp, err := PostWithToken(QC.RootURL+"/api/v1/tcpproxy", QC.AdminToken, bytes.NewBuffer(jsonBytes))
		if err != nil {
			q.Q(err)
		}
		if resp != nil {
			proxies := make(map[string]map[string]TcpProxyInfo)
			_ = json.NewDecoder(resp.Body).Decode(&proxies)
			// q.Q(proxies)
			resp.Body.Close()
		}
	}
}

// TcpProxyCmd
// tcpproxy start [from] [to]
//
//	example: tcpproxy start :1234 10.253.0.2:5566
//
// Note that the port should not be used by other process or unsafe port (0-1024)
//
// tcpproxy stop [from]
//
//	example: tcpproxy stop :1234
func TcpProxyCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")

	if strings.HasPrefix(cmd, "tcpproxy start ") {
		if len(ws) < 4 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		} else if len(ws) > 4 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 4 but got %d", len(ws))
			return cmdinfo
		}
		err := TcpProxy(ws[2], ws[3])
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "tcpproxy stop") {
		if len(ws) < 3 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		} else if len(ws) > 3 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 3 but got %d", len(ws))
			return cmdinfo
		}
		err := TcpProxyClose(ws[2])
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}
