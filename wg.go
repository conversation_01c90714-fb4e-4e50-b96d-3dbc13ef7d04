package mnms

// this is the main entry point for the mnms vpn service
// this will do the following:
// 1. generate wireguard config file
// 2. start wireguard service

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/qeof/q"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

type WgStatusPeerInfo struct {
	AllowedIPs          string `json:"allowed_ips"`
	LatestHandshake     string `json:"latest_handshake"`
	Transfer            string `json:"transfer"`
	PersistentKeepalive string `json:"persistent_keepalive"`
}

// WgStatusInfo is the wireguard status information from wg command
type WgStatusInfo struct {
	Interface string                      `json:"interface"`
	PublicKey string                      `json:"public_key"`
	Peers     map[string]WgStatusPeerInfo `json:"peers"`
}

type WgConfig struct {
	Interface WgInterface `json:"interface"`
	Peers     []WgPeer    `json:"peers"`
}

// WgInterface is the lite wireguard interface configuration
type WgInterface struct {
	PrivateKey string   `json:"-"`
	PublicKey  string   `json:"public_key"`
	Addresses  []string `json:"addresses"`
	ListenPort int      `json:"listen_port"`
	MTU        uint16   `json:"mtu"`
	DNS        []string `json:"dns"`
	PreUp      []string `json:"pre_up"`
	PostUp     []string `json:"post_up"`
	PreDown    []string `json:"pre_down"`
	PostDown   []string `json:"post_down"`
}

// WgPeer is the lite wireguard peer configuration.
type WgPeer struct {
	PublicKey           string   `json:"public_key"`
	PresharedKey        string   `json:"preshared_key"`
	AllowedIPs          []string `json:"allowed_ips"`
	Endpoint            string   `json:"endpoint"`
	PersistentKeepalive uint16   `json:"persistent_keepalive"`
}

type WgInfo struct {
	Enabled bool   `json:"enabled"`
	Name    string `json:"name"`

	Config WgConfig     `json:"config"`
	Status WgStatusInfo `json:"status"`
}

func init() {
	QC.WgData = make(map[string]WgInfo)
}

// WgInit initializes the wireguard configuration from the config file if name.conf exists
// otherwise, it will generate a new key
func WgInit(wgname string, enabled bool) {
	q.Q("wireguard init", wgname, enabled)
	info, ok := QC.WgData[QC.Name]
	if !ok {
		q.Q("wireguard info not found, creating a new one")
		info = WgInfo{
			Enabled: enabled,
			Name:    wgname,
		}
	} else {
		q.Q("wireguard config found, updating it")
		info.Enabled = enabled
		info.Name = wgname
	}

	if !info.Enabled {
		q.Q("wireguard disabled, skipping")
		QC.WgData[QC.Name] = info
		return
	}

	if _, err := os.Stat(fmt.Sprintf("%s.conf", info.Name)); err == nil {
		q.Q("wireguard file found, reading it")
		conf, err := WgReadConfig(fmt.Sprintf("%s.conf", info.Name))
		if err != nil {
			q.Q("wireguard read config failed", err)
		}
		info.Config = conf
		QC.WgData[QC.Name] = info
		return
	}

	q.Q("wireguard file not found or failed to load, generating a new key")
	key, err := generateWgKey()
	if err != nil {
		q.Q("generate wireguard key failed", err)
	}

	info.Config.Interface.PrivateKey = key.String()
	pubKey, err := WgPublicKey(key.String())
	if err != nil {
		q.Q("generate wireguard public key failed", err)
	}
	info.Config.Interface.PublicKey = pubKey
	QC.WgData[QC.Name] = info
}

func assignParameters(src []string, dst ...*string) {
	for i, s := range src {
		*dst[i] = s
	}
}

// generateWgKey generates a new key pair from wireguard wgtypes
func generateWgKey() (wgtypes.Key, error) {
	key, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return wgtypes.Key{}, err
	}
	return key, nil
}

var wgReservedNames = []string{
	"CON", "PRN", "AUX", "NUL",
	"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
	"LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9",
}

const (
	wgServiceNameForbidden = "$"
	wgNetshellDllForbidden = "\\/:*?\"<>|\t"
	wgSpecialChars         = "/\\<>:\"|?*\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19\x1a\x1b\x1c\x1d\x1e\x1f\x00"
)

var wgAllowedNameFormat = regexp.MustCompile("^[a-zA-Z0-9_=+.-]{1,32}$")

func isWgReserved(name string) bool {
	if len(name) == 0 {
		return false
	}
	for _, reserved := range wgReservedNames {
		if strings.EqualFold(name, reserved) {
			return true
		}
		for i := len(name) - 1; i >= 0; i-- {
			if name[i] == '.' {
				if strings.EqualFold(name[:i], reserved) {
					return true
				}
				break
			}
		}
	}
	return false
}

func wgHasSpecialChars(name string) bool {
	return strings.ContainsAny(name, wgSpecialChars) || strings.ContainsAny(name, wgNetshellDllForbidden) || strings.ContainsAny(name, wgServiceNameForbidden)
}

func WgTunnelNameIsValid(name string) bool {
	// Aside from our own restrictions, let's impose the Windows restrictions first
	if isWgReserved(name) || wgHasSpecialChars(name) {
		return false
	}
	return wgAllowedNameFormat.MatchString(name)
}

func WgGenerateConfig() error {
	QC.WgMutex.Lock()
	info := QC.WgData[QC.Name]
	QC.WgMutex.Unlock()
	conf := info.Config
	if !WgTunnelNameIsValid(info.Name) {
		return fmt.Errorf("invalid tunnel name")
	}

	if !info.Enabled {
		return fmt.Errorf("mnms wg is disabled")
	}

	if len(conf.Interface.Addresses) == 0 {
		return fmt.Errorf("invalid wireguard interface")
	}

	if len(conf.Interface.PrivateKey) == 0 {
		return fmt.Errorf("invalid wireguard private key")
	}

	s := fmt.Sprintf(`[Interface]
PrivateKey = %s
Address = %s`, conf.Interface.PrivateKey, strings.Join(conf.Interface.Addresses, ", "))
	// listen port
	if conf.Interface.ListenPort > 0 {
		s += fmt.Sprintf(`
ListenPort = %d`, conf.Interface.ListenPort)
	}
	// mtu
	if conf.Interface.MTU > 0 {
		s += fmt.Sprintf(`
MTU = %d`, conf.Interface.MTU)
	}
	// dns
	if len(conf.Interface.DNS) > 0 {
		s += `
DNS = `
		for i, dns := range conf.Interface.DNS {
			s += dns
			if i != len(conf.Interface.DNS)-1 {
				s += ", "
			}
		}
	}
	// PreUp, PostUp, PreDown, PostDown
	for _, preup := range conf.Interface.PreUp {
		s += fmt.Sprintf(`
PreUp = %s`, preup)
	}
	for _, postup := range conf.Interface.PostUp {
		s += fmt.Sprintf(`
PostUp = %s`, postup)
	}
	for _, predown := range conf.Interface.PreDown {
		s += fmt.Sprintf(`
PreDown = %s`, predown)
	}
	for _, postdown := range conf.Interface.PostDown {
		s += fmt.Sprintf(`
PostDown = %s`, postdown)
	}
	// peer adds
	for _, peer := range conf.Peers {
		// add a new line
		s += `
`
		s += fmt.Sprintf(`
[Peer]
PublicKey = %s
AllowedIPs = `, peer.PublicKey)
		for i, addr := range peer.AllowedIPs {
			s += addr
			if i != len(peer.AllowedIPs)-1 {
				s += ", "
			}
		}
		// preshared key
		if len(peer.PresharedKey) > 0 {
			s += fmt.Sprintf(`
PresharedKey = %s`, peer.PresharedKey)
		}
		// endpoint
		if len(peer.Endpoint) > 0 {
			s += fmt.Sprintf(`
Endpoint = %s`, peer.Endpoint)
		}
		// persistent keepalive
		if peer.PersistentKeepalive > 0 {
			s += fmt.Sprintf(`
PersistentKeepalive = %d`, peer.PersistentKeepalive)
		}
	}

	// write to file
	return os.WriteFile(fmt.Sprintf("%s.conf", info.Name), []byte(s), 0644)
}

func WgPublicKey(privateKey string) (string, error) {

	if len(privateKey) == 0 {
		return "", fmt.Errorf("invalid wireguard private key")
	}
	wgKey, err := wgtypes.ParseKey(privateKey)
	if err != nil {
		return "", err
	}
	pubKey := wgKey.PublicKey()
	return pubKey.String(), nil
}

func validateWgInstalled() error {
	var wg string = ""
	// if windows
	if runtime.GOOS == "windows" {
		wg = "wireguard.exe"
	}

	// if linux
	if runtime.GOOS == "linux" {
		wg = "wg"
	}

	if wg == "" {
		return fmt.Errorf("unsupported os")
	}

	// check if wireguard is installed
	_, err := exec.LookPath(wg)
	if err != nil {
		return err
	}

	return nil
}

// WgStart runs wireguard on windows or linux
func WgStart() error {
	QC.WgMutex.Lock()
	info := QC.WgData[QC.Name]
	QC.WgMutex.Unlock()

	// validate wireguard is installed
	err := validateWgInstalled()
	if err != nil {
		q.Q("wireguard is not installed", err)
		return err
	}

	// get current path
	dir, err := os.Getwd()
	if err != nil {
		q.Q("failed to get current path", err)
		return err
	}

	// if windows
	if runtime.GOOS == "windows" {
		// tunnel service: wireguard /installtunnelservice C:\path\to\some\myconfname.conf
		cmd := exec.Command("wireguard.exe", "/installtunnelservice", fmt.Sprintf("%s\\%s.conf", dir, info.Name))
		err = cmd.Run()
		if err != nil {
			q.Q("failed to install wireguard service", err)
			return err
		}
		return nil
	}

	if runtime.GOOS == "linux" {
		// Usage: wg-quick [ up | down | save | strip ] [ CONFIG_FILE | INTERFACE ]
		cmd := exec.Command("wg-quick", "up", fmt.Sprintf("%s/%s.conf", dir, info.Name))
		err := cmd.Run()
		if err != nil {
			q.Q("failed to start wireguard service", err)
			return err
		}
		return nil
	}

	return fmt.Errorf("unsupported os %s", runtime.GOOS)
}

// WgStop stops wireguard on windows or linux
func WgStop() error {
	QC.WgMutex.Lock()
	info := QC.WgData[QC.Name]
	QC.WgMutex.Unlock()
	// validate wireguard is installed
	err := validateWgInstalled()
	if err != nil {
		q.Q("wireguard is not installed", err)
		return err
	}

	// get current path
	dir, err := os.Getwd()
	if err != nil {
		q.Q("failed to get current path", err)
		return err
	}
	if runtime.GOOS == "windows" {
		//wireguard /uninstalltunnelservice myconfname
		cmd := exec.Command("wireguard.exe", "/uninstalltunnelservice", info.Name)
		err := cmd.Run()
		if err != nil {
			q.Q("failed to uninstall wireguard service", err)
			return err
		}
		return nil
	}

	if runtime.GOOS == "linux" {
		// Usage: wg-quick [ up | down | save | strip ] [ CONFIG_FILE | INTERFACE ]
		cmd := exec.Command("wg-quick", "down", fmt.Sprintf("%s/%s.conf", dir, info.Name))
		err := cmd.Run()
		if err != nil {
			q.Q("failed to stop wireguard service", err)
			return err
		}
		return nil
	}

	return fmt.Errorf("unsupported os %s", runtime.GOOS)
}

func IsWgRunning() (bool, error) {
	ws, err := WgStatus()
	if err != nil {
		q.Q(err)
		return false, err
	}

	if len(ws.Interface) > 0 {
		return true, nil
	}

	return false, nil
}

// Wgstatus returns the status of wireguard interface
// if iface is empty, it will return the status of mnms wgname
func WgStatus(iface ...string) (WgStatusInfo, error) {
	var name string
	if len(iface) == 0 {
		name = QC.WgData[QC.Name].Name
	} else {
		name = iface[0]
	}
	cmd := exec.Command("wg", "show", name)
	out, err := cmd.Output()
	if err != nil {
		if len(string(out)) == 0 {
			// change cmd output error
			return WgStatusInfo{}, errors.New("please check if wireguard is installed, running and as admin")
		}
	}

	// parse output
	wgInfo := WgStatusInfo{}
	wgInfo.Peers = make(map[string]WgStatusPeerInfo)
	lines := strings.Split(string(out), "\n")
	wgPeerInfo := WgStatusPeerInfo{}
	var wgPeer string
	for _, line := range lines {

		if strings.Contains(line, "interface:") {
			wgInfo.Interface = strings.TrimSpace(strings.Split(line, ":")[1])
		}
		if strings.Contains(line, "public key:") {
			wgInfo.PublicKey = strings.TrimSpace(strings.Split(line, ":")[1])
		}
		if strings.Contains(line, "peer:") {
			if wgPeer != "" {
				wgInfo.Peers[wgPeer] = wgPeerInfo
			}
			wgPeer = strings.TrimSpace(strings.Split(line, ":")[1])
			wgPeerInfo = WgStatusPeerInfo{}
		}

		if strings.Contains(line, "allowed ips:") {
			wgPeerInfo.AllowedIPs = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "latest handshake:") {
			wgPeerInfo.LatestHandshake = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "transfer:") {
			wgPeerInfo.Transfer = strings.TrimSpace(strings.Split(line, ":")[1])
		}

		if strings.Contains(line, "persistent keepalive:") {
			wgPeerInfo.PersistentKeepalive = strings.TrimSpace(strings.Split(line, ":")[1])
		}
	}
	if wgPeer != "" {
		wgInfo.Peers[wgPeer] = wgPeerInfo
	}
	return wgInfo, nil
}

func WgReadConfig(filename string) (WgConfig, error) {
	// can't use ini package because wireguard config files may have duplicate peer sections
	// so we need to read the file line by line and parse it ourselves
	file, err := os.Open(filename)
	if err != nil {
		return WgConfig{}, err
	}
	defer file.Close()
	conf := WgConfig{}
	// remove .conf from filename
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if strings.HasPrefix(line, "PrivateKey") {
			line = strings.ReplaceAll(line, " ", "")
			conf.Interface.PrivateKey = strings.TrimPrefix(line, "PrivateKey=")
			pubKey, err := WgPublicKey(conf.Interface.PrivateKey)
			if err != nil {
				return WgConfig{}, err
			}
			conf.Interface.PublicKey = pubKey
		} else if strings.HasPrefix(line, "Address") {
			line = strings.ReplaceAll(line, " ", "")
			conf.Interface.Addresses = strings.Split(strings.Split(line, "=")[1], ",")
		} else if strings.HasPrefix(line, "ListenPort") {
			port, err := strconv.Atoi(strings.TrimSpace(strings.Split(line, "=")[1]))
			if err != nil {
				return WgConfig{}, err
			}
			conf.Interface.ListenPort = port
		} else if strings.HasPrefix(line, "DNS") {
			line = strings.ReplaceAll(line, " ", "")
			conf.Interface.DNS = strings.Split(strings.Split(line, "=")[1], ",")
		} else if strings.HasPrefix(line, "MTU") {
			mtu, err := strconv.ParseUint(strings.TrimSpace(strings.Split(line, "=")[1]), 10, 16)
			if err != nil {
				return WgConfig{}, err
			}
			conf.Interface.MTU = uint16(mtu)
		} else if strings.HasPrefix(line, "PreUp") {
			preup := strings.TrimSpace(strings.Split(line, "=")[1])
			conf.Interface.PreUp = append(conf.Interface.PreUp, preup)
		} else if strings.HasPrefix(line, "PostUp") {
			postup := strings.TrimSpace(strings.Split(line, "=")[1])
			conf.Interface.PostUp = append(conf.Interface.PostUp, postup)
		} else if strings.HasPrefix(line, "PreDown") {
			predown := strings.TrimSpace(strings.Split(line, "=")[1])
			conf.Interface.PreDown = append(conf.Interface.PreDown, predown)
		} else if strings.HasPrefix(line, "PostDown") {
			postdown := strings.TrimSpace(strings.Split(line, "=")[1])
			conf.Interface.PostDown = append(conf.Interface.PostDown, postdown)
		} else if strings.HasPrefix(line, "[Peer]") {
			peer := WgPeer{}
			for scanner.Scan() {
				line := strings.TrimSpace(scanner.Text())
				// fmt.Println("xxx", line)
				if strings.HasPrefix(line, "PublicKey") {
					line = strings.ReplaceAll(line, " ", "")
					peer.PublicKey = strings.TrimPrefix(line, "PublicKey=")
				} else if strings.HasPrefix(line, "AllowedIPs") {
					line = strings.ReplaceAll(line, " ", "")
					peer.AllowedIPs = strings.Split(strings.Split(line, "=")[1], ",")
				} else if strings.HasPrefix(line, "Endpoint") {
					peer.Endpoint = strings.Split(line, "=")[1]
				} else if strings.HasPrefix(line, "PersistentKeepalive") {
					keepAlive, err := strconv.ParseUint(strings.TrimSpace(strings.Split(line, "=")[1]), 10, 16)
					if err != nil {
						return WgConfig{}, err
					}
					peer.PersistentKeepalive = uint16(keepAlive)
				} else if strings.HasPrefix(line, "PresharedKey") {
					line = strings.ReplaceAll(line, " ", "")
					peer.PresharedKey = strings.TrimPrefix(line, "PresharedKey=")
				} else if strings.HasPrefix(line, "[Peer]") {
					conf.Peers = append(conf.Peers, peer)
					peer = WgPeer{}
				}
			}
			conf.Peers = append(conf.Peers, peer)
		}
	}
	return conf, nil
}

// WgPub publishes wireguard information to the root server every interval seconds
func WgPub(interval int) {
	for {
		time.Sleep(time.Duration(interval) * time.Second)
		if QC.RootURL == "" {
			q.Q("WgPub: RootURL not set")
			continue
		}

		QC.WgMutex.Lock()
		data := QC.WgData
		QC.WgMutex.Unlock()
		info, ok := data[QC.Name]
		if !ok {
			q.Q("WgPub: no wireguard data for", QC.Name)
			continue
		}

		// get wireguard status
		wgStatus, err := WgStatus()
		if err != nil {
			q.Q("WgPub: WgStatus error: ", err)
		}

		info.Status = wgStatus
		data[QC.Name] = info
		jsonBytes, err := json.Marshal(data)
		if err != nil {
			q.Q("WgPub: json.Marshal error: ", err)
			continue
		}
		resp, err := PostWithToken(QC.RootURL+"/api/v1/wg", QC.AdminToken, bytes.NewBuffer(jsonBytes))
		if err != nil {
			q.Q("WgPub: PostWithToken error: ", err)
			continue
		}
		if resp != nil {
			respData := make(map[string]WgInfo)
			_ = json.NewDecoder(resp.Body).Decode(&respData)
			// q.Q("respData")
			resp.Body.Close()
		}
	}
}

/*
WgCmd handles all wireguard commands
wg config interface addresses set [addr1] [addr2] [addr3] ...
wg config interface listenport set [port]
wg config interface mtu set [mtu]
wg config interface dns set [dns1] [dns2] [dns3] ...
wg config interface set [address] [...listenport] [...mtu] [...dns]
wg config interface preup add [command]
wg config interface preup delete [index]
wg config interface postup add [command]
wg config interface postup delete [index]
wg config interface predown add [command]
wg config interface predown delete [index]
wg config interface postdown add [command]
wg config interface postdown delete [index]
wg config peer pubkey set [index] [pubkey]
wg config peer allowedips set [index] [addr1] [addr2] [addr3] ...
wg config peer endpoint set [index] [endpoint]
wg config peer persistentkeepalive set [index] [seconds]
wg config peer presharedkey set [index] [presharedkey]
wg config peer add [pubkey] [allowedips] [...endpoint] [...persistentkeepalive] [...presharedkey]
wg config peer delete [index]
wg config generate
wg config show
wg start
wg stop
wg status
*/
func WgCmd(cmdinfo *CmdInfo) *CmdInfo {
	if !QC.WgData[QC.Name].Enabled {
		cmdinfo.Status = "error: wireguard not enabled"
		return cmdinfo
	}

	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	ws := strings.Split(cmd, " ")

	if strings.HasPrefix(cmd, "wg config interface addresses set ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface addresses set [addr1] [addr2] [addr3] ..."
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.Addresses = ws[5:]
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface listenport set ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface listenport set [port]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		port, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid port"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.ListenPort = port
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface mtu set ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface mtu set [mtu]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		mtu, err := strconv.ParseUint(ws[5], 10, 16)
		if err != nil {
			cmdinfo.Status = "error: invalid mtu"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.MTU = uint16(mtu)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface dns set ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface dns set [dns1] [dns2] [dns3] ..."
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.DNS = ws[5:]
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface preup add ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface preup add [command]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.PreUp = append(info.Config.Interface.PreUp, strings.Join(ws[5:], " "))
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface preup delete ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface preup delete [index]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		index, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if index >= len(info.Config.Interface.PreUp) {
			cmdinfo.Status = "error: invalid index"
			QC.WgMutex.Unlock()
			return cmdinfo
		}
		info.Config.Interface.PreUp = append(info.Config.Interface.PreUp[:index], info.Config.Interface.PreUp[index+1:]...)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface postup add ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface postup add [command]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.PostUp = append(info.Config.Interface.PostUp, strings.Join(ws[5:], " "))
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface postup delete ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface postup delete [index]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		index, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if index >= len(info.Config.Interface.PostUp) {
			cmdinfo.Status = "error: invalid index"
			QC.WgMutex.Unlock()
			return cmdinfo
		}
		info.Config.Interface.PostUp = append(info.Config.Interface.PostUp[:index], info.Config.Interface.PostUp[index+1:]...)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface predown add ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface predown add [command]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.PreDown = append(info.Config.Interface.PreDown, strings.Join(ws[5:], " "))
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface predown delete ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface predown delete [index]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		index, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if index >= len(info.Config.Interface.PreDown) {
			cmdinfo.Status = "error: invalid index"
			QC.WgMutex.Unlock()
			return cmdinfo
		}
		info.Config.Interface.PreDown = append(info.Config.Interface.PreDown[:index], info.Config.Interface.PreDown[index+1:]...)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface postdown add ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface postdown add [command]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.PostDown = append(info.Config.Interface.PostDown, strings.Join(ws[5:], " "))
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface postdown delete ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config interface postdown delete [index]"
			return cmdinfo
		} else if len(ws) > 6 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 6 but got %d", len(ws))
			return cmdinfo
		}
		index, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if index >= len(info.Config.Interface.PostDown) {
			cmdinfo.Status = "error: invalid index"
			QC.WgMutex.Unlock()
			return cmdinfo
		}
		info.Config.Interface.PostDown = append(info.Config.Interface.PostDown[:index], info.Config.Interface.PostDown[index+1:]...)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config interface set ") {
		len := len(ws)
		if len < 5 {
			cmdinfo.Status = "error: invalid command, wg config interface set [address] [...listenport] [...mtu] [...dns]"
			return cmdinfo
		}

		var address, listenport, mtu, dns string
		assignParameters(ws[4:], &address, &listenport, &mtu, &dns)
		q.Q(address, listenport, mtu, dns)
		var port int
		var err error
		if listenport == "" {
			port = 0
		} else {
			port, err = strconv.Atoi(listenport)
			if err != nil {
				cmdinfo.Status = "error: invalid port"
				return cmdinfo
			}
		}
		var mtuUint uint64
		if mtu == "" {
			mtuUint = 0
		} else {
			mtuUint, err = strconv.ParseUint(mtu, 10, 16)
			if err != nil {
				cmdinfo.Status = "error: invalid mtu"
				return cmdinfo
			}
		}
		addresses := strings.Split(address, ",")
		dnss := []string{}
		if dns != "" {
			dnss = strings.Split(dns, ",")
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Interface.Addresses = addresses
		info.Config.Interface.ListenPort = port
		info.Config.Interface.MTU = uint16(mtuUint)
		info.Config.Interface.DNS = dnss
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer pubkey set ") {
		if len(ws) < 7 {
			cmdinfo.Status = "error: invalid command, wg config peer pubkey set [peer index] [pubkey]"
			return cmdinfo
		} else if len(ws) > 7 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 7 but got %d", len(ws))
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers[i].PublicKey = ws[6]
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer allowedips set ") {
		if len(ws) < 7 {
			cmdinfo.Status = "error: invalid command, wg config peer allowedips set [peer index] [allowedip] [allowedip] ..."
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers[i].AllowedIPs = ws[6:]
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer endpoint set ") {
		if len(ws) < 7 {
			cmdinfo.Status = "error: invalid command, wg config peer endpoint set [peer index] [endpoint]"
			return cmdinfo
		} else if len(ws) > 7 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 7 but got %d", len(ws))
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers[i].Endpoint = ws[6]
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer persistentkeepalive set ") {
		if len(ws) < 7 {
			cmdinfo.Status = "error: invalid command, wg config peer persistentkeepalive set [peer index] [persistentkeepalive]"
			return cmdinfo
		} else if len(ws) > 7 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 7 but got %d", len(ws))
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		keepalive, err := strconv.ParseUint(ws[6], 10, 16)
		if err != nil {
			cmdinfo.Status = "error: invalid persistentkeepalive"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers[i].PersistentKeepalive = uint16(keepalive)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer presharedkey set ") {
		if len(ws) < 7 {
			cmdinfo.Status = "error: invalid command, wg config peer presharedkey set [peer index] [presharedkey]"
			return cmdinfo
		} else if len(ws) > 7 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 7 but got %d", len(ws))
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[5])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers[i].PresharedKey = ws[6]
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer add ") {
		if len(ws) < 6 {
			cmdinfo.Status = "error: invalid command, wg config peer add [pubkey] [allowedip] [...endpoint] [...persistentkeepalive] [...presharedkey]"
			return cmdinfo
		}

		var pubkey, allowedip, endpoint, persistentkeepalive, presharedkey string
		assignParameters(ws[4:], &pubkey, &allowedip, &endpoint, &persistentkeepalive, &presharedkey)
		var keepalive uint64
		var err error
		if persistentkeepalive == "" {
			keepalive = 0
		} else {
			keepalive, err = strconv.ParseUint(persistentkeepalive, 10, 16)
			if err != nil {
				cmdinfo.Status = "error: invalid persistentkeepalive"
				return cmdinfo
			}
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		info.Config.Peers = append(info.Config.Peers, WgPeer{
			PublicKey:           pubkey,
			AllowedIPs:          []string{allowedip},
			Endpoint:            endpoint,
			PersistentKeepalive: uint16(keepalive),
			PresharedKey:        presharedkey,
		})
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config peer delete ") {
		if len(ws) < 5 {
			cmdinfo.Status = "error: invalid command, wg config peer delete [peer index]"
			return cmdinfo
		} else if len(ws) > 5 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 5 but got %d", len(ws))
			return cmdinfo
		}

		i, err := strconv.Atoi(ws[4])
		if err != nil {
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		QC.WgMutex.Lock()
		info, ok := QC.WgData[QC.Name]
		if !ok {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: wg data not found"
			return cmdinfo
		}
		if i < 0 || i >= len(info.Config.Peers) {
			QC.WgMutex.Unlock()
			cmdinfo.Status = "error: invalid peer index"
			return cmdinfo
		}
		info.Config.Peers = append(info.Config.Peers[:i], info.Config.Peers[i+1:]...)
		QC.WgData[QC.Name] = info
		QC.WgMutex.Unlock()
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config generate") {
		if len(ws) > 3 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 3 but got %d", len(ws))
			return cmdinfo
		}
		err := WgGenerateConfig()
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg config show") {
		if len(ws) > 3 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 3 but got %d", len(ws))
			return cmdinfo
		}
		jsonBytes, err := json.MarshalIndent(QC.WgData, "", "    ")
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		cmdinfo.Result = string(jsonBytes)
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg start") {
		if len(ws) > 2 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
			return cmdinfo
		}
		err := WgStart()
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg stop") {
		if len(ws) > 2 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
			return cmdinfo
		}
		err := WgStop()
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		return cmdinfo
	}

	if strings.HasPrefix(cmd, "wg status") {
		if len(ws) > 2 {
			cmdinfo.Status = fmt.Sprintf("error: too many arguments, expected 2 but got %d", len(ws))
			return cmdinfo
		}
		info, err := WgStatus()
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		jsonBytes, err := json.MarshalIndent(info, "", "    ")
		if err != nil {
			cmdinfo.Status = "error: " + err.Error()
			return cmdinfo
		}
		cmdinfo.Status = "ok"
		cmdinfo.Result = string(jsonBytes)
		return cmdinfo
	}

	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}
