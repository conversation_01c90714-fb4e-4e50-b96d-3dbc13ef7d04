package spm

import (
	"github.com/flier/gohs/hyperscan"
)

type hyperScan struct {
	db      hyperscan.BlockDatabase
	pattern *hyperscan.Pattern
	s       *hyperscan.Scratch
}

func newHyperScan() *hyperScan {
	return &hyperScan{}
}

func (h *hyperScan) Build() error {
	db, err := hyperscan.NewManagedBlockDatabase(h.pattern)
	if err != nil {
		return err
	}
	s, err := hyperscan.NewManagedScratch(db)
	if err != nil {
		return err
	}
	h.s, h.db = s, db
	return nil
}

type ScanResult struct {
	From, To uint16
	Payload  []byte
}

func (h *hyperScan) MatchBytes(b []byte) (bool, error) {
	if h.db == nil || h.s == nil {
		return false, nil
	}
	r := false
	s, err := h.s.Clone()
	if err != nil {
		return r, err
	}
	handler := hyperscan.MatchHandler(func(id uint, from, to uint64, flags uint, context interface{}) error {
		r = true
		return hyperscan.ErrScanTerminated
	})
	defer s.Free()
	h.db.Scan(b, s, handler, nil)
	return r, nil
}

func (h *hyperScan) Scan(b []byte) ScanResult {
	if h.db == nil || h.s == nil {
		return ScanResult{}
	}
	scr, err := h.s.Clone()
	if err != nil {
		return ScanResult{}
	}
	matched := ScanResult{}
	handler := hyperscan.MatchHandler(func(id uint, from, to uint64, flags uint, context interface{}) error {
		matched = ScanResult{Payload: b[from:to], From: uint16(from), To: uint16(to)}
		return hyperscan.ErrScanTerminated
	})
	defer scr.Free()
	h.db.Scan(b, scr, handler, nil)
	return matched
}

func (h *hyperScan) LoadContent(c Content) error {
	var flags hyperscan.CompileFlag
	if c.Nocase {
		flags |= hyperscan.Caseless
	}
	pn := hyperscan.NewPattern(c.readPattern(), flags)
	pn.Id = c.Id
	h.pattern = pn
	return nil
}
