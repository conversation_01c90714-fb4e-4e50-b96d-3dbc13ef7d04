package detect

import (
	"mnms/idpsystem/idps/mpm"
)

type detectprefilterPayload struct {
	tempayloadfilter mpm.Algorithm
	payloadfilter    mpm.Algorithm
}

func newDetectprefilter() (*detectprefilterPayload, error) {
	return &detectprefilterPayload{}, nil
}

func (d *detectprefilterPayload) Build() error {
	if d.tempayloadfilter != nil {
		err := d.tempayloadfilter.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *detectprefilterPayload) loadContents(m mpm.Content) error {
	if d.tempayloadfilter == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		d.tempayloadfilter = a
	}
	return d.tempayloadfilter.AddContent(m)
}

func (d *detectprefilterPayload) Matches(b []byte) []int {
	if d.payloadfilter == nil {
		return []int{}
	}
	return d.payloadfilter.MatcheIds(b)
}

func (d *detectprefilterPayload) Apply() {
	defer func() {
		d.tempayloadfilter = nil
	}()
	d.payloadfilter = d.tempayloadfilter
}

func (d *detectprefilterPayload) LoadSignature(s *signature) error {
	switch s.sigtype {
	case sig_type_stream:
		return d.loadContents(*s.detect.PrefilterData())
	}
	return nil
}
