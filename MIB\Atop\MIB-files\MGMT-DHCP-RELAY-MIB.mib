-- *****************************************************************
-- DHCP-RELAY-MIB:  
-- ****************************************************************

MGMT-DHCP-RELAY-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    ;

mgmtDhcpRelayMib MODULE-IDENTITY
    LAST-UPDATED "201410100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the DHCP Relay MIB"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 55 }


MGMTDhcpRelayInformationPolicyType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the DHCP relay information policy type."
    SYNTAX      INTEGER { replace(0), keep(1), drop(2) }

mgmtDhcpRelayMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMib 1 }

mgmtDhcpRelayConfig OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMibObjects 2 }

mgmtDhcpRelayConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayConfig 1 }

mgmtDhcpRelayConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of DHCP relay. true is to enable DHCP relay and false is to
         disable it."
    ::= { mgmtDhcpRelayConfigGlobals 1 }

mgmtDhcpRelayConfigGlobalsServerIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Server IP address. This IP address is for DHCP server where the DHCP
         relay will relay DHCP packets to."
    ::= { mgmtDhcpRelayConfigGlobals 2 }

mgmtDhcpRelayConfigGlobalsInformationMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP relay information mode option operation. Possible
         modes are - Enabled: Enable DHCP relay information mode operation. When
         DHCP relay information mode operation is enabled, the agent inserts
         specific information (option 82) into a DHCP message when forwarding to
         DHCP server and removes it from a DHCP message when transferring to
         DHCP client. It only works when DHCP relay operation mode is enabled.
         Disabled: Disable DHCP relay information mode operation."
    ::= { mgmtDhcpRelayConfigGlobals 3 }

mgmtDhcpRelayConfigGlobalsInformationPolicy OBJECT-TYPE
    SYNTAX      MGMTDhcpRelayInformationPolicyType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP relay information option policy. When DHCP relay
         information mode operation is enabled, if the agent receives a DHCP
         message that already contains relay agent information it will enforce
         the policy. The 'Replace' policy is invalid when relay information mode
         is disabled."
    ::= { mgmtDhcpRelayConfigGlobals 4 }

mgmtDhcpRelayStatus OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMibObjects 3 }

mgmtDhcpRelayStatusStatistics OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayStatus 1 }

mgmtDhcpRelayStatusStatisticsServerPacketsRelayed OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Packets relayed from server to client."
    ::= { mgmtDhcpRelayStatusStatistics 1 }

mgmtDhcpRelayStatusStatisticsServerPacketErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Errors sending packets to servers."
    ::= { mgmtDhcpRelayStatusStatistics 2 }

mgmtDhcpRelayStatusStatisticsClientPacketsRelayed OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Packets relayed from client to server."
    ::= { mgmtDhcpRelayStatusStatistics 3 }

mgmtDhcpRelayStatusStatisticsClientPacketErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Errors sending packets to clients."
    ::= { mgmtDhcpRelayStatusStatistics 4 }

mgmtDhcpRelayStatusStatisticsAgentOptionErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets forwarded without agent options because there was no
         room."
    ::= { mgmtDhcpRelayStatusStatistics 5 }

mgmtDhcpRelayStatusStatisticsMissingAgentOption OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets dropped because no RAI option matching our ID was
         found."
    ::= { mgmtDhcpRelayStatusStatistics 6 }

mgmtDhcpRelayStatusStatisticsBadCircuitId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Circuit ID option in matching RAI option did not match any known
         circuit ID."
    ::= { mgmtDhcpRelayStatusStatistics 7 }

mgmtDhcpRelayStatusStatisticsMissingCircuitId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Circuit ID option in matching RAI option was missing."
    ::= { mgmtDhcpRelayStatusStatistics 8 }

mgmtDhcpRelayStatusStatisticsBadRemoteId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Remote ID option in matching RAI option did not match any known remote
         ID."
    ::= { mgmtDhcpRelayStatusStatistics 9 }

mgmtDhcpRelayStatusStatisticsMissingRemoteId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Remote ID option in matching RAI option was missing."
    ::= { mgmtDhcpRelayStatusStatistics 10 }

mgmtDhcpRelayStatusStatisticsReceiveServerPackets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Receive DHCP message from server."
    ::= { mgmtDhcpRelayStatusStatistics 11 }

mgmtDhcpRelayStatusStatisticsReceiveClientPackets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Receive DHCP message from client."
    ::= { mgmtDhcpRelayStatusStatistics 12 }

mgmtDhcpRelayStatusStatisticsReceiveClientAgentOption OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Receive relay agent information option from client."
    ::= { mgmtDhcpRelayStatusStatistics 13 }

mgmtDhcpRelayStatusStatisticsReplaceAgentOption OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Replace relay agent information option."
    ::= { mgmtDhcpRelayStatusStatistics 14 }

mgmtDhcpRelayStatusStatisticsKeepAgentOption OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Keep relay agent information option."
    ::= { mgmtDhcpRelayStatusStatistics 15 }

mgmtDhcpRelayStatusStatisticsDropAgentOption OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop relay agent information option."
    ::= { mgmtDhcpRelayStatusStatistics 16 }

mgmtDhcpRelayControl OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMibObjects 4 }

mgmtDhcpRelayControlClearStatistics OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The action to clear statistics. true is to clear the statistics data.
         false, then, does nothing."
    ::= { mgmtDhcpRelayControl 1 }

mgmtDhcpRelayMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMib 2 }

mgmtDhcpRelayMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMibConformance 1 }

mgmtDhcpRelayMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcpRelayMibConformance 2 }

mgmtDhcpRelayConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpRelayConfigGlobalsMode,
                  mgmtDhcpRelayConfigGlobalsServerIpAddress,
                  mgmtDhcpRelayConfigGlobalsInformationMode,
                  mgmtDhcpRelayConfigGlobalsInformationPolicy }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpRelayMibGroups 1 }

mgmtDhcpRelayStatusStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpRelayStatusStatisticsServerPacketsRelayed,
                  mgmtDhcpRelayStatusStatisticsServerPacketErrors,
                  mgmtDhcpRelayStatusStatisticsClientPacketsRelayed,
                  mgmtDhcpRelayStatusStatisticsClientPacketErrors,
                  mgmtDhcpRelayStatusStatisticsAgentOptionErrors,
                  mgmtDhcpRelayStatusStatisticsMissingAgentOption,
                  mgmtDhcpRelayStatusStatisticsBadCircuitId,
                  mgmtDhcpRelayStatusStatisticsMissingCircuitId,
                  mgmtDhcpRelayStatusStatisticsBadRemoteId,
                  mgmtDhcpRelayStatusStatisticsMissingRemoteId,
                  mgmtDhcpRelayStatusStatisticsReceiveServerPackets,
                  mgmtDhcpRelayStatusStatisticsReceiveClientPackets,
                  mgmtDhcpRelayStatusStatisticsReceiveClientAgentOption,
                  mgmtDhcpRelayStatusStatisticsReplaceAgentOption,
                  mgmtDhcpRelayStatusStatisticsKeepAgentOption,
                  mgmtDhcpRelayStatusStatisticsDropAgentOption }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpRelayMibGroups 2 }

mgmtDhcpRelayControlInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpRelayControlClearStatistics }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpRelayMibGroups 3 }

mgmtDhcpRelayMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcpRelayConfigGlobalsInfoGroup,
                       mgmtDhcpRelayStatusStatisticsInfoGroup,
                       mgmtDhcpRelayControlInfoGroup }

    ::= { mgmtDhcpRelayMibCompliances 1 }

END
