package detect

import (
	"github.com/google/gonids"
)

func newManageSignature() *manageSignature {
	return &manageSignature{tmpsSignatures: newsignatures(defaultsize)}
}

type manageSignature struct {
	tmpsSignatures     []*signature
	instanceSignatures []*signature
	size               int
}

func (m *manageSignature) LoadNidRule(r gonids.Rule) error {
	s := &signature{
		nidRule: r,
		sid:     r.SID,
	}
	switch s.nidRule.Action {
	case "alert":
		s.action = ActionAlert
	case "pass":
		s.action = ActionPass
	case "drop":
		s.action = ActionDrop | ActionAlert

	}
	m.loadRule(s)
	new := s.IsBidirectional()
	if new != nil {
		m.loadRule(new)
	}
	return nil
}

func (m *manageSignature) loadRule(s *signature) error {
	order := len(m.tmpsSignatures)
	s.interid = uint32(order)
	m.tmpsSignatures = append(m.tmpsSignatures, s)
	return nil
}

func (m *manageSignature) Build(info ExtraData) (err error) {
	defer func() {
		if err != nil {
			m.clean()

		}
	}()
	for _, s := range m.tmpsSignatures {
		err := s.BuildAll(info)
		if err != nil {
			return err
		}
	}
	return nil
}
func (m *manageSignature) clean() error {
	m.tmpsSignatures = newsignatures(defaultsize)
	return nil
}

func (m *manageSignature) Apply() error {
	m.size = len(m.tmpsSignatures)
	m.instanceSignatures = m.tmpsSignatures
	m.clean()
	return nil
}

func (m *manageSignature) GetSignatures() ([]*signature, error) {
	return m.tmpsSignatures, nil
}

func (m *manageSignature) FindSignatures(id int) (*signature, error) {
	if m.instanceSignatures == nil || id >= m.size {
		return nil, ErrorNotSignature
	}
	return m.instanceSignatures[id], nil

}
