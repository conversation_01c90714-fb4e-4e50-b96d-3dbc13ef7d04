package mnms

import (
	"context"
	"fmt"
	"sync"
	"time"

	llmclient "mnms/llm/client"

	"github.com/qeof/q"
)

// Chat represents a chat session with its configuration and message history
type Chat struct {
	mu           sync.RWMutex
	SessionID    string              `json:"session_id"`
	Messages     []llmclient.Message `json:"messages"`
	LastAccessed time.Time           `json:"last_accessed"`
	CreatedAt    time.Time           `json:"created_at"`
	Provider     string              `json:"provider"`
	Model        string              `json:"model"`
	APIKey       string              `json:"api_key"`
	BaseURL      string              `json:"base_url"`
}

// NewChat creates a new chat session with default configuration
func NewChat(sessionID string, provider, model, apikey, baseurl string) *Chat {
	return &Chat{
		SessionID:    sessionID,
		Provider:     provider,
		Model:        model,
		APIKey:       apikey,
		BaseURL:      baseurl,
		Messages:     []llmclient.Message{},
		LastAccessed: time.Now(),
		CreatedAt:    time.Now(),
	}
}

// AddMessage adds a message to the chat history
func (c *Chat) AddMessage(message llmclient.Message) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.Messages = append(c.Messages, message)
	c.LastAccessed = time.Now()
}

// AddMessages adds multiple messages to the chat history
func (c *Chat) AddMessages(messages []llmclient.Message) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.Messages = append(c.Messages, messages...)
	c.LastAccessed = time.Now()
}

// RemoveLastNMessages removes the last N messages from the chat history
func (c *Chat) RemoveLastNMessages(n int) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if n < 0 || n > len(c.Messages) {
		return
	}
	c.Messages = c.Messages[:len(c.Messages)-n]
	c.LastAccessed = time.Now()
}

// GetMessages returns a copy of the chat messages
func (c *Chat) GetMessages() []llmclient.Message {
	c.mu.RLock()
	defer c.mu.RUnlock()

	c.LastAccessed = time.Now()
	q.Q("GetMessages >", c.SessionID, c.Messages)

	// Return a copy to prevent external modification
	messages := make([]llmclient.Message, len(c.Messages))
	copy(messages, c.Messages)
	return messages
}

// UpdateMessages replaces the entire message history
func (c *Chat) UpdateMessages(messages []llmclient.Message) {
	c.mu.Lock()
	defer c.mu.Unlock()

	q.Q("UpdateMessages >", c.SessionID, messages)
	c.Messages = make([]llmclient.Message, len(messages))
	copy(c.Messages, messages)
	c.LastAccessed = time.Now()
}

// ChatManager manages multiple chat sessions
type ChatManager struct {
	mu               sync.RWMutex
	chats            map[string]*Chat
	maxTotalSessions int
	cleanupThreshold float64 // cleanup threshold (percentage)
}

// NewChatManager creates a new chat manager
func NewChatManager(maxTotalSessions int) *ChatManager {
	cm := &ChatManager{
		chats:            make(map[string]*Chat),
		maxTotalSessions: maxTotalSessions,
		cleanupThreshold: 0.9, // 90% 閾值
	}

	return cm
}

// RemoveSession manually removes a specific chat session
func (cm *ChatManager) RemoveSession(sessionID string) bool {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.chats[sessionID]; exists {
		delete(cm.chats, sessionID)
		return true
	}
	return false
}

// GetSessionCount returns the current number of active sessions
func (cm *ChatManager) GetSessionCount() int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	return len(cm.chats)
}

// Global chat manager instance
var globalChatManager = NewChatManager(1000)

// GetOrCreateChat gets an existing chat or creates a new one
func GetOrCreateChat(sessionID string, provider, model, apikey, baseurl string) *Chat {

	globalChatManager.mu.Lock()
	defer globalChatManager.mu.Unlock()

	if chat, exists := globalChatManager.chats[sessionID]; exists {
		return chat
	}

	chat := NewChat(sessionID, provider, model, apikey, baseurl)
	globalChatManager.chats[sessionID] = chat
	return chat
}

// GetChat gets an existing chat session
func GetChat(sessionID string) (*Chat, bool) {
	globalChatManager.mu.RLock()
	defer globalChatManager.mu.RUnlock()

	chat, exists := globalChatManager.chats[sessionID]
	return chat, exists
}

// Chat errors
var (
	ErrNilClient    = fmt.Errorf("client is nil")
	ErrNilSessionID = fmt.Errorf("session ID is required")
)

// ProcessChat the main function for chat
func ProcessChat(
	ctx context.Context,
	client llmclient.LLMClient,
	messages []llmclient.Message,
	sessionID string) ([]llmclient.Message, error) {
	if client == nil {
		return nil, ErrNilClient
	}

	if sessionID == "" {
		return nil, ErrNilSessionID
	}

	q.Q("Chat session >", sessionID)
	chat, exists := GetChat(sessionID)
	if !exists {
		return nil, fmt.Errorf("chat session not found")
	}

	// Update chat history with whole messages
	chat.UpdateMessages(messages)
	var newMessages []llmclient.Message

	allMessages := chat.GetMessages()
	q.Q("GenerateResponse >", allMessages)

	nimblTools, err := SupportedTools()
	if err != nil {
		return nil, fmt.Errorf("failed to get supported tools: %w", err)
	}

	tools := []llmclient.ToolDefine{}
	for _, tool := range nimblTools {
		tools = append(tools, llmclient.ToolDefine{
			Name:        tool.Name,
			Description: tool.Description,
			Args:        tool.Parameters,
		})
	}

	// Initial LLM response
	llmResponse, err := client.GenerateResponse(ctx, allMessages, tools)
	if err != nil {
		// Restore original messages on error
		chat.UpdateMessages(messages)
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	chat.AddMessage(llmResponse.Message)
	newMessages = append(newMessages, llmResponse.Message)

	// Handle tool calls in a loop until no more tool calls are generated
	maxToolCallRounds := 10 // Prevent infinite loops
	currentRound := 0

	for len(llmResponse.Message.ToolCalls) > 0 && currentRound < maxToolCallRounds {
		currentRound++
		toolResponse := []llmclient.Message{}

		// Execute all tool calls in the current response
		for _, toolCall := range llmResponse.Message.ToolCalls {
			args, err := llmclient.UnmarshalArguments(toolCall.Function.Arguments)
			if err != nil {
				toolMsg := llmclient.Message{
					Role:       "tool",
					ToolCallID: toolCall.ID,
					Name:       toolCall.Function.Name,
					Content:    fmt.Sprintf("Error parsing arguments: %v", err)}
				toolResponse = append(toolResponse, toolMsg)
				continue
			}

			callToolResponse := CallTool(ctx, toolCall.Function.Name, args)
			toolMsg := llmclient.Message{
				Role:       "tool",
				ToolCallID: toolCall.ID,
				Name:       toolCall.Function.Name,
				Content:    callToolResponse,
			}
			toolResponse = append(toolResponse, toolMsg)
		}

		// Add tool responses to chat and newMessages
		chat.AddMessages(toolResponse)
		newMessages = append(newMessages, toolResponse...)

		// Generate next response with tool results
		llmResponse, err = client.GenerateResponse(ctx, chat.GetMessages(), tools)
		if err != nil {
			return newMessages, fmt.Errorf("failed to generate response after tool calls: %w", err)
		}

		// Add the new response to chat and newMessages
		chat.AddMessage(llmResponse.Message)
		newMessages = append(newMessages, llmResponse.Message)
	}

	// Check if we hit the max rounds limit
	if currentRound >= maxToolCallRounds && len(llmResponse.Message.ToolCalls) > 0 {
		return newMessages, fmt.Errorf("maximum tool call rounds exceeded (%d)", maxToolCallRounds)
	}

	return newMessages, nil
}

func NewChatHistory(sessionID string, provider, model, apikey, baseurl string) *Chat {
	chat := GetOrCreateChat(sessionID, provider, model, apikey, baseurl)
	return chat
}

func GetChatHistory(sessionID string) ([]llmclient.Message, error) {
	chat, exists := GetChat(sessionID)
	if !exists {
		return nil, fmt.Errorf("chat session not found")
	}
	return chat.GetMessages(), nil
}

// RemoveChatSession manually removes a specific chat session
func RemoveChatSession(sessionID string) bool {
	return globalChatManager.RemoveSession(sessionID)
}

// GetChatSessionCount returns the current number of active chat sessions
func GetChatSessionCount() int {
	return globalChatManager.GetSessionCount()
}

// GetChatSessionThreshold returns the current threshold for automatic cleanup
func GetChatSessionThreshold() int {
	return int(float64(globalChatManager.maxTotalSessions) * globalChatManager.cleanupThreshold)
}

// UpdateChatMessage updates the chat message
func UpdateChatMessage(sessionID string, messages []llmclient.Message) {
	chat, exists := GetChat(sessionID)
	if !exists {
		return
	}
	chat.UpdateMessages(messages)
}
