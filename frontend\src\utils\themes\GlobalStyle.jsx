import React from "react";
import { Global, css } from "@emotion/react";
import { useTheme } from "antd-style";

const GlobalStyle = () => {
  const token = useTheme();
  return (
    <Global
      styles={css`
        html {
          direction: initial;
          &.rtl {
            direction: rtl;
          }
          overflow-x: hidden;
        }
        body {
          overflow-x: hidden;
          color: ${token.colorText};
          font-size: ${token.fontSize}px;
          font-family: ${token.fontFamily};
          line-height: ${token.lineHeight};
          background: ${token.colorBgLayout};
          transition: all 0s cubic-bezier(0.075, 0.82, 0.165, 1);
          letter-spacing: 0.02857em;
        }
        .ant-btn {
          text-transform: uppercase;
          font-weight: 500;
          letter-spacing: 0.02857em;
        }
        #root {
          height: 100%;
        }
        .table-row-light {
          background-color: ${token.colorBgContainer};
        }
        .table-row-dark {
          background-color: ${token.colorFillAlter};
        }
        .scrollbar-hidden::-webkit-scrollbar {
          display: none;
        }
        * {
          box-sizing: border-box;
          scrollbar-width: thin;
          scrollbar-color: ${token.colorTextSecondary} ${token.colorBgContainer};
        }
        *::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        *::-webkit-scrollbar-track {
          background: ${token.colorBgContainer};
          border-radius: 4px;
        }
        *::-webkit-scrollbar-thumb {
          background: ${token.colorTextSecondary};
          border-radius: 4px;
          border: 2px solid ${token.colorBgContainer};
        }
        *::-webkit-scrollbar-thumb:hover {
          background: ${token.colorPrimary};
        }
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: ${token.colorPrimary} ${token.colorBgContainer};
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: ${token.colorBgContainer};
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: ${token.colorTextSecondary};
          border-radius: 4px;
          border: 2px solid ${token.colorBgContainer};
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: ${token.colorPrimary};
        }
      `}
    />
  );
};

export default GlobalStyle;
