-- *****************************************************************
-- UPNP-MIB:  
-- ****************************************************************

MGMT-UPNP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtUpnpMib MODULE-IDENTITY
    LAST-UPDATED "201508170000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of UPnP"
    REVISION    "201508170000Z"
    DESCRIPTION
        "Add parameters: IpAddressingMode/IpInterfaceId"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 52 }


MGMTUpnpIpAddressingModeType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the type of IP addressing mode."
    SYNTAX      INTEGER { dynamic(0), static(1) }

mgmtUpnpMibObjects OBJECT IDENTIFIER
    ::= { mgmtUpnpMib 1 }

mgmtUpnpCapabilities OBJECT IDENTIFIER
    ::= { mgmtUpnpMibObjects 1 }

mgmtUpnpCapabilitiesSupportTtlWrite OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support the authority of writing function."
    ::= { mgmtUpnpCapabilities 1 }

mgmtUpnpConfig OBJECT IDENTIFIER
    ::= { mgmtUpnpMibObjects 2 }

mgmtUpnpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtUpnpConfig 1 }

mgmtUpnpConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of UPnP. true is to enable the functions of HTTPS and false
         is to disable it."
    ::= { mgmtUpnpConfigGlobals 1 }

mgmtUpnpConfigGlobalsTtl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The TTL value is used by UPnP to send SSDP advertisement messages.Valid
         values are in the range 1 to 255. "
    ::= { mgmtUpnpConfigGlobals 2 }

mgmtUpnpConfigGlobalsAdvertisingDuration OBJECT-TYPE
    SYNTAX      Integer32 (100..86400)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The duration, carried in SSDP packets, is used to inform a control
         point or control points how often it or they should receive an SSDP
         advertisement message from this switch. If a control point does not
         receive any message within the duration, it will think that the switch
         no longer exists. Due to the unreliable nature of UDP, in the standard
         it is recommended that such refreshing of advertisements to be done at
         less than one-half of the advertising duration. In the implementation,
         the switch sends SSDP messages periodically at the interval one-half of
         the advertising duration minus 30 seconds. Valid values are in the
         range 100 to 86400. "
    ::= { mgmtUpnpConfigGlobals 3 }

mgmtUpnpConfigGlobalsIpAddressingMode OBJECT-TYPE
    SYNTAX      MGMTUpnpIpAddressingModeType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IP addressing mode provides two ways to determine IP address
         assignment: dynamic(0) and static(1). Dynamic: Default selection for
         UPnP. UPnP module helps users choosing the IP address of the switch
         device. It finds the first available system IP address. Static: User
         specifies the IP interface VLAN for choosing the IP address of the
         switch device."
    ::= { mgmtUpnpConfigGlobals 4 }

mgmtUpnpConfigGlobalsStaticIpInterfaceId OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The index of the specific IP VLAN interface. It will only be applied
         when IP Addressing Mode is static. Valid configurable values ranges
         from 1 to 4095. Default value is 1."
    ::= { mgmtUpnpConfigGlobals 5 }

mgmtUpnpMibConformance OBJECT IDENTIFIER
    ::= { mgmtUpnpMib 2 }

mgmtUpnpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtUpnpMibConformance 1 }

mgmtUpnpMibGroups OBJECT IDENTIFIER
    ::= { mgmtUpnpMibConformance 2 }

mgmtUpnpCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUpnpCapabilitiesSupportTtlWrite }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUpnpMibGroups 1 }

mgmtUpnpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUpnpConfigGlobalsMode, mgmtUpnpConfigGlobalsTtl,
                  mgmtUpnpConfigGlobalsAdvertisingDuration,
                  mgmtUpnpConfigGlobalsIpAddressingMode,
                  mgmtUpnpConfigGlobalsStaticIpInterfaceId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUpnpMibGroups 2 }

mgmtUpnpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtUpnpCapabilitiesInfoGroup,
                       mgmtUpnpConfigGlobalsInfoGroup }

    ::= { mgmtUpnpMibCompliances 1 }

END
