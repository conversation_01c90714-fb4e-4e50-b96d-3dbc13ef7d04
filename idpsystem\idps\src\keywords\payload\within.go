package payload

import (
	"errors"
	"fmt"
)

func withinSetUp(l *list, v string) error {
	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("within needs preceding content option")
	}
	d, _ := dc.data.(*content)
	if d.flags&within == within {
		return errors.New("can't use multiple withins for the same content")

	}
	if (d.flags&depth) == depth || (d.flags&offset) == offset {
		return errors.New("can't use a relative " +
			"keyword like within/distance with a absolute " +
			"relative keyword like depth/offset for the same " +
			"content")

	}

	if d.flags&contentNegated > 0 && d.flags&fastpPattern > 0 {
		return errors.New("can't have a relative " +
			"negated keyword set along with a fast_pattern")

	}
	if d.flags&fastpPatternOnly > 0 {
		return errors.New("can't have a relative " +
			"keyword set along with a fast_pattern:only;")

	}
	if v[0] != '-' && isalpha(v[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(v, l, &index) {
			return fmt.Errorf("unknown byte_ keyword var seen in within - %s", v)
		}
		d.within = int32(index)
		d.flags |= withinVar
	} else {
		if stringParseI32RangeCheck(&d.within, 0, v, -contentValueMax, contentValueMax) < 0 {
			return fmt.Errorf("invalid value for within:%v", v)
		}
		if d.within < int32(d.contentLen) {
			return fmt.Errorf("within argument %d is "+
				"less than the content length %d which is invalid, since "+
				"this will never match.  Invalidating signature",
				d.within, d.contentLen)

		}
	}

	d.flags |= within
	prev := detectGetLastSMFromLists(l, []detectedId{detectContent, detectPCRE})
	if prev == nil {
		return nil
	}

	if prev.detectedID == detectContent {
		ct, _ := prev.data.(*content)
		if ct.flags&fastpPatternOnly > 0 {
			return errors.New("previous keyword " +
				"has a fast_pattern:only; set. Can't " +
				"have relative keywords around a fast_pattern " +
				"only content")
		}
		ct.flags |= withinNext
	} else if prev.detectedID == detectPCRE {
		//to dop

	}
	return nil
}
