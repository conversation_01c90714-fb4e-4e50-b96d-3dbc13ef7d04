import { api } from "./api";

export const aiassistApi = api.injectEndpoints({
  endpoints: (builder) => ({
    setLLMsession: builder.mutation({
      query: (data) => ({
        url: `api/v1/llm/session/new`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["aiassist"],
    }),
    getLLMsession: builder.query({
      query: (data) => `api/v1/llm/session?session_id=${data}`,
      providesTags: ["aiassist"],
    }),
    getLLMsessionList: builder.query({
      query: () => `api/v1/llm/session/list`,
      providesTags: ["aiassist"],
    }),
    deleteLLMsession: builder.mutation({
      query: (data) => ({
        url: `api/v1/llm/session/delete`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["aiassist"],
    }),
    chatWithLLM: builder.mutation({
      query: (data) => ({
        url: `api/v1/llm/session/chat`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["aiassist"],
    }),
    updateLLMsessionMessages: builder.mutation({
      query: (data) => ({
        url: `api/v1/llm/session/update`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["aiassist"],
    }),
  }),
});

export const {
  useSetLLMsessionMutation,
  useGetLLMsessionQuery,
  useGetLLMsessionListQuery,
  useDeleteLLMsessionMutation,
  useChatWithLLMMutation,
  useUpdateLLMsessionMessagesMutation,
} = aiassistApi;
