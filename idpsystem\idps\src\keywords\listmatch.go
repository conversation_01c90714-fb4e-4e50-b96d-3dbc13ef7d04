package keywords

import (
	"fmt"
	"mnms/idpsystem/idps/src/keywords/icmp"
	"mnms/idpsystem/idps/src/keywords/ip"
	"mnms/idpsystem/idps/src/keywords/payload"
	"mnms/idpsystem/idps/src/keywords/tcp"
	"strings"
)

type ListMatcher interface {
	SetUp(any) error
}

func NewListMatch(v string) (ListMatcher, error) {
	v = strings.ToLower(v)
	if v, ok := listMatchMap()[v]; ok {
		return v, nil
	}
	return nil, fmt.Errorf("not supported KeyWord:%v", v)
}

func listMatchMap() map[string]ListMatcher {
	keywordmap := map[string]ListMatcher{
		"ttl":        ip.NewTTL(),
		"id":         ip.NewId(),
		"seq":        tcp.NewSeq(),
		"ack":        tcp.NewAck(),
		"tcp.mss":    tcp.NewMss(),
		"itype":      icmp.NewItype(),
		"icode":      icmp.NewIcode(),
		"icmp_id":    icmp.NewId(),
		"icmp_seq":   icmp.NewSeq(),
		"icmpv6.mtu": icmp.NewV6Mtu(),
		"rpc":        payload.NewRpc(),
	}
	return keywordmap
}
