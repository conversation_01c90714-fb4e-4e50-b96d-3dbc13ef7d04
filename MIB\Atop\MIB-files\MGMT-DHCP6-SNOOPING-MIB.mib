-- *****************************************************************
-- DHCP6-SNOOPING-MIB:  
-- ****************************************************************

MGMT-DHCP6-SNOOPING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtDhcp6SnoopingMib MODULE-IDENTITY
    LAST-UPDATED "201805250000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the DHCPv6 Snooping MIB"
    REVISION    "201805250000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 146 }


MGMTDhcp6SnoopingModeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the mode of snooping."
    SYNTAX      INTEGER { disabled(0), enabled(1) }

MGMTDhcp6SnoopingPortTrustModeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the trust mode of a port."
    SYNTAX      INTEGER { untrusted(0), trusted(1) }

MGMTDhcp6SnoopingUnknownModeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the mode of operation when meeting a unknown
         IPv6 next header."
    SYNTAX      INTEGER { drop(0), allow(1) }

mgmtDhcp6SnoopingMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMib 1 }

mgmtDhcp6SnoopingConfig OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibObjects 2 }

mgmtDhcp6SnoopingConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingConfig 1 }

mgmtDhcp6SnoopingConfigGlobalsSnoopingMode OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingModeEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP snooping mode operation. Possible modes are -
         enable: Enable DHCP snooping mode operation. When DHCP snooping mode
         operation is enabled, the DHCP request messages will be forwarded to
         trusted ports and only allow reply packets from trusted ports. disable:
         Disable DHCP snooping mode operation."
    ::= { mgmtDhcp6SnoopingConfigGlobals 1 }

mgmtDhcp6SnoopingConfigGlobalsUnknownNextHeaderMode OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingUnknownModeEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP snooping mode operation. Possible modes are - allow:
         Allow packets with unknown IPv6 ext. headers. drop: Drop packets with
         unknown IPv6 ext. header."
    ::= { mgmtDhcp6SnoopingConfigGlobals 2 }

mgmtDhcp6SnoopingConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6SnoopingConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of DHCPv6 Snooping port configuration parameters"
    ::= { mgmtDhcp6SnoopingConfig 2 }

mgmtDhcp6SnoopingConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtDhcp6SnoopingConfigInterfaceIfIndex }
    ::= { mgmtDhcp6SnoopingConfigInterfaceTable 1 }

MGMTDhcp6SnoopingConfigInterfaceEntry ::= SEQUENCE {
    mgmtDhcp6SnoopingConfigInterfaceIfIndex    MGMTInterfaceIndex,
    mgmtDhcp6SnoopingConfigInterfaceTrustMode  MGMTDhcp6SnoopingPortTrustModeEnum
}

mgmtDhcp6SnoopingConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcp6SnoopingConfigInterfaceEntry 1 }

mgmtDhcp6SnoopingConfigInterfaceTrustMode OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingPortTrustModeEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP snooping port mode. Possible port modes are -
         trusted: Configures the port as trusted source of the DHCP messages.
         untrusted: Configures the port as untrusted source of the DHCP
         messages."
    ::= { mgmtDhcp6SnoopingConfigInterfaceEntry 2 }

mgmtDhcp6SnoopingStatus OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibObjects 3 }

mgmtDhcp6SnoopingStatusGlobals OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingStatus 1 }

mgmtDhcp6SnoopingStatusGlobalsLastChangeTs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Timestamp for last change to snooping tables content."
    ::= { mgmtDhcp6SnoopingStatusGlobals 1 }

mgmtDhcp6SnoopingStatusClientTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6SnoopingStatusClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of known DHCPv6 clients"
    ::= { mgmtDhcp6SnoopingStatus 2 }

mgmtDhcp6SnoopingStatusClientEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingStatusClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcp6SnoopingStatusClientClientDuidHash }
    ::= { mgmtDhcp6SnoopingStatusClientTable 1 }

MGMTDhcp6SnoopingStatusClientEntry ::= SEQUENCE {
    mgmtDhcp6SnoopingStatusClientClientDuidHash  Unsigned32,
    mgmtDhcp6SnoopingStatusClientClientDuid      MGMTDisplayString,
    mgmtDhcp6SnoopingStatusClientMacAddress      MacAddress,
    mgmtDhcp6SnoopingStatusClientIfIndex         MGMTInterfaceIndex
}

mgmtDhcp6SnoopingStatusClientClientDuidHash OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Client DUID hash value."
    ::= { mgmtDhcp6SnoopingStatusClientEntry 1 }

mgmtDhcp6SnoopingStatusClientClientDuid OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Client DUID."
    ::= { mgmtDhcp6SnoopingStatusClientEntry 2 }

mgmtDhcp6SnoopingStatusClientMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Client MAC address."
    ::= { mgmtDhcp6SnoopingStatusClientEntry 3 }

mgmtDhcp6SnoopingStatusClientIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcp6SnoopingStatusClientEntry 4 }

mgmtDhcp6SnoopingStatusAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6SnoopingStatusAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of addresses for known DHCPv6 clients"
    ::= { mgmtDhcp6SnoopingStatus 3 }

mgmtDhcp6SnoopingStatusAddressEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingStatusAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcp6SnoopingStatusAddressClientDuidHash,
                  mgmtDhcp6SnoopingStatusAddressInterfaceIaid }
    ::= { mgmtDhcp6SnoopingStatusAddressTable 1 }

MGMTDhcp6SnoopingStatusAddressEntry ::= SEQUENCE {
    mgmtDhcp6SnoopingStatusAddressClientDuidHash  Unsigned32,
    mgmtDhcp6SnoopingStatusAddressInterfaceIaid   Unsigned32,
    mgmtDhcp6SnoopingStatusAddressIpAddress       InetAddressIPv6,
    mgmtDhcp6SnoopingStatusAddressIaid            Unsigned32,
    mgmtDhcp6SnoopingStatusAddressVlanId          MGMTUnsigned16,
    mgmtDhcp6SnoopingStatusAddressLeaseTime       Unsigned32,
    mgmtDhcp6SnoopingStatusAddressDhcpServerIp    InetAddressIPv6
}

mgmtDhcp6SnoopingStatusAddressClientDuidHash OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Client DUID hash value."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 1 }

mgmtDhcp6SnoopingStatusAddressInterfaceIaid OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface Identity Association Identifier."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 2 }

mgmtDhcp6SnoopingStatusAddressIpAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The assigned IPv6 address."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 3 }

mgmtDhcp6SnoopingStatusAddressIaid OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface IAID."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 4 }

mgmtDhcp6SnoopingStatusAddressVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..4095)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The VLAN id of the VLAN."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 5 }

mgmtDhcp6SnoopingStatusAddressLeaseTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The lease time assigned to the address."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 6 }

mgmtDhcp6SnoopingStatusAddressDhcpServerIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address of the DHCP server that assigns the IP address."
    ::= { mgmtDhcp6SnoopingStatusAddressEntry 7 }

mgmtDhcp6SnoopingControl OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibObjects 4 }

mgmtDhcp6SnoopingStatistics OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibObjects 5 }

mgmtDhcp6SnoopingStatisticsInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcp6SnoopingStatisticsInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of port statistics in DHCPv6 Snooping "
    ::= { mgmtDhcp6SnoopingStatistics 1 }

mgmtDhcp6SnoopingStatisticsInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDhcp6SnoopingStatisticsInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcp6SnoopingStatisticsInterfaceIfIndex }
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceTable 1 }

MGMTDhcp6SnoopingStatisticsInterfaceEntry ::= SEQUENCE {
    mgmtDhcp6SnoopingStatisticsInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtDhcp6SnoopingStatisticsInterfaceRxSolicit         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxRequest         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxInfoRequest     Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxConfirm         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxRenew           Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxRebind          Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxDecline         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxAdvertise       Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxReply           Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxReconfigure     Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxRelease         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceRxDiscardUntrust  Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxSolicit         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxRequest         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxInfoRequest     Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxConfirm         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxRenew           Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxRebind          Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxDecline         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxAdvertise       Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxReply           Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxReconfigure     Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceTxRelease         Unsigned32,
    mgmtDhcp6SnoopingStatisticsInterfaceClearStats        MGMTUnsigned8
}

mgmtDhcp6SnoopingStatisticsInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 1 }

mgmtDhcp6SnoopingStatisticsInterfaceRxSolicit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received SOLICIT packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 2 }

mgmtDhcp6SnoopingStatisticsInterfaceRxRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received REQUEST packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 3 }

mgmtDhcp6SnoopingStatisticsInterfaceRxInfoRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received INFOREQUEST packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 4 }

mgmtDhcp6SnoopingStatisticsInterfaceRxConfirm OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received CONFIRM packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 5 }

mgmtDhcp6SnoopingStatisticsInterfaceRxRenew OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received RENEW packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 6 }

mgmtDhcp6SnoopingStatisticsInterfaceRxRebind OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received REBIND packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 7 }

mgmtDhcp6SnoopingStatisticsInterfaceRxDecline OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received DECLINE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 8 }

mgmtDhcp6SnoopingStatisticsInterfaceRxAdvertise OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received ADVERTISE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 9 }

mgmtDhcp6SnoopingStatisticsInterfaceRxReply OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received REPLY packetsreceived."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 10 }

mgmtDhcp6SnoopingStatisticsInterfaceRxReconfigure OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received RECONFIGURE packetsreceived."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 11 }

mgmtDhcp6SnoopingStatisticsInterfaceRxRelease OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received RELEASE packetsreceived."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 12 }

mgmtDhcp6SnoopingStatisticsInterfaceRxDiscardUntrust OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of received packets that was discarded as port was
         untrusted."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 13 }

mgmtDhcp6SnoopingStatisticsInterfaceTxSolicit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted SOLICIT packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 102 }

mgmtDhcp6SnoopingStatisticsInterfaceTxRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted REQUEST packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 103 }

mgmtDhcp6SnoopingStatisticsInterfaceTxInfoRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted INFOREQUEST packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 104 }

mgmtDhcp6SnoopingStatisticsInterfaceTxConfirm OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted CONFIRM packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 105 }

mgmtDhcp6SnoopingStatisticsInterfaceTxRenew OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted RENEW packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 106 }

mgmtDhcp6SnoopingStatisticsInterfaceTxRebind OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted REBIND packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 107 }

mgmtDhcp6SnoopingStatisticsInterfaceTxDecline OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted DECLINE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 108 }

mgmtDhcp6SnoopingStatisticsInterfaceTxAdvertise OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted ADVERTISE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 109 }

mgmtDhcp6SnoopingStatisticsInterfaceTxReply OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted REPLY packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 110 }

mgmtDhcp6SnoopingStatisticsInterfaceTxReconfigure OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted RECONFIGURE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 111 }

mgmtDhcp6SnoopingStatisticsInterfaceTxRelease OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of transmitted RELEASE packets."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 112 }

mgmtDhcp6SnoopingStatisticsInterfaceClearStats OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear statistics counters for this port."
    ::= { mgmtDhcp6SnoopingStatisticsInterfaceEntry 202 }

mgmtDhcp6SnoopingMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMib 2 }

mgmtDhcp6SnoopingMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibConformance 1 }

mgmtDhcp6SnoopingMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcp6SnoopingMibConformance 2 }

mgmtDhcp6SnoopingConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingConfigGlobalsSnoopingMode,
                  mgmtDhcp6SnoopingConfigGlobalsUnknownNextHeaderMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 1 }

mgmtDhcp6SnoopingConfigInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingConfigInterfaceIfIndex,
                  mgmtDhcp6SnoopingConfigInterfaceTrustMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 2 }

mgmtDhcp6SnoopingStatusGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingStatusGlobalsLastChangeTs }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 3 }

mgmtDhcp6SnoopingStatusClientTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingStatusClientClientDuidHash,
                  mgmtDhcp6SnoopingStatusClientClientDuid,
                  mgmtDhcp6SnoopingStatusClientMacAddress,
                  mgmtDhcp6SnoopingStatusClientIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 4 }

mgmtDhcp6SnoopingStatusAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingStatusAddressClientDuidHash,
                  mgmtDhcp6SnoopingStatusAddressInterfaceIaid,
                  mgmtDhcp6SnoopingStatusAddressIpAddress,
                  mgmtDhcp6SnoopingStatusAddressIaid,
                  mgmtDhcp6SnoopingStatusAddressVlanId,
                  mgmtDhcp6SnoopingStatusAddressLeaseTime,
                  mgmtDhcp6SnoopingStatusAddressDhcpServerIp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 5 }

mgmtDhcp6SnoopingStatisticsInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcp6SnoopingStatisticsInterfaceIfIndex,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxSolicit,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxRequest,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxInfoRequest,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxConfirm,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxRenew,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxRebind,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxDecline,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxAdvertise,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxReply,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxReconfigure,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxRelease,
                  mgmtDhcp6SnoopingStatisticsInterfaceRxDiscardUntrust,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxSolicit,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxRequest,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxInfoRequest,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxConfirm,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxRenew,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxRebind,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxDecline,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxAdvertise,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxReply,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxReconfigure,
                  mgmtDhcp6SnoopingStatisticsInterfaceTxRelease,
                  mgmtDhcp6SnoopingStatisticsInterfaceClearStats }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcp6SnoopingMibGroups 6 }

mgmtDhcp6SnoopingMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcp6SnoopingConfigGlobalsInfoGroup,
                       mgmtDhcp6SnoopingConfigInterfaceInfoGroup,
                       mgmtDhcp6SnoopingStatusGlobalsInfoGroup,
                       mgmtDhcp6SnoopingStatusClientTableInfoGroup,
                       mgmtDhcp6SnoopingStatusAddressTableInfoGroup,
                       mgmtDhcp6SnoopingStatisticsInterfaceTableInfoGroup }

    ::= { mgmtDhcp6SnoopingMibCompliances 1 }

END
