//go:build windows
// +build windows

package ips

import (
	"context"
	"fmt"
	"log"
	"net"
	"testing"
	"time"

	"github.com/go-ping/ping"
	"github.com/google/gonids"
)

func pingTest(ip string, count int, timeout time.Duration) (int, error) {
	var err error
	c := 0
	pinger, err := ping.NewPinger(ip)
	if err != nil {
		return c, err
	}
	pinger.SetPrivileged(true)
	pinger.Count = count
	pinger.OnRecv = func(pkt *ping.Packet) {
		fmt.Printf("%d bytes from %s: icmp_seq=%d time=%v\n",
			pkt.Nbytes, pkt.IPAddr, pkt.Seq, pkt.Rtt)
		c++
	}
	go func() {
		err = pinger.Run()
	}()
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	for range ctx.Done() {
		return c, err
	}
	return c, nil
}

func TestDropRule(t *testing.T) {
	defaultfilter = "!loopback && icmp"
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp ******* any <> $HOME_NET any (msg:"test";sid:%v;)`, id)

	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}
}

func TestDropExternalRule(t *testing.T) {
	defaultfilter = "!loopback && icmp"
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:"test";sid:%v;)`, id)

	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}
}

func TestMutitpleRule(t *testing.T) {
	defaultfilter = "!loopback && icmp"
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	id := RandUint16()
	v := fmt.Sprintf(`pass icmp %v any <> $HOME_NET 23 (msg:"test";sid:%v;)`, ip, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Nanosecond)
	id = RandUint16()
	v = fmt.Sprintf(`pass icmp %v any -> $HOME_NET 23 (msg:"test";sid:%v;)`, ip, id)
	r, err = gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}

}

func TestDropStartAndClose(t *testing.T) {
	defaultfilter = "!loopback && icmp"
	ip := "*******"
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	id := RandUint16()
	v := fmt.Sprintf(`drop icmp %v any -> $HOME_NET any (msg:"test";sid:%v;)`, ip, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	c, err := pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}
	log.Print("close and start")
	err = f.Close()
	if err != nil {
		t.Fatal(err)
	}

	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	c, err = pingTest(ip, 3, time.Second*3)
	if err != nil {
		t.Fatal(err)
	}
	if c != 0 {
		t.Fatal("should drop arp")
	}
}

var receivecount = 0
var port = 4096
var localip = "127.0.0.1"
var addr = localip + ":" + fmt.Sprint(port)

func TestDropPayloadRule(t *testing.T) {
	receivecount = 0
	defaultfilter = "loopback && udp.DstPort ==" + fmt.Sprint(port)
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"zxc";sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}

	payload := "zxccvb123456789"
	c := 10

	ch := make(chan bool)
	go udp(ch)
	<-ch

	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}
	defer listen.Close()
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestDropMutiplecontentRule(t *testing.T) {
	receivecount = 0
	defaultfilter = "loopback && udp.DstPort ==" + fmt.Sprint(port)
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"zxc";nocase;content:"abc";nocase;sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	c := 1

	ch := make(chan bool)
	go udp(ch)
	<-ch

	for i := 0; i < c; i++ {
		payload := "ZXCcvb12345abc6789"
		sendUDP(addr, payload)
	}

	for i := 0; i < c; i++ {
		payload := "ABCcvb123ZXC456789"
		sendUDP(addr, payload)
	}
	defer listen.Close()
	time.Sleep(time.Second * 3)
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestDropHexPayloadRule(t *testing.T) {
	receivecount = 0
	defaultfilter = "loopback && udp.DstPort ==" + fmt.Sprint(port)
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"|76 62|123";offset:4;sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}

	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	payload := "zxccvb123456789"
	c := 10

	ch := make(chan bool)
	go udp(ch)
	<-ch

	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}
	defer listen.Close()
	time.Sleep(time.Second * 3)
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}

}

func TestDropOffSetPayloadRule(t *testing.T) {
	receivecount = 0
	defaultfilter = "loopback && udp.DstPort ==" + fmt.Sprint(port)
	f, err := NewIps()
	if err != nil {
		t.Fatal(err)
	}
	id := RandUint16()
	v := fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"987";offset:3;sid:%v;)`, port, id)
	r, err := gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	v = fmt.Sprintf(`drop udp $HOME_NET any -> $HOME_NET %v (msg:"test";content:"cvb123";offset:3;;sid:%v;)`, port, id+1)
	r, err = gonids.ParseRule(v)
	if err != nil {
		t.Fatal(err)
	}
	err = f.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	err = f.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = f.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	err = f.Start()
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	payload := "zxccvb123456987789"
	c := 10

	ch := make(chan bool)
	go udp(ch)
	<-ch

	for i := 0; i < c; i++ {
		sendUDP(addr, payload)
	}
	defer listen.Close()
	time.Sleep(time.Second * 3)
	if receivecount != 0 {
		t.Fatal(fmt.Errorf("expect receice count:%v,actual:%v", 0, receivecount))
	}
}

var listen net.PacketConn

func udp(c chan bool) {
	var err error
	listen, err = net.ListenPacket("udp", addr)
	if err != nil {
		return
	}
	c <- true
	for {
		var data [1024]byte
		n, addr, err := listen.ReadFrom(data[:])
		if err != nil {
			continue
		}
		receivecount++
		log.Printf("data:%v addr:%v count:%v\n", string(data[:n]), addr, n)
	}
}

func sendUDP(addr, msg string) (string, error) {
	conn, _ := net.Dial("udp", addr)
	// send to socket
	_, err := conn.Write([]byte(msg))
	return "", err
}
