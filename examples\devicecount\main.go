package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
)

// We don't need the full Device struct here, as we only care about
// the number of elements in the array.
// We can unmarshal into a slice of empty interfaces or a slice of maps
// if we know they are objects. A slice of empty interfaces is most generic.

func main() {
	// Expecting the filename as a command-line argument
	if len(os.Args) < 2 {
		log.Fatalf("Usage: %s <input_devices_json_file>", os.Args[0])
	}
	filePath := os.Args[1]

	// Open the JSON file
	file, err := os.Open(filePath)
	if err != nil {
		log.Fatalf("Error opening file %s: %v", filePath, err)
	}
	defer file.Close()

	// Read the file content
	byteValue, err := io.ReadAll(file)
	if err != nil {
		log.Fatalf("Error reading file %s: %v", filePath, err)
	}

	// The input JSON is an array of device objects.
	// We can unmarshal it into a slice of interface{} because we
	// don't need to access the fields of each device, just count them.
	// Alternatively, you could use []map[string]interface{} if you are sure
	// each element is an object.
	var devices []interface{} // or var devices []map[string]interface{}

	// Unmarshal the JSON data into our slice
	if err := json.Unmarshal(byteValue, &devices); err != nil {
		// Check if the error is because the input was not a JSON array
		// (e.g., if it was an empty file, null, or a single object not in an array)
		// For an empty file or "null", Unmarshal might not error but result in a nil slice.
		// If it's truly malformed JSON or not an array, it will error.
		var syntaxError *json.SyntaxError
		var unmarshalTypeError *json.UnmarshalTypeError
		if err == io.EOF { // Empty file
			fmt.Println(0)
			return
		} else if json.Unmarshal([]byte("null"), &devices) == nil && string(byteValue) == "null" { // File contains just "null"
            fmt.Println(0)
            return
        } else if ok := (err == syntaxError || err == unmarshalTypeError); ok {
			log.Fatalf("Error unmarshaling JSON: The file content does not appear to be a valid JSON array. Error: %v", err)
		} else {
			log.Fatalf("Error unmarshaling JSON: %v", err)
		}
	}

	// The number of devices is simply the length of the slice
	count := len(devices)

	// Print the count to stdout
	fmt.Println(count)
}
