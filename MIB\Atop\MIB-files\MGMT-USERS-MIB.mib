-- *****************************************************************
-- USERS-MIB:  
-- ****************************************************************

MGMT-USERS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtUsersMib MODULE-IDENTITY
    LAST-UPDATED "201601190000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Users"
    REVISION    "201601190000Z"
    DESCRIPTION
        "Updated the maximum length of password"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 58 }


mgmtUsersMibObjects OBJECT IDENTIFIER
    ::= { mgmtUsersMib 1 }

mgmtUsersConfig OBJECT IDENTIFIER
    ::= { mgmtUsersMibObjects 2 }

mgmtUsersConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTUsersConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The table is Users onfiguration table. The index is user name."
    ::= { mgmtUsersConfig 1 }

mgmtUsersConfigEntry OBJECT-TYPE
    SYNTAX      MGMTUsersConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtUsersConfigUsername }
    ::= { mgmtUsersConfigTable 1 }

MGMTUsersConfigEntry ::= SEQUENCE {
    mgmtUsersConfigUsername   MGMTDisplayString,
    mgmtUsersConfigPrivilege  Unsigned32,
    mgmtUsersConfigEncrypted  TruthValue,
    mgmtUsersConfigPassword   MGMTDisplayString,
    mgmtUsersConfigAction     MGMTRowEditorState
}

mgmtUsersConfigUsername OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of user."
    ::= { mgmtUsersConfigEntry 1 }

mgmtUsersConfigPrivilege OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Privilege level of the user."
    ::= { mgmtUsersConfigEntry 2 }

mgmtUsersConfigEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the password is encrypted or not. TRUE means the
         password is encrypted. FALSE means the password is plain text."
    ::= { mgmtUsersConfigEntry 3 }

mgmtUsersConfigPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Password of the user. The password length depends on the type of
         password. If the password is encrypted, then the length is 128. If it
         is unencrypted, then the maximum length is 31."
    ::= { mgmtUsersConfigEntry 4 }

mgmtUsersConfigAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtUsersConfigEntry 100 }

mgmtUsersConfigTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtUsersConfig 2 }

mgmtUsersConfigTableRowEditorUsername OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of user."
    ::= { mgmtUsersConfigTableRowEditor 1 }

mgmtUsersConfigTableRowEditorPrivilege OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Privilege level of the user."
    ::= { mgmtUsersConfigTableRowEditor 2 }

mgmtUsersConfigTableRowEditorEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the password is encrypted or not. TRUE means the
         password is encrypted. FALSE means the password is plain text."
    ::= { mgmtUsersConfigTableRowEditor 3 }

mgmtUsersConfigTableRowEditorPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Password of the user. The password length depends on the type of
         password. If the password is encrypted, then the length is 128. If it
         is unencrypted, then the maximum length is 31."
    ::= { mgmtUsersConfigTableRowEditor 4 }

mgmtUsersConfigTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtUsersConfigTableRowEditor 100 }

mgmtUsersMibConformance OBJECT IDENTIFIER
    ::= { mgmtUsersMib 2 }

mgmtUsersMibCompliances OBJECT IDENTIFIER
    ::= { mgmtUsersMibConformance 1 }

mgmtUsersMibGroups OBJECT IDENTIFIER
    ::= { mgmtUsersMibConformance 2 }

mgmtUsersConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUsersConfigUsername, mgmtUsersConfigPrivilege,
                  mgmtUsersConfigEncrypted, mgmtUsersConfigPassword,
                  mgmtUsersConfigAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUsersMibGroups 1 }

mgmtUsersConfigTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUsersConfigTableRowEditorUsername,
                  mgmtUsersConfigTableRowEditorPrivilege,
                  mgmtUsersConfigTableRowEditorEncrypted,
                  mgmtUsersConfigTableRowEditorPassword,
                  mgmtUsersConfigTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUsersMibGroups 2 }

mgmtUsersMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtUsersConfigTableInfoGroup,
                       mgmtUsersConfigTableRowEditorInfoGroup }

    ::= { mgmtUsersMibCompliances 1 }

END
