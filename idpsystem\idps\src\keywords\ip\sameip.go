package ip

import (
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewSameIp() *sameIp {
	return &sameIp{ipLayer: protocol.NewIpLayer()}
}

type sameIp struct {
	ipLayer protocol.IPLayer
}

func (s *sameIp) SetUp(v string) error {
	return nil
}

func (s *sameIp) Match(packet gopacket.Packet) bool {
	ver, net, err := s.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		return v4.SrcIP.Equal(v4.DstIP)
	case protocol.IPV6:
		v6 := net.(*layers.IPv6)
		return v6.SrcIP.Equal(v6.DstIP)
	}

	return false
}
