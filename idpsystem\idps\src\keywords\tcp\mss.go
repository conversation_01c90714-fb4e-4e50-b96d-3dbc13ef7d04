package tcp

import (
	"encoding/binary"
	"fmt"
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewMss() *mss {
	return &mss{}
}

type mss struct {
	value    uint16
	max, min uint16
	operator string
	ck       func(uint16) bool
}

func (w *mss) SetUp(i any) error {
	v, err := gonidutil.ConvertToLenMatch(i)
	if err != nil {
		return err
	}
	w.operator = v.Operator
	switch w.operator {
	case "-":
		w.min, w.max = uint16(v.Min), uint16(v.Max)
	case "<":
		w.value = uint16(v.Num)
	case ">":
		w.value = uint16(v.Num)
	case "":
		w.value = uint16(v.Num)
	default:
		return fmt.Errorf("not supported:%v", w)
	}
	f, err := w.creatVerify()
	if err != nil {
		return err
	}
	w.ck = f
	return nil
}
func (w *mss) Match(packet gopacket.Packet) bool {
	tp := protocol.NewTcpParser()
	b := tp.Parse(packet)
	if !b {
		return false
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return false
	}
	if w.ck != nil && tcp.Options != nil && len(tcp.Options) > 0 && tcp.Options[0].OptionType == layers.TCPOptionKindMSS {
		dmss := binary.BigEndian.Uint16(tcp.Options[0].OptionData)
		return w.ck(dmss)
	}

	return false
}

func (m *mss) creatVerify() (func(uint16) bool, error) {
	switch m.operator {
	case "":
		return func(p uint16) bool { return p == m.value }, nil
	case "<":
		return func(p uint16) bool { return p < m.value }, nil
	case ">":
		return func(p uint16) bool { return p > m.value }, nil
	case "-":
		return func(p uint16) bool { return m.min < p && p < m.max }, nil
	default:
		return nil, fmt.Errorf("not supported:%v", m)
	}

}
