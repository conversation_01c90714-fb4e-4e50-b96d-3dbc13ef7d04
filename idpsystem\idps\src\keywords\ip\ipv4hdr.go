package ip

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewV4hdr() *v4hdr {
	return &v4hdr{ipLayer: protocol.NewIpLayer()}
}

type v4hdr struct {
	mpm.Algorithm
	ipLayer protocol.IPLayer
}

func (v *v4hdr) RetrieveBffer(packet gopacket.Packet) []byte {
	return v.getData(packet)
}

// mpm build
func (v *v4hdr) Build() error {
	if v.Algorithm != nil {
		err := v.Algorithm.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

// mpm MatcheIds
func (v *v4hdr) MatchIds(packet gopacket.Packet) []int {
	if v.Algorithm == nil {
		return []int{}
	}
	p := v.getData(packet)
	if p == nil {
		return []int{}
	}
	return v.Algorithm.MatcheIds(p)
}

// // mpm AddContent
func (v *v4hdr) AddContent(contents mpm.Content) error {
	if v.Algorithm == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		v.Algorithm = a
	}
	return v.Algorithm.AddContent(contents)
}

func (v *v4hdr) getData(packet gopacket.Packet) []byte {
	ver, net, err := v.ipLayer.ParseIP(packet)
	if err != nil {
		return nil
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		return v4.Contents
	}
	return nil
}
