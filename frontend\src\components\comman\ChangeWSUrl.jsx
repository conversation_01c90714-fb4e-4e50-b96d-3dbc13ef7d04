import { Button, Input, Space } from "antd";
import React, { useState } from "react";
import { useThemeStore } from "../../utils/themes/useStore";

const ChangeWSUrl = () => {
  const { wsURL, changeWsURL } = useThemeStore();
  const [inputWsUrl, setInputWsUrl] = useState(wsURL);
  return (
    <Space.Compact>
      <Input
        value={inputWsUrl}
        onChange={(e) => setInputWsUrl(e.target.value)}
      />
      <Button type="primary" onClick={() => changeWsURL(inputWsUrl)}>
        save
      </Button>
    </Space.Compact>
  );
};

export default ChangeWSUrl;
