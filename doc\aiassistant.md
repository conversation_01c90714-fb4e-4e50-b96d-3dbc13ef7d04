# AI Assistant Development Guide

## Overview

The NIMBL AI Assistant provides intelligent assistance through HTTP API endpoints. The system supports multiple LLM providers including OpenAI, Gemini, Ollama, and OpenRouter, with session-based chat functionality.

## Supported LLM Providers

The AI Assistant supports the following providers:
- **OpenAI**: GPT models (gpt-4o, gpt-3.5-turbo, etc.)
- **Gemini**: Google's Gemini models (gemini-1.5-flash-latest, etc.)
- **Ollama**: Local models (llama3:8b, qwen2:7b, etc.)
- **OpenRouter**: Access to various models through OpenRouter API

## HTTP API Endpoints

### Session Management

#### Create New Session
```http
POST /api/v1/llm/session/new
Content-Type: application/json

{
  "provider": "openai",
  "model": "gpt-4o",
  "api_key": "optional-api-key",
  "base_url": "optional-base-url"
}
```

**Request Fields:**
- `provider` (required): LLM provider ("openai", "gemini", "ollama", "openrouter")
- `model` (required): Model name (e.g., "gpt-4o", "gemini-1.5-flash-latest", "llama3:8b")
- `api_key` (optional): API key for the provider (falls back to environment variables if not provided)
- `base_url` (optional): Base URL for custom endpoints or Ollama instances

**Response:**
```json
{
  "session_id": "uuid-string",
  "provider": "openai",
  "model": "gpt-4o",
  "api_key": "sk-...",
  "base_url": "",
  "messages": [],
  "created_at": "2024-01-01T00:00:00Z",
  "last_activity": "2024-01-01T00:00:00Z"
}
```

#### Query Session
```http
POST /api/v1/llm/session/chat
Content-Type: application/json

{
  "session_id": "uuid-string",
  "messages": [
    {"role": "system", "content": "You are a helpful AI assistant"},
    {"role": "user", "content": "Your question here"}
  ]
}
```

**Request Fields:**
- `session_id` (required): UUID string for the chat session
- `messages` (required): Array of message objects with role ("system", "user", "assistant") and content

**Response:**
```json
{
  "session_id": "uuid-string",
  "new_messages": [
    {
      "role": "assistant",
      "content": "Assistant response here",
      "tool_calls": []
    }
  ]
}
```

#### Get Session Details
```http
GET /api/v1/llm/session?session_id={session_id}
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:** Returns the complete chat session structure including all messages and configuration.

#### List All Sessions
```http
GET /api/v1/llm/session/list
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:** Returns an array of all active chat sessions.

#### Delete Session
```http
POST /api/v1/llm/session/delete
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "session_id": "uuid-string"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chat session deleted successfully",
  "session_id": "uuid-string"
}
```

## Usage Examples

### Starting bbrootsvc
```bash
# Basic startup
./bbrootsvc.exe -n root

# With environment variables
env OPENAI_API_KEY=sk-xxx-xxx ./bbrootsvc.exe -n root
```

### Using the API

#### Example: OpenAI Chat
```bash
# Create session
curl -X POST http://localhost:27182/api/v1/llm/session/new \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "provider": "openai",
    "model": "gpt-4o"
  }'

# Query session
curl -X POST http://localhost:27182/api/v1/llm/session/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_id": "your-session-id",
    "messages": [
      {"role": "user", "content": "What is NIMBL?"}
    ]
  }'
```

#### Example: Ollama Local Model
```bash
# Create session with Ollama
curl -X POST http://localhost:27182/api/v1/llm/session/new \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "provider": "ollama",
    "model": "llama3:8b"
  }'

# Query with Ollama
curl -X POST http://localhost:27182/api/v1/llm/session/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_id": "your-session-id",
    "messages": [
      {"role": "user", "content": "Explain network monitoring"}
    ]
  }'
```

#### Example: Gemini
```bash
# Create session with Gemini
curl -X POST http://localhost:27182/api/v1/llm/session/new \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "provider": "gemini",
    "model": "gemini-1.5-flash-latest"
  }'

# Query with Gemini
curl -X POST http://localhost:27182/api/v1/llm/session/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_id": "your-session-id",
    "messages": [
      {"role": "user", "content": "Help me understand network topology"}
    ]
  }'
```

#### Example: With System Prompt and Custom API Key
```bash
# Create session with custom API key
curl -X POST http://localhost:27182/api/v1/llm/session/new \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "provider": "openai",
    "model": "gpt-4o",
    "api_key": "sk-your-custom-api-key"
  }'

# Query with system prompt and conversation history
curl -X POST http://localhost:27182/api/v1/llm/session/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_id": "your-session-id",
    "messages": [
      {"role": "system", "content": "You are a network security expert. Provide detailed technical explanations."},
      {"role": "user", "content": "Explain how VLAN tagging works"}
    ]
  }'
```

#### Example: Multi-turn Conversation
```bash
# Continue a conversation with message history
curl -X POST http://localhost:27182/api/v1/llm/session/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_id": "your-session-id",
    "messages": [
      {"role": "user", "content": "What is a network switch?"},
      {"role": "assistant", "content": "A network switch is a device that connects devices on a computer network..."},
      {"role": "user", "content": "How is it different from a router?"}
    ]
  }'
```

#### Example: Get Session Information
```bash
# Get specific session details
curl -X GET "http://localhost:27182/api/v1/llm/session?session_id=uuid-session-id" \
  -H "Authorization: Bearer YOUR_TOKEN"

# List all sessions
curl -X GET http://localhost:27182/api/v1/llm/session/list \
  -H "Authorization: Bearer YOUR_TOKEN"

# Delete a session
curl -X POST http://localhost:27182/api/v1/llm/session/delete \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "uuid-session-id"}'
```

### Example Enlish Queries

- List all devices with model name EHG7504-2SFP
- Show me the IP address and hostname for MAC 00-60-E9-1F-A6-03
- How many devices are online?
- Find devices scanned by nms1 with kernel version 5.60
- Which devices have gwd capability true?
- Show devices where the timestamp unix string is **********
- List devices with modelname EH7520-4G-4SFP and are online.
- What is the AP for device with IPAddress ***********?
- Find devices with hostname 'switch'
- Show MAC and IPAddress for devices with modelname 'RHG7528-R' and an AP version containing 'V2.68'

### Example Device Summarize

- Summarize 10 devices
  - next 10

## API Key Configuration

API keys can be configured in two ways:

### 1. Environment Variables (Recommended)
API keys are retrieved automatically based on the provider when not specified in the request:
- **OpenAI**: `OPENAI_API_KEY`
- **Gemini**: `GEMINI_API_KEY` 
- **OpenRouter**: `OPENROUTER_API_KEY`

### 2. Request Payload (Optional)
API keys can also be provided directly in the chat request payload:
- `api_key`: For OpenAI, Gemini, OpenRouter, and Ollama
- `base_url`: For custom Ollama endpoints or other OpenAI-compatible APIs

When provided in the request, these values take precedence over environment variables.

## Default Models

When `NIMBL_LLM_MODEL` is not set, the following defaults are used:
- **OpenAI**: `gpt-4o`
- **Gemini**: `gemini-1.5-flash-latest`
- **OpenRouter**: `openai/gpt-3.5-turbo`
- **Ollama**: Model must be explicitly specified (e.g., `llama3:8b`)

## Authentication

All AI Assistant endpoints require authentication using JWT tokens. Include the token in the Authorization header:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:
- `400 Bad Request`: Invalid request format or missing required fields
- `401 Unauthorized`: Missing or invalid authentication token
- `500 Internal Server Error`: LLM provider errors or server issues

## Frontend Integration

The system includes a React-based frontend with components for:
- Chat sessions (`ChatSession.jsx`)
- LLM settings configuration (`AIAssistSettings.jsx`)
- Standard JSON response handling

## Session Management

- Sessions are automatically created with unique UUIDs
- Chat history is maintained per session
- Sessions persist until manually deleted or server restart
- Maximum total sessions: 1000 (configurable)
- Session access times are tracked in `last_accessed` field

## Response Format

The chat query endpoint returns a structured response that includes:

```json
{
  "session_id": "uuid-string",
  "new_messages": [
    {
      "role": "assistant",
      "content": "Main assistant response text",
      "tool_calls": [
        {
          "id": "call_xyz123",
          "type": "function", 
          "function": {
            "name": "tool_name",
            "arguments": "{\"param1\": \"value1\"}"
          }
        }
      ]
    },
    {
      "role": "tool",
      "tool_call_id": "call_xyz123",
      "name": "tool_name",
      "content": "Tool execution result"
    }
  ]
}
```

## Streaming Responses

**Note:** The current implementation does NOT use Server-Sent Events (SSE). The query endpoint returns a standard JSON response with all new messages from the conversation turn.
