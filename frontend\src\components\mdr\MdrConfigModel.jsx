import { Form, InputNumber, Modal, Radio } from "antd";
import React from "react";

const MdrConfigModel = ({ open, onCancel, onOk, loading, record }) => {
  const { mac, modelname } = record;
  const [form] = Form.useForm();
  const defaultTitle='MDR Config'
  return (
    <Modal
      open={open}
      width={500}
      forceRender
      maskClosable={false}
      title={mac ? `${defaultTitle} (${mac} ${modelname})`: defaultTitle}
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk({ ...values, mac });
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_mdrconfigSet">
        <Form.Item
          label="Select MDR Zone"
          name="zone"
          rules={[
            {
              required: true,
              message: "Please select zone!",
            },
          ]}
        >
          <Radio.Group>
            <Radio.Button value={1}>ZONE 1</Radio.Button>
            <Radio.Button value={2}>ZONE 2</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Select MDR Mode"
          name="mode"
          rules={[
            {
              required: true,
              message: "Please select mode!",
            },
          ]}
        >
          <Radio.Group>
            <Radio.Button value="normal">NORMAL</Radio.Button>
            <Radio.Button value="turbo">TURBO</Radio.Button>
            <Radio.Button value="turbo+">TURBO+</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Select MDR Holding"
          name="holding"
          rules={[
            {
              required: true,
              message: "Please select holding!",
            },
          ]}
        >
          <Radio.Group>
            <Radio.Button value="enable">ENABLE</Radio.Button>
            <Radio.Button value="disable">DISABLE</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="MDR Speed"
          name="speed"
          rules={[
            {
              required: true,
              message: "Please input the speed!",
            },
          ]}
        >
          <InputNumber min={0} max={3000} style={{ width: "50%" }} />
        </Form.Item>
        <Form.Item
          label="Select MDR Dirction"
          name="direction"
          rules={[
            {
              required: true,
              message: "Please select direction!",
            },
          ]}
        >
          <Radio.Group>
            <Radio.Button value="cw">CW</Radio.Button>
            <Radio.Button value="ccw">CCW</Radio.Button>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="MDR Level"
          name="level"
          rules={[
            {
              required: true,
              message: "Please input the level!",
            },
          ]}
        >
          <InputNumber min={0} max={9} style={{ width: "50%" }} />
        </Form.Item>
        <Form.Item
          label="Select MDR Sensor"
          name="sensor"
          rules={[
            {
              required: true,
              message: "Please select sensor!",
            },
          ]}
        >
          <Radio.Group>
            <Radio.Button value="npm">NPN</Radio.Button>
            <Radio.Button value="pnp">PNP</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default MdrConfigModel;
