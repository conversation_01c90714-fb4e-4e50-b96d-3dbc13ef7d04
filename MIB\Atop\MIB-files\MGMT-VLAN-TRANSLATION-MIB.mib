-- *****************************************************************
-- VLAN-TRANSLATION-MIB:  
-- ****************************************************************

MGMT-VLAN-TRANSLATION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtVlanTranslationMib MODULE-IDENTITY
    LAST-UPDATED "201710250000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private VLAN TRANSLATION MIB."
    REVISION    "201710250000Z"
    DESCRIPTION
        "Added unidirectional translation"
    REVISION    "201406300000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 85 }


MGMTVlanTranslationDir ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The VLAN Translation Direction."
    SYNTAX      INTEGER { both(0), ingress(1), egress(2) }

mgmtVlanTranslationMibObjects OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMib 1 }

mgmtVlanTranslationCapabilities OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMibObjects 1 }

mgmtVlanTranslationCapabilitiesMaxNumberOfTranslations OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of VLAN translation mappings the user can store in the
         VLAN Translation mapping table."
    ::= { mgmtVlanTranslationCapabilities 1 }

mgmtVlanTranslationConfig OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMibObjects 2 }

mgmtVlanTranslationConfigTranslation OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationConfig 1 }

mgmtVlanTranslationConfigTranslationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanTranslationConfigTranslationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the VLAN translation mapping table.
         
         Here the user stores VLAN translation mappings (VID->TVID) inside
         groups that can later be activated on specific switch interfaces"
    ::= { mgmtVlanTranslationConfigTranslation 1 }

mgmtVlanTranslationConfigTranslationEntry OBJECT-TYPE
    SYNTAX      MGMTVlanTranslationConfigTranslationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a VLAN translation mapping stored
         inside a specific VLAN translation Group.
         
         The entry key is the Group ID and the source VLAN ID, while the value
         is the translated VID.
         
         "
    INDEX       { mgmtVlanTranslationConfigTranslationGroupId,
                  mgmtVlanTranslationConfigTranslationDirection,
                  mgmtVlanTranslationConfigTranslationVlanId }
    ::= { mgmtVlanTranslationConfigTranslationTable 1 }

MGMTVlanTranslationConfigTranslationEntry ::= SEQUENCE {
    mgmtVlanTranslationConfigTranslationGroupId    Integer32,
    mgmtVlanTranslationConfigTranslationDirection  MGMTVlanTranslationDir,
    mgmtVlanTranslationConfigTranslationVlanId     Integer32,
    mgmtVlanTranslationConfigTranslationTVlanId    Integer32,
    mgmtVlanTranslationConfigTranslationAction     MGMTRowEditorState
}

mgmtVlanTranslationConfigTranslationGroupId OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Group ID of the VLAN translation mapping key."
    ::= { mgmtVlanTranslationConfigTranslationEntry 1 }

mgmtVlanTranslationConfigTranslationDirection OBJECT-TYPE
    SYNTAX      MGMTVlanTranslationDir
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN Translation Direction."
    ::= { mgmtVlanTranslationConfigTranslationEntry 2 }

mgmtVlanTranslationConfigTranslationVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan ID of the VLAN translation mapping key."
    ::= { mgmtVlanTranslationConfigTranslationEntry 3 }

mgmtVlanTranslationConfigTranslationTVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Translated VLAN ID of the VLAN translation mapping."
    ::= { mgmtVlanTranslationConfigTranslationEntry 4 }

mgmtVlanTranslationConfigTranslationAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVlanTranslationConfigTranslationEntry 100 }

mgmtVlanTranslationConfigTranslationRowEditor OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationConfigTranslation 2 }

mgmtVlanTranslationConfigTranslationRowEditorGroupId OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Group ID of the VLAN translation mapping key."
    ::= { mgmtVlanTranslationConfigTranslationRowEditor 1 }

mgmtVlanTranslationConfigTranslationRowEditorDirection OBJECT-TYPE
    SYNTAX      MGMTVlanTranslationDir
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN Translation Direction."
    ::= { mgmtVlanTranslationConfigTranslationRowEditor 2 }

mgmtVlanTranslationConfigTranslationRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan ID of the VLAN translation mapping key."
    ::= { mgmtVlanTranslationConfigTranslationRowEditor 3 }

mgmtVlanTranslationConfigTranslationRowEditorTVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Translated VLAN ID of the VLAN translation mapping."
    ::= { mgmtVlanTranslationConfigTranslationRowEditor 4 }

mgmtVlanTranslationConfigTranslationRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVlanTranslationConfigTranslationRowEditor 100 }

mgmtVlanTranslationConfigInterfaces OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationConfig 2 }

mgmtVlanTranslationConfigInterfacesIfTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVlanTranslationConfigInterfacesIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the VLAN translation interface table. The number of interfaces
         is the total number of ports available on the switch. Each one of these
         interfaces can be set to use a specific Group of VLAN translation
         mappings, identified by the respective Group ID."
    ::= { mgmtVlanTranslationConfigInterfaces 1 }

mgmtVlanTranslationConfigInterfacesIfEntry OBJECT-TYPE
    SYNTAX      MGMTVlanTranslationConfigInterfacesIfEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Entries in this table represent switch interfaces and their matching
         VLAN translation Groups (identified through their Group IDs)"
    INDEX       { mgmtVlanTranslationConfigInterfacesIfIfIndex }
    ::= { mgmtVlanTranslationConfigInterfacesIfTable 1 }

MGMTVlanTranslationConfigInterfacesIfEntry ::= SEQUENCE {
    mgmtVlanTranslationConfigInterfacesIfIfIndex  MGMTInterfaceIndex,
    mgmtVlanTranslationConfigInterfacesIfGroupId  Integer32
}

mgmtVlanTranslationConfigInterfacesIfIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtVlanTranslationConfigInterfacesIfEntry 1 }

mgmtVlanTranslationConfigInterfacesIfGroupId OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Group ID of the interface configuration."
    ::= { mgmtVlanTranslationConfigInterfacesIfEntry 2 }

mgmtVlanTranslationMibConformance OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMib 2 }

mgmtVlanTranslationMibCompliances OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMibConformance 1 }

mgmtVlanTranslationMibGroups OBJECT IDENTIFIER
    ::= { mgmtVlanTranslationMibConformance 2 }

mgmtVlanTranslationCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtVlanTranslationCapabilitiesMaxNumberOfTranslations }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanTranslationMibGroups 1 }

mgmtVlanTranslationConfigTranslationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanTranslationConfigTranslationGroupId,
                  mgmtVlanTranslationConfigTranslationDirection,
                  mgmtVlanTranslationConfigTranslationVlanId,
                  mgmtVlanTranslationConfigTranslationTVlanId,
                  mgmtVlanTranslationConfigTranslationAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanTranslationMibGroups 2 }

mgmtVlanTranslationConfigTranslationRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtVlanTranslationConfigTranslationRowEditorGroupId,
                  mgmtVlanTranslationConfigTranslationRowEditorDirection,
                  mgmtVlanTranslationConfigTranslationRowEditorVlanId,
                  mgmtVlanTranslationConfigTranslationRowEditorTVlanId,
                  mgmtVlanTranslationConfigTranslationRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanTranslationMibGroups 3 }

mgmtVlanTranslationConfigInterfacesIfTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVlanTranslationConfigInterfacesIfIfIndex,
                  mgmtVlanTranslationConfigInterfacesIfGroupId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVlanTranslationMibGroups 4 }

mgmtVlanTranslationMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtVlanTranslationCapabilitiesInfoGroup,
                       mgmtVlanTranslationConfigTranslationTableInfoGroup,
                       mgmtVlanTranslationConfigTranslationRowEditorInfoGroup,
                       mgmtVlanTranslationConfigInterfacesIfTableInfoGroup }

    ::= { mgmtVlanTranslationMibCompliances 1 }

END
