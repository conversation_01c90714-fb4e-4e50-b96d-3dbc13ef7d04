import { useEffect, useState } from "react";
import React<PERSON>pex<PERSON><PERSON> from "react-apexcharts";
import { theme as antdTheme } from "antd";
import { useSelector } from "react-redux";
import { inventorySliceSelector } from "../../features/inventory/inventorySlice";
import { useTheme } from "antd-style";

const PieChart = () => {
  const { deviceData } = useSelector(inventorySliceSelector);
  const { appearance } = useTheme();
  const { token } = antdTheme.useToken();
  const [pieChartData, setPieChartData] = useState({
    series: [0],
    options: {
      chart: {
        width: 300,
        background: token.colorBgContainer,
        type: "pie",
      },
      theme: {
        mode: appearance,
      },
      legend: {
        position: "bottom",
      },
      plotOptions: {
        pie: {
          dataLabels: {
            offset: -15,
          },
        },
      },
      labels: ["no data"],
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          return opts.w.config.series[opts.seriesIndex];
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 150,
            },
            legend: {
              position: "bottom",
            },
          },
        },
      ],
    },
  });
  useEffect(() => {
    setPieChartData((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        theme: { mode: appearance },
        chart: { ...prev.options.chart, background: token.colorBgContainer },
      },
    }));
  }, [token, appearance]);

  useEffect(() => {
    if (deviceData.length > 0) {
      var counts = deviceData.reduce((p, c) => {
        var name = c.modelname;
        if (!p.hasOwnProperty(name)) {
          p[name] = 0;
        }
        p[name]++;
        return p;
      }, {});
      setPieChartData((prev) => ({
        ...prev,
        series: Object.values(counts),
        options: {
          ...prev.options,
          labels: Object.keys(counts),
        },
      }));
    }
  }, [deviceData]);

  return (
    <ReactApexChart
      options={pieChartData.options}
      series={pieChartData.series}
      type="pie"
      width={300}
    />
  );
};
export default PieChart;
