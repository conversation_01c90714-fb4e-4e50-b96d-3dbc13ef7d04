import { memo, useRef, useEffect, useState, useCallback } from "react";
import { Card } from "antd";
import { useTheme } from "antd-style";
import ChatInterface from "./chat-interface";
import { useChatStore } from "../../../features/chat/chat-store";
import { useSetLLMsessionMutation } from "../../../app/services/aiassistApi";

const ResizableChatPanel = memo(() => {
  const theme = useTheme();
  const panelRef = useRef(null);
  const resizerRef = useRef(null);
  const [isResizing, setIsResizing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    isChatOpen,
    chatWidth,
    toggleChat,
    setChatWidth,
    session_id,
    setSessionID,
  } = useChatStore();
  const [getSessionId, {}] = useSetLLMsessionMutation();

  // Handle mouse down on resizer
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  // Handle mouse move for resizing
  const handleMouseMove = useCallback(
    (e) => {
      if (!isResizing) return;

      const panel = panelRef.current;
      if (!panel) return;

      const rect = panel.getBoundingClientRect();
      const newWidth = window.innerWidth - e.clientX;

      // Constrain width between 400px and 800px
      const constrainedWidth = Math.max(400, Math.min(800, newWidth));
      setChatWidth(constrainedWidth);
    },
    [isResizing, setChatWidth]
  );

  // Handle mouse up to stop resizing
  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  // Add global mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "ew-resize";
      document.body.style.userSelect = "none";
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      document.body.style.cursor = "";
      document.body.style.userSelect = "";
    };
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Toggle expanded state
  const handleToggleExpand = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  if (!isChatOpen) return null;

  const panelWidth = isExpanded ? "100%" : `${chatWidth}px`;
  const maxWidth = isExpanded ? "none" : "800px";
  return (
    <>
      {/* Overlay for expanded mode */}
      {isExpanded && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            zIndex: 999,
          }}
          onClick={handleToggleExpand}
        />
      )}

      {/* Chat Panel */}
      <div
        ref={panelRef}
        style={{
          position: "fixed",
          top: 0, // Account for header height
          right: 0,
          bottom: 0,
          width: panelWidth,
          maxWidth,
          zIndex: isExpanded ? 1000 : 100,
          display: "flex",
          flexDirection: "row",
          boxShadow:
            theme.boxShadowSecondary || "0 4px 12px rgba(0, 0, 0, 0.15)",
          borderRadius: isExpanded ? "8px 0 0 8px" : "0",
          overflow: "hidden",
          transition: isExpanded ? "all 0.3s ease" : "none",
        }}
      >
        {/* Resizer Handle */}
        {!isExpanded && (
          <div
            ref={resizerRef}
            onMouseDown={handleMouseDown}
            style={{
              width: "4px",
              backgroundColor: theme.colorPrimary,
              cursor: "ew-resize",
              position: "relative",
              zIndex: 1,
              opacity: isResizing ? 1 : 0.3,
              transition: "opacity 0.2s ease",
            }}
            onMouseEnter={(e) => {
              e.target.style.opacity = "1";
            }}
            onMouseLeave={(e) => {
              if (!isResizing) {
                e.target.style.opacity = "0.3";
              }
            }}
          >
            {/* Resizer visual indicator */}
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: "2px",
                height: "40px",
                backgroundColor: theme.colorBgContainer,
                borderRadius: "1px",
              }}
            />
          </div>
        )}

        {/* Chat Content */}
        <Card
          style={{
            flex: 1,
            height: "100%",
            border: "none",
            borderRadius: 0,
          }}
          styles={{
            body: {
              padding: 0,
              height: "100%",
              display: "flex",
              flexDirection: "column",
            },
          }}
        >
          <ChatInterface
            handleToggleExpand={handleToggleExpand}
            isExpanded={isExpanded}
            toggleChat={toggleChat}
          />
        </Card>
      </div>
    </>
  );
});

ResizableChatPanel.displayName = "ResizableChatPanel";

export default ResizableChatPanel;
