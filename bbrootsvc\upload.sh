#!/usr/bin/env bash

# First curl command
echo "Updating document reference..."
curl -X POST http://localhost:27182/api/v1/ai-assist/doc-reference \
  -H "Content-Type: application/octet-stream" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MzY0MDMzNDQsInRpbWVzdGFtcCI6IjIwMjQtMTItMTBUMTQ6MTU6NDQrMDg6MDAiLCJ1c2VyIjoiYWRtaW4ifQ.zK1EDwU63QpmFqZ1XfWo46yc1G9-fYpE0YrckT_Bisg" \
  --data-binary @chromeme_store.json

echo ""
echo "-------------------------------------------------"
echo ""

# Second curl command
echo "Updating documents..."
curl -X POST http://localhost:27182/api/v1/ai-assist/documents \
  -H "Content-Type: application/octet-stream" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3MzY0MDMzNDQsInRpbWVzdGFtcCI6IjIwMjQtMTItMTBUMTQ6MTU6NDQrMDg6MDAiLCJ1c2VyIjoiYWRtaW4ifQ.zK1EDwU63QpmFqZ1XfWo46yc1G9-fYpE0YrckT_Bisg" \
  --data-binary @ai-assist-documents.json

echo ""
echo "Done."
