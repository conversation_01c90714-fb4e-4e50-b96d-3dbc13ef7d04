<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SSE Example with Token</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }

    #output {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
      height: 200px;
      overflow-y: scroll;
    }

    #output div,
    #output pre {
      margin-bottom: 10px;
    }

    .error {
      color: red;
    }

    .text {
      color: blue;
    }

    pre {
      background-color: #eee;
      padding: 5px;
      border: 1px solid #ccc;
      white-space: pre-wrap;
    }
  </style>
</head>

<body>
  <h1>SSE Example with Token</h1>
  <div>
    <label for="queryInput">Enter query:</label>
    <input type="text" id="queryInput" placeholder="Type your query here">
  </div>
  <div>
    <label for="tokenInput">Token:</label>
    <input type="text" id="tokenInput" placeholder="Token will be fetched automatically" readonly>
  </div>
  <button id="sendButton">Send</button>
  <button id="test">Test</button>
  <div id="output"></div>

  <script>
    // Append messages to the output
    function appendMessage(type, content) {
      const outputDiv = document.getElementById('output');
      let element;

      if (type === 'error') {
        element = document.createElement('div');
        element.className = 'error';
        element.textContent = `Error: ${content}`;
      } else if (type === 'text') {
        element = document.createElement('div');
        element.className = 'text';
        element.textContent = `${content}`;
      } else {
        element = document.createElement('pre');
        element.textContent = content;
      }

      outputDiv.appendChild(element);
      outputDiv.scrollTop = outputDiv.scrollHeight; // Auto-scroll to bottom
    }

    // Fetch the token on page load
    window.addEventListener('load', () => {
      const loginUrl = 'http://localhost:27182/api/v1/login';
      const loginData = {
        user: 'admin',
        password: 'default'
      };

      fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(loginData)
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`Login failed: ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.token) {
            document.getElementById('tokenInput').value = data.token;
          } else {
            throw new Error('Token not found in response');
          }
        })
        .catch(error => {
          appendMessage('error', `Failed to fetch token: ${error.message}`);
        });
    });

    // SSE Handling and API Interaction
    document.getElementById('sendButton').addEventListener('click', () => {
      const queryInput = document.getElementById('queryInput').value;
      const tokenInput = document.getElementById('tokenInput').value;

      if (!queryInput.trim()) {
        appendMessage('error', 'Please enter a query!');
        return;
      }

      if (!tokenInput.trim()) {
        appendMessage('error', 'Token is missing. Please refresh the page.');
        return;
      }

      const url = `http://localhost:27182/api/v1/ai-assist/actions/chat`;

      fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokenInput}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query: queryInput })
      })
        .then(response => {
          if (!response.ok) {
            appendMessage('error', response.statusText);
            return;
          }

          const reader = response.body.getReader();
          const decoder = new TextDecoder("utf-8");

          function read() {
            reader.read().then(({ done, value }) => {
              if (done) return;

              const text = decoder.decode(value, { stream: true });
    
                if (text.startsWith('data:')) {
                const rawData = text.substring(5).trim();
                  console.log("Raw data:", rawData);

                  // Debugging: Log character codes of the trailing part
                  const trailingChars = rawData.slice(-5);
                  console.log("Trailing characters:", trailingChars.split('').map(c => c.charCodeAt(0)));

                  // Remove trailing escaped characters and actual newline characters
                  const cleanedData = rawData.replace(/(\\n|\n)+$/, '');
                  console.log("Cleaned data:", cleanedData);
                  try {
                    const parsed = JSON.parse(cleanedData);
                    console.log(parsed);
                    if (parsed.type === 'error') {
                      appendMessage('error', parsed.message || cleanedData);
                    } else if (parsed.type === 'text') {
                      appendMessage('text', parsed.message || cleanedData);
                    } else {
                      appendMessage('code', JSON.stringify(parsed, null, 2));
                    }
                  } catch (error) {
                    console.error(error);
                    appendMessage('text', cleanedData);
                  }
                }
              

              read();
            });
          }

          read();
        })
        .catch(err => {
          appendMessage('error', `Connection error: ${err.message}`);
        });
    });
  </script>
</body>

</html>