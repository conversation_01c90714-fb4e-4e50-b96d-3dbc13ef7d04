import { describe, expect, it } from "vitest";
import { getTopologyData,setIsEditMode,setGPhysics,saveNodesPosition } from "../features/topology/topologySlice";
import { store } from "../app/store";
import { loginUser } from "../features/auth/userAuthSlice";
import newTopologySlice from "../features/topology/topologySlice"

describe("TopologyPage ", () => {

  const initialState = {
    isEditMode: false,
    gPhysics: false,
    clientsData: [],
    topologyData: {},
    reqClient: 'All Network Service',
    gData: { nodes: [], edges: [] },
    savedNodes: [],
  };

  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

it("Should handle getTopologyData correctly when successful", async () => {
  await store.dispatch(getTopologyData());
  const updatedState = store.getState().newTopology;
  expect(updatedState.topologyData).not.to.equal("");
  expect(updatedState.clientsData).not.to.equal([]);
  expect(updatedState.gData).not.to.equal("");
  
});
it('should handle setIsEditMode', () => {
  const nextState = newTopologySlice.reducer(initialState, setIsEditMode(true));
  expect(nextState.isEditMode).toBe(true);
});

it('should handle setGPhysics', () => {
  const nextState = newTopologySlice.reducer(initialState, setGPhysics(true));
  expect(nextState.gPhysics).toBe(true);
});

it('should handle saveNodesPosition', () => {
  const position = { id: 1, x: 100, y: 200 };
  const nextState = newTopologySlice.reducer(initialState, saveNodesPosition([position]));
  expect(nextState.savedNodes).toContainEqual(position);
});
})

