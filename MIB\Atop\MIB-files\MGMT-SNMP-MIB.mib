-- *****************************************************************
-- SNMP-MIB:  
-- ****************************************************************

MGMT-SNMP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    <PERSON>TInetAddress FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    ;

mgmtSnmpMib MODULE-IDENTITY
    LAST-UPDATED "201604060000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of SNMP"
    REVISION    "201604060000Z"
    DESCRIPTION
        "Improve descriptions for use of security_model. Change trap source
         table."
    REVISION    "201603070000Z"
    DESCRIPTION
        "Add trap receiver and source tables."
    REVISION    "201602230000Z"
    DESCRIPTION
        "Move IPv6 enabled communities to seperate SnmpConfigCommunity6Table.
         Rename community fields for backward compatibility."
    REVISION    "201602110000Z"
    DESCRIPTION
        "Add SourceIpType as well as SourceIPv6 / SourceIPv6PrefixSize to
         SnmpConfigCommunityTable."
    REVISION    "201512110000Z"
    DESCRIPTION
        "Remove version and communities from mgmtSnmpConfigGlobals, and update
         SnmpConfigCommunityTable with security_name"
    REVISION    "201507240000Z"
    DESCRIPTION
        "Correct the valid length of AuthPassword and PrivPassword, and correct
         the description of mgmtSnmpConfigViewViewType"
    REVISION    "201407180000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 36 }


MGMTSnmpAuthProtocl ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP authentication protocol"
    SYNTAX      INTEGER { snmpNoAuthProtocol(0),
                          snmpMD5AuthProtocol(1),
                          snmpSHAAuthProtocol(2) }

MGMTSnmpPrivProtocl ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP privacy protocol"
    SYNTAX      INTEGER { snmpNoPrivProtocol(0),
                          snmpDESPrivProtocol(1),
                          snmpAESPrivProtocol(2) }

MGMTSnmpSecurityLevel ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP authentication protocol"
    SYNTAX      INTEGER { snmpNoAuthNoPriv(1), snmpAuthNoPriv(2),
                          snmpAuthPriv(3) }

MGMTSnmpSecurityModel ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP security model"
    SYNTAX      INTEGER { any(0), v1(1), v2c(2), usm(3) }

MGMTSnmpTrapNotifyType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP trap notify type"
    SYNTAX      INTEGER { trap(0), inform(1) }

MGMTSnmpVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The version of SNMP"
    SYNTAX      INTEGER { snmpV1(0), snmpV2c(1), snmpV3(2) }

MGMTSnmpViewType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The SNMP view type"
    SYNTAX      INTEGER { included(0), excluded(1) }

mgmtSnmpMibObjects OBJECT IDENTIFIER
    ::= { mgmtSnmpMib 1 }

mgmtSnmpConfig OBJECT IDENTIFIER
    ::= { mgmtSnmpMibObjects 2 }

mgmtSnmpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 1 }

mgmtSnmpConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of SNMP."
    ::= { mgmtSnmpConfigGlobals 1 }

mgmtSnmpConfigGlobalsEngineId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(5..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 engine ID. The size of Engine ID is between 5 ~ 32 bytes.But
         all-zeros and all-'F's are not allowed."
    ::= { mgmtSnmpConfigGlobals 5 }

mgmtSnmpConfigCommunityTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 communities."
    ::= { mgmtSnmpConfig 2 }

mgmtSnmpConfigCommunityEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigCommunityEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index key is community."
    INDEX       { mgmtSnmpConfigCommunityName,
                  mgmtSnmpConfigCommunitySourceIP,
                  mgmtSnmpConfigCommunitySourceIPPrefixSize }
    ::= { mgmtSnmpConfigCommunityTable 1 }

MGMTSnmpConfigCommunityEntry ::= SEQUENCE {
    mgmtSnmpConfigCommunityName                MGMTDisplayString,
    mgmtSnmpConfigCommunitySourceIP            IpAddress,
    mgmtSnmpConfigCommunitySourceIPPrefixSize  Integer32,
    mgmtSnmpConfigCommunitySecret              MGMTDisplayString,
    mgmtSnmpConfigCommunityAction              MGMTRowEditorState
}

mgmtSnmpConfigCommunityName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of SNMP community"
    ::= { mgmtSnmpConfigCommunityEntry 1 }

mgmtSnmpConfigCommunitySourceIP OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv4 address"
    ::= { mgmtSnmpConfigCommunityEntry 2 }

mgmtSnmpConfigCommunitySourceIPPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv4 prefix size"
    ::= { mgmtSnmpConfigCommunityEntry 3 }

mgmtSnmpConfigCommunitySecret OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret of the SNMP community."
    ::= { mgmtSnmpConfigCommunityEntry 4 }

mgmtSnmpConfigCommunityAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigCommunityEntry 100 }

mgmtSnmpConfigCommunityTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 3 }

mgmtSnmpConfigCommunityTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of SNMP community"
    ::= { mgmtSnmpConfigCommunityTableRowEditor 1 }

mgmtSnmpConfigCommunityTableRowEditorSourceIP OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv4 address"
    ::= { mgmtSnmpConfigCommunityTableRowEditor 2 }

mgmtSnmpConfigCommunityTableRowEditorSourceIPPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv4 prefix size"
    ::= { mgmtSnmpConfigCommunityTableRowEditor 3 }

mgmtSnmpConfigCommunityTableRowEditorSecret OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret of the SNMP community."
    ::= { mgmtSnmpConfigCommunityTableRowEditor 4 }

mgmtSnmpConfigCommunityTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigCommunityTableRowEditor 100 }

mgmtSnmpConfigUserTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigUserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 users."
    ::= { mgmtSnmpConfig 4 }

mgmtSnmpConfigUserEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigUserEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys are engine ID and user name."
    INDEX       { mgmtSnmpConfigUserEngineId,
                  mgmtSnmpConfigUserUserName }
    ::= { mgmtSnmpConfigUserTable 1 }

MGMTSnmpConfigUserEntry ::= SEQUENCE {
    mgmtSnmpConfigUserEngineId       OCTET STRING,
    mgmtSnmpConfigUserUserName       MGMTDisplayString,
    mgmtSnmpConfigUserSecurityLevel  MGMTSnmpSecurityLevel,
    mgmtSnmpConfigUserAuthProtocol   MGMTSnmpAuthProtocl,
    mgmtSnmpConfigUserAuthPassword   MGMTDisplayString,
    mgmtSnmpConfigUserPrivProtocol   MGMTSnmpPrivProtocl,
    mgmtSnmpConfigUserPrivPassword   MGMTDisplayString,
    mgmtSnmpConfigUserAction         MGMTRowEditorState
}

mgmtSnmpConfigUserEngineId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(5..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "SNMPv3 engine ID. The length is between 5 ~ 32 bytes. But all-zeros and
         all-'F's are not allowed."
    ::= { mgmtSnmpConfigUserEntry 1 }

mgmtSnmpConfigUserUserName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of this entry"
    ::= { mgmtSnmpConfigUserEntry 2 }

mgmtSnmpConfigUserSecurityLevel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security level of this entry. The object is read-only if the entry
         is existent."
    ::= { mgmtSnmpConfigUserEntry 3 }

mgmtSnmpConfigUserAuthProtocol OBJECT-TYPE
    SYNTAX      MGMTSnmpAuthProtocl
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication protocol of this entry. The object is read-only if
         the entry is existent."
    ::= { mgmtSnmpConfigUserEntry 4 }

mgmtSnmpConfigUserAuthPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication password of this entry. Acceptable string length
         range from 8 to 40.The object is read-only if the entry is existent."
    ::= { mgmtSnmpConfigUserEntry 5 }

mgmtSnmpConfigUserPrivProtocol OBJECT-TYPE
    SYNTAX      MGMTSnmpPrivProtocl
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The privacy protocol of this entry. The object is read-only if the
         entry is existent."
    ::= { mgmtSnmpConfigUserEntry 6 }

mgmtSnmpConfigUserPrivPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The privacy password of this entry. Acceptable string length range from
         8 to 32.The object is read-only if the entry is existent."
    ::= { mgmtSnmpConfigUserEntry 7 }

mgmtSnmpConfigUserAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigUserEntry 100 }

mgmtSnmpConfigUserTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 5 }

mgmtSnmpConfigUserTableRowEditorEngineId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(5..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 engine ID. The length is between 5 ~ 32 bytes. But all-zeros and
         all-'F's are not allowed."
    ::= { mgmtSnmpConfigUserTableRowEditor 1 }

mgmtSnmpConfigUserTableRowEditorUserName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of this entry"
    ::= { mgmtSnmpConfigUserTableRowEditor 2 }

mgmtSnmpConfigUserTableRowEditorSecurityLevel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security level of this entry. The object is read-only if the entry
         is existent."
    ::= { mgmtSnmpConfigUserTableRowEditor 3 }

mgmtSnmpConfigUserTableRowEditorAuthProtocol OBJECT-TYPE
    SYNTAX      MGMTSnmpAuthProtocl
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication protocol of this entry. The object is read-only if
         the entry is existent."
    ::= { mgmtSnmpConfigUserTableRowEditor 4 }

mgmtSnmpConfigUserTableRowEditorAuthPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication password of this entry. Acceptable string length
         range from 8 to 40.The object is read-only if the entry is existent."
    ::= { mgmtSnmpConfigUserTableRowEditor 5 }

mgmtSnmpConfigUserTableRowEditorPrivProtocol OBJECT-TYPE
    SYNTAX      MGMTSnmpPrivProtocl
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The privacy protocol of this entry. The object is read-only if the
         entry is existent."
    ::= { mgmtSnmpConfigUserTableRowEditor 6 }

mgmtSnmpConfigUserTableRowEditorPrivPassword OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The privacy password of this entry. Acceptable string length range from
         8 to 32.The object is read-only if the entry is existent."
    ::= { mgmtSnmpConfigUserTableRowEditor 7 }

mgmtSnmpConfigUserTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigUserTableRowEditor 100 }

mgmtSnmpConfigUserToAccessGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigUserToAccessGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 groups."
    ::= { mgmtSnmpConfig 6 }

mgmtSnmpConfigUserToAccessGroupEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigUserToAccessGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys are security model and user/community name."
    INDEX       { mgmtSnmpConfigUserToAccessGroupSecurityModel,
                  mgmtSnmpConfigUserToAccessGroupUserOrCommunity }
    ::= { mgmtSnmpConfigUserToAccessGroupTable 1 }

MGMTSnmpConfigUserToAccessGroupEntry ::= SEQUENCE {
    mgmtSnmpConfigUserToAccessGroupSecurityModel    MGMTSnmpSecurityModel,
    mgmtSnmpConfigUserToAccessGroupUserOrCommunity  MGMTDisplayString,
    mgmtSnmpConfigUserToAccessGroupAccessGroupName  MGMTDisplayString,
    mgmtSnmpConfigUserToAccessGroupAction           MGMTRowEditorState
}

mgmtSnmpConfigUserToAccessGroupSecurityModel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityModel
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The security model of this entry. When v1 or v2c UserOrCommunity maps
         to community, when v3 UserOrCommunity maps to user. The value 'any' is
         not allowed."
    ::= { mgmtSnmpConfigUserToAccessGroupEntry 1 }

mgmtSnmpConfigUserToAccessGroupUserOrCommunity OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The security name (user or community) of this entry"
    ::= { mgmtSnmpConfigUserToAccessGroupEntry 2 }

mgmtSnmpConfigUserToAccessGroupAccessGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The access group name of this entry."
    ::= { mgmtSnmpConfigUserToAccessGroupEntry 3 }

mgmtSnmpConfigUserToAccessGroupAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigUserToAccessGroupEntry 100 }

mgmtSnmpConfigUserToAccessGroupTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 7 }

mgmtSnmpConfigUserToAccessGroupTableRowEditorSecurityModel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityModel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security model of this entry. When v1 or v2c UserOrCommunity maps
         to community, when v3 UserOrCommunity maps to user. The value 'any' is
         not allowed."
    ::= { mgmtSnmpConfigUserToAccessGroupTableRowEditor 1 }

mgmtSnmpConfigUserToAccessGroupTableRowEditorUserOrCommunity OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security name (user or community) of this entry"
    ::= { mgmtSnmpConfigUserToAccessGroupTableRowEditor 2 }

mgmtSnmpConfigUserToAccessGroupTableRowEditorAccessGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The access group name of this entry."
    ::= { mgmtSnmpConfigUserToAccessGroupTableRowEditor 3 }

mgmtSnmpConfigUserToAccessGroupTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigUserToAccessGroupTableRowEditor 100 }

mgmtSnmpConfigAccessGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigAccessGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 accesse groups."
    ::= { mgmtSnmpConfig 8 }

mgmtSnmpConfigAccessGroupEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigAccessGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys are access group name, security model and security
         level."
    INDEX       { mgmtSnmpConfigAccessGroupAccessGroupName,
                  mgmtSnmpConfigAccessGroupSecurityModel,
                  mgmtSnmpConfigAccessGroupSecurityLevel }
    ::= { mgmtSnmpConfigAccessGroupTable 1 }

MGMTSnmpConfigAccessGroupEntry ::= SEQUENCE {
    mgmtSnmpConfigAccessGroupAccessGroupName  MGMTDisplayString,
    mgmtSnmpConfigAccessGroupSecurityModel    MGMTSnmpSecurityModel,
    mgmtSnmpConfigAccessGroupSecurityLevel    MGMTSnmpSecurityLevel,
    mgmtSnmpConfigAccessGroupReadViewName     MGMTDisplayString,
    mgmtSnmpConfigAccessGroupWriteViewName    MGMTDisplayString,
    mgmtSnmpConfigAccessGroupAction           MGMTRowEditorState
}

mgmtSnmpConfigAccessGroupAccessGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The access group name of this entry"
    ::= { mgmtSnmpConfigAccessGroupEntry 1 }

mgmtSnmpConfigAccessGroupSecurityModel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityModel
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The security model of this entry. Can be v1, v2c, usm or any."
    ::= { mgmtSnmpConfigAccessGroupEntry 2 }

mgmtSnmpConfigAccessGroupSecurityLevel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityLevel
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The security level of this entry"
    ::= { mgmtSnmpConfigAccessGroupEntry 3 }

mgmtSnmpConfigAccessGroupReadViewName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the MIB view defining the MIB objects for which this
         request may request the current values."
    ::= { mgmtSnmpConfigAccessGroupEntry 4 }

mgmtSnmpConfigAccessGroupWriteViewName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the MIB view defining the MIB objects for which this
         request may potentially set new values."
    ::= { mgmtSnmpConfigAccessGroupEntry 5 }

mgmtSnmpConfigAccessGroupAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigAccessGroupEntry 100 }

mgmtSnmpConfigAccessGroupTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 9 }

mgmtSnmpConfigAccessGroupTableRowEditorAccessGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The access group name of this entry"
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 1 }

mgmtSnmpConfigAccessGroupTableRowEditorSecurityModel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityModel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security model of this entry. Can be v1, v2c, usm or any."
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 2 }

mgmtSnmpConfigAccessGroupTableRowEditorSecurityLevel OBJECT-TYPE
    SYNTAX      MGMTSnmpSecurityLevel
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The security level of this entry"
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 3 }

mgmtSnmpConfigAccessGroupTableRowEditorReadViewName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the MIB view defining the MIB objects for which this
         request may request the current values."
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 4 }

mgmtSnmpConfigAccessGroupTableRowEditorWriteViewName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the MIB view defining the MIB objects for which this
         request may potentially set new values."
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 5 }

mgmtSnmpConfigAccessGroupTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigAccessGroupTableRowEditor 100 }

mgmtSnmpConfigViewTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigViewEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 views."
    ::= { mgmtSnmpConfig 10 }

mgmtSnmpConfigViewEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigViewEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys are name and subtree."
    INDEX       { mgmtSnmpConfigViewName, mgmtSnmpConfigViewSubtree }
    ::= { mgmtSnmpConfigViewTable 1 }

MGMTSnmpConfigViewEntry ::= SEQUENCE {
    mgmtSnmpConfigViewName      MGMTDisplayString,
    mgmtSnmpConfigViewSubtree   MGMTDisplayString,
    mgmtSnmpConfigViewViewType  MGMTSnmpViewType,
    mgmtSnmpConfigViewAction    MGMTRowEditorState
}

mgmtSnmpConfigViewName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of this entry"
    ::= { mgmtSnmpConfigViewEntry 1 }

mgmtSnmpConfigViewSubtree OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OID defining the root of the subtree to add to the named view."
    ::= { mgmtSnmpConfigViewEntry 2 }

mgmtSnmpConfigViewViewType OBJECT-TYPE
    SYNTAX      MGMTSnmpViewType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The view type of this entry. The value can be set to 'included' or
         'excluded','included' indicates the view subtree is visible, otherwise
         the view subtree is invisible."
    ::= { mgmtSnmpConfigViewEntry 3 }

mgmtSnmpConfigViewAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigViewEntry 100 }

mgmtSnmpConfigViewTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 11 }

mgmtSnmpConfigViewTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of this entry"
    ::= { mgmtSnmpConfigViewTableRowEditor 1 }

mgmtSnmpConfigViewTableRowEditorSubtree OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OID defining the root of the subtree to add to the named view."
    ::= { mgmtSnmpConfigViewTableRowEditor 2 }

mgmtSnmpConfigViewTableRowEditorViewType OBJECT-TYPE
    SYNTAX      MGMTSnmpViewType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The view type of this entry. The value can be set to 'included' or
         'excluded','included' indicates the view subtree is visible, otherwise
         the view subtree is invisible."
    ::= { mgmtSnmpConfigViewTableRowEditor 3 }

mgmtSnmpConfigViewTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigViewTableRowEditor 100 }

mgmtSnmpConfigCommunity6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigCommunity6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMPv3 communities for IPv6."
    ::= { mgmtSnmpConfig 12 }

mgmtSnmpConfigCommunity6Entry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigCommunity6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index key is community."
    INDEX       { mgmtSnmpConfigCommunity6Name,
                  mgmtSnmpConfigCommunity6SourceIPv6,
                  mgmtSnmpConfigCommunity6SourceIPv6PrefixSize }
    ::= { mgmtSnmpConfigCommunity6Table 1 }

MGMTSnmpConfigCommunity6Entry ::= SEQUENCE {
    mgmtSnmpConfigCommunity6Name                  MGMTDisplayString,
    mgmtSnmpConfigCommunity6SourceIPv6            InetAddressIPv6,
    mgmtSnmpConfigCommunity6SourceIPv6PrefixSize  Integer32,
    mgmtSnmpConfigCommunity6Secret                MGMTDisplayString,
    mgmtSnmpConfigCommunity6Action                MGMTRowEditorState
}

mgmtSnmpConfigCommunity6Name OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of SNMP community"
    ::= { mgmtSnmpConfigCommunity6Entry 1 }

mgmtSnmpConfigCommunity6SourceIPv6 OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv6 address"
    ::= { mgmtSnmpConfigCommunity6Entry 2 }

mgmtSnmpConfigCommunity6SourceIPv6PrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv6 prefix size"
    ::= { mgmtSnmpConfigCommunity6Entry 3 }

mgmtSnmpConfigCommunity6Secret OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret of the SNMP community."
    ::= { mgmtSnmpConfigCommunity6Entry 4 }

mgmtSnmpConfigCommunity6Action OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigCommunity6Entry 100 }

mgmtSnmpConfigCommunity6TableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 13 }

mgmtSnmpConfigCommunity6TableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of SNMP community"
    ::= { mgmtSnmpConfigCommunity6TableRowEditor 1 }

mgmtSnmpConfigCommunity6TableRowEditorSourceIPv6 OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv6 address"
    ::= { mgmtSnmpConfigCommunity6TableRowEditor 2 }

mgmtSnmpConfigCommunity6TableRowEditorSourceIPv6PrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The SNMP access source IPv6 prefix size"
    ::= { mgmtSnmpConfigCommunity6TableRowEditor 3 }

mgmtSnmpConfigCommunity6TableRowEditorSecret OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret of the SNMP community."
    ::= { mgmtSnmpConfigCommunity6TableRowEditor 4 }

mgmtSnmpConfigCommunity6TableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigCommunity6TableRowEditor 100 }

mgmtSnmpConfigTrapReceiverTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigTrapReceiverEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMP trap receivers."
    ::= { mgmtSnmpConfig 20 }

mgmtSnmpConfigTrapReceiverEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigTrapReceiverEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys is trap receiver name."
    INDEX       { mgmtSnmpConfigTrapReceiverName }
    ::= { mgmtSnmpConfigTrapReceiverTable 1 }

MGMTSnmpConfigTrapReceiverEntry ::= SEQUENCE {
    mgmtSnmpConfigTrapReceiverName        MGMTDisplayString,
    mgmtSnmpConfigTrapReceiverEnable      TruthValue,
    mgmtSnmpConfigTrapReceiverAddress     MGMTInetAddress,
    mgmtSnmpConfigTrapReceiverPort        MGMTUnsigned16,
    mgmtSnmpConfigTrapReceiverVersion     MGMTSnmpVersion,
    mgmtSnmpConfigTrapReceiverCommunity   MGMTDisplayString,
    mgmtSnmpConfigTrapReceiverNotifyType  MGMTSnmpTrapNotifyType,
    mgmtSnmpConfigTrapReceiverTimeout     Integer32,
    mgmtSnmpConfigTrapReceiverRetries     Integer32,
    mgmtSnmpConfigTrapReceiverEngineId    OCTET STRING,
    mgmtSnmpConfigTrapReceiverUserName    MGMTDisplayString,
    mgmtSnmpConfigTrapReceiverAction      MGMTRowEditorState
}

mgmtSnmpConfigTrapReceiverName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Name of trap receiver"
    ::= { mgmtSnmpConfigTrapReceiverEntry 1 }

mgmtSnmpConfigTrapReceiverEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMP trap receiver enabled."
    ::= { mgmtSnmpConfigTrapReceiverEntry 2 }

mgmtSnmpConfigTrapReceiverAddress OBJECT-TYPE
    SYNTAX      MGMTInetAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Internet address of the SNMP trap receiver."
    ::= { mgmtSnmpConfigTrapReceiverEntry 3 }

mgmtSnmpConfigTrapReceiverPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port number of the SNMP trap receiver."
    ::= { mgmtSnmpConfigTrapReceiverEntry 4 }

mgmtSnmpConfigTrapReceiverVersion OBJECT-TYPE
    SYNTAX      MGMTSnmpVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMP Version. The supported versions are snmpV1, snmpV2c, snmpV3."
    ::= { mgmtSnmpConfigTrapReceiverEntry 5 }

mgmtSnmpConfigTrapReceiverCommunity OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret to use for SNMP traps."
    ::= { mgmtSnmpConfigTrapReceiverEntry 6 }

mgmtSnmpConfigTrapReceiverNotifyType OBJECT-TYPE
    SYNTAX      MGMTSnmpTrapNotifyType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Notification type: trap or inform."
    ::= { mgmtSnmpConfigTrapReceiverEntry 7 }

mgmtSnmpConfigTrapReceiverTimeout OBJECT-TYPE
    SYNTAX      Integer32 (1..2147)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Timeout. Only relevant for informs."
    ::= { mgmtSnmpConfigTrapReceiverEntry 8 }

mgmtSnmpConfigTrapReceiverRetries OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Retries. Only relevant for informs."
    ::= { mgmtSnmpConfigTrapReceiverEntry 9 }

mgmtSnmpConfigTrapReceiverEngineId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(5..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 engine ID. The length is between 5 ~ 32 bytes. But all-zeros and
         all-'F's are not allowed."
    ::= { mgmtSnmpConfigTrapReceiverEntry 10 }

mgmtSnmpConfigTrapReceiverUserName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 user name."
    ::= { mgmtSnmpConfigTrapReceiverEntry 11 }

mgmtSnmpConfigTrapReceiverAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigTrapReceiverEntry 100 }

mgmtSnmpConfigTrapReceiverTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 21 }

mgmtSnmpConfigTrapReceiverTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Name of trap receiver"
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 1 }

mgmtSnmpConfigTrapReceiverTableRowEditorEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMP trap receiver enabled."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 2 }

mgmtSnmpConfigTrapReceiverTableRowEditorAddress OBJECT-TYPE
    SYNTAX      MGMTInetAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Internet address of the SNMP trap receiver."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 3 }

mgmtSnmpConfigTrapReceiverTableRowEditorPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port number of the SNMP trap receiver."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 4 }

mgmtSnmpConfigTrapReceiverTableRowEditorVersion OBJECT-TYPE
    SYNTAX      MGMTSnmpVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMP Version. The supported versions are snmpV1, snmpV2c, snmpV3."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 5 }

mgmtSnmpConfigTrapReceiverTableRowEditorCommunity OBJECT-TYPE
    SYNTAX      MGMTDisplayString
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The community secret to use for SNMP traps."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 6 }

mgmtSnmpConfigTrapReceiverTableRowEditorNotifyType OBJECT-TYPE
    SYNTAX      MGMTSnmpTrapNotifyType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Notification type: trap or inform."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 7 }

mgmtSnmpConfigTrapReceiverTableRowEditorTimeout OBJECT-TYPE
    SYNTAX      Integer32 (1..2147)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Timeout. Only relevant for informs."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 8 }

mgmtSnmpConfigTrapReceiverTableRowEditorRetries OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Retries. Only relevant for informs."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 9 }

mgmtSnmpConfigTrapReceiverTableRowEditorEngineId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(5..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 engine ID. The length is between 5 ~ 32 bytes. But all-zeros and
         all-'F's are not allowed."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 10 }

mgmtSnmpConfigTrapReceiverTableRowEditorUserName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMPv3 user name."
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 11 }

mgmtSnmpConfigTrapReceiverTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigTrapReceiverTableRowEditor 100 }

mgmtSnmpConfigTrapSourceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTSnmpConfigTrapSourceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for configuring SNMP trap sources.
         
         A trap is sent for the given trap source if at least one filter with
         filter_type included matches the filter, and no filters with
         filter_type excluded matches."
    ::= { mgmtSnmpConfig 22 }

mgmtSnmpConfigTrapSourceEntry OBJECT-TYPE
    SYNTAX      MGMTSnmpConfigTrapSourceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The entry index keys is trap source name and a filter ID.
         
         The filter matches an index OID if the index_filter matches the OID of
         the index taking index_mask into account.
         
         Each bit of index_mask corresponds to a sub-identifier of index_filter,
         with the most significant bit of the i-th octet of this octet string
         value (extended if necessary) corresponding to the (8*i - 7)-th
         sub-identifier.
         
         Each bit of index_mask specifies whether or not the corresponding
         sub-identifiers must match when determining if an OID matches; a '1'
         indicates that an exact match must occur; a '0' indicates 'wild card'.
         
         If the value of index_mask is M bits long and there are more than M
         sub-identifiers in index_filter, then the bit mask is extended with 1's
         to be the required length."
    INDEX       { mgmtSnmpConfigTrapSourceName,
                  mgmtSnmpConfigTrapSourceIndexFilterID }
    ::= { mgmtSnmpConfigTrapSourceTable 1 }

MGMTSnmpConfigTrapSourceEntry ::= SEQUENCE {
    mgmtSnmpConfigTrapSourceName           MGMTDisplayString,
    mgmtSnmpConfigTrapSourceIndexFilterID  Integer32,
    mgmtSnmpConfigTrapSourceIndexFilter    OCTET STRING,
    mgmtSnmpConfigTrapSourceIndexMask      OCTET STRING,
    mgmtSnmpConfigTrapSourceFilterType     MGMTSnmpViewType,
    mgmtSnmpConfigTrapSourceAction         MGMTRowEditorState
}

mgmtSnmpConfigTrapSourceName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "SNMP trap event/table name"
    ::= { mgmtSnmpConfigTrapSourceEntry 1 }

mgmtSnmpConfigTrapSourceIndexFilterID OBJECT-TYPE
    SYNTAX      Integer32 (0..127)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "ID of filter for this SNMP trap"
    ::= { mgmtSnmpConfigTrapSourceEntry 2 }

mgmtSnmpConfigTrapSourceIndexFilter OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..508))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "OID subtree to use as index filter. Every OID is hex encoded as 4
         bytes."
    ::= { mgmtSnmpConfigTrapSourceEntry 3 }

mgmtSnmpConfigTrapSourceIndexMask OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mask for the OID subtree to use as index filter."
    ::= { mgmtSnmpConfigTrapSourceEntry 4 }

mgmtSnmpConfigTrapSourceFilterType OBJECT-TYPE
    SYNTAX      MGMTSnmpViewType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The filter type of this entry. The value can be set to 'included' or
         'excluded'."
    ::= { mgmtSnmpConfigTrapSourceEntry 5 }

mgmtSnmpConfigTrapSourceAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigTrapSourceEntry 100 }

mgmtSnmpConfigTrapSourceTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtSnmpConfig 23 }

mgmtSnmpConfigTrapSourceTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "SNMP trap event/table name"
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 1 }

mgmtSnmpConfigTrapSourceTableRowEditorIndexFilterID OBJECT-TYPE
    SYNTAX      Integer32 (0..127)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "ID of filter for this SNMP trap"
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 2 }

mgmtSnmpConfigTrapSourceTableRowEditorIndexFilter OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..508))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "OID subtree to use as index filter. Every OID is hex encoded as 4
         bytes."
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 3 }

mgmtSnmpConfigTrapSourceTableRowEditorIndexMask OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Mask for the OID subtree to use as index filter."
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 4 }

mgmtSnmpConfigTrapSourceTableRowEditorFilterType OBJECT-TYPE
    SYNTAX      MGMTSnmpViewType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The filter type of this entry. The value can be set to 'included' or
         'excluded'."
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 5 }

mgmtSnmpConfigTrapSourceTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtSnmpConfigTrapSourceTableRowEditor 100 }

mgmtSnmpMibConformance OBJECT IDENTIFIER
    ::= { mgmtSnmpMib 2 }

mgmtSnmpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtSnmpMibConformance 1 }

mgmtSnmpMibGroups OBJECT IDENTIFIER
    ::= { mgmtSnmpMibConformance 2 }

mgmtSnmpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigGlobalsMode,
                  mgmtSnmpConfigGlobalsEngineId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 1 }

mgmtSnmpConfigCommunityTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigCommunityName,
                  mgmtSnmpConfigCommunitySourceIP,
                  mgmtSnmpConfigCommunitySourceIPPrefixSize,
                  mgmtSnmpConfigCommunitySecret,
                  mgmtSnmpConfigCommunityAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 2 }

mgmtSnmpConfigCommunityTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigCommunityTableRowEditorName,
                  mgmtSnmpConfigCommunityTableRowEditorSourceIP,
                  mgmtSnmpConfigCommunityTableRowEditorSourceIPPrefixSize,
                  mgmtSnmpConfigCommunityTableRowEditorSecret,
                  mgmtSnmpConfigCommunityTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 3 }

mgmtSnmpConfigUserTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigUserEngineId,
                  mgmtSnmpConfigUserUserName,
                  mgmtSnmpConfigUserSecurityLevel,
                  mgmtSnmpConfigUserAuthProtocol,
                  mgmtSnmpConfigUserAuthPassword,
                  mgmtSnmpConfigUserPrivProtocol,
                  mgmtSnmpConfigUserPrivPassword,
                  mgmtSnmpConfigUserAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 4 }

mgmtSnmpConfigUserTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigUserTableRowEditorEngineId,
                  mgmtSnmpConfigUserTableRowEditorUserName,
                  mgmtSnmpConfigUserTableRowEditorSecurityLevel,
                  mgmtSnmpConfigUserTableRowEditorAuthProtocol,
                  mgmtSnmpConfigUserTableRowEditorAuthPassword,
                  mgmtSnmpConfigUserTableRowEditorPrivProtocol,
                  mgmtSnmpConfigUserTableRowEditorPrivPassword,
                  mgmtSnmpConfigUserTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 5 }

mgmtSnmpConfigUserToAccessGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigUserToAccessGroupSecurityModel,
                  mgmtSnmpConfigUserToAccessGroupUserOrCommunity,
                  mgmtSnmpConfigUserToAccessGroupAccessGroupName,
                  mgmtSnmpConfigUserToAccessGroupAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 6 }

mgmtSnmpConfigUserToAccessGroupTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtSnmpConfigUserToAccessGroupTableRowEditorSecurityModel,
                  mgmtSnmpConfigUserToAccessGroupTableRowEditorUserOrCommunity,
                  mgmtSnmpConfigUserToAccessGroupTableRowEditorAccessGroupName,
                  mgmtSnmpConfigUserToAccessGroupTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 7 }

mgmtSnmpConfigAccessGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigAccessGroupAccessGroupName,
                  mgmtSnmpConfigAccessGroupSecurityModel,
                  mgmtSnmpConfigAccessGroupSecurityLevel,
                  mgmtSnmpConfigAccessGroupReadViewName,
                  mgmtSnmpConfigAccessGroupWriteViewName,
                  mgmtSnmpConfigAccessGroupAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 8 }

mgmtSnmpConfigAccessGroupTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtSnmpConfigAccessGroupTableRowEditorAccessGroupName,
                  mgmtSnmpConfigAccessGroupTableRowEditorSecurityModel,
                  mgmtSnmpConfigAccessGroupTableRowEditorSecurityLevel,
                  mgmtSnmpConfigAccessGroupTableRowEditorReadViewName,
                  mgmtSnmpConfigAccessGroupTableRowEditorWriteViewName,
                  mgmtSnmpConfigAccessGroupTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 9 }

mgmtSnmpConfigViewTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigViewName, mgmtSnmpConfigViewSubtree,
                  mgmtSnmpConfigViewViewType,
                  mgmtSnmpConfigViewAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 10 }

mgmtSnmpConfigViewTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigViewTableRowEditorName,
                  mgmtSnmpConfigViewTableRowEditorSubtree,
                  mgmtSnmpConfigViewTableRowEditorViewType,
                  mgmtSnmpConfigViewTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 11 }

mgmtSnmpConfigCommunity6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigCommunity6Name,
                  mgmtSnmpConfigCommunity6SourceIPv6,
                  mgmtSnmpConfigCommunity6SourceIPv6PrefixSize,
                  mgmtSnmpConfigCommunity6Secret,
                  mgmtSnmpConfigCommunity6Action }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 12 }

mgmtSnmpConfigCommunity6TableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigCommunity6TableRowEditorName,
                  mgmtSnmpConfigCommunity6TableRowEditorSourceIPv6,
                  mgmtSnmpConfigCommunity6TableRowEditorSourceIPv6PrefixSize,
                  mgmtSnmpConfigCommunity6TableRowEditorSecret,
                  mgmtSnmpConfigCommunity6TableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 13 }

mgmtSnmpConfigTrapReceiverTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigTrapReceiverName,
                  mgmtSnmpConfigTrapReceiverEnable,
                  mgmtSnmpConfigTrapReceiverAddress,
                  mgmtSnmpConfigTrapReceiverPort,
                  mgmtSnmpConfigTrapReceiverVersion,
                  mgmtSnmpConfigTrapReceiverCommunity,
                  mgmtSnmpConfigTrapReceiverNotifyType,
                  mgmtSnmpConfigTrapReceiverTimeout,
                  mgmtSnmpConfigTrapReceiverRetries,
                  mgmtSnmpConfigTrapReceiverEngineId,
                  mgmtSnmpConfigTrapReceiverUserName,
                  mgmtSnmpConfigTrapReceiverAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 14 }

mgmtSnmpConfigTrapReceiverTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigTrapReceiverTableRowEditorName,
                  mgmtSnmpConfigTrapReceiverTableRowEditorEnable,
                  mgmtSnmpConfigTrapReceiverTableRowEditorAddress,
                  mgmtSnmpConfigTrapReceiverTableRowEditorPort,
                  mgmtSnmpConfigTrapReceiverTableRowEditorVersion,
                  mgmtSnmpConfigTrapReceiverTableRowEditorCommunity,
                  mgmtSnmpConfigTrapReceiverTableRowEditorNotifyType,
                  mgmtSnmpConfigTrapReceiverTableRowEditorTimeout,
                  mgmtSnmpConfigTrapReceiverTableRowEditorRetries,
                  mgmtSnmpConfigTrapReceiverTableRowEditorEngineId,
                  mgmtSnmpConfigTrapReceiverTableRowEditorUserName,
                  mgmtSnmpConfigTrapReceiverTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 15 }

mgmtSnmpConfigTrapSourceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigTrapSourceName,
                  mgmtSnmpConfigTrapSourceIndexFilterID,
                  mgmtSnmpConfigTrapSourceIndexFilter,
                  mgmtSnmpConfigTrapSourceIndexMask,
                  mgmtSnmpConfigTrapSourceFilterType,
                  mgmtSnmpConfigTrapSourceAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 16 }

mgmtSnmpConfigTrapSourceTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtSnmpConfigTrapSourceTableRowEditorName,
                  mgmtSnmpConfigTrapSourceTableRowEditorIndexFilterID,
                  mgmtSnmpConfigTrapSourceTableRowEditorIndexFilter,
                  mgmtSnmpConfigTrapSourceTableRowEditorIndexMask,
                  mgmtSnmpConfigTrapSourceTableRowEditorFilterType,
                  mgmtSnmpConfigTrapSourceTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtSnmpMibGroups 17 }

mgmtSnmpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtSnmpConfigGlobalsInfoGroup,
                       mgmtSnmpConfigCommunityTableInfoGroup,
                       mgmtSnmpConfigCommunityTableRowEditorInfoGroup,
                       mgmtSnmpConfigUserTableInfoGroup,
                       mgmtSnmpConfigUserTableRowEditorInfoGroup,
                       mgmtSnmpConfigUserToAccessGroupTableInfoGroup,
                       mgmtSnmpConfigUserToAccessGroupTableRowEditorInfoGroup,
                       mgmtSnmpConfigAccessGroupTableInfoGroup,
                       mgmtSnmpConfigAccessGroupTableRowEditorInfoGroup,
                       mgmtSnmpConfigViewTableInfoGroup,
                       mgmtSnmpConfigViewTableRowEditorInfoGroup,
                       mgmtSnmpConfigCommunity6TableInfoGroup,
                       mgmtSnmpConfigCommunity6TableRowEditorInfoGroup,
                       mgmtSnmpConfigTrapReceiverTableInfoGroup,
                       mgmtSnmpConfigTrapReceiverTableRowEditorInfoGroup,
                       mgmtSnmpConfigTrapSourceTableInfoGroup,
                       mgmtSnmpConfigTrapSourceTableRowEditorInfoGroup }

    ::= { mgmtSnmpMibCompliances 1 }

END
