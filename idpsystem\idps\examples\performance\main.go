package main

import (
	"flag"
	"fmt"
	"log"

	"mnms/idpsystem/idps"
	"os"
	"os/signal"
	"time"

	"math/rand"

	"github.com/google/gonids"
)

type mode int

const (
	payload mode = iota + 1
	ipv4
	tcp
	udp
)

var out = false

func main() {
	count := 0
	m := 0
	flag.IntVar(&m, "m", 1, "mode,use rule:\n1:payload\n2:ipv4\n3:tcp\n4:udp\n")
	show := flag.Bool("show", false, "show info detected")
	flag.IntVar(&count, "count", 100, "rules count")
	flag.BoolVar(&out, "o", false, "out file")

	flag.Parse()
	ipsys, err := idps.NewIdps(false, false)
	if err != nil {
		panic(err)
	}
	if *show {
		ipsys.RegisterEvent(event())
	}

	err = ipsys.Start()
	if err != nil {
		panic(err)
	}
	log.Print("add rule")
	err = addRule(ipsys, count, mode(m))
	if err != nil {
		panic(err)
	}

	log.Print("apply rule")
	err = ipsys.ApplyRules()
	if err != nil {
		panic(err)
	}

	log.Print("start detect")
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	<-c
}

func event() func(idps.EventMessage) {
	v := func(message idps.EventMessage) {
		fmt.Printf("%v\n", message)
	}
	return v
}

func addRule(f *idps.Idps, count int, m mode) error {
	s := []string{}
	for i := 1; i <= count; i++ {
		time.Sleep(time.Nanosecond)
		_, c, err := roundByte()
		if err != nil {
			return err
		}
		off := rand.Intn(12)

		v := roundHdr(m, string(c), off, uint32(i+1000)) // fmt.Sprintf(`pass ip any any -> any any (msg:"test";content:"%v";offset:%v;sid:%v;)`, string(c), off, uint32(i+1000))
		r, err := gonids.ParseRule(v)
		if err != nil {
			return err
		}
		err = f.AddRule(r)
		if err != nil {
			return err
		}
		s = append(s, v)
	}
	if out {
		f, err := os.OpenFile("test_rule.rules", os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return err
		}
		defer f.Close()
		for _, v := range s {
			_, err = f.WriteString(v + "\n")
			if err != nil {
				return err
			}
		}

	}

	return nil
}

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")

func roundHdr(m mode, v string, off int, id uint32) string {
	switch m {
	case payload:
		return fmt.Sprintf(`pass ip any any -> any any (msg:"test content";content:"%v";offset:%v;sid:%v;)`, v, off, id+1000)
	case ipv4:
		return fmt.Sprintf(`pass ip any any -> any any (msg:"test ipv4.hdr";ipv4.hdr; content:"|06|"; offset:9; depth:1;sid:%v;)`, id+1000)
	case tcp:
		return fmt.Sprintf(`pass ip any any -> any any (msg:"test tcp.hdr"; tcp.hdr; content:"|02 04|"; offset:20;sid:%v;)`, id+1000)
	case udp:
		return fmt.Sprintf(`pass ip any any -> any any (msg:"test udp.hdr"; udp.hdr; content:"|00 08|"; offset:4; depth:2;;sid:%v;)`, id+1000)
	default:
		return fmt.Sprintf(`pass ip any any -> any any (msg:"test content";content:"%v";offset:%v;sid:%v;)`, v, off, id+1000)
	}
}

func roundByte() (uint, []byte, error) {
	rand.NewSource(time.Now().UnixNano())
	length := 10
	bufl := rand.Intn(length + 1)
	if bufl == 0 {
		bufl++
	}

	buf := make([]byte, bufl)
	l := uint(0)
	for i := 0; i < len(buf); i++ {
		index := rand.Intn(len(letterRunes))
		buf[i] = byte(letterRunes[index])
	}

	l = uint(rand.Intn(len(buf) + 1))
	return l, buf, nil
}
