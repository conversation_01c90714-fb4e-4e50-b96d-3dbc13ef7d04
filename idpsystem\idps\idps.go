package idps

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"mnms/idpsystem/idps/ids"
	"mnms/idpsystem/idps/ips"

	"github.com/google/gonids"
)

type Idps struct {
	ips   ips.Ipser //ips
	ids   *ids.Ids
	ch    chan EventMessage
	event ReceiveEvent
	once  sync.Once
}

const chsize = 256

// NewIdps
//
// pcap enable/disbale out file.pcap
//
// json enable/disbale out file.json
func NewIdps(pcap bool, json bool) (*Idps, error) {
	ips, err := ips.NewIps()
	if err != nil {
		return nil, err
	}
	ids, err := ids.NewIds(pcap, json)
	if err != nil {
		return nil, err
	}

	return &Idps{ips: ips, ids: ids, ch: make(chan EventMessage, chsize)}, nil
}

func (i *Idps) EnableLo(b bool) {
	i.ids.Enablelo(b)
	i.ips.Enablelo(b)
}

// Start idps start
func (i *Idps) Start() error {
	err := i.ids.Run()
	if err != nil {
		return err
	}
	err = i.ips.Start()
	if err != nil {
		return err
	}
	i.once.Do(func() {
		go i.runEvent()
	})
	return nil
}

// AddRule  add rule into idps
func (i *Idps) AddRule(r *gonids.Rule) error {
	err := i.selectAndAddRule(r)
	if err != nil {
		return err
	}
	return nil

}

// ApplyRules apply all rules of idps
func (i *Idps) ApplyRules() (err error) {
	err = i.ips.Build()
	if err != nil {
		return err
	}
	err = i.ids.Build()
	if err != nil {
		return err
	}
	err = i.ids.ApplyRules()
	if err != nil {
		return err
	}
	err = i.ips.ApplyRules()
	if err != nil {
		return err
	}
	return nil
}

// Close idps
func (i *Idps) Close() error {
	err := i.ips.Close()
	if err != nil {
		return err
	}
	i.ids.Close()
	return nil
}

// RegisterEvent when rule match and it will run this event
func (i *Idps) RegisterEvent(e ReceiveEvent) error {
	if ipsys, ok := i.ips.(ips.MatchEventer); ok {
		ipsys.RegisterMatchEvent(i.createIpsEvent())
	}
	i.ids.RegisterMatchEvent(i.createIdsEvent())
	i.event = e
	return nil

}

func (i *Idps) createIpsEvent() ips.Eventfunc {
	f := func(event ips.Event) {
		msg := EventMessage{}
		msg.Id = event.Id
		tm := time.UnixMicro(event.Timestamp)
		msg.Timestamp = tm.Format(TimeFormat)
		msg.Type = event.Action
		msg.InInterface = event.EthName
		msg.Srcip = event.Srcip
		msg.Destip = event.Destip
		msg.SrcPort = event.SrcPort
		msg.DestPort = event.DestPort
		msg.Protocol = event.Protocol
		msg.Description = event.Message
		i.ch <- msg
	}
	return f
}
func (i *Idps) createIdsEvent() ids.Eventfunc {
	f := func(event ids.Event) {
		msg := EventMessage{}
		msg.Id = event.Id
		tm := time.UnixMicro(event.Timestamp)
		msg.Timestamp = tm.Format(TimeFormat)
		msg.Type = event.Action
		msg.InInterface = event.EthName
		msg.Srcip = event.Srcip
		msg.Destip = event.Destip
		msg.SrcPort = event.SrcPort
		msg.DestPort = event.DestPort
		msg.Protocol = event.Protocol
		msg.Description = event.Message
		i.ch <- msg
	}
	return f
}

func (i *Idps) runEvent() {
	for info := range i.ch {
		if i.event != nil {
			i.event(info)
		}
	}
}

func (i *Idps) selectAndAddRule(r *gonids.Rule) error {
	switch strings.ToLower(r.Action) {
	case "alert":
		return i.ids.AddGonidsRule(r)
	case "pass", "drop":
		return i.ips.AddGonidsRule(r)
	default:
		return fmt.Errorf("not support action:%v", r.Action)
	}
}
