// devicequery_multillm/llm/openai.go
package llm

import (
	"context"
	"crypto/tls"
	"net/http"

	// "encoding/json" // No longer needed here
	"fmt"
	"log"
	"os"
	"strings"

	openai "github.com/sa<PERSON><PERSON>nov/go-openai"
)

// OpenAIClient implements the LLMClient interface for OpenAI and compatible APIs.
type OpenAIClient struct {
	client       *openai.Client
	model        string
	tableSchema  string
	provider     string
	systemPrompt string
}

// SetSystemPrompt sets the system prompt for the OpenAI client.
func (o *OpenAIClient) SetSystemPrompt(systemPrompt string) {
	o.systemPrompt = systemPrompt
}

// GetSystemPrompt gets the system prompt for the OpenAI client.
func (o *OpenAIClient) GetSystemPrompt() string {
	return o.systemPrompt
}

const openAISQLToolName = "sql_query_devices" // Consistent with other clients

// JSONSchemaProps defines properties for a JSON schema.
type JSONSchemaProps map[string]interface{}

// JSONSchema represents a JSON schema definition for tool parameters.
// This structure is designed to be marshaled into the format OpenAI expects.
type JSONSchema struct {
	Type        string          `json:"type"`
	Properties  JSONSchemaProps `json:"properties,omitempty"`
	Required    []string        `json:"required,omitempty"`
	Description string          `json:"description,omitempty"` // Top-level description for the parameter object
}

// customRoundTripper adds custom headers to each request.
type customRoundTripper struct {
	originalTransport http.RoundTripper
	customHeaders     map[string]string
}

func (crt *customRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	transport := crt.originalTransport
	if transport == nil {
		transport = http.DefaultTransport // Use default if original is nil
	}
	// Add custom headers
	for key, value := range crt.customHeaders {
		req.Header.Set(key, value)
	}
	return transport.RoundTrip(req)
}

// Init initializes the OpenAI client.
func (o *OpenAIClient) Init(config LLMConfig) error {
	o.model = config.Model
	o.tableSchema = config.DevTableSchema
	o.provider = config.Provider

	if o.model == "" {
		return fmt.Errorf("model name is required for OpenAI/Ollama/OpenRouter client")
	}

	clientConfig := openai.DefaultConfig(config.APIKey)
	openRouterCustomHeaders := make(map[string]string)
	ollamaHeaders := make(map[string]string)

	switch config.Provider {
	case ProviderOpenRouter:
		clientConfig.BaseURL = "https://openrouter.ai/api/v1"
		if config.BaseURL != "" { // Allow overriding OpenRouter base URL if specified
			clientConfig.BaseURL = config.BaseURL
			log.Printf("Using custom BaseURL for OpenRouter: %s", config.BaseURL)
		}
		// Prepare OpenRouter specific headers
		httpReferer := os.Getenv("OPENROUTER_HTTP_REFERER")
		appName := os.Getenv("OPENROUTER_APP_NAME") // For X-Title header

		if httpReferer != "" {
			openRouterCustomHeaders["HTTP-Referer"] = httpReferer
		}
		if appName != "" {
			openRouterCustomHeaders["X-Title"] = appName
		}
	case ProviderOllama:
		clientConfig.BaseURL = "http://localhost:11434/v1" // Default for Ollama
		if config.BaseURL != "" {                          // Allow overriding Ollama base URL if specified
			clientConfig.BaseURL = config.BaseURL
		}
		// if you want to talk to a proxied Ollama that requires a Bearer token:
		ollamaHeaders = make(map[string]string)
		if config.APIKey != "" {
			ollamaHeaders["Authorization"] = "Bearer " + config.APIKey
		}
	case ProviderOpenAI:
		// Use default OpenAI base URL, do not override
		// fallthrough
	default: // Default to OpenAI behavior
		if config.BaseURL != "" {
			clientConfig.BaseURL = config.BaseURL
		}
		// If config.BaseURL is empty, openai.DefaultConfig already set the standard OpenAI URL.
	}

	// Configure HTTPClient Transport
	// Start with a base transport (http.DefaultTransport)
	var currentTransport http.RoundTripper
	// It's crucial to clone http.DefaultTransport if we intend to modify it (e.g., for TLSClientConfig),
	// to avoid altering the global default.
	if dt, ok := http.DefaultTransport.(*http.Transport); ok {
		currentTransport = dt.Clone()
	} else {
		// Fallback if http.DefaultTransport is not *http.Transport (should not happen in standard Go)
		currentTransport = &http.Transport{}
		log.Println("Warning: http.DefaultTransport was not of type *http.Transport, created a new one.")
	}

	// Apply InsecureSkipVerify if needed (for Ollama/localhost HTTPS)
	// This modification should be on our cloned transport.
	if (config.Provider == ProviderOllama || (config.BaseURL != "" && strings.Contains(config.BaseURL, "localhost"))) &&
		strings.HasPrefix(clientConfig.BaseURL, "https://") {
		log.Printf("Warning: Using Ollama/localhost with HTTPS endpoint (%s). Configuring HTTP client to skip TLS verification. This is intended for development/trusted environments only.", clientConfig.BaseURL)
		if transportWithTLS, ok := currentTransport.(*http.Transport); ok {
			transportWithTLS.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
		} else {
			// This case implies currentTransport was not an *http.Transport to begin with, which is unusual.
			// For safety, create a new transport with InsecureSkipVerify.
			log.Printf("Warning: Could not apply InsecureSkipVerify to current transport type %T. Creating new transport.", currentTransport)
			currentTransport = &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}
		}
	}

	// Apply OpenRouter custom headers by wrapping the currentTransport
	if config.Provider == ProviderOpenRouter && len(openRouterCustomHeaders) > 0 {
		clientConfig.HTTPClient = &http.Client{
			Transport: &customRoundTripper{
				originalTransport: currentTransport,
				customHeaders:     openRouterCustomHeaders,
			},
		}
	} else {
		clientConfig.HTTPClient = &http.Client{
			Transport: currentTransport,
		}
	}

	// if we have Ollama‐specific headers, layer them on top
	if config.Provider == ProviderOllama && len(ollamaHeaders) > 0 {
		clientConfig.HTTPClient = &http.Client{
			Transport: &customRoundTripper{
				originalTransport: currentTransport,
				customHeaders:     ollamaHeaders,
			},
		}
	}

	o.client = openai.NewClientWithConfig(clientConfig)
	log.Printf("%s LLM client initialized for model: %s, BaseURL: %s", strings.Title(string(config.Provider)), config.Model, clientConfig.BaseURL)
	return nil
}

func (o *OpenAIClient) getSystemPrompt() string {
	return fmt.Sprintf(`You are an expert assistant for querying device information from a SQLite database using the '%s' tool.
You have access to a 'devices' table. Your primary function is to translate user's natural language questions about devices into valid SQL SELECT queries.

Database Schema for 'devices' table:
%s

Key considerations for constructing SQL queries:
1.  **Case Sensitivity**: Column names in SQL queries MUST EXACTLY MATCH the case shown in the schema.
2.  **String Literals**: Enclose string values in SQL WHERE clauses with DOUBLE QUOTES.
3.  **Boolean Fields**: (e.g., 'IsOnline') are stored as INTEGER (1 for true, 0 for false). Query as 'IsOnline = 1'.
4.  **Timestamp Field**: 'Timestamp' is Unix timestamp as STRING. Query using SQLite datetime() function: datetime(TimestampUnixStr) > datetime('YYYY-MM-DDTHH:MM:SSZ'))"
5.  **JSON Fields**: 'Capabilities', 'DeviceErrors' are JSON strings. Use 'LIKE' or json_extract().
6.  **Tool Usage**: ONLY use the '%s' tool for SELECT statements.
If a query returns an error or no results, inform the user clearly.
When responding to a tool call, ensure your response is directly related to the output of the tool.
`, openAISQLToolName, o.tableSchema, openAISQLToolName)
}

// GenerateResponse sends messages to OpenAI and gets a response.
func (o *OpenAIClient) GenerateResponse(ctx context.Context, messages []Message, tools []ToolDefine) (*LLMResponse, error) {
	if o.client == nil {
		return nil, fmt.Errorf("OpenAI client is not initialized")
	}

	openAIMessages := make([]openai.ChatCompletionMessage, 0, len(messages)+1)

	systemPrompt := o.GetSystemPrompt() + o.getSystemPrompt()
	if systemPrompt != "" {
		openAIMessages = append(openAIMessages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleSystem,
			Content: systemPrompt,
		})
	}

	for _, msg := range messages {
		apiMsg := openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
		if len(msg.ToolCalls) > 0 {
			apiMsg.ToolCalls = make([]openai.ToolCall, len(msg.ToolCalls))
			for i, tc := range msg.ToolCalls {
				apiMsg.ToolCalls[i] = openai.ToolCall{
					ID:   tc.ID,
					Type: openai.ToolType(tc.Type), // Should be "function"
					Function: openai.FunctionCall{
						Name:      tc.Function.Name,
						Arguments: tc.Function.Arguments,
					},
				}
			}
		}
		if msg.Role == openai.ChatMessageRoleTool {
			apiMsg.ToolCallID = msg.ToolCallID
			apiMsg.Name = msg.Name
		}
		openAIMessages = append(openAIMessages, apiMsg)
	}

	// Convert ToolDefine to openai.Tool
	openaiTools := make([]openai.Tool, len(tools))
	for i, tool := range tools {
		openaiTools[i] = openai.Tool{
			Type: openai.ToolTypeFunction,
			Function: &openai.FunctionDefinition{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters:  tool.Args,
			},
		}
	}

	req := openai.ChatCompletionRequest{
		Model:    o.model,
		Messages: openAIMessages,
		Tools:    openaiTools,
		// ToolChoice: "auto", // Let model decide or specific "none" or {"type": "function", "function": {"name": "my_function"}}
	}
	if o.provider == ProviderOllama {
		// For some Ollama models/setups, explicitly setting tool choice might be necessary
		// if 'auto' doesn't work as expected or if you always want the tool to be considered.
		// However, this can prevent general conversation if the model *must* pick a tool.
		// Defaulting to "auto" is generally preferred.
		// req.ToolChoice = "auto" // This is the default for OpenAI, ensure it works for Ollama
		// req.ToolChoice = openai.ToolChoice{Type: openai.ToolTypeFunction, Function: openai.ToolChoiceFunction{Name: openAISQLToolName}} // Force tool
	}

	resp, err := o.client.CreateChatCompletion(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("OpenAI CreateChatCompletion failed: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("OpenAI returned no choices")
	}

	choice := resp.Choices[0]
	llmResp := &LLMResponse{
		Message: Message{
			Role:    choice.Message.Role,
			Content: choice.Message.Content,
		},
		Usage: map[string]interface{}{
			"prompt_tokens":     resp.Usage.PromptTokens,
			"completion_tokens": resp.Usage.CompletionTokens,
			"total_tokens":      resp.Usage.TotalTokens,
		},
		StopReason: string(choice.FinishReason),
	}

	if len(choice.Message.ToolCalls) > 0 {
		llmResp.Message.ToolCalls = make([]ToolCall, len(choice.Message.ToolCalls))
		for i, tc := range choice.Message.ToolCalls {
			llmResp.Message.ToolCalls[i] = ToolCall{
				ID:   tc.ID,
				Type: string(tc.Type),
				Function: FunctionCall{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			}
		}
	}
	return llmResp, nil
}

// Close is a no-op for this client.
func (o *OpenAIClient) Close() error {
	return nil
}
