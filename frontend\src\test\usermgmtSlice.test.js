import { it, describe, vi, expect } from "vitest";

import usermgmtSlice, {
  CreateNew<PERSON>ser,
  GetAllUsers,
  setEditUserData,
  usermgmtSelector,
} from "../features/usermgmt/usermgmtSlice";
import protectedApis from "../utils/apis/protectedApis";
import { store } from "../app/store";
import { loginUser } from "../features/auth/userAuthSlice";

describe("usermgmtSlice", () => {
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("should fetch all users successfully", async () => {
    const mockApiResponse = [
      {
        name: "admin",
        email: "",
        role: "admin",
        password: "#####",
        enable2FA: false,
        secret: "",
      },
      {
        name: "ishwar",
        email: "<EMAIL>",
        role: "user",
        password: "#####",
        enable2FA: false,
        secret: "",
      },
    ];
    vi.spyOn(protectedApis, "get").mockResolvedValue({
      data: mockApiResponse,
      status: 200,
    });

    await store.dispatch(GetAllUsers());
    const state = store.getState().usermgmt;
    expect(state.usersData).toEqual(mockApiResponse);
  });

  it("should handle failure while fetching all users", async () => {
    const mockErrorResponse = { message: "Error fetching users" };
    vi.spyOn(protectedApis, "get").mockRejectedValue({
      response: {
        data: mockErrorResponse,
        status: 500,
      },
    });

    await store.dispatch(GetAllUsers());
    const state = store.getState().usermgmt;
    expect(state.usersData).toEqual([]);
  });

  it("should set edit user data correctly", () => {
    const editUserData = {
      name: "ishwar",
      email: "<EMAIL>",
      role: "user",
      password: "Test@123",
    };
    store.dispatch(setEditUserData(editUserData));
    const state = store.getState().usermgmt;
    expect(state.editUserData).toEqual(editUserData);
  });

  it("should select usersData and editUserData correctly", () => {
    const usersData = [
      {
        name: "admin",
        email: "",
        role: "admin",
        password: "#####",
        enable2FA: false,
        secret: "",
      },
      {
        name: "ishwar",
        email: "<EMAIL>",
        role: "user",
        password: "#####",
        enable2FA: false,
        secret: "",
      },
    ];
    const editUserData = {
      name: "ishwar",
      email: "<EMAIL>",
      role: "user",
      password: "Test@123",
    };
    const state = {
      usermgmt: {
        usersData,
        editUserData,
      },
    };
    const selectedData = usermgmtSelector(state);
    expect(selectedData.usersData).toEqual(usersData);
    expect(selectedData.editUserData).toEqual(editUserData);
  });
});

  it("should handle setEditUserData", () => {
    const initialState = {
      usersData: [],
      editUserData: {},
    };
    const editUserData = {
      name: "ishwar",
      email: "<EMAIL>",
      role: "user",
      password: "Test@123",
    };
    const action = setEditUserData(editUserData);
    const newState = usermgmtSlice.reducer(initialState, action);

    expect(newState.editUserData).toEqual(editUserData);
  });

  it("Should handle GetAllUsers correctly", async () => {
    await store.dispatch(GetAllUsers());
    const updatedState = store.getState().usermgmt;
    expect(updatedState.usersData).toEqual([]);
  });
  it("Should handle CreateNewUser correctly", async () => {
    let userData = {
      name: "ishwar",
      email: "<EMAIL>",
      role: "user",
      password: "Test@123",
    };
    await store.dispatch(CreateNewUser({ userData }));
    const updatedState = store.getState().usermgmt;
    expect(updatedState.usersData).toEqual([]);
  });
