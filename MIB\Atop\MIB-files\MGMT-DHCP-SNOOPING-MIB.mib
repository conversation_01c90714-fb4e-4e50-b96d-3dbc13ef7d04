-- *****************************************************************
-- DHCP-SNOOPING-MIB:  
-- ****************************************************************

MGMT-DHCP-SNOOPING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SM<PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtDhcpSnoopingMib MODULE-IDENTITY
    LAST-UPDATED "201410100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the DHCP Snooping MIB"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 56 }


mgmtDhcpSnoopingMibObjects OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMib 1 }

mgmtDhcpSnoopingConfig OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibObjects 2 }

mgmtDhcpSnoopingConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingConfig 1 }

mgmtDhcpSnoopingConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP snooping mode operation. Possible modes are - true:
         Enable DHCP snooping mode operation. When DHCP snooping mode operation
         is enabled, the DHCP request messages will be forwarded to trusted
         ports and only allow reply packets from trusted ports. false: Disable
         DHCP snooping mode operation."
    ::= { mgmtDhcpSnoopingConfigGlobals 1 }

mgmtDhcpSnoopingConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpSnoopingConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of DHCP Snooping port configuration parameters"
    ::= { mgmtDhcpSnoopingConfig 2 }

mgmtDhcpSnoopingConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpSnoopingConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtDhcpSnoopingConfigInterfaceIfIndex }
    ::= { mgmtDhcpSnoopingConfigInterfaceTable 1 }

MGMTDhcpSnoopingConfigInterfaceEntry ::= SEQUENCE {
    mgmtDhcpSnoopingConfigInterfaceIfIndex    MGMTInterfaceIndex,
    mgmtDhcpSnoopingConfigInterfaceTrustMode  TruthValue
}

mgmtDhcpSnoopingConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcpSnoopingConfigInterfaceEntry 1 }

mgmtDhcpSnoopingConfigInterfaceTrustMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the DHCP snooping port mode. Possible port modes are - true:
         Configures the port as trusted source of the DHCP messages. false:
         Configures the port as untrusted source of the DHCP messages."
    ::= { mgmtDhcpSnoopingConfigInterfaceEntry 2 }

mgmtDhcpSnoopingStatus OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibObjects 3 }

mgmtDhcpSnoopingStatusAssignedIpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpSnoopingStatusAssignedIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of assigned IP information in DHCP Snooping "
    ::= { mgmtDhcpSnoopingStatus 1 }

mgmtDhcpSnoopingStatusAssignedIpEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpSnoopingStatusAssignedIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcpSnoopingStatusAssignedIpMacAddress,
                  mgmtDhcpSnoopingStatusAssignedIpVlanId }
    ::= { mgmtDhcpSnoopingStatusAssignedIpTable 1 }

MGMTDhcpSnoopingStatusAssignedIpEntry ::= SEQUENCE {
    mgmtDhcpSnoopingStatusAssignedIpMacAddress    MacAddress,
    mgmtDhcpSnoopingStatusAssignedIpVlanId        Integer32,
    mgmtDhcpSnoopingStatusAssignedIpIfIndex       MGMTInterfaceIndex,
    mgmtDhcpSnoopingStatusAssignedIpIpAddress     IpAddress,
    mgmtDhcpSnoopingStatusAssignedIpNetmask       IpAddress,
    mgmtDhcpSnoopingStatusAssignedIpDhcpServerIp  IpAddress
}

mgmtDhcpSnoopingStatusAssignedIpMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "MAC address."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 1 }

mgmtDhcpSnoopingStatusAssignedIpVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN id of the VLAN."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 2 }

mgmtDhcpSnoopingStatusAssignedIpIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port of the DHCP client."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 3 }

mgmtDhcpSnoopingStatusAssignedIpIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address assigned to DHCP client by DHCP server."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 4 }

mgmtDhcpSnoopingStatusAssignedIpNetmask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Netmask assigned to DHCP client by DHCP server."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 5 }

mgmtDhcpSnoopingStatusAssignedIpDhcpServerIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address of the DHCP server that assigns the IP address and netmask."
    ::= { mgmtDhcpSnoopingStatusAssignedIpEntry 6 }

mgmtDhcpSnoopingControl OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibObjects 4 }

mgmtDhcpSnoopingControlInterfaceClearStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpSnoopingControlInterfaceClearStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear port statistics in DHCP Snooping"
    ::= { mgmtDhcpSnoopingControl 1 }

mgmtDhcpSnoopingControlInterfaceClearStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpSnoopingControlInterfaceClearStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       {                   mgmtDhcpSnoopingControlInterfaceClearStatisticsIfIndex }
    ::= { mgmtDhcpSnoopingControlInterfaceClearStatisticsTable 1 }

MGMTDhcpSnoopingControlInterfaceClearStatisticsEntry ::= SEQUENCE {
    mgmtDhcpSnoopingControlInterfaceClearStatisticsIfIndex  MGMTInterfaceIndex,
    mgmtDhcpSnoopingControlInterfaceClearStatisticsClear    TruthValue
}

mgmtDhcpSnoopingControlInterfaceClearStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcpSnoopingControlInterfaceClearStatisticsEntry 1 }

mgmtDhcpSnoopingControlInterfaceClearStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "clear statistics per physical port."
    ::= { mgmtDhcpSnoopingControlInterfaceClearStatisticsEntry 2 }

mgmtDhcpSnoopingStatistics OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibObjects 5 }

mgmtDhcpSnoopingStatisticsInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDhcpSnoopingStatisticsInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of port statistics in DHCP Snooping "
    ::= { mgmtDhcpSnoopingStatistics 2 }

mgmtDhcpSnoopingStatisticsInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDhcpSnoopingStatisticsInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtDhcpSnoopingStatisticsInterfaceIfIndex }
    ::= { mgmtDhcpSnoopingStatisticsInterfaceTable 1 }

MGMTDhcpSnoopingStatisticsInterfaceEntry ::= SEQUENCE {
    mgmtDhcpSnoopingStatisticsInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtDhcpSnoopingStatisticsInterfaceRxDiscover          Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxOffer             Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxRequest           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxDecline           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxAck               Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxNak               Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxRelease           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxInform            Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxLeaseQuery        Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnassigned   Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnknown      Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxLeaseActive       Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxDiscardChksumErr  Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceRxDiscardUntrust    Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxDiscover          Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxOffer             Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxRequest           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxDecline           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxAck               Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxNak               Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxRelease           Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxInform            Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxLeaseQuery        Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnassigned   Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnknown      Unsigned32,
    mgmtDhcpSnoopingStatisticsInterfaceTxLeaseActive       Unsigned32
}

mgmtDhcpSnoopingStatisticsInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 1 }

mgmtDhcpSnoopingStatisticsInterfaceRxDiscover OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of discover (option 53 with value 1) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 2 }

mgmtDhcpSnoopingStatisticsInterfaceRxOffer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of offer (option 53 with value 2) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 3 }

mgmtDhcpSnoopingStatisticsInterfaceRxRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of request (option 53 with value 3) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 4 }

mgmtDhcpSnoopingStatisticsInterfaceRxDecline OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of decline (option 53 with value 4) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 5 }

mgmtDhcpSnoopingStatisticsInterfaceRxAck OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of ACK (option 53 with value 5) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 6 }

mgmtDhcpSnoopingStatisticsInterfaceRxNak OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of NAK (option 53 with value 6) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 7 }

mgmtDhcpSnoopingStatisticsInterfaceRxRelease OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of release (option 53 with value 7) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 8 }

mgmtDhcpSnoopingStatisticsInterfaceRxInform OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of inform (option 53 with value 8) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 9 }

mgmtDhcpSnoopingStatisticsInterfaceRxLeaseQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease query (option 53 with value 10) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 10 }

mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnassigned OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease unassigned (option 53 with value 11) packets
         received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 11 }

mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnknown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease unknown (option 53 with value 12) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 12 }

mgmtDhcpSnoopingStatisticsInterfaceRxLeaseActive OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease active (option 53 with value 13) packets received."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 13 }

mgmtDhcpSnoopingStatisticsInterfaceRxDiscardChksumErr OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of discard packet that IP/UDP checksum is error."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 14 }

mgmtDhcpSnoopingStatisticsInterfaceRxDiscardUntrust OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of discard packet that are coming from untrusted port."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 15 }

mgmtDhcpSnoopingStatisticsInterfaceTxDiscover OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of discover (option 53 with value 1) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 16 }

mgmtDhcpSnoopingStatisticsInterfaceTxOffer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of offer (option 53 with value 2) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 17 }

mgmtDhcpSnoopingStatisticsInterfaceTxRequest OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of request (option 53 with value 3) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 18 }

mgmtDhcpSnoopingStatisticsInterfaceTxDecline OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of decline (option 53 with value 4) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 19 }

mgmtDhcpSnoopingStatisticsInterfaceTxAck OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of ACK (option 53 with value 5) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 20 }

mgmtDhcpSnoopingStatisticsInterfaceTxNak OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of NAK (option 53 with value 6) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 21 }

mgmtDhcpSnoopingStatisticsInterfaceTxRelease OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of release (option 53 with value 7) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 22 }

mgmtDhcpSnoopingStatisticsInterfaceTxInform OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of inform (option 53 with value 8) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 23 }

mgmtDhcpSnoopingStatisticsInterfaceTxLeaseQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease query (option 53 with value 10) packets transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 24 }

mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnassigned OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease unassigned (option 53 with value 11) packets
         transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 25 }

mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnknown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease unknown (option 53 with value 12) packets
         transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 26 }

mgmtDhcpSnoopingStatisticsInterfaceTxLeaseActive OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The number of lease active (option 53 with value 13) packets
         transmited."
    ::= { mgmtDhcpSnoopingStatisticsInterfaceEntry 27 }

mgmtDhcpSnoopingMibConformance OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMib 2 }

mgmtDhcpSnoopingMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibConformance 1 }

mgmtDhcpSnoopingMibGroups OBJECT IDENTIFIER
    ::= { mgmtDhcpSnoopingMibConformance 2 }

mgmtDhcpSnoopingConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpSnoopingConfigGlobalsMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpSnoopingMibGroups 1 }

mgmtDhcpSnoopingConfigInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpSnoopingConfigInterfaceIfIndex,
                  mgmtDhcpSnoopingConfigInterfaceTrustMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpSnoopingMibGroups 2 }

mgmtDhcpSnoopingStatusAssignedIpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpSnoopingStatusAssignedIpMacAddress,
                  mgmtDhcpSnoopingStatusAssignedIpVlanId,
                  mgmtDhcpSnoopingStatusAssignedIpIfIndex,
                  mgmtDhcpSnoopingStatusAssignedIpIpAddress,
                  mgmtDhcpSnoopingStatusAssignedIpNetmask,
                  mgmtDhcpSnoopingStatusAssignedIpDhcpServerIp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpSnoopingMibGroups 3 }

mgmtDhcpSnoopingControlInterfaceClearStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtDhcpSnoopingControlInterfaceClearStatisticsIfIndex,
                  mgmtDhcpSnoopingControlInterfaceClearStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpSnoopingMibGroups 4 }

mgmtDhcpSnoopingStatisticsInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDhcpSnoopingStatisticsInterfaceIfIndex,
                  mgmtDhcpSnoopingStatisticsInterfaceRxDiscover,
                  mgmtDhcpSnoopingStatisticsInterfaceRxOffer,
                  mgmtDhcpSnoopingStatisticsInterfaceRxRequest,
                  mgmtDhcpSnoopingStatisticsInterfaceRxDecline,
                  mgmtDhcpSnoopingStatisticsInterfaceRxAck,
                  mgmtDhcpSnoopingStatisticsInterfaceRxNak,
                  mgmtDhcpSnoopingStatisticsInterfaceRxRelease,
                  mgmtDhcpSnoopingStatisticsInterfaceRxInform,
                  mgmtDhcpSnoopingStatisticsInterfaceRxLeaseQuery,
                  mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnassigned,
                  mgmtDhcpSnoopingStatisticsInterfaceRxLeaseUnknown,
                  mgmtDhcpSnoopingStatisticsInterfaceRxLeaseActive,
                  mgmtDhcpSnoopingStatisticsInterfaceRxDiscardChksumErr,
                  mgmtDhcpSnoopingStatisticsInterfaceRxDiscardUntrust,
                  mgmtDhcpSnoopingStatisticsInterfaceTxDiscover,
                  mgmtDhcpSnoopingStatisticsInterfaceTxOffer,
                  mgmtDhcpSnoopingStatisticsInterfaceTxRequest,
                  mgmtDhcpSnoopingStatisticsInterfaceTxDecline,
                  mgmtDhcpSnoopingStatisticsInterfaceTxAck,
                  mgmtDhcpSnoopingStatisticsInterfaceTxNak,
                  mgmtDhcpSnoopingStatisticsInterfaceTxRelease,
                  mgmtDhcpSnoopingStatisticsInterfaceTxInform,
                  mgmtDhcpSnoopingStatisticsInterfaceTxLeaseQuery,
                  mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnassigned,
                  mgmtDhcpSnoopingStatisticsInterfaceTxLeaseUnknown,
                  mgmtDhcpSnoopingStatisticsInterfaceTxLeaseActive }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDhcpSnoopingMibGroups 5 }

mgmtDhcpSnoopingMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDhcpSnoopingConfigGlobalsInfoGroup,
                       mgmtDhcpSnoopingConfigInterfaceInfoGroup,
                       mgmtDhcpSnoopingStatusAssignedIpTableInfoGroup,
                       mgmtDhcpSnoopingControlInterfaceClearStatisticsTableInfoGroup,
                       mgmtDhcpSnoopingStatisticsInterfaceTableInfoGroup }

    ::= { mgmtDhcpSnoopingMibCompliances 1 }

END
