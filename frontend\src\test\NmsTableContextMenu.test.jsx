import { render, fireEvent } from '@testing-library/react';
import NmsTableContextMenu from '../components/NmsTableContextMenu';
import { describe, expect, it, vi } from "vitest";
import { Provider } from 'react-redux';
import { store } from '../app/store';

const mockMenuItems = [
  { key: 'item1', label: 'Item 1' },
  { key: 'item2', label: 'Item 2' },
];

const mockRecord = {
  id: 1,
  name: 'Record 1',
};

const mockMenuClickHandler = vi.fn();

describe('NmsTableContextMenu', () => {
  it('should render the menu items when showMenu is true', () => {
    const position = {
      showMenu: true,
      xPos: 100,
      yPos: 200,
    };

    const { getByText } = render(
        <Provider store={store}><NmsTableContextMenu
        menuItems={mockMenuItems}
        record={mockRecord}
        onMenuClick={mockMenuClickHandler}
        position={position}
      /></Provider>
      
    );

    const item1 = getByText('Item 1');
    const item2 = getByText('Item 2');

    expect(item1).toBeInTheDocument();
    expect(item2).toBeInTheDocument();
  });


  it('should not render the menu items when showMenu is false', () => {
    const position = {
      showMenu: false,
      xPos: 100,
      yPos: 200,
    };

    const { queryByText } = render(
        <Provider store={store}><NmsTableContextMenu
        menuItems={mockMenuItems}
        record={mockRecord}
        onMenuClick={mockMenuClickHandler}
        position={position}
      /></Provider>
    );

    const item1 = queryByText('Item 1');
    const item2 = queryByText('Item 2');

    expect(item1).toBeNull();
    expect(item2).toBeNull();
  });
});
