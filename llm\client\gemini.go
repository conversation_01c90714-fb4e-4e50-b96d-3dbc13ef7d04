// devicequery_multillm/llm/gemini.go
package llm

import (
	"context"
	"encoding/json"
	"fmt"

	// "strings" // No longer needed here

	"github.com/google/generative-ai-go/genai"
	"github.com/qeof/q"
	"google.golang.org/api/option"
)

// GeminiClient implements the LLMClient interface for Google Gemini.
type GeminiClient struct {
	client       *genai.Client
	model        *genai.GenerativeModel
	tableSchema  string
	systemPrompt string
}

const geminiSQLToolName = "sql_query_devices" // Consistent

// SetSystemPrompt sets the system prompt for the Gemini client.
func (g *GeminiClient) SetSystemPrompt(systemPrompt string) {
	g.systemPrompt = systemPrompt
	if g.model != nil { // Ensure model is initialized
		g.model.SystemInstruction = &genai.Content{
			Parts: []genai.Part{genai.Text(g.systemPrompt)},
		}
	}
}

// GetSystemPrompt gets the system prompt for the Gemini client.
func (g *GeminiClient) GetSystemPrompt() string {
	return g.systemPrompt
}

// Init initializes the Gemini client.
func (g *GeminiClient) Init(config LLMConfig) error {
	if config.APIKey == "" {
		return fmt.Errorf("Gemini API key is required")
	}
	g.tableSchema = config.DevTableSchema

	var err error
	g.client, err = genai.NewClient(context.Background(), option.WithAPIKey(config.APIKey))
	if err != nil {
		return fmt.Errorf("failed to create Gemini client: %w", err)
	}
	g.model = g.client.GenerativeModel(config.Model)
	g.model.SystemInstruction = &genai.Content{
		Parts: []genai.Part{
			genai.Text(g.GetSystemPrompt() + g.getSystemPrompt()),
		},
	}
	q.Q("Gemini LLM client initialized for model:", config.Model)
	return nil
}

func (g *GeminiClient) getSystemPrompt() string {
	return fmt.Sprintf(`You are an expert assistant for querying device information from a SQLite database using the '%s' tool.
You have access to a 'devices' table. Your primary function is to translate user's natural language questions about devices into valid SQL SELECT queries.

Database Schema for 'devices' table:
%s

Key considerations for constructing SQL queries:
1.  **Case Sensitivity**: Column names in SQL queries MUST EXACTLY MATCH the case shown in the schema.
2.  **String Literals**: Enclose string values in SQL WHERE clauses with DOUBLE QUOTES.
3.  **Boolean Fields**: (e.g., 'IsOnline') are stored as INTEGER (1 for true, 0 for false). Query as 'IsOnline = 1'.
4.  **Timestamp Field**: 'Timestamp' is UNIX timestamp as STRING. Query using SQLite datetime() function: datetime(TimestampUnixStr) > datetime('YYYY-MM-DDTHH:MM:SSZ'))"
5.  **JSON Fields**: 'Capabilities', 'DeviceErrors' are JSON strings. Use 'LIKE' or json_extract().
6.  **Tool Usage**: ONLY use the '%s' tool for SELECT statements.
If a query returns an error or no results, inform the user clearly.
`, geminiSQLToolName, g.tableSchema, geminiSQLToolName)
}

// GenerateResponse sends messages to Gemini and gets a response.
func (g *GeminiClient) GenerateResponse(ctx context.Context, messages []Message, tools []ToolDefine) (*LLMResponse, error) {
	if g.model == nil {
		return nil, fmt.Errorf("Gemini model is not initialized")
	}

	// Convert tools to Gemini format if provided
	if len(tools) > 0 {
		var geminiTools []*genai.Tool
		for _, tool := range tools {
			// Convert required fields from interface{} slice to []string
			var requiredFields []string
			if rawReq, ok := tool.Args["required"].([]interface{}); ok {
				for _, r := range rawReq {
					if s, ok := r.(string); ok {
						requiredFields = append(requiredFields, s)
					}
				}
			}

			geminiTool := &genai.Tool{
				FunctionDeclarations: []*genai.FunctionDeclaration{
					{
						Name:        tool.Name,
						Description: tool.Description,
						Parameters: &genai.Schema{
							Type:       genai.TypeObject,
							Properties: convertArgsToSchema(tool.Args),
							Required:   requiredFields,
						},
					},
				},
			}
			geminiTools = append(geminiTools, geminiTool)
		}
		g.model.Tools = geminiTools
	}

	chat := g.model.StartChat()
	// Convert all but the last message for history
	if len(messages) > 1 {
		chat.History = g.convertToGeminiContent(messages[:len(messages)-1])
	} else {
		chat.History = []*genai.Content{} // Empty history if only one message (the current one)
	}

	latestMessage := messages[len(messages)-1]
	var geminiParts []genai.Part

	if latestMessage.Role == "tool" {
		toolResponseArgs := make(map[string]interface{})
		toolResponseArgs["output"] = latestMessage.Content
		geminiParts = append(geminiParts, genai.FunctionResponse{
			Name:     latestMessage.Name,
			Response: toolResponseArgs,
		})
	} else {
		geminiParts = append(geminiParts, genai.Text(latestMessage.Content))
	}

	resp, err := chat.SendMessage(ctx, geminiParts...)
	if err != nil {
		return nil, fmt.Errorf("Gemini SendMessage failed: %w", err)
	}

	llmResp := &LLMResponse{
		Message: Message{Role: "assistant"}, // Default role
	}

	if len(resp.Candidates) > 0 && resp.Candidates[0].Content != nil {
		candidate := resp.Candidates[0].Content
		llmResp.Message.Role = convertGeminiRoleToGeneric(candidate.Role)

		for _, part := range candidate.Parts {
			switch p := part.(type) {
			case genai.Text:
				llmResp.Message.Content += string(p)
			case genai.FunctionCall:
				// Gemini's FunctionCall.Args is map[string]any. Convert to JSON string for our generic ToolCall.
				argsBytes, err := json.Marshal(p.Args)
				if err != nil {
					q.Q("Error marshalling Gemini function call args: %v", err)
					// Potentially skip this tool call or return an error
					continue
				}
				llmResp.Message.ToolCalls = append(llmResp.Message.ToolCalls, ToolCall{
					ID:   p.Name, // Gemini uses Name as ID for the call for now
					Type: "function",
					Function: FunctionCall{
						Name:      p.Name,
						Arguments: string(argsBytes),
					},
				})
				llmResp.StopReason = "tool_calls"
			}
		}
		// If there's content and no tool call, it's a stop.
		// If there's a tool call, StopReason is already tool_calls.
		// If there's neither, it's an empty response (which can happen).
		if llmResp.Message.Content != "" && llmResp.StopReason != "tool_calls" {
			llmResp.StopReason = "stop"
		}
	} else {
		// No candidates or content, might be an issue or an empty response.
		q.Q("Gemini response had no candidates or content.")
		llmResp.Message.Content = "(No content in response)" // Placeholder
		llmResp.StopReason = "error_or_empty"
	}
	return llmResp, nil
}

func (g *GeminiClient) convertToGeminiContent(messages []Message) []*genai.Content {
	var history []*genai.Content
	for _, msg := range messages {
		role := convertGenericRoleToGemini(msg.Role)
		var parts []genai.Part
		if msg.Role == "tool" {
			toolResponseArgs := make(map[string]interface{})
			toolResponseArgs["output"] = msg.Content // Match key used in GenerateResponse
			parts = append(parts, genai.FunctionResponse{
				Name:     msg.Name,
				Response: toolResponseArgs,
			})
		} else if len(msg.ToolCalls) > 0 { // This is an assistant message requesting tool calls
			for _, tc := range msg.ToolCalls {
				var argsMap map[string]interface{}
				// We stored arguments as a JSON string, so unmarshal it back to a map for Gemini
				err := json.Unmarshal([]byte(tc.Function.Arguments), &argsMap)
				if err != nil {
					q.Q("Error unmarshalling tool call arguments for Gemini history: %v", err)
					// Skip this part or handle error
					continue
				}
				parts = append(parts, genai.FunctionCall{Name: tc.Function.Name, Args: argsMap})
			}
		} else {
			parts = append(parts, genai.Text(msg.Content))
		}
		if len(parts) > 0 { // Only add to history if there are parts
			history = append(history, &genai.Content{Role: role, Parts: parts})
		}
	}
	return history
}

func convertGenericRoleToGemini(role string) string {
	switch role {
	case "user":
		return "user"
	case "assistant":
		return "model"
	case "tool": // This represents a message *from* a tool back to the LLM
		return "function" // The content part will be genai.FunctionResponse
	default: // "system" is handled by SystemInstruction, not direct history for Gemini chat
		q.Q("Warning: Unsupported generic role '%s' for Gemini history conversion, defaulting to 'user'", role)
		return "user"
	}
}
func convertGeminiRoleToGeneric(role string) string {
	switch role {
	case "user":
		return "user"
	case "model":
		return "assistant"
	// "function" role from Gemini response indicates a FunctionCall *by the model*
	// or a FunctionResponse *from our code*.
	// If it's a FunctionCall by model, our generic message's Role should be "assistant" and ToolCalls filled.
	// If it's a FunctionResponse from us, this conversion isn't used (we construct generic "tool" message).
	// This function is for converting model's *response message role*.
	default:
		q.Q("Warning: Unknown Gemini role '%s' for generic conversion, defaulting to 'assistant'", role)
		return "assistant"
	}
}

// convertArgsToSchema converts a map of arguments to a Gemini schema
func convertArgsToSchema(args map[string]interface{}) map[string]*genai.Schema {
	q.Q("convertArgsToSchema", args)

	schema := make(map[string]*genai.Schema)

	// If schema properties provided, iterate over them
	if props, ok := args["properties"].(map[string]any); ok {
		for key, raw := range props {
			item, ok := raw.(map[string]any)
			if !ok {
				q.Q("Warning: property '%s' is not a valid schema definition", key)
				continue
			}
			desc, _ := item["description"].(string)
			// Determine type
			typeStr, _ := item["type"].(string)
			var typ genai.Type = genai.TypeString
			switch typeStr {
			case "string":
				typ = genai.TypeString
			case "integer":
				typ = genai.TypeInteger
			case "boolean":
				typ = genai.TypeBoolean
			case "object":
				typ = genai.TypeObject
			case "array":
				typ = genai.TypeArray
			}
			schema[key] = &genai.Schema{Type: typ, Description: desc}
		}
	} else {
		// Fallback for flat schema definitions
		for key := range args {
			item, ok := args[key].(map[string]any)
			if !ok {
				continue
			}
			desc, ok := item["description"].(string)
			if !ok {
				q.Q("Warning: Argument '%s' missing description, defaulting to string type", key)
				continue
			}
			schema[key] = &genai.Schema{Type: genai.TypeString, Description: desc}
		}
	}
	return schema
}

// Close cleans up the Gemini client.
func (g *GeminiClient) Close() error {
	if g.client != nil {
		return g.client.Close()
	}
	return nil
}
