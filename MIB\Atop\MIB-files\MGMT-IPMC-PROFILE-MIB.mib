-- *****************************************************************
-- IPMC-PROFILE-MIB:  
-- ****************************************************************

MGMT-IPMC-PROFILE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtIpmcProfileMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IPMC Profile MIB"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 38 }


MGMTIpmcProfileRuleActionType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the configured action type for IPMC profile
         rule."
    SYNTAX      INTEGER { deny(0), permit(1) }

mgmtIpmcProfileMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileMib 1 }

mgmtIpmcProfileConfig OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileMibObjects 2 }

mgmtIpmcProfileConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileConfig 1 }

mgmtIpmcProfileConfigGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IPMC Profile global functionality."
    ::= { mgmtIpmcProfileConfigGlobals 1 }

mgmtIpmcProfileConfigManagementTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcProfileConfigManagementEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing IPMC profile entries."
    ::= { mgmtIpmcProfileConfig 2 }

mgmtIpmcProfileConfigManagementEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileConfigManagementEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcProfileConfigManagementProfileName }
    ::= { mgmtIpmcProfileConfigManagementTable 1 }

MGMTIpmcProfileConfigManagementEntry ::= SEQUENCE {
    mgmtIpmcProfileConfigManagementProfileName         MGMTDisplayString,
    mgmtIpmcProfileConfigManagementProfileDescription  MGMTDisplayString,
    mgmtIpmcProfileConfigManagementAction              MGMTRowEditorState
}

mgmtIpmcProfileConfigManagementProfileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile management entry."
    ::= { mgmtIpmcProfileConfigManagementEntry 1 }

mgmtIpmcProfileConfigManagementProfileDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The description of the IPMC Profile management entry."
    ::= { mgmtIpmcProfileConfigManagementEntry 2 }

mgmtIpmcProfileConfigManagementAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigManagementEntry 100 }

mgmtIpmcProfileConfigManagementTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileConfig 3 }

mgmtIpmcProfileConfigManagementTableRowEditorProfileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile management entry."
    ::= { mgmtIpmcProfileConfigManagementTableRowEditor 1 }

mgmtIpmcProfileConfigManagementTableRowEditorProfileDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The description of the IPMC Profile management entry."
    ::= { mgmtIpmcProfileConfigManagementTableRowEditor 2 }

mgmtIpmcProfileConfigManagementTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigManagementTableRowEditor 100 }

mgmtIpmcProfileConfigIpv4AddressRangeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcProfileConfigIpv4AddressRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the IPv4 address range entries that will
         be applied for IPMC profile(s)."
    ::= { mgmtIpmcProfileConfig 4 }

mgmtIpmcProfileConfigIpv4AddressRangeEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileConfigIpv4AddressRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcProfileConfigIpv4AddressRangeRangeName }
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeTable 1 }

MGMTIpmcProfileConfigIpv4AddressRangeEntry ::= SEQUENCE {
    mgmtIpmcProfileConfigIpv4AddressRangeRangeName     MGMTDisplayString,
    mgmtIpmcProfileConfigIpv4AddressRangeStartAddress  IpAddress,
    mgmtIpmcProfileConfigIpv4AddressRangeEndAddress    IpAddress,
    mgmtIpmcProfileConfigIpv4AddressRangeAction        MGMTRowEditorState
}

mgmtIpmcProfileConfigIpv4AddressRangeRangeName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range entry."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeEntry 1 }

mgmtIpmcProfileConfigIpv4AddressRangeStartAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv4 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeEntry 2 }

mgmtIpmcProfileConfigIpv4AddressRangeEndAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv4 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeEntry 3 }

mgmtIpmcProfileConfigIpv4AddressRangeAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeEntry 100 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileConfig 5 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorRangeName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range entry."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditor 1 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorStartAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv4 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditor 2 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorEndAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv4 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditor 3 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditor 100 }

mgmtIpmcProfileConfigIpv6AddressRangeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcProfileConfigIpv6AddressRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the IPv6 address range entries that will
         be applied for IPMC profile(s)."
    ::= { mgmtIpmcProfileConfig 6 }

mgmtIpmcProfileConfigIpv6AddressRangeEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileConfigIpv6AddressRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcProfileConfigIpv6AddressRangeRangeName }
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeTable 1 }

MGMTIpmcProfileConfigIpv6AddressRangeEntry ::= SEQUENCE {
    mgmtIpmcProfileConfigIpv6AddressRangeRangeName     MGMTDisplayString,
    mgmtIpmcProfileConfigIpv6AddressRangeStartAddress  InetAddressIPv6,
    mgmtIpmcProfileConfigIpv6AddressRangeEndAddress    InetAddressIPv6,
    mgmtIpmcProfileConfigIpv6AddressRangeAction        MGMTRowEditorState
}

mgmtIpmcProfileConfigIpv6AddressRangeRangeName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range entry."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeEntry 1 }

mgmtIpmcProfileConfigIpv6AddressRangeStartAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv6 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeEntry 2 }

mgmtIpmcProfileConfigIpv6AddressRangeEndAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv6 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeEntry 3 }

mgmtIpmcProfileConfigIpv6AddressRangeAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeEntry 100 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileConfig 7 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorRangeName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range entry."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditor 1 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorStartAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv6 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditor 2 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorEndAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv6 address of the range that IPMC Profile performs
         checking."
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditor 3 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditor 100 }

mgmtIpmcProfileConfigRuleTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcProfileConfigRuleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the filtering rules with respect to a set
         of address range used in a specific IPMC profile management entry."
    ::= { mgmtIpmcProfileConfig 8 }

mgmtIpmcProfileConfigRuleEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileConfigRuleEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcProfileConfigRuleProfileName,
                  mgmtIpmcProfileConfigRuleRuleRange }
    ::= { mgmtIpmcProfileConfigRuleTable 1 }

MGMTIpmcProfileConfigRuleEntry ::= SEQUENCE {
    mgmtIpmcProfileConfigRuleProfileName    MGMTDisplayString,
    mgmtIpmcProfileConfigRuleRuleRange      MGMTDisplayString,
    mgmtIpmcProfileConfigRuleNextRuleRange  MGMTDisplayString,
    mgmtIpmcProfileConfigRuleRuleAction     MGMTIpmcProfileRuleActionType,
    mgmtIpmcProfileConfigRuleRuleLog        TruthValue,
    mgmtIpmcProfileConfigRuleAction         MGMTRowEditorState
}

mgmtIpmcProfileConfigRuleProfileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile management entry."
    ::= { mgmtIpmcProfileConfigRuleEntry 1 }

mgmtIpmcProfileConfigRuleRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range used as a rule."
    ::= { mgmtIpmcProfileConfigRuleEntry 2 }

mgmtIpmcProfileConfigRuleNextRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The next rule's address range name that this IPMC Profile management
         entry performs checking."
    ::= { mgmtIpmcProfileConfigRuleEntry 3 }

mgmtIpmcProfileConfigRuleRuleAction OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileRuleActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The filtering action while this IPMC Profile management entry performs
         checking. deny(0) will prohibit the IPMC control frames destined to
         protocol stack. permit(1) will pass the IPMC control frames destined to
         protocol stack."
    ::= { mgmtIpmcProfileConfigRuleEntry 4 }

mgmtIpmcProfileConfigRuleRuleLog OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the IPMC Profile will log matched group address that is filtered
         by this rule with the corresponding action (deny or permit). Disable
         the IPMC Profile will not log any action for any group address whether
         or not to be filtered by this rule."
    ::= { mgmtIpmcProfileConfigRuleEntry 5 }

mgmtIpmcProfileConfigRuleAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigRuleEntry 100 }

mgmtIpmcProfileConfigRuleTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileConfig 9 }

mgmtIpmcProfileConfigRuleTableRowEditorProfileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile management entry."
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 1 }

mgmtIpmcProfileConfigRuleTableRowEditorRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range used as a rule."
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 2 }

mgmtIpmcProfileConfigRuleTableRowEditorNextRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The next rule's address range name that this IPMC Profile management
         entry performs checking."
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 3 }

mgmtIpmcProfileConfigRuleTableRowEditorRuleAction OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileRuleActionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The filtering action while this IPMC Profile management entry performs
         checking. deny(0) will prohibit the IPMC control frames destined to
         protocol stack. permit(1) will pass the IPMC control frames destined to
         protocol stack."
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 4 }

mgmtIpmcProfileConfigRuleTableRowEditorRuleLog OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the IPMC Profile will log matched group address that is filtered
         by this rule with the corresponding action (deny or permit). Disable
         the IPMC Profile will not log any action for any group address whether
         or not to be filtered by this rule."
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 5 }

mgmtIpmcProfileConfigRuleTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcProfileConfigRuleTableRowEditor 100 }

mgmtIpmcProfileConfigPrecedenceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcProfileConfigPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the rule entries of a specific IPMC
         profile in precedence order."
    ::= { mgmtIpmcProfileConfig 10 }

mgmtIpmcProfileConfigPrecedenceEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileConfigPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcProfileConfigPrecedenceProfileName,
                  mgmtIpmcProfileConfigPrecedenceRulePrecedence }
    ::= { mgmtIpmcProfileConfigPrecedenceTable 1 }

MGMTIpmcProfileConfigPrecedenceEntry ::= SEQUENCE {
    mgmtIpmcProfileConfigPrecedenceProfileName     MGMTDisplayString,
    mgmtIpmcProfileConfigPrecedenceRulePrecedence  Integer32,
    mgmtIpmcProfileConfigPrecedenceRuleRange       MGMTDisplayString,
    mgmtIpmcProfileConfigPrecedenceNextRuleRange   MGMTDisplayString,
    mgmtIpmcProfileConfigPrecedenceRuleAction      MGMTIpmcProfileRuleActionType,
    mgmtIpmcProfileConfigPrecedenceRuleLog         TruthValue
}

mgmtIpmcProfileConfigPrecedenceProfileName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile management entry."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 1 }

mgmtIpmcProfileConfigPrecedenceRulePrecedence OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The precedence of the IPMC profile rule entry."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 2 }

mgmtIpmcProfileConfigPrecedenceRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The name of the IPMC profile address range used as a rule."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 3 }

mgmtIpmcProfileConfigPrecedenceNextRuleRange OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The next rule's address range name that this IPMC Profile management
         entry performs checking."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 4 }

mgmtIpmcProfileConfigPrecedenceRuleAction OBJECT-TYPE
    SYNTAX      MGMTIpmcProfileRuleActionType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The filtering action while this IPMC Profile management entry performs
         checking. deny(0) will prohibit the IPMC control frames destined to
         protocol stack. permit(1) will pass the IPMC control frames destined to
         protocol stack."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 5 }

mgmtIpmcProfileConfigPrecedenceRuleLog OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Enable the IPMC Profile will log matched group address that is filtered
         by this rule with the corresponding action (deny or permit). Disable
         the IPMC Profile will not log any action for any group address whether
         or not to be filtered by this rule."
    ::= { mgmtIpmcProfileConfigPrecedenceEntry 6 }

mgmtIpmcProfileMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileMib 2 }

mgmtIpmcProfileMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileMibConformance 1 }

mgmtIpmcProfileMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpmcProfileMibConformance 2 }

mgmtIpmcProfileConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigGlobalsAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 1 }

mgmtIpmcProfileConfigManagementTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigManagementProfileName,
                  mgmtIpmcProfileConfigManagementProfileDescription,
                  mgmtIpmcProfileConfigManagementAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 2 }

mgmtIpmcProfileConfigManagementTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcProfileConfigManagementTableRowEditorProfileName,
                  mgmtIpmcProfileConfigManagementTableRowEditorProfileDescription,
                  mgmtIpmcProfileConfigManagementTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 3 }

mgmtIpmcProfileConfigIpv4AddressRangeTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigIpv4AddressRangeRangeName,
                  mgmtIpmcProfileConfigIpv4AddressRangeStartAddress,
                  mgmtIpmcProfileConfigIpv4AddressRangeEndAddress,
                  mgmtIpmcProfileConfigIpv4AddressRangeAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 4 }

mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorRangeName,
                  mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorStartAddress,
                  mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorEndAddress,
                  mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 5 }

mgmtIpmcProfileConfigIpv6AddressRangeTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigIpv6AddressRangeRangeName,
                  mgmtIpmcProfileConfigIpv6AddressRangeStartAddress,
                  mgmtIpmcProfileConfigIpv6AddressRangeEndAddress,
                  mgmtIpmcProfileConfigIpv6AddressRangeAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 6 }

mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorRangeName,
                  mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorStartAddress,
                  mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorEndAddress,
                  mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 7 }

mgmtIpmcProfileConfigRuleTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigRuleProfileName,
                  mgmtIpmcProfileConfigRuleRuleRange,
                  mgmtIpmcProfileConfigRuleNextRuleRange,
                  mgmtIpmcProfileConfigRuleRuleAction,
                  mgmtIpmcProfileConfigRuleRuleLog,
                  mgmtIpmcProfileConfigRuleAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 8 }

mgmtIpmcProfileConfigRuleTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcProfileConfigRuleTableRowEditorProfileName,
                  mgmtIpmcProfileConfigRuleTableRowEditorRuleRange,
                  mgmtIpmcProfileConfigRuleTableRowEditorNextRuleRange,
                  mgmtIpmcProfileConfigRuleTableRowEditorRuleAction,
                  mgmtIpmcProfileConfigRuleTableRowEditorRuleLog,
                  mgmtIpmcProfileConfigRuleTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 9 }

mgmtIpmcProfileConfigPrecedenceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcProfileConfigPrecedenceProfileName,
                  mgmtIpmcProfileConfigPrecedenceRulePrecedence,
                  mgmtIpmcProfileConfigPrecedenceRuleRange,
                  mgmtIpmcProfileConfigPrecedenceNextRuleRange,
                  mgmtIpmcProfileConfigPrecedenceRuleAction,
                  mgmtIpmcProfileConfigPrecedenceRuleLog }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcProfileMibGroups 10 }

mgmtIpmcProfileMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpmcProfileConfigGlobalsInfoGroup,
                       mgmtIpmcProfileConfigManagementTableInfoGroup,
                       mgmtIpmcProfileConfigManagementTableRowEditorInfoGroup,
                       mgmtIpmcProfileConfigIpv4AddressRangeTableInfoGroup,
                       mgmtIpmcProfileConfigIpv4AddressRangeTableRowEditorInfoGroup,
                       mgmtIpmcProfileConfigIpv6AddressRangeTableInfoGroup,
                       mgmtIpmcProfileConfigIpv6AddressRangeTableRowEditorInfoGroup,
                       mgmtIpmcProfileConfigRuleTableInfoGroup,
                       mgmtIpmcProfileConfigRuleTableRowEditorInfoGroup,
                       mgmtIpmcProfileConfigPrecedenceTableInfoGroup }

    ::= { mgmtIpmcProfileMibCompliances 1 }

END
