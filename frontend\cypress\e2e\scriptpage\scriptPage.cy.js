// cypress/e2e/scriptpage/scriptPage.cy.js

describe('ScriptPage Component', () => {
    beforeEach(() => {
      cy.visit('/login');
      cy.get('[data-testid="username"]').type('admin');
      cy.get('[data-testid="password"]').type('default');
      cy.get('[data-testid="submit"]').click();
      cy.url().should('not.include', '/login');
      cy.visit('/scripts');
  
    });
  
    it('should allow typing in the textarea', () => {
      cy.get('textarea').first().type('beep 00-60-E9-16-5F-68').should('have.value', 'beep 00-60-E9-16-5F-68');
    });

    it('should validate command syntax', () => {
      // Test invalid commands
      cy.get('textarea').first().type('keep 00-60-E9-16-5F-68').should('have.value', 'keep 00-60-E9-16-5F-68');
      cy.contains('run command').click();
      cy.contains('View Result').click();
      cy.contains('error: unknown command').should('be.visible');
    });
    it('should handle MAC address normalization', () => {
      // Test colon-separated MAC
      cy.get('textarea').first().type('beep 00:60:E9:16:5F:68');
      cy.contains('run command').click();
      cy.contains('View Result').click();
      cy.contains('gwd beep 00-60-E9-16-5F-68').should('be.visible');
    });

   
    it('should allow setting command flags', () => {
      cy.get('input[placeholder="-cc"]').type('client1').should('have.value', 'client1');
      cy.get('input[placeholder="-ct"]').type('tag1').should('have.value', 'tag1');
      cy.get('input[placeholder="-ck"]').type('root').should('have.value', 'root');
      
      cy.contains('clear command flags').click();
      cy.get('input[placeholder="-cc"]').should('have.value', '');
      cy.get('input[placeholder="-ct"]').should('have.value', '');
      cy.get('input[placeholder="-ck"]').should('have.value', '');
    });

    it('should apply flags to commands', () => {
      cy.get('input[placeholder="-cc"]').type('ishwarclient');
      cy.get('textarea').first().type('beep 00-60-E9-16-5F-68');
      cy.contains('run command').click();
      cy.contains('View Result').click();
      cy.contains('Service Name:ishwarclient').should('be.visible');
    });
  
    it('should send a command and display the result', () => {
      const command = 'gwd beep 00-60-E9-16-5F-68';
      cy.intercept('POST', '/api/v1/commands', {
        fixture: 'commandResult.json', 
      }).as('sendCommand');
      cy.get('textarea').first().type(command)
      cy.contains('run command').click();
  
      cy.wait('@sendCommand').then(() => {
        cy.contains(command).should('be.visible');
        cy.contains('View Result').click();
        cy.contains('Command:').should('be.visible');
        cy.contains('Service Name:').should('be.visible');
        cy.contains('Status:').should('be.visible');
        cy.contains('Result:').should('be.visible');
        cy.contains('ok').should('be.visible');
      });
    });
  
    it('should download all results', () => {
      cy.intercept('GET', '/api/v1/commands?cmd=all').as('downloadAll');
      cy.contains('Download all results').click();
      cy.wait('@downloadAll').then((interception) => {
        expect(interception.response.statusCode).to.eq(200);
        if (interception.response.headers['content-disposition']) {
          expect(interception.response.headers['content-disposition']).to.contain('attachment');
        }
      });
    });
    it('should show error for device not found', () => {
      cy.get('textarea').first().type('beep 00-00-00-00-00-00');
      cy.contains('run command').click();
      cy.contains('View Result').click();

      cy.intercept('POST', '/api/v1/commands', {
        statusCode: 200,
        body: {
          command: 'beep 00-00-00-00-00-00',
          status: 'error: device not found',
          result: ''
        }
      }).as('beepCommand');
      
      cy.wait('@beepCommand');
      cy.contains('error: device not found').should('be.visible');
    });

    it('should handle firmware update command', () => {
      cy.get('textarea').first().type('gwd firmware update 00-60-E9-16-5F-68 https://www.atoponline.com/wp-content/uploads/2017/11/EHG7504_EHG7508_EHG7512_EHG7516_EHG7520_EHG9508_EHG9512_EMG8508_EMG8510_RHG7528_RHG9528-K800A800.zip');
      cy.contains('run command').click();
      cy.contains('View Result').click();
      cy.contains('ok').should('be.visible');
    });

     it('should show error for device not found', () => {
      // Test invalid commands
    cy.get('textarea').first().type('beep 00-00-00-00-00-00');
      cy.contains('run command').click();
      cy.contains('View Result').click();
    cy.contains('error: device not found').should('be.visible');
  });

  it('should handle device management commands', () => {
    // Test device delete
    cy.get('textarea').first().type('device delete all');
    cy.contains('run command').click();
    cy.contains('View Result').click();
    cy.contains('ok').should('be.visible');
  });
  });