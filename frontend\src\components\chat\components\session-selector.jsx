import React, { memo, useEffect, useState } from "react";
import { Select, Button, Space, message, Tooltip } from "antd";
import { PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import {
  useGetLLMsessionListQuery,
  useGetLLMsessionQuery,
  useDeleteLLMsessionMutation,
} from "../../../app/services/aiassistApi";
import { useChatStore } from "../../../features/chat/chat-store";

const { Option } = Select;

const SessionSelector = memo(({ disabled }) => {
  const [loadingSession, setLoadingSession] = useState(false);

  const {
    sessions,
    currentSession,
    session_id,
    setSessions,
    setCurrentSession,
    toggleNewSessionModal,
    clearMessages,
    addMessage,
  } = useChatStore();

  const {
    data: sessionsList,
    isLoading: isLoadingSessions,
    refetch: refetchSessions,
  } = useGetLLMsessionListQuery();

  const { data: sessionDetails, isLoading: isLoadingDetails } =
    useGetLLMsessionQuery(session_id, {
      skip: !session_id,
    });

  const [deleteSession] = useDeleteLLMsessionMutation();

  // Update sessions list when data changes
  useEffect(() => {
    if (sessionsList) {
      setSessions(sessionsList);
    }
    // If no sessions exist, reset all store values
    if (!sessionsList || sessionsList.length === 0) {
      setSessions([]);
      setCurrentSession(null);
      clearMessages();
    }
  }, [sessionsList, setSessions, setCurrentSession, clearMessages]);

  // Load session details and messages when session changes
  useEffect(() => {
    if (sessionDetails) {
      setCurrentSession(sessionDetails);

      // Clear current messages and load session messages
      clearMessages();

      // Add session messages to chat
      if (sessionDetails.messages && sessionDetails.messages.length > 0) {
        sessionDetails.messages.forEach((msg) => {
          addMessage({
            ...(msg.content && { content: msg.content }),
            role: msg.role,
            ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
            ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
            ...(msg.name && { name: msg.name }),
            timestamp: new Date(msg.timestamp || Date.now()),
          });
        });
      } else {
        // Add welcome message if no messages exist
        addMessage({
          content: `Session loaded: ${sessionDetails.provider}/${sessionDetails.model}. How can I help you today?`,
          role: "assistant",
          timestamp: new Date(),
        });
      }
    }
  }, [sessionDetails, setCurrentSession, clearMessages, addMessage]);

  const handleSessionChange = async (selectedSessionId) => {
    if (selectedSessionId === session_id) return;

    setLoadingSession(true);
    try {
      // The useGetLLMsessionQuery will automatically fetch the session details
      // when session_id changes in the store
      const selectedSession = sessions.find(
        (s) => s.session_id === selectedSessionId
      );
      if (selectedSession) {
        setCurrentSession(selectedSession);
      }
    } catch (error) {
      console.error("Failed to load session:", error);
      message.error("Failed to load session");
    } finally {
      setLoadingSession(false);
    }
  };

  const handleDeleteSession = async (sessionId, e) => {
    e.stopPropagation(); // Prevent dropdown from closing

    try {
      await deleteSession({ session_id: sessionId }).unwrap();
      message.success("Session deleted successfully");

      // If deleted session was current, clear current session
      if (sessionId === session_id) {
        setCurrentSession(null);
        clearMessages();
      }

      // Refresh sessions list
      refetchSessions();
    } catch (error) {
      console.error("Failed to delete session:", error);
      message.error("Failed to delete session");
    }
  };

  const formatSessionLabel = (session) => {
    return `${session.provider}/${session.model}`;
  };

  const formatSessionDescription = (session) => {
    const createdAt = new Date(session.created_at).toLocaleDateString();
    const messageCount = session.messages?.length || 0;
    return `Created: ${createdAt} • Messages: ${messageCount}`;
  };

  return (
    <Space.Compact style={{ width: "100%" }}>
      <Select
        value={session_id}
        placeholder="Select a session"
        onChange={handleSessionChange}
        loading={isLoadingSessions || loadingSession || isLoadingDetails}
        disabled={disabled}
        style={{ flex: 1, minHeight: 32 }}
        size="default"
        // allowClear
        // onClear={() => {
        //   setCurrentSession(null);
        //   clearMessages();
        // }}
        optionLabelProp="label"
        dropdownStyle={{ maxHeight: 400 }}
        dropdownRender={(menu) => (
          <div>
            {menu}
            <div style={{ padding: 8, borderTop: "1px solid #f0f0f0" }}>
              <Button
                type="text"
                icon={<PlusOutlined />}
                onClick={toggleNewSessionModal}
                style={{ width: "100%", height: 32 }}
              >
                Create New Session
              </Button>
            </div>
          </div>
        )}
      >
        {sessions?.map((session) => (
          <Option
            key={session.session_id}
            value={session.session_id}
            label={formatSessionLabel(session)}
            title={formatSessionDescription(session)}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                minHeight: 48,
                padding: "4px 0",
              }}
            >
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    fontWeight: 500,
                    fontSize: 14,
                    lineHeight: "20px",
                    marginBottom: 2,
                  }}
                >
                  {formatSessionLabel(session)}
                </div>
                <div
                  style={{
                    fontSize: 12,
                    color: "#666",
                    lineHeight: "16px",
                  }}
                >
                  {formatSessionDescription(session)}
                </div>
              </div>
              <Button
                type="text"
                size="small"
                danger
                onClick={(e) => handleDeleteSession(session.session_id, e)}
                style={{ marginLeft: 8, flexShrink: 0 }}
              >
                Delete
              </Button>
            </div>
          </Option>
        ))}
      </Select>

      <Tooltip title="Refresh sessions">
        <Button
          icon={<ReloadOutlined />}
          onClick={() => refetchSessions()}
          loading={isLoadingSessions}
          disabled={disabled}
        />
      </Tooltip>

      <Tooltip title="New session">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={toggleNewSessionModal}
          disabled={disabled}
        />
      </Tooltip>
    </Space.Compact>
  );
});

SessionSelector.displayName = "SessionSelector";

export default SessionSelector;
