# AI Assistant Response Format Update

## New Response Structure

The `HandleAIAssistantSessionQuery` function now returns a structured response that separates different types of content for better presentation.

### Response Format

```json
{
  "session_id": "uuid-string",
  "assistant_content": "Main assistant response text",
  "tool_calls": [
    {
      "id": "call_xyz123",
      "type": "function", 
      "function": {
        "name": "tool_name",
        "arguments": "{\"param1\": \"value1\"}"
      }
    }
  ],
  "tool_results": [
    {
      "role": "tool",
      "tool_call_id": "call_xyz123",
      "name": "tool_name",
      "content": "Tool execution result"
    }
  ],
  "new_messages": [
    // All messages that were added to the conversation
  ]
}
```

### Client Display Format

The Python client now displays responses in the requested format:

```
A:
hello there

tool-call-1:
  Function: search_devices
  Arguments: {"query": "network devices"}

tool_result-1:
  Function: search_devices
  Result: Found 5 network devices: router1, switch1, ap1, ap2, gateway1
```

### Changes Made

1. **Server Side (Go)**: Modified `HandleAIAssistantSessionQuery` to structure the response with separate fields for assistant content, tool calls, and tool results.

2. **Client Side (Python)**: Updated `send_message` method to parse the structured response and display it in the requested format with proper color coding.

### Benefits

- **Clear Separation**: Tool calls and results are clearly distinguished from assistant responses
- **Better Readability**: Each component is displayed with appropriate formatting and colors
- **Structured Data**: The response maintains both the formatted display and raw message data for further processing
- **Backward Compatibility**: The `new_messages` field still contains all messages for compatibility

### Usage

No changes required for existing clients - the new format is automatically applied when using the updated server and Python client. 