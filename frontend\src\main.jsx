import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.jsx";
import { BrowserRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "./app/store";
import dayjs from "dayjs";

import "antd/dist/reset.css";
import "./index.css";
import "react-contexify/dist/ReactContexify.css";
import "highlight.js/styles/monokai.css";

dayjs.locale("en");

if (process.env.NODE_ENV === "production") {
  console.log = function () {};
}

ReactDOM.createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>
);
